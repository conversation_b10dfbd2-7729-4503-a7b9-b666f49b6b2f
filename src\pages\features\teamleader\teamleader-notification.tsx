import React, { useState } from 'react';
import { Search, ChevronDown, Bell } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Notifications } from './type/teamleadertype';
import { notificationData } from './common/mockData';


export function TeamleaderNotification() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const isDateInRange = (notificationDate: string, filter: string): boolean => {
    const today = new Date();
    const notifDate = new Date(notificationDate);
    
    switch (filter) {
      case 'Today':
        return notifDate.toDateString() === today.toDateString();
      
      case 'This Week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        weekStart.setHours(0, 0, 0, 0);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        
        return notifDate >= weekStart && notifDate <= weekEnd;
      
      case 'This Month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);
        
        return notifDate >= monthStart && notifDate <= monthEnd;
      
      case 'Last 30 Days':
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        thirtyDaysAgo.setHours(0, 0, 0, 0);
        
        return notifDate >= thirtyDaysAgo && notifDate <= today;
      
      default:
        return true;
    }
  };

  // Filter notifications based on search and date filter
  const filteredNotifications = notificationData.filter(notification => {
    const matchesSearch = notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.id.toString().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || isDateInRange(notification.date, filterStatus);
    return matchesSearch && matchesFilter;
  });

  // Pagination
  const totalRecords = filteredNotifications.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentNotifications = filteredNotifications.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    }
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title */}
      <div className="flex items-center mb-4 sm:mb-6">
        <Bell className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Notification Centre</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
                value={filterStatus} 
                onValueChange={(value) => setFilterStatus(value)}>
                <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
                <SelectValue placeholder="Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Today">Today</SelectItem>
                  <SelectItem value="This Week">This Week</SelectItem>
                  <SelectItem value="This Month">This Month</SelectItem>
                <SelectItem value="Last 30 Days">Last 30 Days</SelectItem>
                </SelectContent>
             </Select>
          </div>
        
        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Results Summary */}
      <div className="mb-3 sm:mb-4">
        <p className="text-xs sm:text-sm text-gray-600">
          Showing {currentNotifications.length} of {totalRecords} notifications
          {filterStatus !== 'All' && (
            <span className="ml-1">
              filtered by <span className="font-medium">{filterStatus}</span>
            </span>
          )}
        </p>
      </div>

      {/* Notifications List */}
      <div className="space-y-3 sm:space-y-4">
        {currentNotifications.length === 0 ? (
          <div className="text-center py-6 sm:py-8">
            <p className="text-gray-500 text-sm sm:text-base">No notifications found matching your criteria.</p>
          </div>
        ) : (
          currentNotifications.map((notification) => (
            <div
              key={notification.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-2 sm:space-x-3">
                <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#4A0000] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 sm:mt-0">
                  <span className="text-white text-xs sm:text-sm font-medium">i</span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-gray-800 text-sm sm:text-base leading-relaxed break-words">
                    {notification.message}
                  </p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalRecords > 0 && (
        <div className="mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="w-full"
          />
        </div>
      )}
    </div>
  );
}