import React from 'react';
import { Search, FileText, RotateCcw, Car } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate, useParams } from 'react-router-dom';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useBookingHistory } from './hook/useBookingHistory';
import { mockBookingsData } from './common/mockdata';
import { BookingHistoryProps } from './type/customer-type';

export function BookingHistoryPage({}: BookingHistoryProps) {
  const { bookingId } = useParams<{ bookingId: string }>();
  const {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    mobileBookings,
    currentBookings,
    totalPages,
    totalRecords,
    getStatusBadge,
    getLoanVehicleBadge,
    handleBookingClick,
    handleViewAgreement,
    handleViewReceipt,
    navigate,
  } = useBookingHistory();

  const BookingCard = ({ booking }: { booking: Booking }) => (
    <div className="bg-white rounded-lg shadow-md border border-gray-200 p-4 mb-4 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div>
          <span 
            className="text-lg font-semibold text-blue-600 cursor-pointer hover:text-blue-800"
            onClick={() => handleBookingClick(booking.id)}
          >
            {booking.id}
          </span>
        </div>
        <div className="flex flex-col gap-2">
          <span className={getStatusBadge(booking.status)}>
            {booking.status}
          </span>
          <span className={getLoanVehicleBadge(booking.loanVehicle)}>
            Loan: {booking.loanVehicle}
          </span>
        </div>
      </div>
      <div className="flex items-center mb-3">
        <Car className="w-6 h-6 mr-2 text-gray-500" />
        <span className="text-sm font-medium text-gray-700">{booking.vehicle}</span>
      </div>
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium mb-1">Pickup</div>
          <div className="flex items-center text-sm">
            <span>{booking.pickupDate}</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <span>{booking.pickupTime}</span>
          </div>
        </div>
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium mb-1">Return</div>
          <div className="flex items-center text-sm">
            <span>{booking.returnDate}</span>
          </div>
          <div className="flex items-center text-sm text-gray-600">
            <span>{booking.returnTime}</span>
          </div>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium mb-1">Total Price</div>
          <div className="flex items-center text-sm font-semibold">
            <span>${booking.totalPrice.toFixed(2)}</span>
          </div>
        </div>
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium mb-1">Outstanding Balance</div>
          <div className="flex items-center text-sm font-semibold">
            <span className={booking.outstandingBalance > 0 ? 'text-red-600' : 'text-green-600'}>
              ${booking.outstandingBalance.toFixed(2)}
            </span>
          </div>
        </div>
      </div>
      <div className="flex justify-end gap-2 pt-3 border-t border-gray-100">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewAgreement(booking.id)}
          className="text-gray-600 hover:text-gray-800"
        >
          <FileText className="w-4 h-4 mr-1" />
          Agreement
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewReceipt(booking.id)}
          className="text-gray-600 hover:text-gray-800"
        >
          <FileText className="w-4 h-4 mr-1" />
          Receipt
        </Button>
      </div>
    </div>
  );

  return (
    <div className="p-4 md:p-6 min-h-screen">
      <div className="flex items-center mb-4 md:mb-6">
        <RotateCcw className="w-6 h-6 mr-3 text-earth-dark" />
        <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Booking History</h1>
      </div>
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterStatus} 
            onValueChange={(value) => setFilterStatus(value)}
          >
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Rental">Rental</SelectItem>
              <SelectItem value="Open">Open</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>
      <div className="block md:hidden">
        {mobileBookings.length > 0 ? (
          <div className="space-y-4">
            {mobileBookings.map((booking) => (
              <BookingCard key={booking.id} booking={booking} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No bookings found matching your search criteria.
          </div>
        )}
      </div>
      <div className="hidden md:block">
        <div className="rounded-md border">
          <Table>
            <TableHeader>
              <TableRow className='bg-gray-50 uppercase'>
                <TableHead>Rental ID</TableHead>
                <TableHead>Pickup Date</TableHead>
                <TableHead>Return Date</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Total Price</TableHead>
                <TableHead>Outstanding Balance</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Loan Vehicle</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentBookings.map((booking) => (
                <TableRow key={booking.id}>
                  <TableCell>
                    <span 
                      className="text-blue-400 cursor-pointer hover:text-blue-800"
                      onClick={() => handleBookingClick(booking.id)}
                    >
                      {booking.id}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div>{booking.pickupDate}</div>
                      <div className="text-gray-500">{booking.pickupTime}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div>{booking.returnDate}</div>
                      <div className="text-gray-500">{booking.returnTime}</div>
                    </div>
                  </TableCell>
                  <TableCell>{booking.vehicle}</TableCell>
                  <TableCell>${booking.totalPrice.toFixed(2)}</TableCell>
                  <TableCell>${booking.outstandingBalance.toFixed(2)}</TableCell>
                  <TableCell>
                    <span className={getStatusBadge(booking.status)}>
                      {booking.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={getLoanVehicleBadge(booking.loanVehicle)}>
                      {booking.loanVehicle}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Button
                        variant="ghost"
                        onClick={() => handleViewAgreement(booking.id)}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <FileText className="w-4 h-4" />
                      </Button>
                      <Button
                        variant="ghost"
                        onClick={() => handleViewReceipt(booking.id)}
                        className="text-gray-600 hover:text-gray-800"
                      >
                        <FileText className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-6"
        />
      </div>
    </div>
  );
}