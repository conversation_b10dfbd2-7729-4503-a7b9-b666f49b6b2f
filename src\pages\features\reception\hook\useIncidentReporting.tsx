import { IncidentBooking } from '../type/reception-type';

export const getStatusBadge = (status: string): string => {
  switch (status) {
    case 'Accept':
      return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
    case 'Decline':
      return 'bg-red-500 text-white px-3 py-1 rounded text-sm font-medium';
    case 'InProgress':
      return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
    default:
      return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
  }
};

export const handleSearch = (booking: IncidentBooking, searchTerm: string): boolean => {
  return (
    booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    booking.vehicle.toLowerCase().includes(searchTerm.toLowerCase())
  );
};

export const handleFilter = (booking: IncidentBooking, filterStatus: string): boolean => {
  return filterStatus === 'All' || booking.reportType === filterStatus;
};