import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, Plus, Calendar } from 'lucide-react';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface AccidentFormSecondData {
  // Other Vehicle/Property Damage
  damageType: 'vehicle' | 'property' | '';
  
  // Driver/Contact Details
  driverName: string;
  phoneNumber: string;
  address: string;
  postCode: string;
  country: string;
  email: string;
  
  // License Details
  dlNumber: string;
  issueCountry: string;
  issueDate: string;
  expiryDate: string;
  conditions: string;
  frontViewImage: File | null;
  backViewImage: File | null;
  
  // Vehicle Details
  vehicleType: string;
  rego: string;
  color: string;
  
  // Damage Images
  interiorImages: File[];
  exteriorImages: File[];
  leftSideImages: File[];
  rightSideImages: File[];
  frontSideImages: File[];
  backSideImages: File[];
  sideMirrorImages: File[];
  vehicleBodyImages: File[];
}

export const AccidentFormSecond: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<AccidentFormSecondData>({
    damageType: '',
    driverName: '',
    phoneNumber: '',
    address: '',
    postCode: '',
    country: 'Australia',
    email: '',
    dlNumber: '',
    issueCountry: '',
    issueDate: '',
    expiryDate: '',
    conditions: '',
    frontViewImage: null,
    backViewImage: null,
    vehicleType: '',
    rego: '',
    color: '',
    interiorImages: [],
    exteriorImages: [],
    leftSideImages: [],
    rightSideImages: [],
    frontSideImages: [],
    backSideImages: [],
    sideMirrorImages: [],
    vehicleBodyImages: []
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRadioChange = (value: 'vehicle' | 'property') => {
    setFormData(prev => ({
      ...prev,
      damageType: value
    }));
  };

  const handleSingleFileUpload = (field: keyof AccidentFormSecondData, file: File | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: file
    }));
  };

  const handleMultipleFileUpload = (field: keyof AccidentFormSecondData, files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        [field]: fileArray
      }));
    }
  };

  const handleGoBack = () => {
    navigate('/admin/workshopMasterAdmin/accident-form');
  };

  const handleNext = () => {
    navigate('/admin/workshopMasterAdmin/accident-policedetails'); 
  };

  return (
    <div className="min-h-screen">
      {/* Go Back Button */}
      <div className="flex items-center mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2'
          size="sm"
          onClick={() => navigate('/admin/workshopMasterAdmin/accident-form')}
          >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
            Go Back
        </Button>
      </div>

      {/* Main Content */}
      <div className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8 p-2 xs:p-3 sm:p-4 md:p-6">
        <h1 className="text-base xs:text-sm sm:text-xl md:text-xl lg:text-2xl font-bold text-gray-800 mb-4 xs:mb-5 sm:mb-6 md:mb-8">Other Details</h1>

        <form className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8">
          {/* Other Vehicle or Property Damage Section */}
          <div>
            <h2 className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-xl font-semibold text-gray-800 mb-3 xs:mb-4 sm:mb-5 md:mb-6">Other Vehicle or Property Damage (If Applicable)</h2>
            
            <div className="mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              <p className="text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600 mb-2 xs:mb-3 sm:mb-4">What Caused Your Accident?</p>
              <div className="flex gap-4 xs:gap-5 sm:gap-6 md:gap-8">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="damageType"
                    value="vehicle"
                    checked={formData.damageType === 'vehicle'}
                    onChange={() => handleRadioChange('vehicle')}
                    className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500"
                  />
                  <span className="ml-1 xs:ml-2 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-700">Vehicle</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="damageType"
                    value="property"
                    checked={formData.damageType === 'property'}
                    onChange={() => handleRadioChange('property')}
                    className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500"
                  />
                  <span className="ml-1 xs:ml-2 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-700">Property</span>
                </label>
              </div>
            </div>

            {/* Vehicle 1 Details Button */}
            <Button
              type="button"
              variant="outline"
              className="border-gray-300 text-gray-700 hover:bg-gray-50 px-2 xs:px-3 sm:px-4 md:px-6 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm mb-4 xs:mb-5 sm:mb-6 md:mb-8"
            >
              <Plus className="mr-1 xs:mr-2 h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5" />
              Vehicle 1 Details
            </Button>

            {/* Driver/Contact Information */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <div className="relative">
                <Input
                  type="text"
                  id="driverName"
                  name="driverName"
                  value={formData.driverName}
                  onChange={handleInputChange}
                  placeholder="Enter"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="driverName" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Driver Name
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="tel"
                  id="phoneNumber"
                  name="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={handleInputChange}
                  placeholder="Enter"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="phoneNumber" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Phone Number
                </Label>
              </div>
            </div>

            <div className="relative mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              <Input
                type="text"
                id="address"
                name="address"
                value={formData.address}
                onChange={handleInputChange}
                placeholder="16 Bendigo st, Melbourne"
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
              />
              <Label htmlFor="address" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Address
              </Label>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <div className="relative">
                <Input
                  type="text"
                  id="postCode"
                  name="postCode"
                  value={formData.postCode}
                  onChange={handleInputChange}
                  placeholder="3064"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="postCode" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Post Code
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="country"
                  name="country"
                  value={formData.country}
                  onChange={handleInputChange}
                  placeholder="Australia"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="country" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Country
                </Label>
              </div>
            </div>

            <div className="relative mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <Input
                type="email"
                id="email"
                name="email"
                value={formData.email}
                onChange={handleInputChange}
                placeholder="Enter"
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
              />
              <Label htmlFor="email" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Email
              </Label>
            </div>
          </div>

          {/* License Details Section */}
          <div>
            <h3 className="text-sm xs:text-base sm:text-lg md:text-lg font-semibold text-gray-800 mb-3 xs:mb-4 sm:mb-5 md:mb-6">License Details :</h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              <div className="relative">
                <Input
                  type="text"
                  id="dlNumber"
                  name="dlNumber"
                  value={formData.dlNumber}
                  onChange={handleInputChange}
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="dlNumber" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  DL Number
                </Label>
              </div>
              <div className="relative">
                <select
                  id="issueCountry"
                  name="issueCountry"
                  value={formData.issueCountry}
                  onChange={handleInputChange}
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent appearance-none"
                >
                  <option value="">Select Country</option>
                  <option value="Australia">Australia</option>
                  <option value="USA">USA</option>
                  <option value="UK">UK</option>
                  <option value="Canada">Canada</option>
                </select>
                <Label htmlFor="issueCountry" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Issue Country
                </Label>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              <div className="relative">
                <Input
                  type="date"
                  id="issueDate"
                  name="issueDate"
                  value={formData.issueDate}
                  onChange={handleInputChange}
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="issueDate" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Issue Date
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="date"
                  id="expiryDate"
                  name="expiryDate"
                  value={formData.expiryDate}
                  onChange={handleInputChange}
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="expiryDate" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Expiry Date
                </Label>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              <div className="relative">
                <select
                  id="conditions"
                  name="conditions"
                  value={formData.conditions}
                  onChange={handleInputChange}
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent appearance-none"
                >
                  <option value="">Select Conditions</option>
                  <option value="None">None</option>
                  <option value="Glasses Required">Glasses Required</option>
                  <option value="Automatic Only">Automatic Only</option>
                  <option value="Daylight Only">Daylight Only</option>
                </select>
                <Label htmlFor="conditions" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Conditions
                </Label>
              </div>
            </div>

            {/* License Image Upload */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Upload Front View
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleSingleFileUpload('frontViewImage', e.target.files?.[0] || null)}
                    className="hidden"
                    id="front-view-image"
                  />
                  <label htmlFor="front-view-image" className="cursor-pointer">
                    {formData.frontViewImage ? (
                      <img 
                        src={URL.createObjectURL(formData.frontViewImage)} 
                        alt="Front View" 
                        className="w-full h-16 xs:h-20 sm:h-24 md:h-28 object-cover rounded"
                      />
                    ) : (
                      <span className="text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">Choose File</span>
                    )}
                  </label>
                </div>
              </div>
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Upload Back View
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleSingleFileUpload('backViewImage', e.target.files?.[0] || null)}
                    className="hidden"
                    id="back-view-image"
                  />
                  <label htmlFor="back-view-image" className="cursor-pointer">
                    {formData.backViewImage ? (
                      <img 
                        src={URL.createObjectURL(formData.backViewImage)} 
                        alt="Back View" 
                        className="w-full h-16 xs:h-20 sm:h-24 md:h-28 object-cover rounded"
                      />
                    ) : (
                      <span className="text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">Choose File</span>
                    )}
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Vehicle Details Section */}
          <div>
            <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold text-gray-800 mb-3 xs:mb-4 sm:mb-5 md:mb-6">Vehicle Details :</h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              <div className="relative">
                <Input
                  type="text"
                  id="vehicleType"
                  name="vehicleType"
                  value={formData.vehicleType}
                  onChange={handleInputChange}
                  placeholder="Enter"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="vehicleType" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Vehicle Type
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="rego"
                  name="rego"
                  value={formData.rego}
                  onChange={handleInputChange}
                  placeholder="Enter"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="rego" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Rego
                </Label>
              </div>
            </div>

            <div className="relative mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <Input
                type="text"
                id="color"
                name="color"
                value={formData.color}
                onChange={handleInputChange}
                placeholder="Enter"
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
              />
              <Label htmlFor="color" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Color
              </Label>
            </div>
          </div>

          {/* Damage Images Section */}
          <div>
            <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold text-gray-800 mb-3 xs:mb-4 sm:mb-5 md:mb-6">Damage Images</h3>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
              {/* Interior Images */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Interior Images
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('interiorImages', e.target.files)}
                    className="hidden"
                    id="interior-images"
                  />
                  <label htmlFor="interior-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Exterior Images */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Exterior Images
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('exteriorImages', e.target.files)}
                    className="hidden"
                    id="exterior-images"
                  />
                  <label htmlFor="exterior-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Left Side Doors */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Left Side Doors
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('leftSideImages', e.target.files)}
                    className="hidden"
                    id="left-side-images"
                  />
                  <label htmlFor="left-side-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Right Side Doors */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Right Side Doors
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('rightSideImages', e.target.files)}
                    className="hidden"
                    id="right-side-images"
                  />
                  <label htmlFor="right-side-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Front Side Images */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Front Side Images
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('frontSideImages', e.target.files)}
                    className="hidden"
                    id="front-side-images"
                  />
                  <label htmlFor="front-side-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Back Side Images */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Back Side Images
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('backSideImages', e.target.files)}
                    className="hidden"
                    id="back-side-images"
                  />
                  <label htmlFor="back-side-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Side Mirrors */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Side Mirrors
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('sideMirrorImages', e.target.files)}
                    className="hidden"
                    id="side-mirror-images"
                  />
                  <label htmlFor="side-mirror-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Vehicle Body */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Vehicle Body
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('vehicleBodyImages', e.target.files)}
                    className="hidden"
                    id="vehicle-body-images"
                  />
                  <label htmlFor="vehicle-body-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-sm md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>
            </div>
          </div>

          {/* Next Button */}
          <div className="flex justify-end mt-4 xs:mt-5 sm:mt-6 md:mt-8">
            <Button
              type="button"
              onClick={handleNext}
              className="bg-[#330101] text-white px-4 xs:px-6 sm:px-8 md:px-10 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm rounded transition-colors"
            >
              Next
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};