import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { CustomerFormData } from './type/reception-type';
import { conditionsOptions, countryOptions, phoneOptions, discountOptions } from './common/mockData';
import { handleSave, handleFrontViewChange, handleBackViewChange, handleInputChange, handleBirthdayChange } from './hook/useAddCustomer'

export function ReceptionCustomerAddPage() {
  const navigate = useNavigate();

  const [formData, setFormData] = useState<CustomerFormData>({
    customerId: `CUST${Math.floor(1000 + Math.random() * 9000)}`, // Generate a random customer ID
    firstName: '',
    lastName: '',
    address: '',
    postCode: '',
    country: '',
    email: '',
    phone: '',
    birthday: { day: '', month: '', year: '' },
    companyName: '',
    emergencyContactName: '',
    emergencyContactNumber: '',
    code: 'Mobile',
    dlNumber: '',
    issueCountry: '',
    issueDate: '',
    expiryDate: '',
    conditions: '',
    password: '',
    isCorporate: '',
    discount: '',
    discountCode: ''
  });
  const [frontViewFile, setFrontViewFile] = useState<File | null>(null);
  const [backViewFile, setBackViewFile] = useState<File | null>(null);
  const [sendEmail, setSendEmail] = useState(false);

  return (
    <div className="min-h-screen">
      <div className="flex items-center mb-4 sm:mb-6">
        <Button
          className="bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2"
          size="sm"
          onClick={() => navigate('/reception/reception-customer')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="bg-white rounded-lg shadow-sm p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
        <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl font-semibold mb-2 sm:mb-4 md:mb-6">Add Customer</h2>

        {/* Customer ID */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.customerId}
            onChange={(e) => handleInputChange('customerId', e.target.value, setFormData)}
            className="bg-gray-200 w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            required
            disabled
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Customer ID
          </Label>
        </div>

        {/* Name Fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value, setFormData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              First Name
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value, setFormData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Last Name
            </Label>
          </div>
        </div>

        {/* Address */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value, setFormData)}
            className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            required
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Address
          </Label>
        </div>

        {/* Post Code and Country */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.postCode}
              onChange={(e) => handleInputChange('postCode', e.target.value, setFormData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Post Code
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Country</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {formData.country || 'Select Country'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {countryOptions.map((country) => (
                  <DropdownMenuItem
                    key={country}
                    onSelect={() => handleInputChange('country', country, setFormData)}
                    className={formData.country === country ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {country || 'Select Country'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Email */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value, setFormData)}
            className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            required
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Email
          </Label>
        </div>

        {/* Phone and Birthday */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value, setFormData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Phone Number
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={`${formData.birthday.year}-${formData.birthday.month.padStart(2, '0')}-${formData.birthday.day.padStart(2, '0')}`}
              onChange={(e) => {
                const [year, month, day] = e.target.value.split('-');
                handleBirthdayChange('day', day, setFormData);
                handleBirthdayChange('month', month, setFormData);
                handleBirthdayChange('year', year, setFormData);
              }}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Birthday
            </Label>
          </div>
        </div>

        {/* Company Name */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.companyName}
            onChange={(e) => handleInputChange('companyName', e.target.value, setFormData)}
            className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Company Name
          </Label>
        </div>

        {/* Emergency Contact Details */}
        <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Emergency Contact Details</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.emergencyContactName}
              onChange={(e) => handleInputChange('emergencyContactName', e.target.value, setFormData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Emergency Contact Person's Name
            </Label>
          </div>
          <div className="relative">
            <div className="flex">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                    {formData.code || 'Mobile'}
                    <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  {phoneOptions.map((code) => (
                    <DropdownMenuItem
                      key={code}
                      onSelect={() => handleInputChange('code', code, setFormData)}
                      className={formData.code === code ? 'bg-amber-100 font-semibold' : ''}
                    >
                      {code || 'Mobile'}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              <Input
                type="tel"
                value={formData.emergencyContactNumber}
                onChange={(e) => handleInputChange('emergencyContactNumber', e.target.value, setFormData)}
                className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-r-md border-l-0 focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
            </div>
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Emergency Contact Number
            </Label>
          </div>
        </div>

        {/* Corporate Customer Section */}
        <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Corporate Customer</h3>
        <div className="mb-4 sm:mb-6 md:mb-8">
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isCorporate === 'true'}
                onChange={(e) => handleInputChange('isCorporate', e.target.checked ? 'true' : '', setFormData)}
                className="mr-2"
              />
              Yes
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isCorporate !== 'true'}
                onChange={(e) => handleInputChange('isCorporate', e.target.checked ? '' : 'true', setFormData)}
                className="mr-2"
              />
              No
            </label>
          </div>
        </div>

        {/* Add Discount Code */}
        <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Discount Code</h3>
        <div className="mb-4 sm:mb-6 md:mb-8">
          <div className="grid grid-cols-2 gap-2 sm:gap-4 md:gap-6">
            <div className="relative">
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Discount Type</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                    {formData.discount || 'Select'}
                    <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  {discountOptions.map((discount) => (
                    <DropdownMenuItem
                      key={discount}
                      onSelect={() => handleInputChange('discount', discount, setFormData)}
                      className={formData.discount === discount ? 'bg-amber-100 font-semibold' : ''}
                    >
                      {discount || 'Select'}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="relative">
              <Input
                type="text"
                value={formData.discountCode}
                onChange={(e) => handleInputChange('discountCode', e.target.value, setFormData)}
                className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                Discount Code
              </Label>
            </div>
          </div>
          <div className="mb-4 sm:mb-6 md:mb-8 relative">
            <Button className="mt-2 px-4 py-2 bg-[#330101] text-white rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
              Generate Code
            </Button>
          </div>
          <div className="grid grid-cols-2 gap-2 sm:gap-4 md:gap-6 mt-2">
            <div className="relative">
              <Input
                type="text"
                value={formData.discountCode}
                readOnly
                className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md bg-gray-50 text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                Discount Code
              </Label>
            </div>
            <div className="flex items-end space-x-2">
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={sendEmail}
                    onChange={(e) => setSendEmail(e.target.checked)}
                    className="mr-2"
                  />
                  Send Email to Customer
                </label>
              </div>
              <Button className="px-6 py-2 bg-[#330101] text-white rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
                Copy
              </Button>
            </div>
          </div>
        </div>

        {/* Driver's License */}
        <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Driver's License</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.dlNumber}
              onChange={(e) => handleInputChange('dlNumber', e.target.value, setFormData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              DL Number
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top16:10:47
            -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Country</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {formData.issueCountry || 'Select Country'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {countryOptions.map((country) => (
                  <DropdownMenuItem
                    key={country}
                    onSelect={() => handleInputChange('issueCountry', country, setFormData)}
                    className={formData.issueCountry === country ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {country || 'Select Country'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="date"
              value={formData.issueDate}
              onChange={(e) => handleInputChange('issueDate', e.target.value, setFormData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Issue Date
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={formData.expiryDate}
              onChange={(e) => handleInputChange('expiryDate', e.target.value, setFormData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Expiry Date
            </Label>
          </div>
        </div>
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Conditions</Label>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                {formData.conditions || 'Select Conditions'}
                <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full">
              {conditionsOptions.map((condition) => (
                <DropdownMenuItem
                  key={condition}
                  onSelect={() => handleInputChange('conditions', condition, setFormData)}
                  className={formData.conditions === condition ? 'bg-amber-100 font-semibold' : ''}
                >
                  {condition || 'Select Conditions'}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Upload Files */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <div className="flex">
              <Input
                type="text"
                value={frontViewFile?.name || 'No file selected'}
                readOnly
                className="flex-1 p-2 sm:p-3 border border-gray-500 rounded-l-md bg-gray-50 text-[10px] sm:text-xs md:text-sm lg:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
              />
              <Label className="flex items-center px-3 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Choose File</span>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleFrontViewChange(e, setFrontViewFile)}
                  className="hidden"
                />
              </Label>
            </div>
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Upload Front View
            </Label>
          </div>
          <div className="relative">
            <div className="flex">
              <Input
                type="text"
                value={backViewFile?.name || 'No file selected'}
                readOnly
                className="flex-1 p-2 sm:p-3 border border-gray-500 rounded-l-md bg-gray-50 text-[10px] sm:text-xs md:text-sm lg:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
              />
              <Label className="flex items-center px-3 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Choose File</span>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={(e) => handleBackViewChange(e, setBackViewFile)}
                  className="hidden"
                />
              </Label>
            </div>
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Upload Back View
            </Label>
          </div>
        </div>

        {/* Password */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value, setFormData)}
            className="bg-gray-200 w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
            disabled
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Password
          </Label>
        </div>

        <Button
          onClick={() => handleSave(navigate, formData, frontViewFile, backViewFile)}
          className="px-2 sm:px-3 md:px-4 py-1 sm:py-2 text-white rounded-md bg-[#330101] transition-colors text-[10px] sm:text-xs md:text-sm lg:text-base"
        >
          Save Customer
        </Button>
      </div>
    </div>
  );
}