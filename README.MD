# Lion Rentals Front-End - Routing Structure

## Overview
This project now has a structured routing system organized by user roles: Customer, Admin, and Staff. Each role has its own dedicated routes, layouts, and components.

## Routing Structure

### 📁 Project Structure
```
src/
├── routes/                     # Route definitions organized by role
│   ├── customer/              
│   │   └── customer-routes.tsx # Customer-specific routes
│   ├── admin/                 
│   │   └── admin-routes.tsx   # Admin-specific routes
│   ├── staff/                 
│   │   └── staff-routes.tsx   # Staff-specific routes
│   └── index.ts               # Route exports
├── components/
│   └── layout/                # Layout components for each role
│       ├── CustomerLayout.tsx # Customer portal layout
│       ├── CustomerSidebar.tsx
│       ├── AdminLayout.tsx    # Admin portal layout
│       ├── AdminSidebar.tsx
│       ├── StaffLayout.tsx    # Staff portal layout
│       ├── StaffSidebar.tsx
│       ├── Header.tsx         # Shared header component
│       └── Layout.tsx         # Legacy layout (for backward compatibility)
└── pages/
    └── features/              # Feature-specific pages
        ├── customerDashboard/
        ├── admin/             # Admin-specific pages
        └── staff/             # Staff-specific pages
```

## Route URLs

### 🔐 Authentication Routes
- `/login` - Login page
- `/forgot-password` - Password reset options
- `/forgot-password/sms` - SMS password reset
- `/forgot-password/email` - Email password reset
- `/verify-otp` - OTP verification
- `/verify-otp-email` - Email OTP verification

### 👤 Customer Routes (`/customer/*`)
- `/customer/dashboard` - Customer dashboard overview
- `/customer/profile` - User profile management
- `/customer/add-rental` - Create new rental booking
- `/customer/booking-history` - View past and current bookings
- `/customer/incident-reporting` - Report incidents
- `/customer/change-requests` - Modify existing bookings
- `/customer/invoices` - View invoices and billing
- `/customer/fines` - View fines and penalties
- `/customer/notifications` - Notification center
- `/customer/activity-log` - Account activity history
- `/customer/settings` - Account settings

### 👨‍💼 Admin Routes (`/admin/*`)
- `/admin/dashboard` - Admin dashboard with system overview
- `/admin/users` - User management (customers & staff)
- `/admin/vehicles` - Vehicle fleet management
- `/admin/bookings` - Booking management and oversight
- `/admin/staff` - Staff management
- `/admin/reports` - Reports and analytics
- `/admin/financial-reports` - Financial reporting
- `/admin/audit-log` - System audit logs
- `/admin/settings` - System configuration

### 👨‍🔧 Staff Routes (`/staff/*`)
- `/staff/dashboard` - Staff dashboard with daily tasks
- `/staff/vehicle-inspection` - Vehicle inspection tools
- `/staff/customer-support` - Customer support interface
- `/staff/booking-assistance` - Help customers with bookings
- `/staff/incident-management` - Handle incident reports
- `/staff/maintenance` - Vehicle maintenance scheduling
- `/staff/tasks` - Task management system
- `/staff/reports` - Staff performance reports

## Layout Features

### 🎨 Customer Layout
- **Color Scheme**: Earth tones (cream background)
- **Navigation**: Customer-focused menu items
- **Features**: Profile access, booking management, support

### 🔴 Admin Layout  
- **Color Scheme**: Red accent colors
- **Navigation**: Administrative controls and system management
- **Features**: User management, system oversight, reporting
- **Icon**: Shield icon indicating security/administration

### 🔵 Staff Layout
- **Color Scheme**: Blue accent colors  
- **Navigation**: Operational tools and customer service
- **Features**: Task management, vehicle operations, customer support
- **Icon**: UserCog icon indicating operational role

## Backward Compatibility

Legacy routes are automatically redirected to customer routes:
- `/dashboard` → `/customer/dashboard`
- `/profile` → `/customer/profile`
- `/add-rental` → `/customer/add-rental`
- (and all other legacy customer routes)

## Navigation Features

Each layout includes:
- **Role-specific header** with appropriate branding
- **Contextual sidebar** with role-based navigation
- **Active state indicators** for current page
- **Responsive design** for mobile and desktop
- **Consistent styling** with role-based color schemes

## Getting Started

1. **Development Server**:
   ```bash
   npm run dev
   ```

2. **Access Different Portals**:
   - Customer Portal: `http://localhost:5175/customer/dashboard`
   - Admin Portal: `http://localhost:5175/admin/dashboard`  
   - Staff Portal: `http://localhost:5175/staff/dashboard`

3. **Login**: Start at `http://localhost:5175/login`

## Adding New Routes

### For Customer Routes:
1. Add component to `src/pages/features/customerDashboard/`
2. Import and add route in `src/routes/customer/customer-routes.tsx`
3. Add navigation item to `src/components/layout/CustomerSidebar.tsx`

### For Admin Routes:
1. Add component to `src/pages/features/admin/`
2. Import and add route in `src/routes/admin/admin-routes.tsx`
3. Add navigation item to `src/components/layout/AdminSidebar.tsx`

### For Staff Routes:
1. Add component to `src/pages/features/staff/`
2. Import and add route in `src/routes/staff/staff-routes.tsx`
3. Add navigation item to `src/components/layout/StaffSidebar.tsx`

## Technology Stack

- **React 18** with TypeScript
- **React Router Dom v6** for routing
- **Tailwind CSS** for styling
- **Lucide React** for icons
- **Vite** for build tooling

## Notes

- Each role has its own isolated routing structure
- Layouts are role-specific with appropriate styling and navigation
- The system supports role-based access control (ready for authentication integration)
- All routes are type-safe with TypeScript
- The design is responsive and follows modern UI/UX patterns
