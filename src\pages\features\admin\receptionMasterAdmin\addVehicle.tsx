import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Upload, Plus, ArrowLeft, Car } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { tr } from 'date-fns/locale';
import { type } from '../../../../components/ui/calendar';

export function ReceptionAddVehiclePage (){
  const [formData, setFormData] = useState({
    // Fleet data
    fleet: '',
    class: '',
    
    // Purchase data
    purchaseDate: '',
    supplier: '',
    owner: '',
    mileage: '',
    financeBank: '',
    noOfRepay: '',
    monthlyPay: '',
    balloon: '',
    priceIncGST: '',
    wdv: '',
    purchasePrice: '',
    expectedRestValue: '',
    odometerAtPurchase: '',
    depreciationTime: '',

    // Vehicle Details
    vehicleRegistration: '',
    oldRegistration: '',
    make: '',
    model: '',
    vin: '',
    type: '',
    yom: '',
    lastMileage: '',
    transmiss: '',
    colour: '',
    fuel: '',
    recordDate: '',
    capacity: '',
    dimension: '',
    availableDate: '',
    availableUntil: '',
    vehicleEntryDate: '',
    vehicleExitDate: '',
    status: 'Available',
    
    // Rates
    rate_1_3: '',
    rate_4_6: '',
    rate7: '',
    longTerm: '',
    rb: '',
    
    // Vehicle Further Details
    location: '',
    regoExpiry: '',
    insuranceClass: '',
    roadSide: '',
    fuelType: '',
    serviceFrequency: '',
    
    // Insurance Providers
    companyName: '',
    
    // Available Policies
    policyReference: '',
    
    // GPS
    gpsStatus: '',
    gpsDevice: '',
    gpsPhoneNumber: '',
    
    // Quality
    lastDetailClean: '',
    lastService: '',
    lastInspection: '',
    
    // Disposal
    date: '',
    value: '',
    buyer: '',
    notes: '',
    
    // Note
    note: ''
  });

  const handleInputChange = (field: string, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleSubmit = () => {
    navigate('/admin/receptionMasterAdmin/fleet/vehicles');
  };
  const navigate = useNavigate();

  return (
    <div className="p-6 min-h-screen">
      <div className="mb-6">
        {/* Go Back Button */}
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={() => navigate('/admin/receptionMasterAdmin/fleet/vehicles')}
        >
          <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
          <span className="hidden md:inline">Go Back</span>
        </Button>

        {/* Title */}
        <div className="flex items-center space-x-2 mt-4">
          <Car className="w-6 h-6 text-[#330101]" />
          <h1 className="text-2xl font-bold text-gray-900">Vehicle - Add</h1>
        </div>
      </div>

      {/* Fleet Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Fleet</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Label htmlFor="fleet" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Fleet</Label>
              <Select value={formData.fleet} onValueChange={(value) => handleInputChange('fleet', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="passenger">Passenger</SelectItem>
                  <SelectItem value="commercial">Commercial</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label htmlFor="class" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Class</Label>
              <Select value={formData.class} onValueChange={(value) => handleInputChange('class', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="economy">Economy</SelectItem>
                  <SelectItem value="economyPlus">Economy Plus </SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Purchase Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Purchase</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Label htmlFor="purchaseDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Purchase Date</Label>
              <Input 
                id="purchaseDate"
                type="date"
                value={formData.purchaseDate}
                onChange={(e) => handleInputChange('purchaseDate', e.target.value)}
                placeholder="dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="supplier" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Supplier</Label>
              <Input 
                id="supplier"
                value={formData.supplier}
                onChange={(e) => handleInputChange('supplier', e.target.value)}
                placeholder="eg. INV-123"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="owner" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Owner</Label>
              <Input 
                id="owner"
                value={formData.owner}
                onChange={(e) => handleInputChange('owner', e.target.value)}
                placeholder="eg. Premier Autos"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="mileage" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Mileage</Label>
              <Input 
                id="mileage"
                value={formData.mileage}
                onChange={(e) => handleInputChange('mileage', e.target.value)}
                placeholder="eg. ********"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="financeBank" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Finance Bank</Label>
              <Input 
                id="financeBank"
                value={formData.financeBank}
                onChange={(e) => handleInputChange('financeBank', e.target.value)}
                placeholder="eg. Barclays"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="noOfRepay" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">No of Repay</Label>
              <Input 
                id="noOfRepay"
                value={formData.noOfRepay}
                onChange={(e) => handleInputChange('noOfRepay', e.target.value)}
                placeholder="eg. 2"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="monthlyPay" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Monthly Pay</Label>
              <Input 
                id="monthlyPay"
                value={formData.monthlyPay}
                onChange={(e) => handleInputChange('monthlyPay', e.target.value)}
                placeholder="eg. $500.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="balloon" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Balloon</Label>
              <Input 
                id="balloon"
                value={formData.balloon}
                onChange={(e) => handleInputChange('balloon', e.target.value)}
                placeholder="eg. $15000.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="priceIncGST" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Price-inc GST</Label>
              <Input 
                id="priceIncGST"
                value={formData.priceIncGST}
                onChange={(e) => handleInputChange('priceIncGST', e.target.value)}
                placeholder="eg. $500.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="wdv" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">WDV</Label>
              <Input 
                id="wdv"
                value={formData.wdv}
                onChange={(e) => handleInputChange('wdv', e.target.value)}
                placeholder="eg. $2000.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="purchasePrice" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Purchase Price</Label>
              <Input 
                id="purchasePrice"
                value={formData.purchasePrice}
                onChange={(e) => handleInputChange('purchasePrice', e.target.value)}
                placeholder="eg. $15000.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="expectedRestValue" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Expected Rest Value</Label>
              <Input 
                id="expectedRestValue"
                value={formData.expectedRestValue}
                onChange={(e) => handleInputChange('expectedRestValue', e.target.value)}
                placeholder="eg. $2000.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="odometerAtPurchase" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Odometer at Purchase</Label>
              <Input 
                id="odometerAtPurchase"
                value={formData.odometerAtPurchase}
                onChange={(e) => handleInputChange('odometerAtPurchase', e.target.value)}
                placeholder="eg. 50000.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
             <div className="md:col-span-1 relative">
              <Label htmlFor="depreciationTime" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Depreciation Time (In Months)</Label>
              <Input
                id="depreciationTime"
                value={formData.depreciationTime}
                onChange={(e) => handleInputChange('depreciationTime', e.target.value)}
                placeholder="03"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Details Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Vehicle Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Label htmlFor="vehicleKey"  className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Vehicle Key</Label>
              <Input 
                id="vehicleKey"
                value={formData.vehicleRegistration}
                onChange={(e) => handleInputChange('vehicleRegistration', e.target.value)}
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="oldRegistration" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Old Rego</Label>
              <Input 
                id="oldRegistration"
                value={formData.oldRegistration}
                onChange={(e) => handleInputChange('oldRegistration', e.target.value)}
                placeholder="eg. ABC123"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="make" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Make</Label>
              <Select value={formData.make} onValueChange={(value) => handleInputChange('make', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none" >
                  <SelectValue placeholder="Select" />
                </SelectTrigger >
                <SelectContent>
                  <SelectItem value="toyota">Toyota</SelectItem>
                  <SelectItem value="honda">Honda</SelectItem>
                  <SelectItem value="ford">Ford</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label htmlFor="model" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Model</Label>
              <Select value={formData.model} onValueChange={(value) => handleInputChange('model', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="corolla">Corolla</SelectItem>
                  <SelectItem value="camry">Camry</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label htmlFor="vin" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">VIN</Label>
              <Input 
                id="vin"
                value={formData.vin}
                onChange={(e) => handleInputChange('vin', e.target.value)}
                placeholder="eg. 1HGBH41JXMN109186"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="type" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Type</Label>
              <Select value={formData.type} onValueChange={(value) => handleInputChange('type', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="car">Car</SelectItem>
                  <SelectItem value="van">Van</SelectItem>
                  <SelectItem value="truck">Truck</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="relative">
              <Label htmlFor="yom" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">YOM</Label>
              <Input 
                id="yom"
                type="date"
                value={formData.yom}
                onChange={(e) => handleInputChange('yom', e.target.value)}
                placeholder="dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
           
            
            <div className="relative">
              <Label htmlFor="lastMileage" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Last Mileage</Label>
              <Input 
                id="lastMileage"
                value={formData.lastMileage}
                onChange={(e) => handleInputChange('lastMileage', e.target.value)}
                placeholder="eg. 100000"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>

            <div className="relative">
              <Label htmlFor="transmiss" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Transmiss</Label>
              <Select value={formData.transmiss} onValueChange={(value) => handleInputChange('transmiss', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="auto">Auto</SelectItem>
                  <SelectItem value="manually">Manually</SelectItem>
                </SelectContent>
              </Select>
            </div>
             <div className="relative">
              <Label htmlFor="colour" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Colour</Label>
              <Input 
                id="colour"
                value={formData.colour}
                onChange={(e) => handleInputChange('colour', e.target.value)}
                placeholder="eg. White"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>

            <div className="relative">
              <Label htmlFor="fuel" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Fuel</Label>
              <Select value={formData.fuel} onValueChange={(value) => handleInputChange('fuel', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="manual">1</SelectItem>
                  <SelectItem value="automatic">2</SelectItem>
                  <SelectItem value="automatic">3</SelectItem>
                  <SelectItem value="automatic">4</SelectItem>
                  <SelectItem value="automatic">5</SelectItem>
                  <SelectItem value="automatic">6</SelectItem>
                  <SelectItem value="automatic">7</SelectItem>
                  <SelectItem value="automatic">8</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label htmlFor="recordDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Record Date</Label>
              <Input 
                id="recordDate"
                type="date"
                value={formData.recordDate}
                onChange={(e) => handleInputChange('recordDate', e.target.value)}
                placeholder="eg. dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="capacity" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Capacity</Label>
              <Input 
                id="capacity"
                value={formData.capacity}
                onChange={(e) => handleInputChange('capacity', e.target.value)}
                placeholder="eg. 5"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="dimension" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Dimensions</Label>
              <Input 
                id="dimension"
                value={formData.dimension}
                onChange={(e) => handleInputChange('dimension', e.target.value)}
                placeholder="eg. 16x7x5"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="availableDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Available Date</Label>
              <Input 
                id="availableDate"
                type="date"
                value={formData.availableDate}
                onChange={(e) => handleInputChange('availableDate', e.target.value)}
                placeholder="dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="availableUntil" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Available Until</Label>
              <Input 
                id="availableUntil"
                type="date"
                value={formData.availableUntil}
                onChange={(e) => handleInputChange('availableUntil', e.target.value)}
                placeholder="dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="vehicleEntryDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Vehicle Entry Date</Label>
              <Input 
                id="vehicleEntryDate"
                type="date"
                value={formData.vehicleEntryDate}
                onChange={(e) => handleInputChange('vehicleEntryDate', e.target.value)}
                placeholder="dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="vehicleExitDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Vehicle Exit Date</Label>
              <Input 
                id="vehicleExitDate"
                type="date"
                value={formData.vehicleExitDate}
                onChange={(e) => handleInputChange('vehicleExitDate', e.target.value)}
                placeholder="dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="status" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Status</Label>
              <Select value={formData.status} onValueChange={(value) => handleInputChange('status', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none">
                  <SelectValue placeholder="Available" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Available">Available</SelectItem>
                  <SelectItem value="Unavailable">Unavailable</SelectItem>
                  <SelectItem value="Maintenance">Maintenance</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Rates Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Rates</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Label htmlFor="rate-1-3" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Rate-1-3</Label>
              <Input 
                id="rate-1-3"
                value={formData.rate_1_3}
                onChange={(e) => handleInputChange('rate-1-3', e.target.value)}
                placeholder="eg. $500.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="rate24" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Rate-4-6</Label>
              <Input 
                id="rate24"
                value={formData.rate_4_6}
                onChange={(e) => handleInputChange('rate24', e.target.value)}
                placeholder="eg. $35.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="rate12" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Rate7+</Label>
              <Input 
                id="rate12"
                value={formData.rate7}
                onChange={(e) => handleInputChange('rate12', e.target.value)}
                placeholder="eg. $30.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="longTerm" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Long Term</Label>
              <Input 
                id="longTerm"
                value={formData.longTerm}
                onChange={(e) => handleInputChange('longTerm', e.target.value)}
                placeholder="eg. $25.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="rb" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">RB</Label>
              <Input 
                id="rate1"
                value={formData.rb}
                onChange={(e) => handleInputChange('rb', e.target.value)}
                placeholder="eg. $37.00"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Vehicle Further Details Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Vehicle Further Details</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Label htmlFor="location" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Location</Label>
              <Input 
                id="location"
                value={formData.location}
                onChange={(e) => handleInputChange('location', e.target.value)}
                placeholder="eg. Somerton"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="regoExpiry" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Rego Expiry</Label>
              <Input 
                id="regoExpiry"
                type="date"
                value={formData.regoExpiry}
                onChange={(e) => handleInputChange('regoExpiry', e.target.value)}
                placeholder="dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="insuranceClass" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Insurance Class</Label>
              <Input 
                id="insuranceClass"
                value={formData.insuranceClass}
                onChange={(e) => handleInputChange('insuranceClass', e.target.value)}
                placeholder="eg. SDP"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="roadSide" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Road Side</Label>
              <Input 
                id="roadSide"
                value={formData.roadSide}
                onChange={(e) => handleInputChange('roadSide', e.target.value)}
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative"> 
              <Label htmlFor="fuelType" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Fuel Type</Label>
              <Select value={formData.fuelType} onValueChange={(value) => handleInputChange('fuelType', value)}>
                <SelectTrigger className="h-12 focus:outline-none focus:border-none">
                  <SelectValue placeholder="Select" />
                </SelectTrigger>
                <SelectContent>
                    <SelectItem value="diesel">Diesel</SelectItem>
                    <SelectItem value="major">Petrol</SelectItem>
                    <SelectItem value="critical">Electric</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label htmlFor="serviceFrequency" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Service Frequency</Label>
              <Input 
                id="serviceFrequency"
                value={formData.serviceFrequency}
                onChange={(e) => handleInputChange('serviceFrequency', e.target.value)}
                placeholder="eg. 6 MONTHS"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Upload Images Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Upload Images</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-4 gap-4">
            <div className="relative">
              <Label className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Upload Images</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <Upload className="mx-auto h-4 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">+ Add Photo</p>
              </div>
            </div>
            <div className="relative">
              <Label className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Exterior Images</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <Upload className="mx-auto h-4 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">+ Add Photo</p>
              </div>
            </div>
            <div className="relative">
              <Label className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Upload Odx Image</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <Upload className="mx-auto h-4 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">+ Add Photo</p>
              </div>
            </div>
            <div className="relative">
              <Label className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Police Side Images</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <Upload className="mx-auto h-4 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">+ Add Photo</p>
              </div>
            </div>
            <div className="relative">
              <Label className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Front Side Images</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <Upload className="mx-auto h-4 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">+ Add Photo</p>
              </div>
            </div>
            <div className="relative">
              <Label className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Police Side Images</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <Upload className="mx-auto h-4 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">+ Add Photo</p>
              </div>
            </div>
            <div className="relative">
              <Label className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Side Various</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <Upload className="mx-auto h-4 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">+ Add Photo</p>
              </div>
            </div>
            <div className="relative">
              <Label className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Other Images</Label>
              <div className="border-2 border-dashed border-gray-300 rounded-lg p-4 text-center">
                <Upload className="mx-auto h-4 text-gray-400 mb-2" />
                <p className="text-sm text-gray-600">+ Add Photo</p>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Insurance Providers Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Insurance Providers</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Label htmlFor="companyName" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Company Name</Label>
            <Input 
              id="companyName"
              value={formData.companyName}
              onChange={(e) => handleInputChange('companyName', e.target.value)}
              placeholder="eg. AXA"
              className="h-12 focus:outline-none focus:border-none w-1/3"
            />
          </div>
        </CardContent>
      </Card>

      {/* Available Policies Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Available Policies</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2  w-1/3">
            <Input 
              value={formData.policyReference}
              onChange={(e) => handleInputChange('policyReference', e.target.value)}
              placeholder="eg. GB Insurance Cover"
              className="flex-1 h-12 focus:outline-none focus:border-none"
            />
            <Button variant="outline" size="sm">
              <Plus className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* GPS Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">GPS</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Label htmlFor="gpsStatus" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">GPS Status</Label>
              <Input 
                id="gpsStatus"
                value={formData.gpsStatus}
                onChange={(e) => handleInputChange('gpsStatus', e.target.value)}
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="gpsDevice" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">GPS - Device</Label>
              <Input 
                id="gpsDevice"
                value={formData.gpsDevice}
                onChange={(e) => handleInputChange('gpsDevice', e.target.value)}
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="gpsPhoneNumber" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">GPS - Phone No</Label>
              <Input 
                id="gpsPhoneNumber"
                value={formData.gpsPhoneNumber}
                onChange={(e) => handleInputChange('gpsPhoneNumber', e.target.value)}
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Quality Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Quality</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            <div className="relative">
              <Label htmlFor="lastDetailClean" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Last Detail Clean</Label>
              <Textarea 
                id="lastDetailClean"
                value={formData.lastDetailClean}
                onChange={(e) => handleInputChange('lastDetailClean', e.target.value)}
                className="h-16 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="lastService" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Last Service</Label>
              <Textarea 
                id="lastService"
                value={formData.lastService}
                onChange={(e) => handleInputChange('lastService', e.target.value)}
                className="h-16 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="lastInspection" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Last Inspection</Label>
              <Textarea 
                id="lastInspection"
                value={formData.lastInspection}
                onChange={(e) => handleInputChange('lastInspection', e.target.value)}
                className="h-16 focus:outline-none focus:border-none"
              />
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Disposal Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Disposal</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="relative">
              <Label htmlFor="disposalDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Date</Label>
              <Input 
                id="disposalDate"
                type="date"
                value={formData.date}
                onChange={(e) => handleInputChange('date', e.target.value)}
                placeholder="dd/mm/yyyy"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="disposalValue" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Value</Label>
              <Input 
                id="disposalValue"
                value={formData.value}
                onChange={(e) => handleInputChange('value', e.target.value)}
                placeholder="eg. $5000"
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
            <div className="relative">
              <Label htmlFor="buyer" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Buyer</Label>
              <Input 
                id="buyer"
                value={formData.buyer}
                onChange={(e) => handleInputChange('buyer', e.target.value)}
                className="h-12 focus:outline-none focus:border-none"
              />
            </div>
          </div>
          <div className="mt-4 relative">
            <Label htmlFor="disposalNotes" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Notes</Label>
            <Textarea 
              id="disposalNotes"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value)}
              className="h-16 focus:outline-none focus:border-none"
            />
          </div>
        </CardContent>
      </Card>

      {/* Note Section */}
      <Card className="border-none shadow-none">
        <CardHeader>
          <CardTitle className="text-lg text-black ">Note</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="relative">
            <Textarea 
              id="note"
              value={formData.note}
              onChange={(e) => handleInputChange('note', e.target.value)}
              placeholder="Note"
              className="h-20 focus:outline-none focus:border-none"
            />
          </div>
        </CardContent>
      </Card>

      {/* Submit Button */}
      <div className="flex justify-end">
        <Button 
          onClick={handleSubmit}
          className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]"
        >
          Add
        </Button>
      </div>
    </div>
  );
}