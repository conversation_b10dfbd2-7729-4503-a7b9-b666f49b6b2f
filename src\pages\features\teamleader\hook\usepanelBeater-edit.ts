import { NavigateFunction } from 'react-router-dom';
import { PanelBeaterEditFormData } from '../type/teamleadertype';

export const handleInputChange = (
  field: keyof PanelBeaterEditFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<PanelBeaterEditFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};

export const handleSave = (navigate: NavigateFunction): void => {
  navigate('/teamleader/panelBeater');
};