import React, { useState } from 'react';
import { Search, Eye, CarFront } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Vehicle } from './type/reception-type';
import { mockVehicles } from './common/mockData';
import {
  filterVehicles,
  getStatusBadge,
  getAvailabilityBadge,
  handleViewVehicle,
  handleSearchChange,
  handleFilterOptionChange,
  handleRecordsPerPageChange,
} from './hook/useVehicle';

export function ReceptionVehicle() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterOption, setFilterOption] = useState<string>('All');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);

  const filteredVehicles = filterVehicles(mockVehicles, searchTerm, filterOption);
  const mobileVehicles = filteredVehicles;
  const totalRecords = filteredVehicles.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentVehicles = filteredVehicles.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const VehicleCard = ({ vehicle }: { vehicle: Vehicle }) => (
    <div className="border border-gray-200 p-4 mb-4 hover:shadow-lg transition-shadow">
      <div className="flex justify-between items-start mb-4">
        <div>
          <span className="text-lg font-semibold text-blue-600">{vehicle.id}</span>
        </div>
        <div className="flex flex-col gap-2">
          <span className={getStatusBadge(vehicle.status)}>{vehicle.status}</span>
          <span className={getAvailabilityBadge(vehicle.availability)}>{vehicle.availability}</span>
        </div>
      </div>
      <div className="mb-4">
        <div className="text-sm font-medium text-gray-700">Class: {vehicle.class}</div>
        <div className="text-sm text-gray-600">Rego: {vehicle.rego}</div>
        <div className="text-sm text-gray-600">VIN: {vehicle.vin}</div>
        <div className="text-sm text-gray-600">Type: {vehicle.type}</div>
        <div className="text-sm text-gray-600">Renter: {vehicle.currentRenter || 'None'}</div>
        <div className="text-sm text-gray-600">Mileage: {vehicle.lastMileage} km</div>
        <div className="text-sm text-gray-600">Fuel: {vehicle.fuel}</div>
      </div>
      <div className="flex justify-end pt-3 border-t border-gray-100">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewVehicle(vehicle.id, navigate)}
          className="text-gray-600 hover:text-gray-800"
        >
          <Eye className="w-4 h-4 mr-1" /> View
        </Button>
      </div>
    </div>
  );

  return (
    <div className="p-4 md:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <CarFront className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Vehicles</h1>
        </div>
      </div>
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterOption} onValueChange={(value) => handleFilterOptionChange(value, setFilterOption, setCurrentPage)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter by Status or Availability" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Operational">Operational</SelectItem>
              <SelectItem value="Under Maintenance">Under Maintenance</SelectItem>
              <SelectItem value="Available">Available</SelectItem>
              <SelectItem value="Rented">Rented</SelectItem>
              <SelectItem value="Unavailable">Unavailable</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => handleSearchChange(e.target.value, setSearchTerm, setCurrentPage)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>
      <div className="block md:hidden">
        {mobileVehicles.length > 0 ? (
          <div className="space-y-4">
            {mobileVehicles.map((vehicle) => (
              <VehicleCard key={vehicle.id} vehicle={vehicle} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No vehicles found matching your search criteria.
          </div>
        )}
      </div>
      <div className="hidden md:block">
        <div className="rounded-md border bg-white">
          <Table className="w-full">
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-[13px]">Rego</TableHead>
                <TableHead className="text-[13px]">VIN</TableHead>
                <TableHead className="text-[13px]">Class</TableHead>
                <TableHead className="text-[13px]">Type</TableHead>
                <TableHead className="text-[13px]">Current Renter</TableHead>
                <TableHead className="text-[13px]">Last Mileage</TableHead>
                <TableHead className="text-[13px]">Fuel</TableHead>
                <TableHead className="text-[13px]">Status</TableHead>
                <TableHead className="text-[13px]">Availability</TableHead>
                <TableHead className="text-[13px]">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentVehicles.map((vehicle) => (
                <TableRow key={vehicle.id}>
                  <TableCell className="text-[13px]">{vehicle.rego}</TableCell>
                  <TableCell className="text-[13px]">{vehicle.vin}</TableCell>
                  <TableCell className="text-[13px]">{vehicle.class}</TableCell>
                  <TableCell className="text-[13px]">{vehicle.type}</TableCell>
                  <TableCell className="text-[13px]">{vehicle.currentRenter || 'None'}</TableCell>
                  <TableCell className="text-[13px]">{vehicle.lastMileage} km</TableCell>
                  <TableCell className="text-[13px]">{vehicle.fuel}</TableCell>
                  <TableCell className="text-[13px]">
                    <span className={getStatusBadge(vehicle.status)}>{vehicle.status}</span>
                  </TableCell>
                  <TableCell className="text-[13px]">
                    <span className={getAvailabilityBadge(vehicle.availability)}>{vehicle.availability}</span>
                  </TableCell>
                  <TableCell className="text-[13px]">
                    <Button
                      variant="ghost"
                      onClick={() => handleViewVehicle(vehicle.id, navigate)}
                      className="text-gray-600 hover:text-gray-800 transition-colors"
                      title="View Vehicle"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => handleRecordsPerPageChange(records, setRecordsPerPage, setCurrentPage)}
          className="mt-6"
        />
      </div>
    </div>
  );
}