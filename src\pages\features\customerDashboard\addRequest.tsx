import React from 'react';
import { ArrowLeft, ClipboardPenLine, Calendar, Clock } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { useNavigate } from 'react-router-dom';
import { useAddRequestHook } from './hook/useAddRequestHook';
import { AddRequestPageProps } from './type/customer-type';
import { mockRentalData } from './common/mockdata';

export function AddRequestPage({}: AddRequestPageProps) {
  const navigate = useNavigate();
  const {
    formData,
    setFormData,
    errors,
    setErrors,
    handleRegoChange,
    handleRequestTypeChange,
    handleInputChange,
    validateForm,
    handleSubmit,
    handleCancel,
  } = useAddRequestHook();

  return (
    <div className="p-6 min-h-screen">
      {/* Header */}
      <div className="mb-6">
         <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={() => navigate('/change-requests')}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
         </Button>
        
        <div className="flex items-center space-x-2 mt-4">
          <ClipboardPenLine className="w-6 h-6 text-[#330101]" />
          <h1 className="text-2xl font-bold text-gray-900">Change Requests - Add</h1>
        </div>
      </div>

      {/* Form */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Rego Selection */}
          <div className="space-y-2">
            <Label htmlFor="rego" className="absolute bg-white px-2 text-xs text-gray-600 z-10 ">
              Rego <span className="text-red-500">*</span>
            </Label>
            <Select value={formData.rego} onValueChange={handleRegoChange}>
              <SelectTrigger className={`w-full h-12 focus:outline-none focus:ring-0 focus:border-none  ${errors.rego ? 'border-red-500' : ''}`}>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {mockRentalData.map((rental) => (
                  <SelectItem key={rental.rego} value={rental.rego}>
                    {rental.rego}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
            {errors.rego && <p className="text-red-500 text-xs">{errors.rego}</p>}
          </div>

          {/* Rental ID */}
          <div className="space-y-2">
            <Label htmlFor="rentalId" className="absolute bg-white px-2 text-xs text-gray-600 z-10">
              Rental ID
            </Label>
            <Input
              id="rentalId"
              value={formData.rentalId}
              readOnly
              className="bg-gray-50 focus:outline-none focus:ring-0 focus:border-none "
            />
          </div>

          {/* Vehicle Class */}
          <div className="space-y-2">
            <Label htmlFor="vehicleClass" className="absolute bg-white px-2 text-xs text-gray-600 z-10">
              Vehicle Class
            </Label>
            <Input
              id="vehicleClass"
              value={formData.vehicleClass}
              readOnly
              className="bg-gray-50 focus:outline-none focus:ring-0 focus:border-none "
            />
          </div>

          {/* Pickup Date */}
          <div className="space-y-2 ">
            <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10 ">
              Pickup Date
            </Label>
            <div className="flex space-x-2">
              <Input
                value={formData.pickupDate}
                readOnly
                className="bg-gray-50 flex-1 focus:outline-none focus:ring-0 focus:border-none "
                placeholder="Date"
              />
              <Input
                value={formData.pickupTime}
                readOnly
                className="bg-gray-50 w-20 focus:outline-none focus:ring-0 focus:border-none "
                placeholder="Time"
              />
            </div>
          </div>

          {/* Return Date */}
          <div className="space-y-2">
            <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10">
              Return Date
            </Label>
            <div className="flex space-x-2">
              <Input
                value={formData.returnDate}
                readOnly
                className="bg-gray-50 flex-1 focus:outline-none focus:ring-0 focus:border-none "
                placeholder="Date"
              />
              <Input
                value={formData.returnTime}
                readOnly
                className="bg-gray-50 w-20 focus:outline-none focus:ring-0 focus:border-none "
                placeholder="Time"
              />
            </div>
          </div>

          {/* Request Type */}
          <div className="space-y-2">
            <Label htmlFor="requestType" className="absolute bg-white px-2 text-xs text-gray-600 z-10">
              Request Type <span className="text-red-500">*</span>
            </Label>
            <Select value={formData.requestType} onValueChange={handleRequestTypeChange}>
              <SelectTrigger className={`w-full h-12 focus:outline-none focus:ring-0 focus:border-none  ${errors.requestType ? 'border-red-500' : ''}`}>
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Extension of Duration">Extension of Duration</SelectItem>
                <SelectItem value="Early Termination">Early Termination</SelectItem>
              </SelectContent>
            </Select>
            {errors.requestType && <p className="text-red-500 text-xs">{errors.requestType}</p>}
          </div>
        </div>

        {/* Conditional Fields */}
        {formData.requestType === 'Extension of Duration' && (
          <div className="mt-6">
            <Label className="text-sm font-medium text-gray-700 mb-4 block">
              Date Range <span className="text-red-500">*</span>
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="extensionFromDate" className="absolute bg-white px-2 text-xs text-gray-600 z-10 ">
                  From Date
                </Label>
                <Input
                  id="extensionFromDate"
                  type="date"
                  value={formData.extensionFromDate}
                  onChange={(e) => handleInputChange('extensionFromDate', e.target.value)}
                  className={`focus:outline-none focus:ring-0 focus:border-none ${errors.extensionFromDate ? 'border-red-500' : ''}`}
                />
                {errors.extensionFromDate && <p className="text-red-500 text-xs">{errors.extensionFromDate}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="extensionToDate" className="absolute bg-white px-2 text-xs text-gray-600 z-10">
                  To Date
                </Label>
                <Input
                  id="extensionToDate"
                  type="date"
                  value={formData.extensionToDate}
                  onChange={(e) => handleInputChange('extensionToDate', e.target.value)}
                  className={`focus:outline-none focus:ring-0 focus:border-none ${errors.extensionToDate ? 'border-red-500' : ''}`}
                />
                {errors.extensionToDate && <p className="text-red-500 text-xs">{errors.extensionToDate}</p>}
              </div>
            </div>
          </div>
        )}

        {formData.requestType === 'Early Termination' && (
          <div className="mt-6">
            <Label className="text-sm font-medium text-gray-700 mb-4 block">
              New Return Date <span className="text-red-500">*</span>
            </Label>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="space-y-2">
                <Label htmlFor="newReturnDate" className="absolute bg-white px-2 text-xs text-gray-600 z-10">
                  Date
                </Label>
                <Input
                  id="newReturnDate"
                  type="date"
                  value={formData.newReturnDate}
                  onChange={(e) => handleInputChange('newReturnDate', e.target.value)}
                  className={`focus:outline-none focus:ring-0 focus:border-none ${errors.newReturnDate ? 'border-red-500' : ''}`}
                />
                {errors.newReturnDate && <p className="text-red-500 text-xs">{errors.newReturnDate}</p>}
              </div>
              <div className="space-y-2">
                <Label htmlFor="newReturnTime" className="absolute bg-white px-2 text-xs text-gray-600 z-10">
                  Time
                </Label>
                <Input
                  id="newReturnTime"
                  type="time"
                  value={formData.newReturnTime}
                  onChange={(e) => handleInputChange('newReturnTime', e.target.value)}
                  className={`focus:outline-none focus:ring-0 focus:border-none ${errors.newReturnTime ? 'border-red-500' : ''}`}
                />
                {errors.newReturnTime && <p className="text-red-500 text-xs">{errors.newReturnTime}</p>}
              </div>
            </div>
          </div>
        )}

        {/* Note */}
        <div className="mt-6 space-y-2">
          <Label htmlFor="note" className="text-sm font-medium text-gray-700">
            Note
          </Label>
          <Textarea
            placeholder="Type Here ..."
            value={formData.note}
            onChange={(e) => handleInputChange('note', e.target.value)}
            className="min-h-[120px] resize-none focus:outline-none focus:ring-0 focus:border-none "
          />
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 mt-8">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="px-6 py-2"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]"
          >
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
}