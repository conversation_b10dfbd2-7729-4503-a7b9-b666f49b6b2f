import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { RequestService } from "../type/mechanictype";

export function useRequestServiceAdd() {
  const navigate = useNavigate();
  const [form, setForm] = useState<RequestService>({
    serviceCode: "SC" + String(Math.floor(Math.random() * 900) + 100), // e.g. SC123
    serviceName: "",
    description: "",
  });

  function handleInputChange(field: keyof RequestService, value: string) {
    setForm(prev => ({ ...prev, [field]: value }));
  }

  function handleAdd() {
    // Submit logic here
    navigate(-1);
  }

  function handleCancel() {
    navigate(-1);
  }

  return { form, handleInputChange, handleAdd, handleCancel };
}