import React, { useState } from 'react';
import { Search, ChevronDown, ChevronLeft, ChevronRight, Wrench, Edit, Eye, ArrowLeft } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate, useParams } from 'react-router-dom';

export function MaintenanceHistory() {
const { vehicleId } = useParams<{ vehicleId: string }>();
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data for maintenance history
  const maintenanceData = [
    {
      serviceType: 'General Maintenance',
      lastServicedate: '12/07/2025',
      lastServiceat: '5000 km',
      nextSrviceat: '10000 km',
    },
    {
      serviceType: 'Accident',
      lastServicedate: '05/06/2025',
      lastServiceat: '-',
      nextSrviceat: '-',
    },
    {
      serviceType: 'Damage',
      lastServicedate: '12/05/2025',
      lastServiceat: '-',
      nextSrviceat: '-',
    },
  ];

  // Filter data based on search term and filter status
  const filteredData = maintenanceData.filter((item) =>
    (item.serviceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.serviceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.nextSrviceat.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || item.nextSrviceat === filterStatus)
  );

  // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);


  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
   <div className="flex flex-col gap-4 mb-4 md:mb-6">
  
  {/* Go Back Button */}
  <div className="flex items-center">
    <Button
      className="flex items-center space-x-2 text-white bg-[#330101] px-4 py-2 rounded"
      onClick={() => navigate('/admin/workshopMasterAdmin/preventive-maintenance')}
    >
      <ArrowLeft className="w-4 h-4" />
      <span>Go Back</span>
    </Button>
  </div>

  {/* Maintenance History Title */}
  <div className="flex items-center space-x-2">
    <Wrench className="w-6 h-6 text-earth-dark" />
    <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Maintenance History</h1>
  </div>

</div>


      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Table View */}
      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>Service Types</TableHead>
              <TableHead>Last Service Date</TableHead>
              <TableHead>Last srvice at</TableHead>
              <TableHead>Next Service at</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item) => (
              <TableRow key={item.serviceType} className="hover:bg-gray-50 transition-colors">
                <TableCell>
                  <span className="font-medium">
                    {item.serviceType}
                  </span>
                </TableCell>
                <TableCell>{item.lastServicedate}</TableCell>
                <TableCell>{item.lastServiceat}</TableCell>
                <TableCell>{item.nextSrviceat}
             
                </TableCell>
         
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-6"
      />
    </div>
  );
}
