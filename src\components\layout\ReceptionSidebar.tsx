import { NavLink, useLocation } from 'react-router-dom';
import { Home, FileWarning, FileEdit, FileText, ClipboardList, ChevronRight, Users, AlertTriangle, Wrench, CarFront, Percent, CircleUser, Bell, Settings, CalendarDays, MessageCircle } from 'lucide-react';
import { useState } from 'react';

export function ReceptionSidebar() {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>({
    'Customer Management': false,
    'Service Management': false,
    'Fleet': false,
  });

  const navItems = [
    {
      path: '/reception/reception-dashboard/:receptionId',
      label: 'Dashboard Overview',
      icon: <Home className="w-5 h-5 mr-3" />
    },
    
    {
      path: '/reception/interaction-management',
      label: 'Interaction Management',
      icon: <MessageCircle className="w-5 h-5 mr-3" />
    },
    {
      path: '/reception/reception-reservantionmanagement',
      label: 'Reservation Management',
      icon: <CalendarDays className="w-5 h-5 mr-3" />
    },
    {
      path: '',
      label: 'Customer Management',
      icon: <CircleUser className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/reception/reception-customer', label: 'Customers', icon: <Users className="w-4 h-4 mr-2" /> },
        { path: '/reception/reception-fines', label: 'Fines', icon: <AlertTriangle className="w-4 h-4 mr-2" /> }
      ]
    },
    {
      path: '',
      label: 'Service Management',
      icon: <Settings className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/reception/reception-incidentReporting', label: 'Incident Reporting', icon: <FileWarning className="w-4 h-4 mr-2" /> },
        { path: '/reception/reception-workshops', label: 'Workshop', icon: <Wrench className="w-4 h-4 mr-2" /> }
      ]
    },
    {
      path: '',
      label: 'Fleet',
      icon: <FileText className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/reception/reception-vehicle', label: 'Vehicle', icon: <CarFront className="w-4 h-4 mr-2" /> },
        { path: '/reception/reception-fleetrates', label: 'Rates', icon: <Percent className="w-4 h-4 mr-2" /> }
      ]
    },
    {
      path: '/reception/reception-changerequest',
      label: 'Change Requests',
      icon: <FileEdit className="w-5 h-5 mr-3" />
    },
    {
      path: '/reception/reception-notificationcentre',
      label: 'Notifications Centre',
      icon: <Bell className="w-5 h-5 mr-3" />
    },
    {
      path: '/reception/reception-activitylogs',
      label: 'Activity Log',
      icon: <ClipboardList className="w-5 h-5 mr-3" />
    },
  ];

  const toggleExpand = (label: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  return (
    <aside className="bg-earth-cream w-64 min-h-screen flex flex-col">
      <nav className="flex-1">
        <div className="p-4 border-b border-gold-lighter">
          <h2 className="text-lg font-semibold text-earth-dark">Reception Portal</h2>
        </div>
        <ul className="space-y-1 pt-4">
          {navItems.map(item => (
            <li key={item.path || item.label}>
              {item.subItems ? (
                <div>
                  <div
                    className={`flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors cursor-pointer ${
                      location.pathname.startsWith(item.subItems[0].path.split('/')[1]) ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                    }`}
                    onClick={() => toggleExpand(item.label)}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                    <ChevronRight
                      className={`w-4 h-4 ml-auto transition-transform ${
                        expandedItems[item.label] ? 'rotate-90' : ''
                      }`}
                    />
                  </div>
                  {expandedItems[item.label] && (
                    <ul className="ml-6 space-y-1">
                      {item.subItems.map(subItem => (
                        <li key={subItem.path}>
                          <NavLink
                            to={subItem.path}
                            className={({ isActive }) =>
                              `flex items-center px-4 py-2 text-earth-dark hover:bg-gold-lighter transition-colors ${
                                isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                              }`
                            }
                          >
                            {subItem.icon}
                            <span>{subItem.label}</span>
                          </NavLink>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors ${
                      isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                    }`
                  }
                >
                  {item.icon}
                  <span>{item.label}</span>
                </NavLink>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}