import { Routes, Route } from 'react-router-dom'
import { AdminLayout } from '../../components/layout/AdminLayout'
import { AdminDashboard } from '../../pages/features/admin/admin-dashboard'
import { MasterAdminQuotes } from '../../pages/features/admin/masterAdmin-quotes'
import { MasterAdminQuotesView } from '../../pages/features/admin/masterAdmin-quotes-view'


import { CustomerMOverview } from '@/pages/features/admin/customerMasterAdmin/customerM-overview'
import { CustomerMCustomers } from '@/pages/features/admin/customerMasterAdmin/customerM-customers'
import { CustomerMDocument } from '@/pages/features/admin/customerMasterAdmin/customerM-document'
import { CustomerMEditcustomer } from '@/pages/features/admin/customerMasterAdmin/customerM-editcustomer'
import { CustomerMAddNewCustomer } from '@/pages/features/admin/customerMasterAdmin/customerM-addnewcustomer'
import { CustomerMFines } from '@/pages/features/admin/customerMasterAdmin/customerM-fines'
import { CustomerMAddfine } from '@/pages/features/admin/customerMasterAdmin/customerM-addfine'
import { CustomerMEditfine } from '@/pages/features/admin/customerMasterAdmin/customerM-editfine'
import { CustomerMInvoices } from '@/pages/features/admin/customerMasterAdmin/customerM-invoices'
import {CustomerMViewInvoice} from '@/pages/features/admin/customerMasterAdmin/customerM-viewInvoice'
import { CustomerMNotificationCentre } from '@/pages/features/admin/customerMasterAdmin/customerM-notificationCentre'
import { CustomerMActivityLog } from '@/pages/features/admin/customerMasterAdmin/customerM-activityLog'
import { CustomerMBookingSummery } from '@/pages/features/admin/customerMasterAdmin/customerM-bookingSummery'
import { ReceptionManagementPage } from '@/pages/features/admin/receptionMasterAdmin/receptionManagement'
import { ReceptionReservationManagement } from '@/pages/features/reception/reservationManagement'
import { ReceptionChangeRequestPage } from '@/pages/features/admin/receptionMasterAdmin/changeRequest'
import { ReceptionRatesPage } from '@/pages/features/admin/receptionMasterAdmin/rates'
import { ReceptionAddRatePage } from '@/pages/features/admin/receptionMasterAdmin/addRate'
import { ReceptionEditRatePage } from '@/pages/features/admin/receptionMasterAdmin/editRate'
import { ReceptionVehiclePage } from '@/pages/features/admin/receptionMasterAdmin/vehicles'
import { ReceptionAddVehiclePage } from '@/pages/features/admin/receptionMasterAdmin/addVehicle'
import { ReceptionEditVehiclePage } from '@/pages/features/admin/receptionMasterAdmin/editVehicle'
import { CustomerMAssignVehicle } from '@/pages/features/admin/customerMasterAdmin/customerM-assignVehicle'
import { CustomerMReturnVehicle } from '@/pages/features/admin/customerMasterAdmin/customerM-returnVehicle'
import { VehicleReservationsPage } from '@/pages/features/admin/receptionMasterAdmin/vehicleReservations'
import { VehicleBlockPeriodPage } from '@/pages/features/admin/receptionMasterAdmin/vehicleBlockPeriod'
import { VehicleRelocationPage } from '@/pages/features/admin/receptionMasterAdmin/vehicleRelocations'
import { VehicleExpensesPage } from '@/pages/features/admin/receptionMasterAdmin/VehicleExpenses'
import { VehicleRepairOrdersPage } from '@/pages/features/admin/receptionMasterAdmin/VehicleRepairOrders'
import { ReceptionMANotificationCentrePage } from '@/pages/features/admin/receptionMasterAdmin/notifications'
import { ReceptionMAActivitylogPage } from '@/pages/features/admin/receptionMasterAdmin/activityLog'
import { MAInteractionManagementPage } from '@/pages/features/admin/receptionMasterAdmin/interactionManagement'
import MAAddInteractionsPage from '@/pages/features/admin/receptionMasterAdmin/addInteractions'
import ReceptionMAoverviewPage from '@/pages/features/admin/receptionMasterAdmin/overview'
import { ReceptionMAIncidentReportingPage } from '@/pages/features/admin/receptionMasterAdmin/incidentReporting'
import { ReceptionMAWorkshopPage } from '@/pages/features/admin/receptionMasterAdmin/workshop'
import { ReceptionMABookingSummary } from '@/pages/features/admin/receptionMasterAdmin/bookingSummary'
import ReceptionOverviewPage from '@/pages/features/reception/overview'


import WorkshopMOverview from '@/pages/features/admin/workshopMasterAdmin/workshopM-overview'
import { FleetSummaryPage } from '@/pages/features/admin/workshopMasterAdmin/fleet-summary'
import { FleetQualityPage } from '@/pages/features/admin/workshopMasterAdmin/fleet-quality'
import { FleetTaskPage } from '@/pages/features/admin/workshopMasterAdmin/fleet-task'
import { FleetTaskViewPage } from '@/pages/features/admin/workshopMasterAdmin/fleet-task-view'
import { PanelBeaterPage } from '@/pages/features/admin/workshopMasterAdmin/panel-beater'
import { PanelBeaterAddPage } from '@/pages/features/admin/workshopMasterAdmin/panel-beater-add'
import { PanelBeaterEditPage } from '@/pages/features/admin/workshopMasterAdmin/panel-beater-edit'
import { VendorPage } from '@/pages/features/admin/workshopMasterAdmin/vendor'
import { VendorAddPage } from '@/pages/features/admin/workshopMasterAdmin/vendor-add'
import { VendorEditPage } from '@/pages/features/admin/workshopMasterAdmin/vendor-edit'
import { RequestedParts } from '@/pages/features/admin/workshopMasterAdmin/requested-parts'
import { RequestedAddParts } from '@/pages/features/admin/workshopMasterAdmin/requested-parts-add'
import { RequestedEditParts } from '@/pages/features/admin/workshopMasterAdmin/requested-parts-edit'
import { RequestedServices } from '@/pages/features/admin/workshopMasterAdmin/requested-services'
import { RequestedServicesAdd } from '@/pages/features/admin/workshopMasterAdmin/requested-services-add'
import { RequestedServicesEdit } from '@/pages/features/admin/workshopMasterAdmin/requested-services-edit'
import { IncidentReporting } from '@/pages/features/admin/workshopMasterAdmin/incident-reporting'
import IncidentReportingEdit from '@/pages/features/admin/workshopMasterAdmin/incident-reporting-edit'
import { AccidentForm } from '@/pages/features/admin/workshopMasterAdmin/accident-form'
import { AccidentFormSecond } from '@/pages/features/admin/workshopMasterAdmin/accident-form-second'
import { AccidentPoliceDetails } from '@/pages/features/admin/workshopMasterAdmin/accident-policedetails'
import { AccidentSignatureForm } from '@/pages/features/admin/workshopMasterAdmin/accident-signatureForm'
import { ServiceManagement } from '@/pages/features/admin/workshopMasterAdmin/service-management'
import { ServiceManagementView } from '@/pages/features/admin/workshopMasterAdmin/service-management-view'
import { ServiceManagementEdit } from '@/pages/features/admin/workshopMasterAdmin/service-management-edit'
import { ServiceMaintenance } from '@/pages/features/admin/workshopMasterAdmin/service-maintenance'
import { ServiceMaintenanceEdit } from '@/pages/features/admin/workshopMasterAdmin/service-maintenance-edit'
import { ServiceAccident } from '@/pages/features/admin/workshopMasterAdmin/service-accident'
import { ServiceAccidentEdit } from '@/pages/features/admin/workshopMasterAdmin/service-accident-edit'
import { ServiceBreakdown } from '@/pages/features/admin/workshopMasterAdmin/service-breakdown'
import { ServiceBreakdownEdit } from '@/pages/features/admin/workshopMasterAdmin/service-breakdown-edit'
import { ServiceDamage } from '@/pages/features/admin/workshopMasterAdmin/service-damage'
import { ServiceDamageEdit } from '@/pages/features/admin/workshopMasterAdmin/service-damage-edit'
import { PreventiveMaintenance } from '@/pages/features/admin/workshopMasterAdmin/preventive-maintenance'
import { MaintenanceHistory } from '@/pages/features/admin/workshopMasterAdmin/maintenance-history'
import { AuditTrail } from '@/pages/features/admin/workshopMasterAdmin/audit-trail'
import { AuditTrailView } from '@/pages/features/admin/workshopMasterAdmin/audit-trail-view'
import { Notification } from '@/pages/features/admin/workshopMasterAdmin/notification'
import { Activity } from '@/pages/features/admin/workshopMasterAdmin/activity'
import { Mechanics } from '@/pages/features/admin/workshopMasterAdmin/mechanics'
import { Teamleaders } from '@/pages/features/admin/workshopMasterAdmin/teamleaders'
import { MasterAdminPayments } from '@/pages/features/admin/masteradmin-payments'
import { MasterAdminViewPayment } from '@/pages/features/admin/masterAdmin-viewPayment'
import { MasterAdminRequestPayment } from '@/pages/features/admin/masterAdmin-requestPayment'
import { CustomerMCheckVehicle } from '@/pages/features/admin/customerMasterAdmin/customerM-checkVehicle'

import { MasterAdminRefund } from '@/pages/features/admin/masterAdmin-refunds'
import {MasterAdminRefundView} from '@/pages/features/admin/masterAdmin-refunds-view';

import MasterAdminReports from '@/pages/features/admin/masteradmin-reports'





// Admin-specific pages (placeholders for now)
/* const UserManagementPage = () => <div className="p-6">User Management Page</div>
const VehicleManagementPage = () => <div className="p-6">Vehicle Management Page</div>
const BookingManagementPage = () => <div className="p-6">Booking Management Page</div>
const ReportsPage = () => <div className="p-6">Reports & Analytics Page</div>
const SystemSettingsPage = () => <div className="p-6">System Settings Page</div>
const AuditLogPage = () => <div className="p-6">Audit Log Page</div>
const StaffManagementPage = () => <div className="p-6">Staff Management Page</div>
const FinancialReportsPage = () => <div className="p-6">Financial Reports Page</div> */

export const AdminRoutes = () => {
  return (
    <Routes>
      <Route element={<AdminLayout />}>
      <Route path="dashboard" element={<AdminDashboard />} />

      <Route path="masteradmin-payments" element={<MasterAdminPayments/>}/>
      <Route path="reports" element={<MasterAdminReports/>}/>
      <Route path="masterAdmin-viewPayment/:reservationID" element={<MasterAdminViewPayment/>}/>

      <Route path="masterAdmin-quotes" element={<MasterAdminQuotes />} />
      <Route path="masterAdmin-quotes-view/:id" element={<MasterAdminQuotesView />} />

      <Route path="masterAdmin-requestPayment" element={<MasterAdminRequestPayment/>}/>


      <Route path="masterAdmin-refunds" element={<MasterAdminRefund />} />
      <Route path="masterAdmin-refund-view/:id" element={<MasterAdminRefundView />} />
      

        {/*   <Route path="users" element={<UserManagementPage />} />
        <Route path="vehicles" element={<VehicleManagementPage />} />
        <Route path="bookings" element={<BookingManagementPage />} />
        <Route path="reports" element={<ReportsPage />} />
        <Route path="staff" element={<StaffManagementPage />} />
        <Route path="financial-reports" element={<FinancialReportsPage />} />
        <Route path="audit-log" element={<AuditLogPage />} />
        <Route path="settings" element={<SystemSettingsPage />} /> */}

            {/* Start Customer Mater admin Routes*/}
        <Route path="customerMasterAdmin/customerM-overview" element={<CustomerMOverview/>}/>
        <Route path="customerMasterAdmin/customerM-customers" element={<CustomerMCustomers/>}/>
        <Route path="customerMasterAdmin/customerM-document" element={<CustomerMDocument/>}/>
        <Route path="customerMasterAdmin/customerM-editcustomer" element={<CustomerMEditcustomer/>}/>
        <Route path="customerMasterAdmin/customerM-addnewcustomer" element={<CustomerMAddNewCustomer/>}/>
        <Route path="customerMasterAdmin/customerM-fines" element={<CustomerMFines/>}/>
        <Route path="customerMasterAdmin/customerM-addfine" element={<CustomerMAddfine/>}/>
        <Route path="customerMasterAdmin/customerM-editfine/:id"element={<CustomerMEditfine/>}/>
        <Route path="customerMasterAdmin/customerM-invoices" element={<CustomerMInvoices/>}/>
        <Route path="customerMasterAdmin/customerM-viewInvoice/:rentalId" element={<CustomerMViewInvoice/>}/>
        <Route path="customerMasterAdmin/customerM-notificationCentre" element={<CustomerMNotificationCentre/>}/>
        <Route path="customerMasterAdmin/customerM-ActivityLog" element={<CustomerMActivityLog/>}/>
        <Route path="customerMasterAdmin/customerM-bookingSummery/:name" element={<CustomerMBookingSummery/>}/>
        <Route path="customerMasterAdmin/customerM-assignVehicle" element={<CustomerMAssignVehicle/>}/>
        <Route path="customerMasterAdmin/customerM-returnVehicle" element={<CustomerMReturnVehicle/>}/>
        <Route path="customerMasterAdmin/customerM-checkVehicle" element={<CustomerMCheckVehicle/>}/>

        {/* End Customer Mater admin Routes*/}


        {/* RECEPTION MA */}


        <Route path="receptionMasterAdmin/dashboard" element={<ReceptionMAoverviewPage />} />
        <Route path= "receptionMasterAdmin/reception-management" element={<ReceptionManagementPage/>}/>
        <Route path= "/reception/reception-dashboard/:receptionId" element={<ReceptionOverviewPage/>}/>
        <Route path= "receptionMasterAdmin/reservations" element={<ReceptionReservationManagement/>} />
        <Route path= "receptionMasterAdmin/reservations/reception-bookingsummary/:bookingId" element={<ReceptionMABookingSummary/>}/>
        <Route path= "receptionMasterAdmin/interactions" element={<MAInteractionManagementPage/>}/>
        <Route path= "receptionMasterAdmin/interactions/add-interaction" element={<MAAddInteractionsPage/>}/>
        <Route path= "receptionMasterAdmin/change-request" element={<ReceptionChangeRequestPage />} />
        <Route path= "receptionMasterAdmin/fleet/rates" element={<ReceptionRatesPage />} />
        <Route path= "receptionMasterAdmin/fleet/rates/new-rate" element={<ReceptionAddRatePage />} />
        <Route path= "receptionMasterAdmin/fleet/rates/edit/:id" element={<ReceptionEditRatePage />} />
        {/* <Route path="receptionMasterAdmin/fleet/vehicles/add-vehicle" element={<ReceptionAddVehiclePage />} /> */}
        {/* <Route path="receptionMasterAdmin/fleet/vehicles/edit-vehicle/:id" element={<ReceptionEditVehiclePage/>} /> */}
        {/* <Route path="receptionMasterAdmin/vehicleReservations" element={<VehicleReservationsPage />} /> */}
        <Route path="receptionMasterAdmin/fleet/vehicles">
          <Route index element={<ReceptionVehiclePage />} />
          <Route path="add-vehicle" element={<ReceptionAddVehiclePage />} />
          <Route path="edit-vehicle/:id" element={<ReceptionEditVehiclePage />} />
          <Route path="reservations/:id" element={<VehicleReservationsPage />} />
          <Route path="blocked-periods/:id" element={<VehicleBlockPeriodPage />} />
          <Route path="relocations/:id" element={<VehicleRelocationPage />} />
          <Route path="expenses/:id" element={<VehicleExpensesPage />} />
          <Route path="repair-orders/:id" element={<VehicleRepairOrdersPage />} />
        </Route>
        <Route path="receptionMasterAdmin/incident-reporting" element={<ReceptionMAIncidentReportingPage />} />
        <Route path="receptionMasterAdmin/workshops" element={<ReceptionMAWorkshopPage />} />
        <Route path="receptionMasterAdmin/notifications" element={<ReceptionMANotificationCentrePage />} />
        <Route path="receptionMasterAdmin/activity" element={<ReceptionMAActivitylogPage />} />


        {/* Workshop MA */}
        <Route path="workshopMasterAdmin/workshopM-overview" element={<WorkshopMOverview/>}/>
        <Route path="workshopMasterAdmin/fleet-summary" element={<FleetSummaryPage/>}/>
        <Route path="workshopMasterAdmin/fleet-quality" element={<FleetQualityPage/>}/>
        <Route path="workshopMasterAdmin/fleet-task" element={<FleetTaskPage/>}/>
        <Route path="workshopMasterAdmin/fleet-task-view/:taskId" element={<FleetTaskViewPage/>}/>
        <Route path="workshopMasterAdmin/panel-beater" element={<PanelBeaterPage/>}/>
        <Route path="workshopMasterAdmin/panel-beater-add" element={<PanelBeaterAddPage/>}/>
        <Route path="workshopMasterAdmin/panel-beater-edit/:id" element={<PanelBeaterEditPage/>}/>
        <Route path="workshopMasterAdmin/vendor" element={<VendorPage/>}/>
        <Route path="workshopMasterAdmin/vendor-add" element={<VendorAddPage/>}/>
        <Route path="workshopMasterAdmin/vendor-edit/:id" element={<VendorEditPage/>}/>
        <Route path="workshopMasterAdmin/requested-parts" element={<RequestedParts/>}/>
        <Route path="workshopMasterAdmin/requested-parts-add" element={<RequestedAddParts/>}/>
        <Route path="workshopMasterAdmin/requested-parts-edit/:partNo" element={<RequestedEditParts/>}/>
        <Route path="workshopMasterAdmin/requested-services" element={<RequestedServices/>}/>
        <Route path="workshopMasterAdmin/requested-services-add" element={<RequestedServicesAdd/>}/>
        <Route path="workshopMasterAdmin/requested-services-edit/:serviceCode" element={<RequestedServicesEdit/>}/>
        <Route path="workshopMasterAdmin/incident-reporting" element={<IncidentReporting/>}/>
        <Route path="workshopMasterAdmin/incident-reporting-edit/:id" element={<IncidentReportingEdit/>}/>
        <Route path="workshopMasterAdmin/accident-form" element={<AccidentForm/>}/>
        <Route path="workshopMasterAdmin/accident-form-second" element={<AccidentFormSecond/>}/>
        <Route path="workshopMasterAdmin/accident-policedetails" element={<AccidentPoliceDetails/>}/>
        <Route path="workshopMasterAdmin/accident-signatureForm" element={<AccidentSignatureForm/>}/>
        <Route path="workshopMasterAdmin/service-management" element={<ServiceManagement/>}/>
        <Route path="workshopMasterAdmin/service-management-view/:vehicleId" element={<ServiceManagementView/>}/>
        <Route path="workshopMasterAdmin/service-management-edit/:vehicleId" element={<ServiceManagementEdit/>}/>
        <Route path="workshopMasterAdmin/service-maintenance" element={<ServiceMaintenance/>}/>
        <Route path="workshopMasterAdmin/service-maintenance-edit/:vehicleId" element={<ServiceMaintenanceEdit/>}/>
        <Route path="workshopMasterAdmin/service-accident" element={<ServiceAccident/>}/>
        <Route path="workshopMasterAdmin/service-accident-edit/:vehicleId" element={<ServiceAccidentEdit/>}/>
        <Route path="workshopMasterAdmin/service-breakdown" element={<ServiceBreakdown/>}/>
        <Route path="workshopMasterAdmin/service-breakdown-edit/:vehicleId" element={<ServiceBreakdownEdit/>}/>
        <Route path="workshopMasterAdmin/service-damage" element={<ServiceDamage/>}/>
        <Route path="workshopMasterAdmin/service-damage-edit/:vehicleId" element={<ServiceDamageEdit/>}/>
        <Route path="workshopMasterAdmin/preventive-maintenance" element={<PreventiveMaintenance/>}/>
        <Route path="workshopMasterAdmin/maintenance-history/:vehicleId" element={<MaintenanceHistory/>}/>
        <Route path="workshopMasterAdmin/audit-trail" element={<AuditTrail/>}/>
        <Route path="workshopMasterAdmin/audit-trail-view/:mechanicId" element={<AuditTrailView/>}/>
        <Route path="workshopMasterAdmin/notification" element={<Notification/>}/>
        <Route path="workshopMasterAdmin/activity" element={<Activity/>}/>
        <Route path="workshopMasterAdmin/mechanics" element={<Mechanics/>}/>
        <Route path="workshopMasterAdmin/teamleaders" element={<Teamleaders/>}/>



        {/* End RECEPTION MA */}

        {/* Start Reception Master admin Routes*/}
        
        {/* End RECEPTION MA */}

        {/* Start Reception Master admin Routes*/}

        {/* End Reception Master admin Routes*/}

        {/* Start Customer Mater admin Routes*/}
       




      </Route>
    </Routes>
  )
}
