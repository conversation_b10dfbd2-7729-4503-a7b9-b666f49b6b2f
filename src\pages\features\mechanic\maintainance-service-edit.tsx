import { useNavigate, useParams } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useMaintainanceServiceEdit } from "./hook/usemaintainance-service-edit";
import { ArrowLeft } from "lucide-react";

export function MaintainanceServiceEditPage() {
  const navigate = useNavigate();
  const { vehicleId } = useParams();
  const { formData, handleInputChange } = useMaintainanceServiceEdit();

  // Status badge helpers
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Pending":
        return "bg-gray-400 text-white";
      case "In Progress":
        return "bg-blue-500 text-white";
      case "Done":
        return "bg-green-500 text-white";
      default:
        return "bg-gray-200 text-gray-700";
    }
  };
  const getVehicleStatusBadge = (status: string) => {
    switch (status) {
      case "Cleaned":
        return "bg-yellow-400 text-white";
      case "Dirty":
        return "bg-red-500 text-white";
      default:
        return "bg-gray-200 text-gray-700";
    }
  };

  return (
    <div className="min-h-screen p-4 md:p-6">
      {/* Back Button - Desktop only */}
      <div className="hidden md:flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <Button
          className="w-full sm:w-auto px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center"
          size="sm"
          onClick={() => navigate('/mechanic/maintainance-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>

      {/* Mobile/Tablet Card View */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/mechanic/maintainance-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
        <div className="bg-white rounded-lg shadow-sm p-4">
          {/* Vehicle Details */}
          <h2 className="text-xl font-semibold mb-2">Vehicle Details</h2>
          <div className="grid grid-cols-1 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle ID</Label>
              <Input
                value={formData.vehicleId}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</Label>
              <Input
                value={formData.model}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Reg No</Label>
              <Input
                value={formData.regNo}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Maintenance Type</Label>
              <Input
                value={formData.maintenanceType}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Maintenance Type Interval</Label>
              <Input
                value={formData.maintenanceTypeInterval}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Odometer at Maintenance Due Date</Label>
              <Input
                value={formData.odometerAtDue}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Current Odometer</Label>
              <Input
                value={formData.currentOdometer}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Current Renter</Label>
              <Input
                value={formData.vehicleCurrentRenter}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Current Location</Label>
              <Input
                value={formData.vehicleCurrentLocation}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</Label>
              <Input
                value={formData.description}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>

          {/* Workshop Details */}
          <div className="flex justify-between items-start mb-2 mt-6">
            <h2 className="text-lg font-semibold">Workshop Details</h2>
            <div className="flex flex-col items-end">
              <span className={`px-3 py-1 rounded text-sm font-medium w-28 flex justify-center items-center ${getStatusBadge(formData.status)}`}>
                {formData.status}
              </span>
              <span className={`mt-1 px-3 py-1 rounded text-sm font-medium flex items-center gap-1 ${getVehicleStatusBadge(formData.vehicleStatus)}`}>
                Vehicle: <span className="font-semibold">{formData.vehicleStatus}</span>
              </span>
            </div>
          </div>
          <div className="grid grid-cols-1 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Branch</Label>
              <Input
                value={formData.branch}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Mechanic Assigned</Label>
              <Input
                value={formData.mechanicAssigned.join(", ")}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Estimated End Date</Label>
              <Input
                type="date"
                value={formData.estimatedEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Actual End Date</Label>
              <Input
                type="date"
                value={formData.actualEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
              <Input
                value={formData.status}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Status</Label>
              <Input
                value={formData.vehicleStatus}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input
                value={formData.comment}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>

          {/* Mechanic Note */}
          <h2 className="text-xl font-semibold mb-4 mt-6">Mechanic Note</h2>
          <div className="grid grid-cols-1 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
              <Select
                value={formData.mechanicNoteStatus}
                onValueChange={value => handleInputChange("mechanicNoteStatus", value)}
              >
                <SelectTrigger className="w-full p-2 border rounded focus:outline-none focus:ring-[#ffde5c] focus:border-transparent text-sm mt-2">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Done">Done</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input
                value={formData.mechanicNoteComment}
                onChange={e => handleInputChange("mechanicNoteComment", e.target.value)}
              />
            </div>
          </div>

          {/* Buttons */}
          <div className="flex flex-row gap-4 mt-6">
            <Button className="bg-[#330101] text-white w-full" onClick={() => {/* handle update logic here */}}>Update</Button>
            <Button className="w-full" variant="outline" onClick={() => navigate("/mechanic/maintainance-service")}>Cancel</Button>
          </div>
        </div>
      </div>

      {/* Desktop View (md and up) */}
      <div className="hidden md:block">
        <div className="bg-white rounded-lg shadow-sm p-4">
          {/* Vehicle Details */}
          <div className="flex justify-between items-start mb-4">
            <h2 className="text-xl font-semibold">Vehicle Details</h2>
            
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle ID</Label>
              <Input
                value={formData.vehicleId}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</Label>
              <Input
                value={formData.model}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Reg No</Label>
              <Input
                value={formData.regNo}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Maintenance Type</Label>
              <Input
                value={formData.maintenanceType}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Maintenance Type Interval</Label>
              <Input
                value={formData.maintenanceTypeInterval}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Odometer at Maintenance Due Date</Label>
              <Input
                value={formData.odometerAtDue}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Current Odometer</Label>
              <Input
                value={formData.currentOdometer}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Current Renter</Label>
              <Input
                value={formData.vehicleCurrentRenter}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Current Location</Label>
              <Input
                value={formData.vehicleCurrentLocation}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="col-span-2 relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</Label>
              <Input
                value={formData.description}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>

          {/* Workshop Details */}
          <div className="flex justify-between items-start mb-4 mt-6">
            <h2 className="text-xl font-semibold">Workshop Details</h2>
           
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Branch</Label>
              <Input
                value={formData.branch}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Mechanic Assigned</Label>
              <Input
                value={formData.mechanicAssigned.join(", ")}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Estimated End Date</Label>
              <Input
                type="date"
                value={formData.estimatedEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Actual End Date</Label>
              <Input
                type="date"
                value={formData.actualEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
              <Input
                value={formData.status}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Status</Label>
              <Input
                value={formData.vehicleStatus}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="col-span-2 relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input
                value={formData.comment}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>

          {/* Mechanic Note */}
          <h2 className="text-xl font-semibold mb-4 mt-6">Mechanic Note</h2>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
              <Select
                value={formData.mechanicNoteStatus}
                onValueChange={value => handleInputChange("mechanicNoteStatus", value)}
              >
                <SelectTrigger className=" w-full p-2 border  rounded focus:outline-none  focus:ring-[#ffde5c] focus:border-transparent text-sm mt-2">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Done">Done</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="col-span-2 relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input
                value={formData.mechanicNoteComment}
                onChange={e => handleInputChange("mechanicNoteComment", e.target.value)}
              />
            </div>
          </div>

          {/* Buttons */}
          <div className="flex gap-4 mt-6">
            <Button className="bg-[#330101] text-white" onClick={() => {/* handle update logic here */}}>Update</Button>
            <Button variant="outline" onClick={() => navigate("/mechanic/maintainance-service")}>Cancel</Button>
          </div>
        </div>
      </div>
    </div>
  );
}