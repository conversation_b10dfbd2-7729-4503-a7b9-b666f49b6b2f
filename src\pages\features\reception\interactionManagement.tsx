import React, { useState } from 'react';
import { Search, Eye, MessageCircle } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import { interactions } from './common/mockData';
import { getStatusBadge, handleViewInteraction } from './hook/useInteraction';

export function InteractionManagementPage() {
  const [searchTerm, setSearchTerm] = useState('');
  const [statusFilter, setStatusFilter] = useState('all');
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);

  const navigate = useNavigate();

  // Updated filtering logic to include status
  const filteredInteractions = interactions.filter(interaction =>
    (
      interaction.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interaction.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interaction.phoneNumber.includes(searchTerm) ||
      interaction.mode.toLowerCase().includes(searchTerm.toLowerCase()) ||
      interaction.purpose.toLowerCase().includes(searchTerm.toLowerCase())
    ) &&
    (statusFilter === 'all' || interaction.status.toLowerCase() === statusFilter.toLowerCase())
  );

  const totalEntries = filteredInteractions.length;
  const totalPages = Math.max(1, Math.ceil(totalEntries / recordsPerPage));
  const paginatedInteractions = filteredInteractions.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  return (
    <div className="p-6 min-h-screen">
      {/* Header */}
      <div className="flex items-center justify-between mb-6">
        <div className="flex items-center gap-2">
          <MessageCircle className="h-6 w-6 text-gray-600" />
          <h1 className="text-2xl font-semibold text-gray-900">Interaction Management</h1>
        </div>
        <Button
          className="bg-red-900 hover:bg-red-800 text-white"
          onClick={() => navigate('add-interaction')}
        >
          Add New Interaction
        </Button>
      </div>

      {/* Search and Filter Section */}
      <div className="flex items-center gap-4 mb-6">
        <Select
          value={statusFilter}
          onValueChange={(value) => {
            setStatusFilter(value);
            setCurrentPage(1); 
          }}
        >
          <SelectTrigger className="w-32 h-12 focus:border-none">
            <SelectValue />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">All</SelectItem>
            <SelectItem value="In Progress">In Progress</SelectItem>
            <SelectItem value="Resolved">Resolved</SelectItem>
            <SelectItem value="Escalated">Escalated</SelectItem>
          </SelectContent>
        </Select>

        <div className="relative flex-1 max-w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            type="text"
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => {
              setSearchTerm(e.target.value);
              setCurrentPage(1); 
            }}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button variant="outline">Save this search</Button>
      </div>

      {/* Table */}
      <div className="bg-white rounded-lg border overflow-hidden">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>Interaction ID</TableHead>
              <TableHead>Customer Name</TableHead>
              <TableHead>Phone Number</TableHead>
              <TableHead>Mode</TableHead>
              <TableHead>Date and Time</TableHead>
              <TableHead>Purpose</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginatedInteractions.map((interaction) => (
              <TableRow key={interaction.id} className="hover:bg-gray-50">
                <TableCell>{interaction.id}</TableCell>
                <TableCell>{interaction.customerName}</TableCell>
                <TableCell>{interaction.phoneNumber}</TableCell>
                <TableCell>{interaction.mode}</TableCell>
                <TableCell>{interaction.dateTime}</TableCell>
                <TableCell>{interaction.purpose}</TableCell>
                <TableCell>
                  <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusBadge(interaction.status)}`}>
                    {interaction.status}
                  </span>
                </TableCell>
                <TableCell>
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-8 w-8 p-0"
                    onClick={() => handleViewInteraction(interaction.id, navigate)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(value) => {
          setRecordsPerPage(value);
          setCurrentPage(1);
        }}
        recordsPerPageOptions={[10, 25, 50]}
        className="mt-6"
      />
    </div>
  );
}