import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { mockChangeRequests } from '../common/mockdata';
import { ChangeRequest, FormErrors } from '../type/customer-type';

export const useEditRequestHook = () => {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const [formData, setFormData] = useState<ChangeRequest>({
    id: '',
    vehicle: '',
    vehicleClass: '',
    pickupDate: '',
    pickupTime: '',
    returnDate: '',
    returnTime: '',
    requestType: 'Extension of Duration',
    status: 'In-Progress',
    note: '',
    extensionFromDate: '',
    extensionToDate: '',
    newReturnDate: '',
    newReturnTime: '',
  });

  const [errors, setErrors] = useState<FormErrors>({});
  const [isLoading, setIsLoading] = useState(false);
  const [isDataLoaded, setIsDataLoaded] = useState(false);

  useEffect(() => {
    if (id) {
      const foundRequest = mockChangeRequests.find(req => req.id === `#${id}`);
      if (foundRequest) {
        setFormData({
          ...foundRequest,
          extensionFromDate: '',
          extensionToDate: '',
          newReturnDate: '',
          newReturnTime: '',
        });
        setIsDataLoaded(true);
      }
    } else {
      setIsDataLoaded(true);
    }
  }, [id]);

  const handleRequestTypeChange = (requestType: string) => {
    setFormData(prev => ({
      ...prev,
      requestType,
      extensionFromDate: '',
      extensionToDate: '',
      newReturnDate: '',
      newReturnTime: ''
    }));
  };

  const handleInputChange = (field: keyof ChangeRequest, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: FormErrors = {};

    if (!formData.vehicle) newErrors.vehicle = 'Please select a rego number';
    if (!formData.requestType) newErrors.requestType = 'Please select a request type';
    
    if (formData.requestType === 'Extension of Duration') {
      if (!formData.extensionFromDate) newErrors.extensionFromDate = 'Please select from date';
      if (!formData.extensionToDate) newErrors.extensionToDate = 'Please select to date';
      if (formData.extensionFromDate && formData.extensionToDate && 
          new Date(formData.extensionFromDate) >= new Date(formData.extensionToDate)) {
        newErrors.extensionToDate = 'To date must be after from date';
      }
    }
    
    if (formData.requestType === 'Early Termination') {
      if (!formData.newReturnDate) newErrors.newReturnDate = 'Please select new return date';
      if (!formData.newReturnTime) newErrors.newReturnTime = 'Please select new return time';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = async () => {
    if (!validateForm()) {
      return;
    }

    setIsLoading(true);
    
    try {
      console.log('Updated request:', formData);
      await new Promise(resolve => setTimeout(resolve, 1000));
      navigate('/customer/change-requests');
    } catch (error) {
      console.error('Error updating request:', error);
    } finally {
      setIsLoading(false);
    }
  };

  const handleCancel = () => {
    setFormData({
      id: '',
      vehicle: '',
      vehicleClass: '',
      pickupDate: '',
      pickupTime: '',
      returnDate: '',
      returnTime: '',
      requestType: '',
      status: 'In-Progress',
      extensionFromDate: '',
      extensionToDate: '',
      newReturnDate: '',
      newReturnTime: '',
      note: ''
    });
    setErrors({});
    navigate('/customer/change-requests');
  };

  const handleGoBack = () => {
    navigate('/customer/change-requests');
  };

  return {
    formData,
    setFormData,
    errors,
    setErrors,
    isLoading,
    setIsLoading,
    isDataLoaded,
    setIsDataLoaded,
    handleRequestTypeChange,
    handleInputChange,
    validateForm,
    handleSubmit,
    handleCancel,
    handleGoBack,
  };
};