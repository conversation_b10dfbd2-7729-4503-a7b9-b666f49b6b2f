import React, { useState } from 'react';
import { Search, Edit, Eye, Plus, ClipboardPenLineIcon, Percent } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

// Define the interface for fleet rates
interface FleetRate {
  id: string;
  vehicleClass: string;
  fleet: string;
  rate1_3: number;
  rate4_6: number;
  rate6_7: number;
  sat: string;
  longTerm: number;
  perDayKM: number;
  additionalKM: number;
  fuelLevel: string;
  serviceFrequency: string;
  status: string;
}

// Mock data for fleet rates (24 rows)
const mockFleetRates: FleetRate[] = [
  { id: '#FR001', vehicleClass: 'Economy', fleet: 'Passenger', rate1_3: 45.00, rate4_6: 42.00, rate6_7: 40.00, sat: '$ -', longTerm: 1000.00, perDayKM: 150, additionalKM: 0.25, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR002', vehicleClass: 'Economy Plus', fleet: 'Passenger', rate1_3: 55.00, rate4_6: 52.00, rate6_7: 50.00, sat: '$ -', longTerm: 1200.00, perDayKM: 200, additionalKM: 0.30, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR003', vehicleClass: 'Mid-size', fleet: 'Passenger', rate1_3: 65.00, rate4_6: 62.00, rate6_7: 60.00, sat: '$ -', longTerm: 1400.00, perDayKM: 250, additionalKM: 0.35, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Inactive' },
  { id: '#FR004', vehicleClass: 'SUV', fleet: 'Passenger', rate1_3: 80.00, rate4_6: 77.00, rate6_7: 75.00, sat: '$ -', longTerm: 1600.00, perDayKM: 300, additionalKM: 0.40, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR005', vehicleClass: 'Luxury', fleet: 'Passenger', rate1_3: 120.00, rate4_6: 115.00, rate6_7: 110.00, sat: '$ -', longTerm: 2000.00, perDayKM: 200, additionalKM: 0.50, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR006', vehicleClass: 'Compact', fleet: 'Passenger', rate1_3: 40.00, rate4_6: 38.00, rate6_7: 36.00, sat: '$ -', longTerm: 900.00, perDayKM: 150, additionalKM: 0.20, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Inactive' },
  { id: '#FR007', vehicleClass: 'Van', fleet: 'Passenger', rate1_3: 90.00, rate4_6: 85.00, rate6_7: 80.00, sat: '$ -', longTerm: 1800.00, perDayKM: 250, additionalKM: 0.45, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR008', vehicleClass: 'Truck', fleet: 'Passenger', rate1_3: 110.00, rate4_6: 105.00, rate6_7: 100.00, sat: '$ -', longTerm: 1900.00, perDayKM: 300, additionalKM: 0.50, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR009', vehicleClass: 'Sedan', fleet: 'Passenger', rate1_3: 60.00, rate4_6: 57.00, rate6_7: 55.00, sat: '$ -', longTerm: 1300.00, perDayKM: 200, additionalKM: 0.30, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Inactive' },
  { id: '#FR010', vehicleClass: 'Hybrid', fleet: 'Passenger', rate1_3: 70.00, rate4_6: 67.00, rate6_7: 65.00, sat: '$ -', longTerm: 1500.00, perDayKM: 250, additionalKM: 0.35, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR011', vehicleClass: 'Economy', fleet: 'Commercial', rate1_3: 45.50, rate4_6: 42.50, rate6_7: 40.50, sat: '$ -', longTerm: 1010.00, perDayKM: 160, additionalKM: 0.26, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR012', vehicleClass: 'Economy Plus', fleet: 'Commercial', rate1_3: 56.00, rate4_6: 53.00, rate6_7: 51.00, sat: '$ -', longTerm: 1210.00, perDayKM: 210, additionalKM: 0.31, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
  { id: '#FR013', vehicleClass: 'Mid-size', fleet: 'Commercial', rate1_3: 66.00, rate4_6: 63.00, rate6_7: 61.00, sat: '$ -', longTerm: 1410.00, perDayKM: 260, additionalKM: 0.36, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Inactive' },
  { id: '#FR014', vehicleClass: 'SUV', fleet: 'Commercial', rate1_3: 81.00, rate4_6: 78.00, rate6_7: 76.00, sat: '$ -', longTerm: 1610.00, perDayKM: 310, additionalKM: 0.41, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Active' },
  { id: '#FR015', vehicleClass: 'Luxury', fleet: 'Commercial', rate1_3: 121.00, rate4_6: 116.00, rate6_7: 111.00, sat:'$ -', longTerm: 2010.00, perDayKM: 210, additionalKM: 0.51, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Active' },
  { id: '#FR016', vehicleClass: 'Compact', fleet: 'Commercial', rate1_3: 41.00, rate4_6: 39.00, rate6_7: 37.00, sat: '$ -', longTerm: 910.00, perDayKM: 160, additionalKM: 0.21, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Inactive' },
  { id: '#FR017', vehicleClass: 'Van', fleet: 'Commercial', rate1_3: 91.00, rate4_6: 86.00, rate6_7: 81.00, sat: '$ -', longTerm: 1810.00, perDayKM: 260, additionalKM: 0.46, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Active' },
  { id: '#FR018', vehicleClass: 'Truck', fleet: 'Commercial', rate1_3: 111.00, rate4_6: 106.00, rate6_7: 101.00, sat: '$ -', longTerm: 1910.00, perDayKM: 310, additionalKM: 0.51, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Active' },
  { id: '#FR019', vehicleClass: 'Sedan', fleet: 'Commercial', rate1_3: 61.00, rate4_6: 58.00, rate6_7: 56.00, sat: '$ -', longTerm: 1310.00, perDayKM: 210, additionalKM: 0.31, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Inactive' },
  { id: '#FR020', vehicleClass: 'Hybrid', fleet: 'Commercial', rate1_3: 71.00, rate4_6: 68.00, rate6_7: 66.00, sat: '$ -', longTerm: 1510.00, perDayKM: 260, additionalKM: 0.36, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Active' },
  { id: '#FR021', vehicleClass: 'Economy', fleet: 'Commercial', rate1_3: 46.00, rate4_6: 43.00, rate6_7: 41.00, sat: '$ -', longTerm: 1020.00, perDayKM: 170, additionalKM: 0.27, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Active' },
  { id: '#FR022', vehicleClass: 'Economy Plus', fleet: 'Commercial', rate1_3: 57.00, rate4_6: 54.00, rate6_7: 52.00, sat: '$ -', longTerm: 1220.00, perDayKM: 220, additionalKM: 0.32, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Active' },
  { id: '#FR023', vehicleClass: 'Mid-size', fleet: 'Commercial', rate1_3: 67.00, rate4_6: 64.00, rate6_7: 62.00, sat: '$ -', longTerm: 1420.00, perDayKM: 270, additionalKM: 0.37, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Inactive' },
  { id: '#FR024', vehicleClass: 'SUV', fleet: 'Commercial', rate1_3: 82.00, rate4_6: 79.00, rate6_7: 77.00, sat: '$ -', longTerm: 1620.00, perDayKM: 320, additionalKM: 0.42, fuelLevel: '2.00', serviceFrequency: '10000km', status: 'Active' },
];

export function ReceptionRatesPage() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [recordsPerPage, setRecordsPerPage] = useState<number>(10);

  const filteredRates = mockFleetRates.filter((rate) => {
  const matchesSearch =
    rate.vehicleClass.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rate.fleet.toLowerCase().includes(searchTerm.toLowerCase()) ||
    rate.id.toLowerCase().includes(searchTerm.toLowerCase());
  const matchesFilter = filterStatus === 'All' || rate.fleet === filterStatus; // Filter by fleet (Passenger/Commercial)
  return matchesSearch && matchesFilter;
});

  // For mobile view rates
  const mobileRates = filteredRates;

  // For desktop view - paginated rates
  const totalRecords = filteredRates.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentRates = filteredRates.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const getStatusBadge = (status: string): string => {
    switch (status) {
      case 'Active':
        return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Inactive':
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };

  const handleEditRate = (id: string) => {
  navigate(`/admin/receptionMasterAdmin/fleet/rates/edit/${id.replace('#', '')}`);
};

  const handleViewRate = (id: string) => {
    navigate(`/reception/fleet-rate/view/${id.replace('#', '')}`);
  };

  // Card Component for Mobile view
  const FleetRateCard = ({ rate }: { rate: FleetRate }) => (
    <div className="border border-gray-200 p-4 mb-4 hover:shadow-lg transition-shadow">
      {/* Header with ID and Status */}
      <div className="flex justify-between items-start mb-4">
        <div>
          <span className="text-lg font-semibold text-blue-600">
            {rate.id}
          </span>
        </div>
        <div className="flex flex-col gap-2">
          <span className={getStatusBadge(rate.status)}>
            {rate.status}
          </span>
        </div>
      </div>

      {/* Vehicle Class and Fleet */}
      <div className="mb-4">
        <div className="text-sm font-medium text-gray-700">Vehicle Class: {rate.vehicleClass}</div>
        <div className="text-sm text-gray-600">Fleet: {rate.fleet}</div>
      </div>

      {/* Rates and Mileage */}
      <div className="mb-4">
        <div className="text-sm text-gray-600">
          Rate 1-3: ${rate.rate1_3.toFixed(2)} | Rate 4-6: ${rate.rate4_6.toFixed(2)} | Rate 6-7: ${rate.rate6_7.toFixed(2)}
        </div>
       <div className="text-sm text-gray-600">
  Sat: {rate.sat} | Long Term: ${rate.longTerm.toFixed(2)} | Per Day KM: {rate.perDayKM} | Additional KM: ${rate.additionalKM.toFixed(2)}
</div>
      </div>

      {/* Fuel and Service */}
      <div className="mb-4">
        <div className="text-sm text-gray-600">Fuel Level: {rate.fuelLevel} | Service Frequency: {rate.serviceFrequency}</div>
      </div>

      {/* Action Buttons */}
      <div className="flex justify-end pt-3 border-t border-gray-100 space-x-2">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleEditRate(rate.id)}
          className="text-gray-600 hover:text-gray-800"
        >
          <Edit className="w-4 h-4 mr-1" /> Edit
        </Button>
        <Button
          variant="ghost"
          size="sm"
          onClick={() => handleViewRate(rate.id)}
          className="text-gray-600 hover:text-gray-800"
        >
          <Eye className="w-4 h-4 mr-1" /> View
        </Button>
      </div>
    </div>
  );

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <Percent className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Rates</h1>
        </div>
        <Button
          className="px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors w-full sm:w-auto"
          onClick={() => navigate('new-rate')}
        
        >
          Add New Rate
        </Button>
     
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Passenger">Passenger</SelectItem>
              <SelectItem value="Commercial">Commercial</SelectItem>
            </SelectContent>
          </Select>
        </div>

          <div className="relative flex-1">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
            <Input
              placeholder="Start typing here..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
            />
          </div>

          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
            Save this search
          </Button>
      </div>

      {/* Mobile Card View (xs to sm) */}
      <div className="block md:hidden">
        {mobileRates.length > 0 ? (
          <div className="space-y-4">
            {mobileRates.map((rate) => (
              <FleetRateCard key={rate.id} rate={rate} />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No fleet rates found matching your search criteria.
          </div>
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block">
        <div className="rounded-md border bg-white">
          <Table className="w-full">
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead>Vehicle Class</TableHead>
                <TableHead>Fleet</TableHead>
                <TableHead>Rate 1-3 ($)</TableHead>
                <TableHead>Rate 4-6 ($)</TableHead>
                <TableHead>Rate 6-7 ($)</TableHead>
                <TableHead>Sat ($)</TableHead>
                <TableHead>Long Term (28 or above)</TableHead>
                <TableHead>Per Day KM</TableHead>
                <TableHead>Additional KM (per km)</TableHead>
                <TableHead>Fuel Level (per gauge)</TableHead>
                <TableHead>Service Frequency</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              {currentRates.map((rate) => (
                <TableRow key={rate.id}>
                  <TableCell>{rate.vehicleClass}</TableCell>
                  <TableCell>{rate.fleet}</TableCell>
                  <TableCell>${rate.rate1_3.toFixed(2)}</TableCell>
                  <TableCell>${rate.rate4_6.toFixed(2)}</TableCell>
                  <TableCell>${rate.rate6_7.toFixed(2)}</TableCell>
                  <TableCell>{rate.sat}</TableCell>
                  <TableCell>${rate.longTerm.toFixed(2)}</TableCell>
                  <TableCell>{rate.perDayKM}km</TableCell>
                  <TableCell>${rate.additionalKM.toFixed(2)}</TableCell>
                  <TableCell>${rate.fuelLevel}</TableCell>
                  <TableCell>{rate.serviceFrequency}</TableCell>
                  <TableCell className="flex space-x-2">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleEditRate(rate.id)}
                      className="text-gray-600 hover:text-gray-800"
                    >
                      <Edit className="w-4 h-4 mr-1" /> 
                    </Button>
                   
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination - Only for desktop */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-6"
        />
      </div>
    </div>
  );
}