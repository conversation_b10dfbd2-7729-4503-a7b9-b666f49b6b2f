import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { IncidentFormData, Reservation, RentalDetails, AccidentReportFields, AdditionalAccidentFields, BreakdownFields } from './type/reception-type';
import { reservationsData, rentalDetailsData } from './common/mockData';
import {
  handleRegoChange,
  handlePhoneNumberChange,
  handleReportTypeChange,
  handleFileChange,
  handleDrugsAlcoholChange,
  handleInputChange,
  handleAccidentFieldChange,
  handleAdditionalAccidentFieldChange,
  handleSubmit
} from './hook/useAddIncidentReport';

export default function ReceptionIncidentReportingAddPage() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<IncidentFormData>({
    rego: '',
    rentalId: '',
    pickupDate: '',
    pickupTime: '',
    returnDate: '',
    returnTime: '',
    customerName: '',
    email: '',
    phoneNumber: '',
    agreementNo: '',
    reportType: '',
    replacementRequested: '',
    vehicleDamaged: '',
    payingExcess: '',
    estimatedAmount: '',
    actualPaid: '',
    totalAmount: '',
    escalateToManager: '',
  });
  const [accidentFields, setAccidentFields] = useState<AccidentReportFields>({
    payment: '',
    insuranceExcessCover: '',
    driverName: '',
    phoneNumber: '',
    email: '',
    birthday: undefined,
    address: '',
    postcode: '',
    country: '',
    drugsAlcoholIncident: '',
    licenseNumber: '',
    issueDate: undefined,
    expiryDate: undefined,
    conditions: '',
    frontView: null,
    backView: null,
    nextDue: undefined,
    obtainDetails: '',
    isDrugsAlcoholConsumed: undefined,
  });
  const [additionalAccidentFields, setAdditionalAccidentFields] = useState<AdditionalAccidentFields>({
    accidentLocation: '',
    damageDescription: '',
    policeReport: null,
    witnessDetails: '',
  });
  const [breakdownFields, setBreakdownFields] = useState<BreakdownFields>({
    interiorImages: null,
    exteriorImages: null,
  });
  const [showPickupCalendar, setShowPickupCalendar] = useState(false);
  const [showReturnCalendar, setShowReturnCalendar] = useState(false);
  const [showBirthdayCalendar, setShowBirthdayCalendar] = useState(false);
  const [showIssueDateCalendar, setShowIssueDateCalendar] = useState(false);
  const [showExpiryDateCalendar, setShowExpiryDateCalendar] = useState(false);
  const [showAdditionalAccidentForm, setShowAdditionalAccidentForm] = useState(false);

  const handleBack = useCallback(() => {
    navigate('/reception/reception-incidentReporting');
  }, [navigate]);

  return (
    <div className="p-4 sm:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex items-center mb-4 sm:mb-6">
        <Button size="default" className="bg-[#330101] text-white px-4 py-2 rounded text-sm sm:text-base" onClick={handleBack}>
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go Back
        </Button>
      </div>

      {/* Form */}
      <div className="max-w-4xl mx-auto">
        {/* Vehicle Details Section */}
        <div className="mb-6 sm:mb-8">
          <h2 className="text-lg sm:text-xl font-semibold mb-4">Vehicle Details</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4 sm:mb-6">
            <div className="relative">
              <Select value={formData.rego} onValueChange={(value) => handleRegoChange(value, setFormData, reservationsData)}>
                <SelectTrigger className="w-full h-[48px] justify-between border border-gray-500 rounded px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm">
                  <SelectValue placeholder="Rego" />
                </SelectTrigger>
                <SelectContent>
                  {reservationsData.map((reservation) => (
                    <SelectItem key={reservation.rego} value={reservation.rego}>
                      {reservation.rego}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Label htmlFor="rego" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Rego*
              </Label>
            </div>

            <div className="relative">
              <Input
                type="text"
                id="rentalId"
                value={formData.rentalId}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                required
              />
              <Label htmlFor="rentalId" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Rental ID*
              </Label>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="text"
                id="pickupDate"
                value={formData.pickupDate}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                required
                onClick={() => setShowPickupCalendar(true)}
              />
              <Label htmlFor="pickupDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Pickup Date*
              </Label>
              {showPickupCalendar && (
                <div className="mt-2">
                  <Calendar
                    mode="single"
                    selected={formData.pickupDate ? new Date(formData.pickupDate) : undefined}
                    onSelect={(date: Date | undefined) => {
                      setFormData((prev) => ({ ...prev, pickupDate: date?.toLocaleDateString() || '' }));
                      setShowPickupCalendar(false);
                    }}
                    className="border rounded-md p-2"
                    initialFocus
                  />
                </div>
              )}
            </div>

            <div className="relative">
              <Input
                type="time"
                id="pickupTime"
                value={formData.pickupTime}
                onChange={(e) => handleInputChange('pickupTime', e.target.value, setFormData)}
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                required
              />
              <Label htmlFor="pickupTime" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Pickup Time*
              </Label>
            </div>

            <div className="relative">
              <Input
                type="text"
                id="returnDate"
                value={formData.returnDate}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                required
                onClick={() => setShowReturnCalendar(true)}
              />
              <Label htmlFor="returnDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Return Date*
              </Label>
              {showReturnCalendar && (
                <div className="mt-2">
                  <Calendar
                    mode="single"
                    selected={formData.returnDate ? new Date(formData.returnDate) : undefined}
                    onSelect={(date: Date | undefined) => {
                      setFormData((prev) => ({ ...prev, returnDate: date?.toLocaleDateString() || '' }));
                      setShowReturnCalendar(false);
                    }}
                    className="border rounded-md p-2"
                    initialFocus
                  />
                </div>
              )}
            </div>

            <div className="relative">
              <Input
                type="time"
                id="returnTime"
                value={formData.returnTime}
                onChange={(e) => handleInputChange('returnTime', e.target.value, setFormData)}
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                required
              />
              <Label htmlFor="returnTime" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Return Time*
              </Label>
            </div>
          </div>
        </div>

        {/* Renter Details Section */}
        <div className="mb-6 sm:mb-8">
          <h2 className="text-lg sm:text-xl font-semibold mb-4">Renter Details</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4 sm:mb-6">
            <div className="space-y-4">
              <div className="relative">
                <Input
                  type="tel"
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={(e) => handlePhoneNumberChange(e.target.value, setFormData, rentalDetailsData)}
                  className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  placeholder="Enter Phone Number"
                  required
                />
                <Label className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Phone Number*
                </Label>
              </div>

              <div className="relative">
                <Input
                  type="text"
                  id="customerName"
                  value={formData.customerName}
                  onChange={(e) => handleInputChange('customerName', e.target.value, setFormData)}
                  className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                />
                <Label className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Customer Name*
                </Label>
              </div>

              <div className="relative">
                <Select value={formData.reportType} onValueChange={(value) => handleReportTypeChange(value, setFormData, setAccidentFields, setAdditionalAccidentFields, setBreakdownFields, setShowAdditionalAccidentForm, formData)}>
                  <SelectTrigger className="w-full h-[48px] justify-between border border-gray-500 rounded px-3 py-2 focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm">
                    <SelectValue placeholder="Report Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Accident">Accident</SelectItem>
                    <SelectItem value="Breakdown">Breakdown</SelectItem>
                    <SelectItem value="General Maintenance">General Maintenance</SelectItem>
                    <SelectItem value="Damage">Damage</SelectItem>
                  </SelectContent>
                </Select>
                <Label htmlFor="reportType" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Report Type*
                </Label>
              </div>
            </div>

            <div className="space-y-4">
              <div className="relative">
                <Input
                  type="email"
                  id="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value, setFormData)}
                  className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                />
                <Label className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Email*
                </Label>
              </div>

              <div className="relative">
                <Input
                  type="text"
                  id="agreementNo"
                  value={formData.agreementNo}
                  onChange={(e) => handleInputChange('agreementNo', e.target.value, setFormData)}
                  className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                />
                <Label className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Agreement Number*
                </Label>
              </div>
            </div>
          </div>
        </div>

        {['Accident', 'Breakdown', 'General Maintenance', 'Damage'].includes(formData.reportType) && (
          <>
            {/* Questions Section */}
            <div className="mb-6 sm:mb-8 space-y-4">
              <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                <span className="text-sm font-medium">Is the customer requesting a replacement?</span>
                <div className="flex items-center gap-4">
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="replacementRequested"
                      value="Yes"
                      checked={formData.replacementRequested === 'Yes'}
                      onChange={(e) => handleInputChange('replacementRequested', e.target.value, setFormData)}
                      className="w-4 h-4"
                    />
                    <span className="text-sm">Yes</span>
                  </label>
                  <label className="flex items-center gap-2">
                    <input
                      type="radio"
                      name="replacementRequested"
                      value="No"
                      checked={formData.replacementRequested === 'No'}
                      onChange={(e) => handleInputChange('replacementRequested', e.target.value, setFormData)}
                      className="w-4 h-4"
                    />
                    <span className="text-sm">No</span>
                  </label>
                </div>
              </div>

              {formData.reportType !== 'Accident' && formData.reportType !== 'Damage' && (
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  <span className="text-sm font-medium">Is the vehicle damaged?</span>
                  <div className="flex items-center gap-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="vehicleDamaged"
                        value="Yes"
                        checked={formData.vehicleDamaged === 'Yes'}
                        onChange={(e) => handleInputChange('vehicleDamaged', e.target.value, setFormData)}
                        className="w-4 h-4"
                      />
                      <span className="text-sm">Yes</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="vehicleDamaged"
                        value="No"
                        checked={formData.vehicleDamaged === 'No'}
                        onChange={(e) => handleInputChange('vehicleDamaged', e.target.value, setFormData)}
                        className="w-4 h-4"
                      />
                      <span className="text-sm">No</span>
                    </label>
                  </div>
                </div>
              )}

              {(formData.replacementRequested === 'Yes' && formData.vehicleDamaged === 'Yes') || 
               (formData.replacementRequested === 'No' && formData.vehicleDamaged === 'Yes') ? (
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  <span className="text-sm font-medium">Is the customer paying excess?</span>
                  <div className="flex items-center gap-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="payingExcess"
                        value="Yes"
                        checked={formData.payingExcess === 'Yes'}
                        onChange={(e) => handleInputChange('payingExcess', e.target.value, setFormData)}
                        className="w-4 h-4"
                      />
                      <span className="text-sm">Yes</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="payingExcess"
                        value="No"
                        checked={formData.payingExcess === 'No'}
                        onChange={(e) => handleInputChange('payingExcess', e.target.value, setFormData)}
                        className="w-4 h-4"
                      />
                      <span className="text-sm">No</span>
                    </label>
                  </div>
                </div>
              ) : null}
            </div>

            {/* Amount Fields */}
            {(formData.replacementRequested === 'Yes' && formData.vehicleDamaged === 'Yes') || 
             (formData.replacementRequested === 'No' && formData.vehicleDamaged === 'Yes') ? (
              <div className="mb-6 sm:mb-8">
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 sm:gap-6">
                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="estimatedAmount" className="text-sm font-medium">Estimated Amount</Label>
                      <Input
                        id="estimatedAmount"
                        placeholder="$ 500.00"
                        value={formData.estimatedAmount}
                        onChange={(e) => handleInputChange('estimatedAmount', e.target.value, setFormData)}
                        className="mt-1 focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent border border-gray-500 text-sm"
                      />
                    </div>

                    <div>
                      <Label htmlFor="totalAmount" className="text-sm font-medium">Total Amount</Label>
                      <Input
                        id="totalAmount"
                        placeholder="$ 500.00"
                        value={formData.totalAmount}
                        onChange={(e) => handleInputChange('totalAmount', e.target.value, setFormData)}
                        className="mt-1 focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent border border-gray-500 text-sm"
                      />
                    </div>
                  </div>

                  <div className="space-y-4">
                    <div>
                      <Label htmlFor="actualPaid" className="text-sm font-medium">Actual Paid</Label>
                      <Input
                        id="actualPaid"
                        placeholder="$ 500.00"
                        value={formData.actualPaid}
                        onChange={(e) => handleInputChange('actualPaid', e.target.value, setFormData)}
                        className="mt-1 focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent border border-gray-500 text-sm"
                      />
                    </div>
                  </div>
                </div>
              </div>
            ) : null}

            {/* Escalate to Manager */}
            {formData.replacementRequested === 'Yes' && formData.payingExcess === 'No' ? (
              <div className="mb-6 sm:mb-8">
                <div className="flex flex-col sm:flex-row items-start sm:items-center gap-4">
                  <span className="text-sm font-medium">Escalate to Manager Admin</span>
                  <div className="flex items-center gap-4">
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="escalateToManager"
                        value="Yes"
                        checked={formData.escalateToManager === 'Yes'}
                        onChange={(e) => handleInputChange('escalateToManager', e.target.value, setFormData)}
                        className="w-4 h-4"
                      />
                      <span className="text-sm">Yes</span>
                    </label>
                    <label className="flex items-center gap-2">
                      <input
                        type="radio"
                        name="escalateToManager"
                        value="No"
                        checked={formData.escalateToManager === 'No'}
                        onChange={(e) => handleInputChange('escalateToManager', e.target.value, setFormData)}
                        className="w-4 h-4"
                      />
                      <span className="text-sm">No</span>
                    </label>
                  </div>
                </div>
              </div>
            ) : null}
          </>
        )}

        {/* Accident Report Fields */}
        {formData.reportType === 'Accident' && !showAdditionalAccidentForm && (
          <div className="space-y-4">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-800">Driver Details</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="driverName"
                  value={accidentFields.driverName}
                  onChange={(e) => handleAccidentFieldChange('driverName', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Driver Name"
                  required
                />
                <Label htmlFor="driverName" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Driver Name*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="tel"
                  id="phoneNumber"
                  value={accidentFields.phoneNumber}
                  onChange={(e) => handleAccidentFieldChange('phoneNumber', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Phone Number"
                  required
                />
                <Label htmlFor="phoneNumber" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Phone Number*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="email"
                  id="email"
                  value={accidentFields.email}
                  onChange={(e) => handleAccidentFieldChange('email', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Email"
                  required
                />
                <Label htmlFor="email" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Email*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="birthday"
                  value={accidentFields.birthday ? accidentFields.birthday.toLocaleDateString() : ''}
                  readOnly
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Select Birthday"
                  required
                  onClick={() => setShowBirthdayCalendar(true)}
                />
                <Label htmlFor="birthday" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Birthday*
                </Label>
                {showBirthdayCalendar && (
                  <div className="mt-2">
                    <Calendar
                      mode="single"
                      selected={accidentFields.birthday}
                      onSelect={(date: Date | undefined) => {
                        handleAccidentFieldChange('birthday', date, setAccidentFields);
                        setShowBirthdayCalendar(false);
                      }}
                      className="border rounded-md p-2"
                      initialFocus
                    />
                  </div>
                )}
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="address"
                  value={accidentFields.address}
                  onChange={(e) => handleAccidentFieldChange('address', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Address"
                  required
                />
                <Label htmlFor="address" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Address*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="postcode"
                  value={accidentFields.postcode}
                  onChange={(e) => handleAccidentFieldChange('postcode', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Postcode"
                  required
                />
                <Label htmlFor="postcode" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Postcode*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="country"
                  value={accidentFields.country}
                  onChange={(e) => handleAccidentFieldChange('country', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Country"
                  required
                />
                <Label htmlFor="country" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Country*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Label className="text-sm font-medium text-gray-700">Drugs or alcohol consumed in the 12 hours prior to the incident:*</Label>
                <div className="flex items-center gap-6 mt-2">
                  <label className="flex items-center gap-2 text-sm text-gray-600">
                    <Input
                      type="radio"
                      name="drugsAlcohol"
                      checked={accidentFields.isDrugsAlcoholConsumed === true}
                      onChange={() => handleDrugsAlcoholChange(true, setAccidentFields)}
                      className="w-4 h-4"
                    />
                    Yes
                  </label>
                  <label className="flex items-center gap-2 text-sm text-gray-600">
                    <Input
                      type="radio"
                      name="drugsAlcohol"
                      checked={accidentFields.isDrugsAlcoholConsumed === false}
                      onChange={() => handleDrugsAlcoholChange(false, setAccidentFields)}
                      className="w-4 h-4"
                    />
                    No
                  </label>
                </div>
              </div>
            </div>

            {/* Driver’s License Section */}
            <div className="flex justify-between items-center mb-4 sm:mb-6">
              <h2 className="text-lg sm:text-xl font-semibold text-gray-800">Driver’s License</h2>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="licenseNumber"
                  value={accidentFields.licenseNumber}
                  onChange={(e) => handleAccidentFieldChange('licenseNumber', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter DL Number"
                  required
                />
                <Label htmlFor="licenseNumber" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  DL Number*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="licenseIssueCountry"
                  value={accidentFields.country}
                  onChange={(e) => handleAccidentFieldChange('country', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Issue Country"
                  required
                />
                <Label htmlFor="licenseIssueCountry" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Issue Country*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="licenseIssueDate"
                  value={accidentFields.issueDate ? accidentFields.issueDate.toLocaleDateString() : ''}
                  readOnly
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Select Issue Date"
                  required
                  onClick={() => setShowIssueDateCalendar(true)}
                />
                <Label htmlFor="licenseIssueDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Issue Date*
                </Label>
                {showIssueDateCalendar && (
                  <div className="mt-2">
                    <Calendar
                      mode="single"
                      selected={accidentFields.issueDate}
                      onSelect={(date: Date | undefined) => {
                        handleAccidentFieldChange('issueDate', date, setAccidentFields);
                        setShowIssueDateCalendar(false);
                      }}
                      className="border rounded-md p-2"
                      initialFocus
                    />
                  </div>
                )}
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="licenseExpiryDate"
                  value={accidentFields.expiryDate ? accidentFields.expiryDate.toLocaleDateString() : ''}
                  readOnly
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Select Expiry Date"
                  required
                  onClick={() => setShowExpiryDateCalendar(true)}
                />
                <Label htmlFor="licenseExpiryDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Expiry Date*
                </Label>
                {showExpiryDateCalendar && (
                  <div className="mt-2">
                    <Calendar
                      mode="single"
                      selected={accidentFields.expiryDate}
                      onSelect={(date: Date | undefined) => {
                        handleAccidentFieldChange('expiryDate', date, setAccidentFields);
                        setShowExpiryDateCalendar(false);
                      }}
                      className="border rounded-md p-2"
                      initialFocus
                    />
                  </div>
                )}
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="licenseType"
                  value={accidentFields.conditions}
                  onChange={(e) => handleAccidentFieldChange('conditions', e.target.value, setAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Conditions"
                  required
                />
                <Label htmlFor="licenseType" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Conditions*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="mt-4 relative">
                <Label htmlFor="frontView" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Upload Front View*
                </Label>
                <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                  {accidentFields.frontView && (
                    <img
                      src={URL.createObjectURL(accidentFields.frontView)}
                      alt="Front view preview"
                      className="max-w-full max-h-32 object-contain mb-2"
                    />
                  )}
                  <input
                    type="file"
                    id="frontView"
                    accept="image/*"
                    onChange={(e) => handleFileChange('frontView', e, accidentFields, setAccidentFields)}
                    className="w-full mt-2"
                  />
                </div>
                {accidentFields.frontView && <p className="text-sm text-gray-600 mt-1">{accidentFields.frontView.name}</p>}
              </div>

              <div className="mt-4 relative">
                <Label htmlFor="backView" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Upload Back View*
                </Label>
                <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                  {accidentFields.backView && (
                    <img
                      src={URL.createObjectURL(accidentFields.backView)}
                      alt="Back view preview"
                      className="max-w-full max-h-32 object-contain mb-2"
                    />
                  )}
                  <input
                    type="file"
                    id="backView"
                    accept="image/*"
                    onChange={(e) => handleFileChange('backView', e, accidentFields, setAccidentFields)}
                    className="w-full mt-2"
                  />
                </div>
                {accidentFields.backView && <p className="text-sm text-gray-600 mt-1">{accidentFields.backView.name}</p>}
              </div>
            </div>

            <div className="mt-6 flex gap-4 justify-end">
              <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded text-sm sm:text-base" onClick={handleBack}>
                Cancel
              </Button>
              <Button
                variant="default"
                className="bg-[#330101] text-white px-4 py-2 rounded text-sm sm:text-base"
                onClick={() => setShowAdditionalAccidentForm(true)}
              >
                Next
              </Button>
            </div>
          </div>
        )}

        {/* Breakdown and Damage Fields */}
        {(formData.reportType === 'Breakdown' || formData.reportType === 'General Maintenance' || formData.reportType === 'Damage') && (
          <div className="space-y-4">
            {(formData.reportType === 'Breakdown' || formData.reportType === 'Damage') && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                <div className="relative">
                  <Label htmlFor="interiorImages" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                    Interior Images*
                  </Label>
                  <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                    {breakdownFields.interiorImages && (
                      <img
                        src={URL.createObjectURL(breakdownFields.interiorImages)}
                        alt="Interior images preview"
                        className="max-w-full max-h-32 object-contain mb-2"
                      />
                    )}
                    <input
                      type="file"
                      id="interiorImages"
                      accept="image/*"
                      onChange={(e) => handleFileChange('interiorImages', e, breakdownFields, setBreakdownFields)}
                      className="w-full mt-2"
                    />
                  </div>
                  {breakdownFields.interiorImages && <p className="text-sm text-gray-600 mt-1">{breakdownFields.interiorImages.name}</p>}
                </div>
                <div className="relative">
                  <Label htmlFor="exteriorImages" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                    Exterior Images*
                  </Label>
                  <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                    {breakdownFields.exteriorImages && (
                      <img
                        src={URL.createObjectURL(breakdownFields.exteriorImages)}
                        alt="Exterior images preview"
                        className="max-w-full max-h-32 object-contain mb-2"
                      />
                    )}
                    <input
                      type="file"
                      id="exteriorImages"
                      accept="image/*"
                      onChange={(e) => handleFileChange('exteriorImages', e, breakdownFields, setBreakdownFields)}
                      className="w-full mt-2"
                    />
                  </div>
                  {breakdownFields.exteriorImages && <p className="text-sm text-gray-600 mt-1">{breakdownFields.exteriorImages.name}</p>}
                </div>
              </div>
            )}

            <div className="mt-6 flex gap-4 justify-end">
              <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded text-sm sm:text-base" onClick={handleBack}>
                Cancel
              </Button>
              <Button
                variant="default"
                className="bg-[#330101] text-white px-4 py-2 rounded text-sm sm:text-base"
                onClick={() => handleSubmit(formData, accidentFields, additionalAccidentFields, breakdownFields, showAdditionalAccidentForm, navigate)}
              >
                Submit & Proceed
              </Button>
            </div>
          </div>
        )}

        {/* Additional Accident Details */}
        {showAdditionalAccidentForm && (
          <div className="space-y-4">
            <h2 className="text-lg sm:text-xl font-semibold text-gray-800">Additional Accident Details</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="accidentLocation"
                  value={additionalAccidentFields.accidentLocation}
                  onChange={(e) => handleAdditionalAccidentFieldChange('accidentLocation', e.target.value, setAdditionalAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Accident Location"
                  required
                />
                <Label htmlFor="accidentLocation" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Accident Location*
                </Label>
              </div>
              <div className="relative">
                <Textarea
                  id="damageDescription"
                  value={additionalAccidentFields.damageDescription}
                  onChange={(e) => handleAdditionalAccidentFieldChange('damageDescription', e.target.value, setAdditionalAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Describe Damage"
                  required
                />
                <Label htmlFor="damageDescription" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Damage Description*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="policeReport" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Police Report
                </Label>
                <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                  {additionalAccidentFields.policeReport && (
                    <p className="text-sm text-gray-600 mb-2">{additionalAccidentFields.policeReport.name}</p>
                  )}
                  <input
                    type="file"
                    id="policeReport"
                    accept="image/*,.pdf"
                    onChange={(e) => handleFileChange('policeReport', e, additionalAccidentFields, setAdditionalAccidentFields)}
                    className="w-full mt-2"
                  />
                </div>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="witnessDetails"
                  value={additionalAccidentFields.witnessDetails}
                  onChange={(e) => handleAdditionalAccidentFieldChange('witnessDetails', e.target.value, setAdditionalAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Enter Witness Details"
                />
                <Label htmlFor="witnessDetails" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Witness Details
                </Label>
              </div>
            </div>

            <div className="mt-6 flex gap-4 justify-end">
              <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded text-sm sm:text-base" onClick={handleBack}>
                Cancel
              </Button>
              <Button
                variant="default"
                className="bg-[#330101] text-white px-4 py-2 rounded text-sm sm:text-base"
                onClick={() => handleSubmit(formData, accidentFields, additionalAccidentFields, breakdownFields, showAdditionalAccidentForm, navigate)}
              >
                Submit & Proceed
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}