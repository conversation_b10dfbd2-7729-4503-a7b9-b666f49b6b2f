import { useState } from "react";
import { MechanicBreakdownServiceData } from "../type/mechanictype";

const MOCK_DATA: MechanicBreakdownServiceData = {
  vehicleId: "VID0001", // Example value
  model: "Civic Hybrid Sedan",
  regNo: "1PX 1ZR",
  incidentDate: "21-02-2025",
  description: "Engine failure during operation",
  estimatedEndDate: "2025-05-26",
  actualEndDate: "2025-05-31",
  status: "Pending",
  mechanicAssigned: ["<PERSON>", "<PERSON>"],
  branch: "Somerton",
  comment: "Type here...",
  mechanicNoteStatus: "Pending",
  mechanicNoteComment: "Needs further inspection.",
};

export function useBreakdownServiceEdit() {
  const [formData, setFormData] = useState<MechanicBreakdownServiceData>(MOCK_DATA);

  const handleInputChange = (field: keyof MechanicBreakdownServiceData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  return { formData, setFormData, handleInputChange };
}