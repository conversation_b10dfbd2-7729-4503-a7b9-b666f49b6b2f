import { useState, useEffect } from 'react'
import { JobCardData, CustomerInfo, JobCardFilters } from '../type/mechanictype'

export const useJobcardRegularMaintainance = () => {
  const [jobCards, setJobCards] = useState<JobCardData[]>([])
  const [filters, setFilters] = useState<JobCardFilters>({
    status: 'All',
    searchTerm: ''
  })
  const [currentPage, setCurrentPage] = useState(1)
  const [recordsPerPage, setRecordsPerPage] = useState(10)

  // Sample customer data
  const customerInfo: CustomerInfo = {
    name: '01 Lion Car Rentals Somerton',
    address: '2/85 Hume Hwy',
    city: 'Somerton, VIC 3062',
    postcode: '3062',
    phone: '0390374447',
    email: '<EMAIL>'
  }

  // Sample job card data
  const sampleJobCards: JobCardData[] = [
    {
      id: '1',
      registration: 'ASV 002',
      
      prodDate: '26-06-2025',
      nextService: 'General Maintenance every 5000 km',
      regoDue: '26-06-2025',
      
      buildDate: '26-06-2025',
      repairedBy: '<PERSON>',
      status: 'Completed'
    },
    {
      id: '2',
      registration: 'AAD 432',
      
      prodDate: '29-04-2025',
      nextService: 'General Maintenance every 5000 km',
      regoDue: '29-04-2025',
      
      buildDate: '28-05-2025',
      repairedBy: 'John Doe',
      status: 'In Progress'
    },
    {
      id: '3',
      registration: 'BBC 321',
     
      prodDate: '20-04-2025',
      nextService: 'General Maintenance every 5000 km',
      regoDue: '20-04-2025',
      
      buildDate: '29-05-2025',
      repairedBy: 'John Doe',
      status: 'Pending'
    }
  ]

  useEffect(() => {
    const savedData = localStorage.getItem('jobCardData')
    if (savedData) {
      setJobCards(JSON.parse(savedData))
    } else {
      setJobCards(sampleJobCards)
      localStorage.setItem('jobCardData', JSON.stringify(sampleJobCards))
    }
  }, [])

  const filteredJobCards = jobCards.filter(card => {
    const matchesStatus = filters.status === 'All' || card.status === filters.status
    const matchesSearch = !filters.searchTerm || 
      Object.values(card).some(value => 
        value.toString().toLowerCase().includes(filters.searchTerm.toLowerCase())
      )
    return matchesStatus && matchesSearch
  })

  const totalPages = Math.ceil(filteredJobCards.length / recordsPerPage)
  const startIndex = (currentPage - 1) * recordsPerPage
  const paginatedJobCards = filteredJobCards.slice(startIndex, startIndex + recordsPerPage)

  const updateFilters = (newFilters: Partial<JobCardFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }))
    setCurrentPage(1)
  }

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-500 text-white'
      case 'In Progress':
        return 'bg-blue-500 text-white'
      case 'Pending':
        return 'bg-yellow-500 text-white'
      default:
        return 'bg-gray-500 text-white'
    }
  }

  return {
    jobCards: paginatedJobCards,
    customerInfo,
    filters,
    currentPage,
    recordsPerPage,
    totalPages,
    totalRecords: filteredJobCards.length,
    updateFilters,
    setCurrentPage,
    setRecordsPerPage,
    getStatusColor
  }
}