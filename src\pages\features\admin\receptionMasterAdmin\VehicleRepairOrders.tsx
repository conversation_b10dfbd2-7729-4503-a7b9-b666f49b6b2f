import React, { useState } from 'react';
import { ArrowLeft, ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Pagination } from '@/components/layout/Pagination';

export function VehicleRepairOrdersPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);

  const repairOrdersData: any[] = []; // Empty array as per original code
  const totalEntries = repairOrdersData.length;
  const totalPages = Math.max(1, Math.ceil(totalEntries / recordsPerPage)); // Ensure at least 1 page
  const startEntry = totalEntries > 0 ? (currentPage - 1) * recordsPerPage + 1 : 0;
  const endEntry = totalEntries > 0 ? Math.min(currentPage * recordsPerPage, totalEntries) : 0;

  const handleTabClick = (tab: string) => {
    if (!id) {
      console.error('Vehicle ID is undefined for tab navigation');
      navigate('/receptionMasterAdmin/fleet/vehicles');
      return;
    }

    const tabRoutes: { [key: string]: string } = {
      'View': `/receptionMasterAdmin/fleet/vehicles/${id}`,
      'Edit': `/receptionMasterAdmin/fleet/vehicles/edit-vehicle/${id}`,
      'Reservations': `/receptionMasterAdmin/fleet/vehicles/${id}/reservations`,
      'Damages': `/receptionMasterAdmin/fleet/vehicles/${id}/damages`,
      'Blocked Periods': `/receptionMasterAdmin/fleet/vehicles/${id}/blocked-periods`,
      'Expenses': `/receptionMasterAdmin/fleet/vehicles/${id}/expenses`,
      'Relocations': `/receptionMasterAdmin/fleet/vehicles/${id}/relocations`,
      'Repair Orders': `/receptionMasterAdmin/fleet/vehicles/${id}/repair-orders`,
      'Files': `/receptionMasterAdmin/fleet/vehicles/${id}/files`,
      'Check List': `/receptionMasterAdmin/fleet/vehicles/${id}/check-list`
    };

    navigate(tabRoutes[tab]);
  };

  const handleGoBack = () => {
    navigate('/receptionMasterAdmin/fleet/vehicles');
  };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-semibold text-gray-900">
                Mercedes-Benz E 280 CDI Classic - {id || 'Unknown'}
              </h1>
              <span className="bg-[#330101] text-white px-3 py-1 rounded text-sm font-medium">
                Rental
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button className="bg-[#330101] hover:bg-[#660404] text-white">
                Create Repair Order
              </Button>
            </div>
          </div>

          {/* Tabs */}
          <div className="flex space-x-1">
            {['View', 'Edit', 'Reservations', 'Damages', 'Blocked Periods', 'Expenses', 'Relocations', 'Repair Orders', 'Files', 'Check List'].map((tab) => (
              <Button
                key={tab}
                onClick={() => handleTabClick(tab)}
                className={`px-4 py-2 text-sm font-medium border border-gray-300 transition-colors hover:bg-gray-100 ${
                  tab === 'Repair Orders'
                    ? 'bg-white text-gray-900'
                    : 'bg-gray-100 text-gray-600'
                }`}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        {/* Table */}
        <div className="hidden md:block rounded-md border overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className="bg-gray-50 uppercase">
                  <TableHead>ID</TableHead>
                  <TableHead>Reservation</TableHead>
                  <TableHead>Maintenance Type</TableHead>
                  <TableHead>Date Out</TableHead>
                  <TableHead>Date Due</TableHead>
                  <TableHead>Date In</TableHead>
                  <TableHead>Total In Parts</TableHead>
                  <TableHead>Total In Labor</TableHead>
                  <TableHead>Fuel Level Out</TableHead>
                  <TableHead>Fuel Level In</TableHead>
                  <TableHead>Odometer Out</TableHead>
                  <TableHead>Odometer In</TableHead>
                  <TableHead>Notes</TableHead>
                  <TableHead>Comments</TableHead>
                  <TableHead>Status</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {repairOrdersData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={15} className="p-8 text-center text-gray-500">
                      No data available in table
                    </TableCell>
                  </TableRow>
                ) : (
                  repairOrdersData.slice(
                    (currentPage - 1) * recordsPerPage,
                    currentPage * recordsPerPage
                  ).map((record, index) => (
                    <TableRow key={index} className="hover:bg-gray-50 border-b">
                      <TableCell>{record.id}</TableCell>
                      <TableCell>{record.reservation}</TableCell>
                      <TableCell>{record.maintenanceType}</TableCell>
                      <TableCell>{record.dateOut}</TableCell>
                      <TableCell>{record.dateDue}</TableCell>
                      <TableCell>{record.dateIn}</TableCell>
                      <TableCell>{record.totalInParts}</TableCell>
                      <TableCell>{record.totalInLabor}</TableCell>
                      <TableCell>{record.fuelLevelOut}</TableCell>
                      <TableCell>{record.fuelLevelIn}</TableCell>
                      <TableCell>{record.odometerOut}</TableCell>
                      <TableCell>{record.odometerIn}</TableCell>
                      <TableCell>{record.notes}</TableCell>
                      <TableCell>{record.comments}</TableCell>
                      <TableCell>{record.status}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(value) => {
            setRecordsPerPage(value);
            setCurrentPage(1);
          }}
          recordsPerPageOptions={[5, 10, 25, 50]}
          className="mt-4"
        />
      </div>
    </div>
  );
}