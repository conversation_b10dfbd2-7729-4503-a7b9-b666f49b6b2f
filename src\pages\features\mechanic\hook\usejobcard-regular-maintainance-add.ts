import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { JobCardFormData, JobCardData } from '../type/mechanictype'

export const useJobcardRegularMaintainanceAdd = () => {
  const navigate = useNavigate()
  const [isSubmitting, setIsSubmitting] = useState(false)
  
  const [formData, setFormData] = useState<JobCardFormData>({
    registration: '',
    make: '',
    model: '',
    modelSeries: '',
    colour: '',
    prodDate: '',
    nextService: '',
    regoDue: '',
    engineNumber: '',
    vin: '',
    buildDate: '',
    trans: '',
    air: '',
    cyl: '',
    body: '',
    odo: '',
    hours: '',
    repairedBy: '',
    drainRefillEngineOil: false,
    engineOilLitres: '',
    replaceOilFilter: false,
    replaceFuelFilter: false,
    airFilterChecked: false,
    airFilterReplaced: false,
    cabinFilterChecked: false,
    cabinFilterReplaced: false,
    checkAdjustTyrePressure: false,
    checkTopUpAllFluids: false,
    checkCleanAdjustBreak: false,
    checkCleanAdjustBreak2: false,
    checkWiperBlades: false,
    checkLights: false,
    sparkPlugsChecked: false,
    sparkPlugsReplaced: false,
    checkCorrectOperation: false,
    tyreConditionRF: '',
    tyreConditionLF: '',
    tyreConditionRR: '',
    tyreConditionLR: '',
    breakConditionFront: '',
    breakConditionRear: '',
    breakDiscsRear: '',
    breakDiscsFront: '',
    checkGPS: false,
    roadTest: false,
    comments: ''
  })

  const handleInputChange = (field: keyof JobCardFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleCheckboxChange = (field: keyof JobCardFormData, checked: boolean) => {
    setFormData(prev => ({ ...prev, [field]: checked }))
  }

  const validateForm = () => {
    const requiredFields = ['registration', 'make', 'model', 'repairedBy']
    const errors: string[] = []

    requiredFields.forEach(field => {
      if (!formData[field as keyof JobCardFormData]) {
        errors.push(`${field} is required`)
      }
    })

    if (errors.length > 0) {
      alert(`Please fill in the following fields: ${errors.join(', ')}`)
      return false
    }

    return true
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }

    setIsSubmitting(true)

    try {
      // Get existing job cards from localStorage
      const existingData = localStorage.getItem('jobCardData')
      const existingJobCards: JobCardData[] = existingData ? JSON.parse(existingData) : []

      // Create new job card entry
      const newJobCard: JobCardData = {
        id: Date.now().toString(),
        registration: formData.registration,
        make: formData.make,
        model: formData.model,
        modelSeries: formData.modelSeries,
        colour: formData.colour,
        prodDate: formData.prodDate,
        nextService: formData.nextService,
        regoDue: formData.regoDue,
        engineNumber: formData.engineNumber,
        vin: formData.vin,
        buildDate: formData.buildDate,
        repairedBy: formData.repairedBy,
        status: 'Pending'
      }

      // Add new job card to existing data
      const updatedJobCards = [...existingJobCards, newJobCard]
      
      // Save to localStorage
      localStorage.setItem('jobCardData', JSON.stringify(updatedJobCards))
      
      // Save full form data separately for future editing
      const fullFormData = localStorage.getItem('jobCardFullData')
      const existingFullData = fullFormData ? JSON.parse(fullFormData) : []
      existingFullData.push({ ...formData, id: newJobCard.id })
      localStorage.setItem('jobCardFullData', JSON.stringify(existingFullData))

      // Reset form
      setFormData({
        registration: '',
        make: '',
        model: '',
        modelSeries: '',
        colour: '',
        prodDate: '',
        nextService: '',
        regoDue: '',
        engineNumber: '',
        vin: '',
        buildDate: '',
        trans: '',
        air: '',
        cyl: '',
        body: '',
        odo: '',
        hours: '',
        repairedBy: '',
        drainRefillEngineOil: false,
        engineOilLitres: '',
        replaceOilFilter: false,
        replaceFuelFilter: false,
        airFilterChecked: false,
        airFilterReplaced: false,
        cabinFilterChecked: false,
        cabinFilterReplaced: false,
        checkAdjustTyrePressure: false,
        checkTopUpAllFluids: false,
        checkCleanAdjustBreak: false,
        checkCleanAdjustBreak2: false,
        checkWiperBlades: false,
        checkLights: false,
        sparkPlugsChecked: false,
        sparkPlugsReplaced: false,
        checkCorrectOperation: false,
        tyreConditionRF: '',
        tyreConditionLF: '',
        tyreConditionRR: '',
        tyreConditionLR: '',
        breakConditionFront: '',
        breakConditionRear: '',
        breakDiscsRear: '',
        breakDiscsFront: '',
        checkGPS: false,
        roadTest: false,
        comments: ''
      })

      alert('Job card added successfully!')
      
    } catch (error) {
      console.error('Error saving job card:', error)
      alert('Error saving job card. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    navigate('/mechanic/services')
  }

  return {
    formData,
    isSubmitting,
    handleInputChange,
    handleCheckboxChange,
    handleSubmit,
    handleCancel
  }
}
