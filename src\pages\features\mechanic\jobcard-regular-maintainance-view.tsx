import { useJobcardRegularMaintainanceView } from './hook/usejobcard-regular-maintainance-view'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Button } from '../../../components/ui/button'
import { ArrowLeft, Download } from 'lucide-react'
import { useNavigate, useParams } from "react-router-dom";
import { useRef } from 'react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';

export function JobcardRegularMaintainanceView() {
  const navigate = useNavigate();
  const { id } = useParams();

  const { selectedJobCard } = useJobcardRegularMaintainanceView(id);

  // Mock job card details if selectedJobCard is not available
  const mockJobCard = {
    postDate: "25/07/2025",
    jobCardNumber: "JC-123456",
    odo: "123456",
    hours: "1200",
  };

  // Ref for the full page content
  const pageRef = useRef<HTMLDivElement>(null);

  // Download handler using html2canvas + jsPDF (WYSIWYG)
  const handleDownload = async () => {
    if (!pageRef.current) return;

    // Hide all buttons before capture
    const buttons = pageRef.current.querySelectorAll('button');
    buttons.forEach(btn => (btn.style.display = 'none'));

    // Optionally, scroll to top to ensure all content is visible
    window.scrollTo(0, 0);

    // Wait for UI to update
    await new Promise(res => setTimeout(res, 100));

    // Capture the page as an image
    const canvas = await html2canvas(pageRef.current, { scale: 2, useCORS: true });
    const imgData = canvas.toDataURL('image/png');

    // Restore buttons after capture
    buttons.forEach(btn => (btn.style.display = ''));

    // Create PDF (auto fit to A4, multi-page if needed)
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'px',
      format: 'a4'
    });

    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    const imgWidth = pageWidth;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    let position = 0;
    let remainingHeight = imgHeight;

    while (remainingHeight > 0) {
      pdf.addImage(
        imgData,
        'PNG',
        0,
        position ? 0 : 0,
        imgWidth,
        imgHeight
      );
      remainingHeight -= pageHeight;
      if (remainingHeight > 0) {
        pdf.addPage();
        position -= pageHeight;
      }
    }

    pdf.save('jobcard-details.pdf');
  };

  if (!selectedJobCard) return <div className="p-6">No job card found.</div>;

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen" ref={pageRef}>
      {/* Top right action buttons */}
      <div className="flex flex-col gap-2 sm:flex-row sm:justify-between sm:items-center mb-6">
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-sm sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={() => navigate('/mechanic/jobcard-regular-maintainance')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
        <div className="flex flex-col gap-2 sm:flex-row sm:gap-2 w-full sm:w-auto">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-4 py-2 text-sm sm:text-sm md:text-base w-full sm:w-auto"
            onClick={() => navigate("/mechanic/request-parts-add")}
          >
            Request parts
          </Button>
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-4 py-2 text-sm sm:text-sm md:text-base w-full sm:w-auto"
            onClick={() => navigate("/mechanic/request-service-add")}
          >
            Request Services
          </Button>
        </div>
      </div>

      {/* Customer and Job Card Details */}
      <div className="mb-8 flex flex-col md:flex-row md:justify-between md:items-start gap-8 text-sm text-gray-700">
        <div className="flex-1">
          <span className="font-semibold">Customer:</span><br />
          01 Lion Car Rentals Somerton,<br />
          2/85 Hume Hwy, Somerton,<br />
          VIC 3062,<br />
          0390374447,<br />
          <EMAIL>
        </div>
        <div className="flex-1 md:text-right mt-4 md:mt-0">
          <div className="font-semibold">Job Card Details</div>
          <div>Post Date: {selectedJobCard.postDate ?? mockJobCard.postDate}</div>
          <div>Job Card: {selectedJobCard.jobCardNumber ?? mockJobCard.jobCardNumber}</div>
          <div>New Odometer / Hours: {selectedJobCard.odo ?? mockJobCard.odo} / {selectedJobCard.hours ?? mockJobCard.hours}</div>
        </div>
      </div>

      {/* Vehicle Details */}
      <div className="mb-8">
        {/* Mobile Card View */}
        <div className="block md:hidden space-y-4">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-3">
            { [
              { label: "Registration", value: selectedJobCard.registration },
              { label: "Make", value: selectedJobCard.make },
              { label: "Model", value: selectedJobCard.model },
              { label: "Model Series", value: selectedJobCard.modelSeries },
              { label: "Colour", value: selectedJobCard.colour },
              { label: "Prod Date", value: selectedJobCard.prodDate },
              { label: "Next Service", value: selectedJobCard.nextService },
              { label: "Rego Due", value: selectedJobCard.regoDue },
              { label: "Engine #", value: selectedJobCard.engineNumber },
              { label: "VIN", value: selectedJobCard.vin },
              { label: "Build Date", value: selectedJobCard.buildDate },
              { label: "Litres", value: selectedJobCard.engineOilLitres },
              { label: "Trans", value: selectedJobCard.trans },
              { label: "Air", value: selectedJobCard.air },
              { label: "Cyl", value: selectedJobCard.cyl },
              { label: "Body", value: selectedJobCard.body },
              { label: "Odo", value: selectedJobCard.odo },
              { label: "Hours", value: selectedJobCard.hours },
             
            ].map((item) => (
              <div className="relative" key={item.label}>
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">{item.label}</span>
                <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{item.value}</div>
              </div>
            ))}
          </div>
        </div>
        {/* Desktop/Grid View */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Registration</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.registration}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Make</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.make}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.model}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model Series</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.modelSeries}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Colour</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.colour}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Prod Date</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.prodDate}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Next Service</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.nextService}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rego Due</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.regoDue}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Engine #</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.engineNumber}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">VIN</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.vin}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Build Date</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.buildDate}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Litres</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.engineOilLitres}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Trans</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.trans}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Air</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.air}</div>
          </div>
          <div className=" relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Cyl</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.cyl}</div>
          </div>
          <div className=" relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Body</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.body}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Odo</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.odo}</div>
          </div>
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Hours</span>
            <div className="text-sm bg-gray-100 rounded border px-2 py-2 mt-1">{selectedJobCard.hours}</div>
          </div>
          
        </div>
      </div>

      {/* Job Card Notes */}
      <div className="mb-8">
        <h2 className="text-lg font-bold text-gray-800 mb-4">Job Card Notes</h2>
        <h3 className="text-sm font-semibold text-gray-700 mb-4">The Service Requires:</h3>
        {/* Mobile & Desktop: Same simple list style */}
        <div className="space-y-2 text-sm">
          {selectedJobCard.drainRefillEngineOil && (
            <div>
              Drain & Refilled Engine Oil {selectedJobCard.engineOilLitres} L
            </div>
          )}
          {selectedJobCard.replaceOilFilter && (
            <div>Replace Oil Filter</div>
          )}
          {selectedJobCard.replaceFuelFilter && (
            <div>Replace Fuel Filter</div>
          )}
          {selectedJobCard.airFilterChecked && (
            <div>Air Filter - Checked</div>
          )}
          {selectedJobCard.airFilterReplaced && (
            <div>Air Filter - Replaced</div>
          )}
          {selectedJobCard.cabinFilterChecked && (
            <div>Cabin Filter - Checked</div>
          )}
          {selectedJobCard.cabinFilterReplaced && (
            <div>Cabin Filter - Replaced</div>
          )}
          {selectedJobCard.checkAdjustTyrePressure && (
            <div>Check & Adjust Tyre Air Pressure</div>
          )}
          {selectedJobCard.checkTopUpAllFluids && (
            <div>Check & Top Up all fluids</div>
          )}
          {selectedJobCard.checkCleanAdjustBreak && (
            <div>Check, Clean & Adjust Break</div>
          )}
          {selectedJobCard.checkCleanAdjustBreak2 && (
            <div>Check, Clean & Adjust Break (Rear)</div>
          )}
          {selectedJobCard.checkWiperBlades && (
            <div>Check Wiper Blades</div>
          )}
          {selectedJobCard.checkLights && (
            <div>Check Lights</div>
          )}
          {selectedJobCard.sparkPlugsChecked && (
            <div>Spark Plugs - Checked</div>
          )}
          {selectedJobCard.sparkPlugsReplaced && (
            <div>Spark Plugs - Replaced</div>
          )}
          {selectedJobCard.checkCorrectOperation && (
            <div>Check for correct operation. Safety Check & Report</div>
          )}
          <div>
            Tyre Conditions: RF {selectedJobCard.tyreConditionRF}% LF {selectedJobCard.tyreConditionLF}% RR {selectedJobCard.tyreConditionRR}% LR {selectedJobCard.tyreConditionLR}%
          </div>
          <div>
            Break Condition: Front {selectedJobCard.breakConditionFront}% Discs {selectedJobCard.breakDiscsFront}mm, Rear {selectedJobCard.breakConditionRear}% Discs {selectedJobCard.breakDiscsRear}mm
          </div>
          {selectedJobCard.checkGPS && (
            <div>Check GPS</div>
          )}
          {selectedJobCard.roadTest && (
            <div>Road test</div>
          )}
          {selectedJobCard.comments && (
            <div>Comments: {selectedJobCard.comments}</div>
          )}
        </div>
      </div>

      {/* Download Button at the end */}
      <div className="flex justify-end mt-8">
        <Button
          className="bg-[#330101] text-white flex items-center gap-2 w-full sm:w-auto"
          onClick={handleDownload}
        >
          <Download className="w-4 h-4" />
          Download Details
        </Button>
      </div>
    </div>
  );
}