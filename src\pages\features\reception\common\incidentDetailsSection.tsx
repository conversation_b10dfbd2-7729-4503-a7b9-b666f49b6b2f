import React from 'react';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { SectionProps } from './types';

const IncidentDetailsSection: React.FC<SectionProps> = ({ formData, handleInputChange }) => (
  <div className="space-y-4 mt-8">
    <h3 className="text-xl font-semibold text-gray-900 pb-2">Incident Details</h3>
    <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
      <div className="space-y-2">
        <Label htmlFor="breakdownDate" className="absolute bg-white px-4 text-xs text-gray-600">
          Breakdown Date
        </Label>
        <Input
          id="breakdownDate"
          type="date"
          value={formData.breakdownDate}
          onChange={(e) => handleInputChange('breakdownDate', e.target.value)}
          className="bg-white focus:outline-none focus:ring-0 focus:border-none"
        />
      </div>
      <div className="space-y-2">
        <Label htmlFor="location" className="absolute bg-white px-4 text-xs text-gray-600">
          Location
        </Label>
        <Input
          id="location"
          value={formData.location}
          onChange={(e) => handleInputChange('location', e.target.value)}
          className="bg-white focus:outline-none focus:ring-0 focus:border-none"
        />
      </div>
    </div>
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mt-6">
      <div className="space-y-2">
        <Label htmlFor="insuranceClaimed" className="absolute bg-white px-4 text-xs text-gray-600">
          Insurance Claimed
        </Label>
        <Select
          value={formData.insuranceClaimed}
          onValueChange={(value) => handleInputChange('insuranceClaimed', value)}
        >
          <SelectTrigger className="bg-white focus:border-transparent">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="yes">Yes</SelectItem>
            <SelectItem value="no">No</SelectItem>
            <SelectItem value="pending">Pending</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="insuranceCompany" className="absolute bg-white px-4 text-xs text-gray-600">
          Insurance Company Name
        </Label>
        <Select
          value={formData.insuranceCompany}
          onValueChange={(value) => handleInputChange('insuranceCompany', value)}
        >
          <SelectTrigger className="bg-white focus:border-transparent">
            <SelectValue placeholder="Select" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="allianz">Allianz</SelectItem>
            <SelectItem value="axa">AXA</SelectItem>
            <SelectItem value="zurich">Zurich</SelectItem>
            <SelectItem value="other">Other</SelectItem>
          </SelectContent>
        </Select>
      </div>
      <div className="space-y-2">
        <Label htmlFor="claimedNumber" className="absolute bg-white px-4 text-xs text-gray-600">
          Claimed Number
        </Label>
        <Input
          id="claimedNumber"
          value={formData.claimedNumber}
          onChange={(e) => handleInputChange('claimedNumber', e.target.value)}
          className="bg-white focus:outline-none focus:ring-0 focus:border-none"
        />
      </div>
    </div>
  </div>
);

export default IncidentDetailsSection;
