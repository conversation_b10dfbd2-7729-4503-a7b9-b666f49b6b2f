import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { policeMockData } from '../common/mockdata';

export const usePoliceDetailsHook = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    policeNotified: '',
    policeInformation: '',
    policeAction: '',
    officerBatchNo: '',
    policeStation: '',
    stationPhoneNumber: '',
    reportNumber: '',
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleRadioChange = (value: 'yes' | 'no') => {
    setFormData(prev => ({
      ...prev,
      policeNotified: value,
    }));
  };

  const handleGoBack = () => {
    navigate('/customer/accident-formsecond');
  };

  const handleNext = () => {
    navigate('/customer/accident-signatureform');
  };

  return {
    formData,
    setFormData,
    handleInputChange,
    handleRadioChange,
    handleGoBack,
    handleNext,
  };
};