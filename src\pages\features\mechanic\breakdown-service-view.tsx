import React from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useBreakdownServiceView } from "./hook/usebreakdown-service-view";

export function BreakdownServiceViewPage() {
  const navigate = useNavigate();
  const { formData } = useBreakdownServiceView();

  return (
    <div className="min-h-screen p-4">
      <Button
        className="bg-[#330101] text-white text-sm px-3 py-2 mb-4"
        size="sm"
        onClick={() => navigate('/mechanic/breakdown-service')}
      >
        <ArrowLeft className="w-4 h-4 mr-1" />
        Go Back
      </Button>
      <div className="bg-white rounded-lg shadow-sm p-4">
        {/* Vehicle Details */}
        <h2 className="text-xl font-semibold mb-4">Vehicle Details</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</Label>
            <Input value={formData.model} readOnly className="bg-gray-200" />
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Reg No</Label>
            <Input value={formData.regNo} readOnly className="bg-gray-200" />
          </div>
        </div>
        <div className="relative mb-4">
          <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Incident Date</Label>
          <Input value={formData.incidentDate} readOnly className="bg-gray-200" />
        </div>
        <div className="relative mb-4">
          <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</Label>
          <Input value={formData.description} readOnly className="bg-gray-200" />
        </div>

        {/* Workshop Details */}
        <h2 className="text-xl font-semibold mb-4 mt-6">Workshop Details</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Branch</Label>
            <Input value={formData.branch} readOnly className="bg-gray-200" />
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Mechanic Assigned</Label>
            <Input value={formData.mechanicAssigned.join(", ")} readOnly className="bg-gray-200" />
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Estimated End Date</Label>
            <Input type="date" value={formData.estimatedEndDate} readOnly className="bg-gray-200" />
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Actual End Date</Label>
            <Input type="date" value={formData.actualEndDate} readOnly className="bg-gray-200" />
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
            <Input value={formData.status} readOnly className="bg-gray-200" />
          </div>
          <div className="col-span-2 relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
            <Input value={formData.comment} readOnly className="bg-gray-200" />
          </div>
        </div>

        {/* Mechanic Note */}
        <h2 className="text-xl font-semibold mb-4 mt-6">Mechanic Note</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
            <Input value={formData.mechanicNoteStatus} readOnly className="bg-gray-200" />
          </div>
          <div className="col-span-2 relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
            <Input value={formData.mechanicNoteComment} readOnly className="bg-gray-200" />
          </div>
        </div>
      </div>
    </div>
  );
}