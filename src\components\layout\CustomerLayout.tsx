/* import { Outlet } from 'react-router-dom';
import { Header } from './Header';
import { CustomerSidebar } from './CustomerSidebar';

export function CustomerLayout() {
  return (
    <div className="flex flex-col min-h-screen">
      <Header />
      <div className="flex flex-1">
        <CustomerSidebar />
        <main className="flex-1 p-6 bg-white">
          <Outlet />
        </main>
      </div>
    </div>
  );
}
 */


import React, { useState } from 'react';
import { Outlet } from 'react-router-dom';
import { Header } from './Header';
import { CustomerSidebar } from './CustomerSidebar';

export function CustomerLayout() {
  const [isSidebarOpen, setIsSidebarOpen] = useState(false);

  const toggleSidebar = () => {
    setIsSidebarOpen(!isSidebarOpen);
  };

  const closeSidebar = () => {
    setIsSidebarOpen(false);
  };

  return (
    <div className="flex flex-col min-h-screen">
      <Header 
        onMenuToggle={toggleSidebar} 
        isSidebarOpen={isSidebarOpen}
      />
      <div className="flex flex-1 relative">
        <CustomerSidebar 
          isOpen={isSidebarOpen} 
          onClose={closeSidebar}
        />
        <main className="flex-1 p-4 sm:p-6 bg-white overflow-x-hidden">
          <Outlet />
        </main>
      </div>
    </div>
  );
}