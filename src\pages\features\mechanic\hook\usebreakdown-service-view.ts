import { useState } from "react";
import { MechanicBreakdownServiceData } from "../type/mechanictype";

const MOCK_DATA: MechanicBreakdownServiceData = {
  model: "Civic Hybrid Sedan",
  regNo: "1PX 1ZR",
  incidentDate: "21-02-2025",
  description: "Engine failure during operation",
  estimatedEndDate: "2025-05-26",
  actualEndDate: "2025-05-31",
  status: "Pending",
  branch: "Somerton",
  mechanicAssigned: ["<PERSON>", "<PERSON>"],
  comment: "Type here...",
  mechanicNoteStatus: "Pending",
  mechanicNoteComment: "Needs further inspection.",
};

export function useBreakdownServiceView() {
  const [formData] = useState<MechanicBreakdownServiceData>(MOCK_DATA);
  return { formData };
}