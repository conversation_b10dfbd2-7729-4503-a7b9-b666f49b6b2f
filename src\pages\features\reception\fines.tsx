import React, { useState } from 'react';
import { Search, Edit, AlertTriangle } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { Booking } from './type/reception-type';
import { bookingsData } from './common/mockData';
import { handleEditClick } from './hook/useFines';

export function ReceptionFinesPage() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const navigate = useNavigate();

  // Filter bookings based on search and filter
  const filteredBookings = bookingsData.filter(booking => {
    const matchesSearch = booking.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.id.toLowerCase().includes(searchTerm.toLowerCase()) || 
                         booking.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.customerPhone.includes(searchTerm);
    const matchesFilter = filterStatus === 'All' || booking.vehicle === filterStatus;
    return matchesSearch && matchesFilter;
  });

  // Pagination
  const totalRecords = filteredBookings.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentBookings = filteredBookings.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center mb-4 sm:mb-6">
          <AlertTriangle className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
          <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Fines</h1>
        </div>
        <Button
          onClick={() => navigate('reception-addFine')}
          className="px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors w-full sm:w-auto"
        >
          Add New Fine
        </Button>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Mobile Cards View */}
      <div className="block md:hidden space-y-2">
        {currentBookings.map((booking) => (
          <div key={booking.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm relative max-w-md mx-auto w-full">
            <div className="grid grid-cols-2 gap-x-6 gap-y-4 text-sm mb-3">
              <div>
                <span className="text-gray-500 font-medium block">Rental Id</span>
                <span className="text-gray-900">{booking.id}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Customer Name</span>
                <span className="text-gray-900">{booking.customerName}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Customer Phone</span>
                <span className="text-gray-900">{booking.customerPhone}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Vehicle</span>
                <span className="text-gray-900">{booking.vehicle}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Penalty Amount</span>
                <span className="text-red-500 font-medium">${booking.penaltyAmount.toFixed(2)}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Due Date</span>
                <span className="text-gray-900">{booking.dueDate}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Offense Date</span>
                <span className="text-gray-900">{booking.offenseDate}</span>
                <span className="text-gray-600 text-xs block">{booking.offenseTime}</span>
              </div>
            </div>
            <div className="text-sm">
              <span className="text-gray-500 font-medium">Obligation No: </span>
              <span className="text-gray-900">{booking.obligtionNo}</span>
            </div>
            <Button
              onClick={() => handleEditClick(booking.id, navigate)}
              variant="ghost"
              className="absolute bottom-2 right-2 text-gray-600 hover:text-gray-800 p-2"
            >
              <Edit className="w-4 h-4" />
            </Button>
          </div>
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden md:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className='bg-gray-50 uppercase'>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Rental ID</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Customer Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Customer Phone</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Obligation No</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Penalty Amount</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Due Date</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Offense Date</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentBookings.map((booking) => (
                <TableRow key={booking.id}>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{booking.id}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{booking.customerName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{booking.customerPhone}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4 hidden lg:table-cell">{booking.obligtionNo}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4 font-medium text-red-600">${booking.penaltyAmount.toFixed(2)}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{booking.dueDate}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{booking.vehicle}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    <div>
                      <div>{booking.offenseDate}</div>
                      <div className="text-gray-500 text-xs">{booking.offenseTime}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    <div className="flex items-center">
                      <Button
                        onClick={() => handleEditClick(booking.id, navigate)}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 lg:p-2"
                      >
                        <Edit className="w-3 h-3 lg:w-4 lg:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="mt-4 sm:mt-6">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="text-sm sm:text-base"
        />
      </div>
    </div>
  );
}