import { Routes, Route } from 'react-router-dom'
import { StaffLayout } from '../../components/layout/StaffLayout'
import { StaffDashboard } from '../../pages/features/staff/staff-dashboard'

// Staff-specific pages (placeholders for now)
const VehicleInspectionPage = () => <div className="p-6">Vehicle Inspection Page</div>
const CustomerSupportPage = () => <div className="p-6">Customer Support Page</div>
const BookingAssistancePage = () => <div className="p-6">Booking Assistance Page</div>
const IncidentManagementPage = () => <div className="p-6">Incident Management Page</div>
const MaintenanceSchedulePage = () => <div className="p-6">Maintenance Schedule Page</div>
const StaffReportsPage = () => <div className="p-6">Staff Reports Page</div>
const TaskManagementPage = () => <div className="p-6">Task Management Page</div>

export const StaffRoutes = () => {
  return (
    <Routes>
      <Route element={<StaffLayout />}>
        <Route path="dashboard" element={<StaffDashboard />} />
        <Route path="vehicle-inspection" element={<VehicleInspectionPage />} />
        <Route path="customer-support" element={<CustomerSupportPage />} />
        <Route path="booking-assistance" element={<BookingAssistancePage />} />
        <Route path="incident-management" element={<IncidentManagementPage />} />
        <Route path="maintenance" element={<MaintenanceSchedulePage />} />
        <Route path="tasks" element={<TaskManagementPage />} />
        <Route path="reports" element={<StaffReportsPage />} />
        
      </Route>
    </Routes>
  )
}
