import { NavigateFunction } from 'react-router-dom';
import { PanelBeaterFormData } from '../type/teamleadertype';

export const handleInputChange = (
  field: keyof PanelBeaterFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<PanelBeaterFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};

export const handleSave = (navigate: NavigateFunction): void => {
  navigate('/teamleader/panelBeater');
};