import { useState, useMemo } from "react";
import { ServiceManagementAll } from "../type/mechanictype";

const MOCK_DATA: ServiceManagementAll[] = [
  {
    vehicleId: "VID0001",
    vehicle: "Civic Hybrid Sedan - 1PX 1ZR",
    serviceTypes: "General Maintenance",
    EstimatedEndDate: "2025-07-21",
    ActualEndDate: "2025-07-22",
    vehicleStatus: "pending",
    status: "Pending",
    dateIn: "",
    dateOut: ""
  },
  {
    vehicleId: "VID0002",
    vehicle: "Atom - 1PX 5ZP",
    serviceTypes: "General Maintenance",
    EstimatedEndDate: "2025-06-12",
    ActualEndDate: "2025-06-10",
    status: "Done",
    vehicleStatus: "pending",
    dateIn: "",
    dateOut: ""
  },
  // ...add more mock data as needed
];

export function useMaintainanceService() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const filteredData = useMemo(() => {
    let data = MOCK_DATA;

    if (filterStatus !== "All") {
      data = data.filter((item) => item.status === filterStatus);
    }

    if (searchTerm.trim()) {
      data = data.filter(
        (item) =>
          item.vehicleId.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.vehicle.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return data;
  }, [searchTerm, filterStatus]);

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);

  const currentData = useMemo(() => {
    const start = (currentPage - 1) * recordsPerPage;
    return filteredData.slice(start, start + recordsPerPage);
  }, [filteredData, currentPage, recordsPerPage]);

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    filteredData,
    totalPages,
    totalEntries,
    currentData,
  };
}