import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { changeRequestMockData } from '../common/mockdata';
import { ChangeRequest } from '../../reception/type/reception-type';

export const useChangeRequestHook = () => {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);

  const filteredRequests = changeRequestMockData.filter((request) => {
    const matchesSearch =
      request.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.vehicleClass.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.requestType.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || request.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const mobileRequests = filteredRequests;

  const totalRecords = filteredRequests.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentRequests = filteredRequests.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const getStatusBadge = (status: string): string => {
    switch (status) {
      case 'In-Progress':
        return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Approved':
        return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };

  const handleEditRequest = (request: ChangeRequest) => {
    const cleanId = request.id.replace('#', '');
    navigate(`/customer/change-requests/edit-request/${cleanId}`);
  };

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    currentPage,
    setCurrentPage,
    recordsPerPage,
    setRecordsPerPage,
    mobileRequests,
    currentRequests,
    totalPages,
    getStatusBadge,
    handleEditRequest,
    navigate,
  };
};