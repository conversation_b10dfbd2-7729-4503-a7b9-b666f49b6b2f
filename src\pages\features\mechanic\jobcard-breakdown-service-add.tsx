import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Card } from '../../../components/ui/card';
import { Textarea } from '../../../components/ui/textarea';
import { ArrowLeft, AlertTriangle, ChevronDown } from 'lucide-react'; // <-- Add ChevronDown
import { useJobcardBreakdownServiceAdd } from './hook/usejobcard-breakdown-service-add';
import { useState } from 'react';

export function JobcardBreakdownServiceAdd() {
  const {
    form,
    handleInputChange,
    handleCheckboxChange,
    handleTowingChange,
    handleAdd,
    handleCancel
  } = useJobcardBreakdownServiceAdd();

  const [notesComment, setNotesComment] = useState('');

  // Mock vehicle rego options
  const regoOptions = [
    { rego: 'ACV 003', engineNumber: '#457815' },
    { rego: 'XYZ 789', engineNumber: '#123456' },
    { rego: 'DEF 456', engineNumber: '#987654' },
    { rego: 'GHI 321', engineNumber: '#654321' }
  ];
  const [showRegoDropdown, setShowRegoDropdown] = useState(false);

  const handleRegoSelect = (option: { rego: string; engineNumber: string }) => {
    handleInputChange('rego', `${option.rego} (${option.engineNumber})`);
    setShowRegoDropdown(false);
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Back Button & Title - Mobile only */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] flex items-center justify-center"
          size="sm"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>
      {/* Header - Desktop only */}
      <div className="hidden md:flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
        </div>
      </div>

      <form onSubmit={e => { e.preventDefault(); handleAdd(); }}>
        {/* Vehicle Details */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Vehicle Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-3">
              {/* Rego */}
              <div className="relative">
                <span className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600 z-10">Rego</span>
                <Input
                  id="rego"
                  placeholder="Select"
                  value={form.rego}
                  readOnly
                  onClick={() => setShowRegoDropdown((prev) => !prev)}
                  className="text-sm h-8 w-full cursor-pointer pr-8 border border-gray-400 rounded-md focus:outline-none focus:border-[#FF6F3D]"
                  required
                />
                <ChevronDown
                  style={{ pointerEvents: 'none' }}
                  className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400"
                />
                {showRegoDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow left-0 max-h-56 overflow-y-auto">
                    {regoOptions.map(option => (
                      <div
                        key={option.rego}
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                        onClick={() => handleRegoSelect(option)}
                      >
                        {option.rego} ({option.engineNumber})
                      </div>
                    ))}
                  </div>
                )}
              </div>
              {/* Vehicle Class */}
              <div className="relative">
                <span className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600 z-10">Vehicle Class</span>
                <Input
                  id="vehicleClass"
                  placeholder=""
                  value={form.vehicleClass}
                  onChange={e => handleInputChange('vehicleClass', e.target.value)}
                  className="text-sm h-8 w-full border border-gray-400 rounded-md focus:outline-none focus:border-[#FF6F3D]"
                  required
                />
              </div>
              {/* Vehicle Model */}
              <div className="relative">
                <span className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600 z-10">Vehicle Model</span>
                <Input
                  id="vehicleModel"
                  placeholder=""
                  value={form.vehicleModel || ''}
                  onChange={e => handleInputChange('vehicleModel', e.target.value)}
                  className="text-sm h-8 w-full border border-gray-400 rounded-md focus:outline-none focus:border-[#FF6F3D]"
                />
              </div>
              {/* Description */}
              <div className="relative">
                <span className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600 z-10">Description</span>
                <Textarea
                  id="description"
                  placeholder=""
                  value={form.description || ''}
                  onChange={e => handleInputChange('description', e.target.value)}
                  className="text-sm min-h-[40px] resize-none border border-gray-400 rounded-md w-full focus:outline-none focus:border-[#FF6F3D] pt-4"
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-4 gap-4 mb-4">
            <div className="space-y-2 relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rego</span>
              <div className="relative">
                <Input
                  id="rego"
                  placeholder="Select"
                  value={form.rego}
                  readOnly
                  onClick={() => setShowRegoDropdown((prev) => !prev)}
                  className="text-sm h-8 w-full cursor-pointer pr-8"
                  required
                />
                <ChevronDown
                  style={{ pointerEvents: 'none' }}
                  className="absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400"
                />
                {showRegoDropdown && (
                  <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow left-0 max-h-56 overflow-y-auto">
                    {regoOptions.map(option => (
                      <div
                        key={option.rego}
                        className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                        onClick={() => handleRegoSelect(option)}
                      >
                        {option.rego} ({option.engineNumber})
                      </div>
                    ))}
                  </div>
                )}
              </div>
            </div>
            <div className="space-y-2 relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Class</span>
              <Input
                id="vehicleClass"
                placeholder=""
                value={form.vehicleClass}
                onChange={e => handleInputChange('vehicleClass', e.target.value)}
                className="text-sm h-8 w-full"
                required
              />
            </div>
            <div className="space-y-2 relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Model</span>
              <Input
                id="vehicleModel"
                placeholder=""
                value={form.vehicleModel || ''}
                onChange={e => handleInputChange('vehicleModel', e.target.value)}
                className="text-sm h-8 w-full"
              />
            </div>
            <div className="space-y-2 relative col-span-1 md:col-span-4">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</span>
              <Textarea
                id="description"
                placeholder=""
                value={form.description || ''}
                onChange={e => handleInputChange('description', e.target.value)}
                className="text-sm min-h-[40px] resize-none border border-gray-300 w-full"
              />
            </div>
          </div>
        </div>

        {/* Incident Details */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Incident Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-3">
              {/* Parts Replaced */}
              <div className="relative">
                <span className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600 z-10">Parts Replaced</span>
                <Input
                  id="partsReplaced"
                  placeholder=""
                  value={form.partsReplaced || ''}
                  onChange={e => handleInputChange('partsReplaced', e.target.value)}
                  className="text-sm h-8 w-full border border-gray-400 rounded-md focus:outline-none focus:border-[#FF6F3D]"
                />
              </div>
              {/* Repair Tasks Required */}
              <div className="relative">
                <span className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600 z-10">Repair Tasks Required</span>
                <Input
                  id="repairTasksRequired"
                  placeholder=""
                  value={form.repairTasksRequired || ''}
                  onChange={e => handleInputChange('repairTasksRequired', e.target.value)}
                  className="text-sm h-8 w-full border border-gray-400 rounded-md focus:outline-none focus:border-[#FF6F3D]"
                />
              </div>
              {/* Repaired By */}
              <div className="relative">
                <span className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600 z-10">Repaired By</span>
                <Input
                  id="repairedBy"
                  placeholder="Select"
                  value={form.repairedBy}
                  onChange={e => handleInputChange('repairedBy', e.target.value)}
                  className="text-sm h-8 w-full border border-gray-400 rounded-md focus:outline-none focus:border-[#FF6F3D]"
                  required
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="space-y-2 relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Parts Replaced</span>
              <Input
                id="partsReplaced"
                placeholder=""
                value={form.partsReplaced || ''}
                onChange={e => handleInputChange('partsReplaced', e.target.value)}
                className="text-sm h-8 w-full"
              />
            </div>
            <div className="space-y-2 relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
              <Input
                id="repairTasksRequired"
                placeholder=""
                value={form.repairTasksRequired || ''}
                onChange={e => handleInputChange('repairTasksRequired', e.target.value)}
                className="text-sm h-8 w-full"
              />
            </div>
            <div className="space-y-2 relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
              <Input
                id="repairedBy"
                placeholder="Select"
                value={form.repairedBy}
                onChange={e => handleInputChange('repairedBy', e.target.value)}
                className="text-sm h-8 w-full"
                required
              />
            </div>
          </div>
        </div>

        {/* Job Card Notes */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Job Card Notes</h2>
          <div className="font-semibold mb-2">The Service Requires;</div>
          <div className="space-y-3 mb-4">
            {/* Mobile Card View */}
            <div className="block md:hidden bg-white rounded-lg shadow-sm p-4 space-y-3">
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={form.notes.initialInspection}
                  onChange={() => handleCheckboxChange('initialInspection')}
                  className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                  required
                />
                Initial inspection at breakdown site
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={form.notes.batteryBoost}
                  onChange={() => handleCheckboxChange('batteryBoost')}
                  className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                  required
                />
                Battery boost/replacement
              </label>
              <div className="flex flex-wrap items-center gap-2">
                Towing service -
                <label className="flex items-center gap-1">
                  <input
                    type="checkbox"
                    checked={form.notes.towing === 'Required'}
                    onChange={() => handleTowingChange('Required')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Required
                </label>
                <span className="inline-block w-3" />
                <label className="flex items-center gap-1">
                  <input
                    type="checkbox"
                    checked={form.notes.towing === 'Not Required'}
                    onChange={() => handleTowingChange('Not Required')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Not Required
                </label>
              </div>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={form.notes.engineFault}
                  onChange={() => handleCheckboxChange('engineFault')}
                  className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                  required
                />
                Diagnose engine fault
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={form.notes.engineRepair}
                  onChange={() => handleCheckboxChange('engineRepair')}
                  className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                  required
                />
                Engine component repair/replacement
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={form.notes.checkRadiator}
                  onChange={() => handleCheckboxChange('checkRadiator')}
                  className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                  required
                />
                Check radiator and coolant system
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={form.notes.checkGPS}
                  onChange={() => handleCheckboxChange('checkGPS')}
                  className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                  required
                />
                Check GPS
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="checkbox"
                  checked={form.notes.roadTest}
                  onChange={() => handleCheckboxChange('roadTest')}
                  className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                  required
                />
                Road test
              </label>
            </div>
            {/* Desktop/Grid View */}
            <div className="hidden md:block">
              <div className="space-y-3 mb-4">
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.notes.initialInspection}
                    onChange={() => handleCheckboxChange('initialInspection')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Initial inspection at breakdown site
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.notes.batteryBoost}
                    onChange={() => handleCheckboxChange('batteryBoost')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Battery boost/replacement
                </label>
                <div className="flex flex-wrap items-center gap-2">
                  Towing service -
                  <label className="flex items-center gap-1">
                    <input
                      type="checkbox"
                      checked={form.notes.towing === 'Required'}
                      onChange={() => handleTowingChange('Required')}
                      className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                      required
                    />
                    Required
                  </label>
                  <span className="inline-block w-3" />
                  <label className="flex items-center gap-1">
                    <input
                      type="checkbox"
                      checked={form.notes.towing === 'Not Required'}
                      onChange={() => handleTowingChange('Not Required')}
                      className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                      required
                    />
                    Not Required
                  </label>
                </div>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.notes.engineFault}
                    onChange={() => handleCheckboxChange('engineFault')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Diagnose engine fault
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.notes.engineRepair}
                    onChange={() => handleCheckboxChange('engineRepair')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Engine component repair/replacement
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.notes.checkRadiator}
                    onChange={() => handleCheckboxChange('checkRadiator')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Check radiator and coolant system
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.notes.checkGPS}
                    onChange={() => handleCheckboxChange('checkGPS')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Check GPS
                </label>
                <label className="flex items-center gap-2">
                  <input
                    type="checkbox"
                    checked={form.notes.roadTest}
                    onChange={() => handleCheckboxChange('roadTest')}
                    className="appearance-none w-4 h-4 border border-gray-400 rounded-sm checked:bg-white checked:border-black checked:after:content-['✔'] checked:after:block checked:after:text-black checked:after:text-xs checked:after:leading-4 checked:after:text-center"
                    required
                  />
                  Road test
                </label>
              </div>
            </div>
          </div>
        </div>

        {/* Comments - Mobile Floating Label */}
        <div className="block md:hidden space-y-4">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-3">
            <div className="relative">
              <span className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600 z-10">Comments</span>
              <Textarea
                id="additionalNotes"
                placeholder=""
                value={notesComment}
                onChange={e => setNotesComment(e.target.value)}
                className="text-sm min-h-[40px] resize-none border border-gray-400 rounded-md w-full focus:outline-none focus:border-[#FF6F3D] pt-4"
              />
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex flex-row flex-wrap justify-end gap-2 pt-4">
          <Button
            type="submit"
            className="bg-[#330110] hover:bg-gray-800 text-white px-8 py-2  sm:w-32 text-sm"
          >
            Add
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="px-8 py-2 sm:w-32 text-sm"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}