import { Dispatch, SetStateAction } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { Vehicle } from '../type/reception-type';

export const filterVehicles = (
  vehicles: Vehicle[],
  searchTerm: string,
  filterOption: string
): Vehicle[] => {
  return vehicles.filter((vehicle) => {
    const matchesSearch =
      vehicle.class.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.rego.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.vin.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter =
      filterOption === 'All' ||
      vehicle.status === filterOption ||
      vehicle.availability === filterOption;
    return matchesSearch && matchesFilter;
  });
};

export const getStatusBadge = (status: string): string => {
  switch (status) {
    case 'Operational':
      return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
    case 'Under Maintenance':
      return 'bg-yellow-500 text-white px-3 py-1 rounded text-sm font-medium';
    default:
      return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
  }
};

export const getAvailabilityBadge = (availability: string): string => {
  switch (availability) {
    case 'Available':
      return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
    case 'Rented':
      return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
    case 'Unavailable':
      return 'bg-red-500 text-white px-3 py-1 rounded text-sm font-medium';
    default:
      return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
  }
};

export const handleViewVehicle = (id: string, navigate: NavigateFunction) => {
  navigate(`/admin/receptionMasterAdmin/fleet/vehicles/edit-vehicle/${id.replace('#', '')}`);
};

export const handleSearchChange = (
  value: string,
  setSearchTerm: Dispatch<SetStateAction<string>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setSearchTerm(value);
  setCurrentPage(1);
};

export const handleFilterOptionChange = (
  value: string,
  setFilterOption: Dispatch<SetStateAction<string>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setFilterOption(value);
  setCurrentPage(1);
};

export const handleRecordsPerPageChange = (
  records: number,
  setRecordsPerPage: Dispatch<SetStateAction<number>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setRecordsPerPage(records);
  setCurrentPage(1);
};