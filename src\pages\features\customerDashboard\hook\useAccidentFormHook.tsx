import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { accidentMockData } from '../common/accidentData';
import { AccidentFormSecondData } from '../type/customer-type';

export const useAccidentFormHook = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState({
    damageType: '',
    driverName: '',
    phoneNumber: '',
    address: '',
    postCode: '',
    country: 'Australia',
    email: '',
    dlNumber: '',
    issueCountry: '',
    issueDate: '',
    expiryDate: '',
    conditions: '',
    frontViewImage: null,
    backViewImage: null,
    vehicleType: '',
    rego: '',
    color: '',
    interiorImages: [],
    exteriorImages: [],
    leftSideImages: [],
    rightSideImages: [],
    frontSideImages: [],
    backSideImages: [],
    sideMirrorImages: [],
    vehicleBodyImages: [],
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRadioChange = (value: 'vehicle' | 'property') => {
    setFormData(prev => ({
      ...prev,
      damageType: value
    }));
  };

  const handleSingleFileUpload = (field: keyof AccidentFormSecondData, file: File | null) => {
    setFormData(prev => ({
      ...prev,
      [field]: file
    }));
  };

  const handleMultipleFileUpload = (field: keyof AccidentFormSecondData, files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        [field]: fileArray
      }));
    }
  };

  const handleGoBack = () => {
    navigate('/customer/accident-form');
  };

  const handleNext = () => {
    navigate('/customer/accident-policedetails');
  };

  return {
    formData,
    setFormData,
    handleInputChange,
    handleRadioChange,
    handleSingleFileUpload,
    handleMultipleFileUpload,
    handleGoBack,
    handleNext,
  };
};