import { Button } from '../../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Textarea } from '../../../components/ui/textarea'
import { Checkbox } from '../../../components/ui/checkbox'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import { ArrowLeft, Save, X, Wrench } from 'lucide-react'
import { useJobcardRegularMaintainanceEdit } from './hook/usejobcard-regular-maintainance-edit'
import { JobCardData } from './type/mechanictype'

const JobcardRegularMaintainanceEdit = () => {
  const {
    formData,
    selectedJobCard,
    jobCards,
    isSubmitting,
    handleRegistrationChange,
    handleInputChange,
    handleCheckboxChange,
    handleStatusChange,
    handleSubmit,
    handleCancel
  } = useJobcardRegularMaintainanceEdit()

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Back Button & Title - Mobile only */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] flex items-center justify-center"
          size="sm"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>
      {/* Header - Desktop only */}
      <div className="hidden md:flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Vehicle Details Section */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Vehicle Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
              {/* Registration */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Registration</span>
                <Select
                  value={formData.registration}
                  onValueChange={handleRegistrationChange}
                >
                  <SelectTrigger className="text-sm h-8">
                    <SelectValue placeholder="Select registration number" />
                  </SelectTrigger>
                  <SelectContent>
                    {jobCards.map((card: JobCardData) => (
                      <SelectItem key={card.id} value={card.registration}>
                        {card.registration} - {card.make} {card.model}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              </div>
              {/* Make */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Make</span>
                <Input
                  value={selectedJobCard?.make || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Model */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</span>
                <Input
                  value={selectedJobCard?.model || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Model Series */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model Series</span>
                <Input
                  value={selectedJobCard?.modelSeries || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Colour */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Colour</span>
                <Input
                  value={selectedJobCard?.colour || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Prod Date */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Prod Date</span>
                <Input
                  value={selectedJobCard?.prodDate || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Next Service */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Next Service</span>
                <Input
                  value={selectedJobCard?.nextService || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Rego Due */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rego Due</span>
                <Input
                  value={selectedJobCard?.regoDue || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Engine # */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Engine #</span>
                <Input
                  value={selectedJobCard?.engineNumber || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* VIN */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">VIN</span>
                <Input
                  value={selectedJobCard?.vin || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Build Date */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Build Date</span>
                <Input
                  value={selectedJobCard?.buildDate || ''}
                  readOnly
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Litres */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Litres</span>
                <Input
                  value={formData.engineOilLitres}
                  onChange={(e) => handleInputChange('engineOilLitres', e.target.value)}
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Trans */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Trans</span>
                <Input
                  value={formData.trans}
                  onChange={(e) => handleInputChange('trans', e.target.value)}
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Air */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Air</span>
                <Input
                  value={formData.air}
                  onChange={(e) => handleInputChange('air', e.target.value)}
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Cyl */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Cyl</span>
                <Input
                  value={formData.cyl}
                  onChange={(e) => handleInputChange('cyl', e.target.value)}
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Body */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Body</span>
                <Input
                  value={formData.body}
                  onChange={(e) => handleInputChange('body', e.target.value)}
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Odo */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Odo</span>
                <Input
                  value={formData.odo}
                  onChange={(e) => handleInputChange('odo', e.target.value)}
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Hours */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Hours</span>
                <Input
                  value={formData.hours}
                  onChange={(e) => handleInputChange('hours', e.target.value)}
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
              {/* Repaired By */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
                <Input
                  value={formData.repairedBy}
                  onChange={(e) => handleInputChange('repairedBy', e.target.value)}
                  className="text-sm h-8 bg-gray-50"
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-4">
            {/* Row 1 */}
            <div className=" relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Registration</span>
              <Select
                value={formData.registration}
                onValueChange={handleRegistrationChange}
              >
                <SelectTrigger className="text-sm h-8">
                  <SelectValue placeholder="Select registration number" />
                </SelectTrigger>
                <SelectContent>
                  {jobCards.map((card: JobCardData) => (
                    <SelectItem key={card.id} value={card.registration}>
                      {card.registration} - {card.make} {card.model}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Make</span>
              <Input
                value={selectedJobCard?.make || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</span>
              <Input
                value={selectedJobCard?.model || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>

            {/* Row 2 */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model Series</span>
              <Input
                value={selectedJobCard?.modelSeries || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Colour</span>
              <Input
                value={selectedJobCard?.colour || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Prod Date</span>
              <Input
                value={selectedJobCard?.prodDate || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>

            {/* Row 3 */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Next Service</span>
              <Input
                value={selectedJobCard?.nextService || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rego Due</span>
              <Input
                value={selectedJobCard?.regoDue || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Engine #</span>
              <Input
                value={selectedJobCard?.engineNumber || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>

            {/* Row 4 */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">VIN</span>
              <Input
                value={selectedJobCard?.vin || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Build Date</span>
              <Input
                value={selectedJobCard?.buildDate || ''}
                readOnly
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            {/* Litres */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Litres</span>
              <Input
                value={formData.engineOilLitres}
                onChange={(e) => handleInputChange('engineOilLitres', e.target.value)}
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            {/* Trans */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Trans</span>
              <Input
                value={formData.trans}
                onChange={(e) => handleInputChange('trans', e.target.value)}
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            {/* Air */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Air</span>
              <Input
                value={formData.air}
                onChange={(e) => handleInputChange('air', e.target.value)}
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            {/* Cyl */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Cyl</span>
              <Input
                value={formData.cyl}
                onChange={(e) => handleInputChange('cyl', e.target.value)}
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            {/* Body */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Body</span>
              <Input
                value={formData.body}
                onChange={(e) => handleInputChange('body', e.target.value)}
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            {/* Odo */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Odo</span>
              <Input
                value={formData.odo}
                onChange={(e) => handleInputChange('odo', e.target.value)}
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            {/* Hours */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Hours</span>
              <Input
                value={formData.hours}
                onChange={(e) => handleInputChange('hours', e.target.value)}
                className="text-sm h-8 bg-gray-50"
              />
            </div>
            {/* Repaired By */}
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
              <Input
                value={formData.repairedBy}
                onChange={(e) => handleInputChange('repairedBy', e.target.value)}
                className="text-sm h-8 bg-gray-50"
              />
            </div>
          </div>
        </div>

        {/* Job Card Notes Section */}
        <div className="mb-8">
          <h2 className="text-lg font-bold text-gray-800 mb-4">Job Card Notes</h2>
          <h3 className="text-sm font-semibold text-gray-700 mb-6">The Service Requires;</h3>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
              {/* Drain & Refilled Engine Oil */}
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="drainRefillEngineOil"
                    checked={formData.drainRefillEngineOil}
                    onCheckedChange={(checked) => handleCheckboxChange('drainRefillEngineOil', checked as boolean)}
                  />
                  <Label htmlFor="drainRefillEngineOil" className="text-sm">Drain & Refilled Engine Oil</Label>
                </div>
                <div className="flex items-center space-x-2 pl-6">
                  <Input
                    placeholder="5"
                    value={formData.engineOilLitres}
                    onChange={(e) => handleInputChange('engineOilLitres', e.target.value)}
                    className="w-14 h-6 text-sm"
                  />
                  <span className="text-sm">L</span>
                </div>
              </div>
              {/* Replace Oil Filter */}
              <div className="flex flex-col space-y-2">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="replaceOilFilter"
                    checked={formData.replaceOilFilter}
                    onCheckedChange={(checked) => handleCheckboxChange('replaceOilFilter', checked as boolean)}
                  />
                  <Label htmlFor="replaceOilFilter" className="text-sm">Replace Oil Filter</Label>
                </div>
                <div className="flex items-center space-x-2 pl-6">
                  <Input
                    placeholder=""
                    value={formData.replaceOilFilterLitres}
                    onChange={(e) => handleInputChange('replaceOilFilterLitres', e.target.value)}
                    className="w-14 h-6 text-sm"
                  />
                  <span className="text-sm">L</span>
                </div>
              </div>
              {/* Replace Fuel Filter */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="replaceFuelFilter"
                  checked={formData.replaceFuelFilter}
                  onCheckedChange={(checked) => handleCheckboxChange('replaceFuelFilter', checked as boolean)}
                />
                <Label htmlFor="replaceFuelFilter" className="text-sm">Replace Fuel Filter</Label>
              </div>
              {/* Air Filter */}
              <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-2 md:space-y-0">
                <span className="text-sm">Air Filter -</span>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="airFilterChecked"
                    checked={formData.airFilterChecked}
                    onCheckedChange={(checked) => handleCheckboxChange('airFilterChecked', checked as boolean)}
                  />
                  <Label htmlFor="airFilterChecked" className="text-sm">Checked</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="airFilterReplaced"
                    checked={formData.airFilterReplaced}
                    onCheckedChange={(checked) => handleCheckboxChange('airFilterReplaced', checked as boolean)}
                  />
                  <Label htmlFor="airFilterReplaced" className="text-sm">Replaced</Label>
                </div>
              </div>

              {/* Cabin Filter */}
              <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-2 md:space-y-0">
                <span className="text-sm">Cabin Filter -</span>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="cabinFilterChecked"
                    checked={formData.cabinFilterChecked}
                    onCheckedChange={(checked) => handleCheckboxChange('cabinFilterChecked', checked as boolean)}
                  />
                  <Label htmlFor="cabinFilterChecked" className="text-sm">Checked</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="cabinFilterReplaced"
                    checked={formData.cabinFilterReplaced}
                    onCheckedChange={(checked) => handleCheckboxChange('cabinFilterReplaced', checked as boolean)}
                  />
                  <Label htmlFor="cabinFilterReplaced" className="text-sm">Replaced</Label>
                </div>
              </div>

              {/* Check and Adjust Tyre Air Pressure */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkAdjustTyrePressure"
                  checked={formData.checkAdjustTyrePressure}
                  onCheckedChange={(checked) => handleCheckboxChange('checkAdjustTyrePressure', checked as boolean)}
                />
                <Label htmlFor="checkAdjustTyrePressure" className="text-sm">Check and Adjust Tyre Air Pressure</Label>
              </div>

              {/* Check & Top Up all fluids */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkTopUpAllFluids"
                  checked={formData.checkTopUpAllFluids}
                  onCheckedChange={(checked) => handleCheckboxChange('checkTopUpAllFluids', checked as boolean)}
                />
                <Label htmlFor="checkTopUpAllFluids" className="text-sm">Check & Top Up all fluids</Label>
              </div>

              {/* Check, Clean & Adjust Break (two instances) */}
              <div className="flex flex-col md:flex-row md:space-x-12 space-y-2 md:space-y-0">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="checkCleanAdjustBreak"
                    checked={formData.checkCleanAdjustBreak}
                    onCheckedChange={(checked) => handleCheckboxChange('checkCleanAdjustBreak', checked as boolean)}
                  />
                  <Label htmlFor="checkCleanAdjustBreak" className="text-sm">Check, Clean & Adjust Break</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="checkCleanAdjustBreak2"
                    checked={formData.checkCleanAdjustBreak2}
                    onCheckedChange={(checked) => handleCheckboxChange('checkCleanAdjustBreak2', checked as boolean)}
                  />
                  <Label htmlFor="checkCleanAdjustBreak2" className="text-sm">Check, Clean & Adjust Break</Label>
                </div>
              </div>

              {/* Check Wiper Blades */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkWiperBlades"
                  checked={formData.checkWiperBlades}
                  onCheckedChange={(checked) => handleCheckboxChange('checkWiperBlades', checked as boolean)}
                />
                <Label htmlFor="checkWiperBlades" className="text-sm">Check Wiper Blades</Label>
              </div>

              {/* Check Lights */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkLights"
                  checked={formData.checkLights}
                  onCheckedChange={(checked) => handleCheckboxChange('checkLights', checked as boolean)}
                />
                <Label htmlFor="checkLights" className="text-sm">Check Lights</Label>
              </div>

              {/* Spark Plugs */}
              <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-2 md:space-y-0">
                <span className="text-sm">Spark Plugs -</span>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sparkPlugsChecked"
                    checked={formData.sparkPlugsChecked}
                    onCheckedChange={(checked) => handleCheckboxChange('sparkPlugsChecked', checked as boolean)}
                  />
                  <Label htmlFor="sparkPlugsChecked" className="text-sm">Checked</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sparkPlugsReplaced"
                    checked={formData.sparkPlugsReplaced}
                    onCheckedChange={(checked) => handleCheckboxChange('sparkPlugsReplaced', checked as boolean)}
                  />
                  <Label htmlFor="sparkPlugsReplaced" className="text-sm">Replaced</Label>
                </div>
              </div>

              {/* Check for correct operation */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkCorrectOperation"
                  checked={formData.checkCorrectOperation}
                  onCheckedChange={(checked) => handleCheckboxChange('checkCorrectOperation', checked as boolean)}
                />
                <Label htmlFor="checkCorrectOperation" className="text-sm">Check for correct operation. Safety Check & Report</Label>
              </div>

              {/* Tyre Conditions - Mobile */}
              <div className="text-sm flex flex-col gap-2">
                <span className="mb-1">Tyre Conditions :</span>
                <div className="flex flex-col gap-2 pl-2">
                  <div className="flex items-center gap-2">
                    <span>RF</span>
                    <Input
                      placeholder="..."
                      value={formData.tyreConditionRF}
                      onChange={(e) => handleInputChange('tyreConditionRF', e.target.value)}
                      className="w-14 h-6 text-sm"
                    />
                    <span>%</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>LF</span>
                    <Input
                      placeholder="..."
                      value={formData.tyreConditionLF}
                      onChange={(e) => handleInputChange('tyreConditionLF', e.target.value)}
                      className="w-14 h-6 text-sm"
                    />
                    <span>%</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>RR</span>
                    <Input
                      placeholder="..."
                      value={formData.tyreConditionRR}
                      onChange={(e) => handleInputChange('tyreConditionRR', e.target.value)}
                      className="w-14 h-6 text-sm"
                    />
                    <span>%</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>LR</span>
                    <Input
                      placeholder="..."
                      value={formData.tyreConditionLR}
                      onChange={(e) => handleInputChange('tyreConditionLR', e.target.value)}
                      className="w-14 h-6 text-sm"
                    />
                    <span>%</span>
                  </div>
                </div>
              </div>

              {/* Break Condition - Mobile */}
              <div className="text-sm flex flex-col gap-2">
                <span className="mb-1">Break Condition :</span>
                <div className="flex flex-col gap-2 pl-2">
                  <div className="flex items-center gap-2">
                    <span>Front</span>
                    <Input
                      placeholder="..."
                      value={formData.breakConditionFront}
                      onChange={(e) => handleInputChange('breakConditionFront', e.target.value)}
                      className="w-14 h-6 text-sm mx-1"
                    />
                    <span>% Discs</span>
                    <Input
                      placeholder="..."
                      value={formData.breakDiscsFront}
                      onChange={(e) => handleInputChange('breakDiscsFront', e.target.value)}
                      className="w-14 h-6 text-sm mx-1"
                    />
                    <span>MM</span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span>Rear</span>
                    <Input
                      placeholder="..."
                      value={formData.breakConditionRear}
                      onChange={(e) => handleInputChange('breakConditionRear', e.target.value)}
                      className="w-14 h-6 text-sm mx-1"
                    />
                    <span>% Discs</span>
                    <Input
                      placeholder="..."
                      value={formData.breakDiscsRear}
                      onChange={(e) => handleInputChange('breakDiscsRear', e.target.value)}
                      className="w-14 h-6 text-sm mx-1"
                    />
                    <span>MM</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:block">
            <div className="space-y-6">
              {/* ...existing desktop layout for checkboxes/inputs... */}
              {/* Drain & Refilled Engine Oil */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="drainRefillEngineOil"
                  checked={formData.drainRefillEngineOil}
                  onCheckedChange={(checked) => handleCheckboxChange('drainRefillEngineOil', checked as boolean)}
                />
                <Label htmlFor="drainRefillEngineOil" className="text-sm">Drain & Refilled Engine Oil</Label>
                <span className="text-sm">......</span>
                <Input
                  placeholder="5"
                  value={formData.engineOilLitres}
                  onChange={(e) => handleInputChange('engineOilLitres', e.target.value)}
                  className="w-12 h-6 text-sm"
                />
                <span className="text-sm">.................... L</span>
              </div>

              {/* Replace Oil Filter and Replace Fuel Filter */}
              <div className="flex flex-col md:flex-row md:space-x-12 space-y-2 md:space-y-0">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="replaceOilFilter"
                    checked={formData.replaceOilFilter}
                    onCheckedChange={(checked) => handleCheckboxChange('replaceOilFilter', checked as boolean)}
                  />
                  <Label htmlFor="replaceOilFilter" className="text-sm">Replace Oil Filter</Label>
                  <span className="text-sm">.................................... L</span>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="replaceFuelFilter"
                    checked={formData.replaceFuelFilter}
                    onCheckedChange={(checked) => handleCheckboxChange('replaceFuelFilter', checked as boolean)}
                  />
                  <Label htmlFor="replaceFuelFilter" className="text-sm">Replace Fuel Filter</Label>
                </div>
              </div>

              {/* Air Filter */}
              <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-2 md:space-y-0">
                <span className="text-sm">Air Filter -</span>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="airFilterChecked"
                    checked={formData.airFilterChecked}
                    onCheckedChange={(checked) => handleCheckboxChange('airFilterChecked', checked as boolean)}
                  />
                  <Label htmlFor="airFilterChecked" className="text-sm">Checked</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="airFilterReplaced"
                    checked={formData.airFilterReplaced}
                    onCheckedChange={(checked) => handleCheckboxChange('airFilterReplaced', checked as boolean)}
                  />
                  <Label htmlFor="airFilterReplaced" className="text-sm">Replaced</Label>
                </div>
              </div>

              {/* Cabin Filter */}
              <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-2 md:space-y-0">
                <span className="text-sm">Cabin Filter -</span>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="cabinFilterChecked"
                    checked={formData.cabinFilterChecked}
                    onCheckedChange={(checked) => handleCheckboxChange('cabinFilterChecked', checked as boolean)}
                  />
                  <Label htmlFor="cabinFilterChecked" className="text-sm">Checked</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="cabinFilterReplaced"
                    checked={formData.cabinFilterReplaced}
                    onCheckedChange={(checked) => handleCheckboxChange('cabinFilterReplaced', checked as boolean)}
                  />
                  <Label htmlFor="cabinFilterReplaced" className="text-sm">Replaced</Label>
                </div>
              </div>

              {/* Check and Adjust Tyre Air Pressure */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkAdjustTyrePressure"
                  checked={formData.checkAdjustTyrePressure}
                  onCheckedChange={(checked) => handleCheckboxChange('checkAdjustTyrePressure', checked as boolean)}
                />
                <Label htmlFor="checkAdjustTyrePressure" className="text-sm">Check and Adjust Tyre Air Pressure</Label>
              </div>

              {/* Check & Top Up all fluids */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkTopUpAllFluids"
                  checked={formData.checkTopUpAllFluids}
                  onCheckedChange={(checked) => handleCheckboxChange('checkTopUpAllFluids', checked as boolean)}
                />
                <Label htmlFor="checkTopUpAllFluids" className="text-sm">Check & Top Up all fluids</Label>
              </div>

              {/* Check, Clean & Adjust Break (two instances) */}
              <div className="flex flex-col md:flex-row md:space-x-12 space-y-2 md:space-y-0">
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="checkCleanAdjustBreak"
                    checked={formData.checkCleanAdjustBreak}
                    onCheckedChange={(checked) => handleCheckboxChange('checkCleanAdjustBreak', checked as boolean)}
                  />
                  <Label htmlFor="checkCleanAdjustBreak" className="text-sm">Check, Clean & Adjust Break</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="checkCleanAdjustBreak2"
                    checked={formData.checkCleanAdjustBreak2}
                    onCheckedChange={(checked) => handleCheckboxChange('checkCleanAdjustBreak2', checked as boolean)}
                  />
                  <Label htmlFor="checkCleanAdjustBreak2" className="text-sm">Check, Clean & Adjust Break</Label>
                </div>
              </div>

              {/* Check Wiper Blades */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkWiperBlades"
                  checked={formData.checkWiperBlades}
                  onCheckedChange={(checked) => handleCheckboxChange('checkWiperBlades', checked as boolean)}
                />
                <Label htmlFor="checkWiperBlades" className="text-sm">Check Wiper Blades</Label>
              </div>

              {/* Check Lights */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkLights"
                  checked={formData.checkLights}
                  onCheckedChange={(checked) => handleCheckboxChange('checkLights', checked as boolean)}
                />
                <Label htmlFor="checkLights" className="text-sm">Check Lights</Label>
              </div>

              {/* Spark Plugs */}
              <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-2 md:space-y-0">
                <span className="text-sm">Spark Plugs -</span>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sparkPlugsChecked"
                    checked={formData.sparkPlugsChecked}
                    onCheckedChange={(checked) => handleCheckboxChange('sparkPlugsChecked', checked as boolean)}
                  />
                  <Label htmlFor="sparkPlugsChecked" className="text-sm">Checked</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <Checkbox
                    id="sparkPlugsReplaced"
                    checked={formData.sparkPlugsReplaced}
                    onCheckedChange={(checked) => handleCheckboxChange('sparkPlugsReplaced', checked as boolean)}
                  />
                  <Label htmlFor="sparkPlugsReplaced" className="text-sm">Replaced</Label>
                </div>
              </div>

              {/* Check for correct operation */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkCorrectOperation"
                  checked={formData.checkCorrectOperation}
                  onCheckedChange={(checked) => handleCheckboxChange('checkCorrectOperation', checked as boolean)}
                />
                <Label htmlFor="checkCorrectOperation" className="text-sm">Check for correct operation. Safety Check & Report</Label>
              </div>

              {/* Tyre Conditions */}
              <div className="text-sm mt-4 flex flex-wrap items-center gap-x-4 gap-y-2">
                <span>Tyre Conditions : RF </span>
                <Input
                  placeholder="..................."
                  value={formData.tyreConditionRF}
                  onChange={(e) => handleInputChange('tyreConditionRF', e.target.value)}
                  className="inline-block w-16 h-6 text-sm mx-1"
                />
                <span>% LF </span>
                <Input
                  placeholder="..................."
                  value={formData.tyreConditionLF}
                  onChange={(e) => handleInputChange('tyreConditionLF', e.target.value)}
                  className="inline-block w-16 h-6 text-sm mx-1"
                />
                <span>% RR </span>
                <Input
                  placeholder="..................."
                  value={formData.tyreConditionRR}
                  onChange={(e) => handleInputChange('tyreConditionRR', e.target.value)}
                  className="inline-block w-16 h-6 text-sm mx-1"
                />
                <span>% LR </span>
                <Input
                  placeholder="..................."
                  value={formData.tyreConditionLR}
                  onChange={(e) => handleInputChange('tyreConditionLR', e.target.value)}
                  className="inline-block w-16 h-6 text-sm mx-1"
                />
                <span>%</span>
              </div>

              {/* Break Condition */}
              <div className="text-sm flex flex-col gap-2">
                <div>
                  <span>Break Condition : Front </span>
                  <Input
                    placeholder="..................."
                    value={formData.breakConditionFront}
                    onChange={(e) => handleInputChange('breakConditionFront', e.target.value)}
                    className="inline-block w-16 h-6 text-sm mx-1"
                  />
                  <span>% Discs </span>
                  <Input
                    placeholder="................"
                    value={formData.breakDiscsFront}
                    onChange={(e) => handleInputChange('breakDiscsFront', e.target.value)}
                    className="inline-block w-16 h-6 text-sm mx-1"
                  />
                  <span>.......... MM</span>
                </div>
                <div className="ml-0 md:ml-24">
                  <span>Rear </span>
                  <Input
                    placeholder="..................."
                    value={formData.breakConditionRear}
                    onChange={(e) => handleInputChange('breakConditionRear', e.target.value)}
                    className="inline-block w-16 h-6 text-sm mx-1"
                  />
                  <span>% Discs </span>
                  <Input
                    placeholder="................"
                    value={formData.breakDiscsRear}
                    onChange={(e) => handleInputChange('breakDiscsRear', e.target.value)}
                    className="inline-block w-16 h-6 text-sm mx-1"
                  />
                  <span>.......... MM</span>
                </div>
              </div>

              {/* Check GPS */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkGPS"
                  checked={formData.checkGPS}
                  onCheckedChange={(checked) => handleCheckboxChange('checkGPS', checked as boolean)}
                />
                <Label htmlFor="checkGPS" className="text-sm">Check GPS</Label>
              </div>

              {/* Road test */}
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="roadTest"
                  checked={formData.roadTest}
                  onCheckedChange={(checked) => handleCheckboxChange('roadTest', checked as boolean)}
                />
                <Label htmlFor="roadTest" className="text-sm">Road test</Label>
              </div>

              {/* Comments */}
              <div>
                <Label htmlFor="notesComment" className="text-sm text-gray-600">Comment</Label>
                <Textarea
                  placeholder="Type Here ---"
                  value={formData.comments}
                  onChange={(e) => handleInputChange('comments', e.target.value)}
                  className="text-sm min-h-[80px] resize-none border-gray-300 p-0"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex flex-row flex-wrap justify-end gap-2 pt-4">
          <Button
            type="submit"
            className="bg-[#330101] hover:bg-gray-800 text-white px-8 py-2  sm:w-32 text-sm"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Updating...' : 'Update'}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="px-8 py-2  sm:w-32 text-sm"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  )
}

export default JobcardRegularMaintainanceEdit
