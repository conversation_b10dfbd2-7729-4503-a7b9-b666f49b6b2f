import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { initialVehicleFormData } from './common/mockData';
import { handleInputChange, handleGoBack } from './hook/useservice-management-view';
import { VehicleFormData } from './type/teamleadertype';

export function ServiceManagementViewPage() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<VehicleFormData>(initialVehicleFormData);

  return (
    <div className="min-h-screen p-4">
      <div className="flex items-center mb-4">
        <Button
          className="bg-[#330101] text-white text-sm px-3 py-2"
          size="sm"
          onClick={() => handleGoBack(navigate)}
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div  className="bg-white rounded-lg p-4">
        {/* General Information */}
        <h2 className="text-lg font-semibold mb-4">General Information</h2>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={formData.vehicleId}
              onChange={(e) => handleInputChange('vehicleId', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Vehicle ID</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.vehicleModel}
              onChange={(e) => handleInputChange('vehicleModel', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Vehicle Model</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.regoNumber}
              onChange={(e) => handleInputChange('regoNumber', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Rego #</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.serviceType}
              onChange={(e) => handleInputChange('serviceType', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Service Type</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.reservation}
              onChange={(e) => handleInputChange('reservation', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Reservation</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.totalLabor}
              onChange={(e) => handleInputChange('totalLabor', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Total in Labour</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.totalParts}
              onChange={(e) => handleInputChange('totalParts', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Total in Parts</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.status}
              onChange={(e) => handleInputChange('status', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Status</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.damages}
              onChange={(e) => handleInputChange('damages', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Damages</Label>
          </div>
          <div className="relative col-span-2">
            <Input
              type="text"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Notes</Label>
          </div>
        </div>

        {/* Vehicle Out Information */}
        <h2 className="text-lg font-semibold mb-4 mt-6">Vehicle Out Information</h2>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input
              type="date"
              value={formData.dateIn}
              onChange={(e) => handleInputChange('dateIn', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Date In</Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={formData.dateOut}
              onChange={(e) => handleInputChange('dateOut', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Date Out</Label>
          </div>
          <div className="relative">
            <Input
              type="time"
              value={formData.timeIn}
              onChange={(e) => handleInputChange('timeIn', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Time In</Label>
          </div>
          <div className="relative">
            <Input
              type="time"
              value={formData.timeOut}
              onChange={(e) => handleInputChange('timeOut', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Time Out</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.fuelIn}
              onChange={(e) => handleInputChange('fuelIn', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Fuel In</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.fuelOut}
              onChange={(e) => handleInputChange('fuelOut', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Fuel Out</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.odometerIn}
              onChange={(e) => handleInputChange('odometerIn', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Odometer In</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.odometerOut}
              onChange={(e) => handleInputChange('odometerOut', e.target.value, setFormData)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Odometer Out</Label>
          </div>
        </div>

        <div className="mb-4 relative">
          <Input
            type="text"
            value={formData.comments}
            onChange={(e) => handleInputChange('comments', e.target.value, setFormData)}
            className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Comments</Label>
        </div>
      </div>
    </div>
  );
}