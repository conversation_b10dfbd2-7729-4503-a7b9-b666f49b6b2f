import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; 
import { ArrowLeft, Users } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface CustomerFormData {
  id: string;
  vendorName: string;
  phone: string;
  email: string;
  address: string;
}

export function VendorEditPage() {
  const navigate = useNavigate(); 
  const handleSave = (): void => {
    navigate('');
  };

  const [formData, setFormData] = useState<CustomerFormData>({
    id: 'VID001',
    vendorName: 'ARB Somerton',
    phone: '0421 458 431',
    email: '<EMAIL>',
    address: '26 Somerton Road, Somerton VIC 3062'
  });

  const handleInputChange = (field: keyof CustomerFormData, value: string): void => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  return (
    <div className="min-h-screen p-2 sm:p-4 md:p-6">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <Button
          className="bg-[#330101] text-white w-full sm:w-auto text-sm sm:text-base px-3 sm:px-4 py-2 flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/admin/workshopMasterAdmin/vendor')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="flex items-center mb-4 sm:mb-6">
        <Users className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-medium text-earth-dark">Edit Vendors</h1>
      </div>
      
      <div className="bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.id}
              onChange={(e) => handleInputChange('id', e.target.value)}
              className="bg-gray-300 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              ID
            </Label>
          </div>
        </div>
        {/* Name Fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.vendorName}
              onChange={(e) => handleInputChange('vendorName', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-600 bg-white px-1 transition-all duration-200 pointer-events-none">
              Vendor Name
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-600 bg-white px-1 transition-all duration-200 pointer-events-none">
              Phone Number
            </Label>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-600 bg-white px-1 transition-all duration-200 pointer-events-none">
              Email
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.address}
              onChange={(e) => handleInputChange('address', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-600 bg-white px-1 transition-all duration-200 pointer-events-none">
              Address
            </Label>
          </div>
        </div>
      </div>
      <div className="flex justify-end mt-4">
        <Button 
          onClick={handleSave}
          className="bg-[#330101] text-white w-1/3 sm:w-auto text-sm sm:text-base px-3 sm:px-4 py-2 flex items-center justify-center"
        >
          Update
        </Button>
      </div>
    </div>
  );
}