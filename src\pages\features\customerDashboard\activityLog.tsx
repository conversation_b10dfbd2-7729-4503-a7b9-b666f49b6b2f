import React from 'react';
import { Search, ChevronDown, RotateCcw } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { useActivityLogHook } from './hook/useActivityLogHook';


export function ActivityLogPage() {
  const {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    filteredActivityEntries,
    totalRecords,
    totalPages,
    currentActivityEntries,
    formatDate,
  } = useActivityLogHook();

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title */}
      <div className="flex items-center mb-4 sm:mb-6">
        <RotateCcw className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Activity Log</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <select 
            value={filterStatus}
            onChange={(e) => {
              setFilterStatus(e.target.value);
              setCurrentPage(1);
            }}
            className="appearance-none bg-white border border-gray-300 rounded-md px-3 sm:px-4 py-2 w-full sm:w-auto text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
          >
            <option value="All">Filter</option>
            <option value="Today">Today</option>
            <option value="This Week">This Week</option>
            <option value="This Month">This Month</option>
            <option value="Last 30 Days">Last 30 Days</option>
          </select>
          <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
        </div>
        
        <div className="relative flex-1 w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm sm:text-base rounded-md focus:border-transparent"
          />
        </div>
        
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 w-full sm:w-auto text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Activity Log List */}
      <div className="space-y-3 sm:space-y-4">
        {currentActivityEntries.length === 0 ? (
          <div className="text-center py-6 sm:py-8">
            <p className="text-gray-500 text-sm sm:text-base">No notifications found matching your criteria.</p>
          </div>
        ) : (
          currentActivityEntries.map((activityEntry) => (
            <div
              key={activityEntry.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-2 sm:space-x-3">
                <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#4A0000] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 sm:mt-0">
                  <span className="text-white text-xs sm:text-sm font-medium">i</span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-gray-800 text-sm sm:text-base leading-relaxed break-words">
                    {activityEntry.message}
                  </p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalRecords > 0 && (
        <div className="mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="overflow-x-auto"
          />
        </div>
      )}
    </div>
  );
}