import { useState } from "react";

const mockJobCards = [
  {
    id: "1",
    vehicleClass: "SUV",
    rego: "ABC123",
    accidentDate: "2025-07-01",
    repairedBy: "<PERSON>",
    repairCompletionDate: "2025-07-10",
  },
  {
    id: "2",
    vehicleClass: "Sedan",
    rego: "XYZ789",
    accidentDate: "2025-06-15",
    repairedBy: "<PERSON>",
    repairCompletionDate: "2025-06-25",
  },
];

export function useJobcardAccidentRepair() {
  const [jobCards] = useState(mockJobCards);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);

  const totalRecords = jobCards.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);

  const getStatusColor = (status: any) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-800";
      case "In Progress":
        return "bg-yellow-100 text-yellow-800";
      case "Pending":
        return "bg-gray-100 text-gray-800";
      default:
        return "bg-gray-100 text-gray-800";
    }
  };

  return {
    jobCards,
    currentPage,
    recordsPerPage,
    totalPages,
    totalRecords,
    setCurrentPage,
    setRecordsPerPage,
    getStatusColor,
  };
}