import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { initialVehicleFormData, statusOptions, initialImageData } from './common/mockData';
import { handleInputChange, handleSave, handleImageUpload } from './hook/useservice-management-edit';
import { VehicleFormData, ImageData } from './type/teamleadertype';
import { useToast } from '@/components/ui/use-toast';

export function ServiceManagementEditPage() {
  const navigate = useNavigate();
  const { toast } = useToast();
  const [formData, setFormData] = useState<VehicleFormData>(initialVehicleFormData);
  const [imageData, setImageData] = useState<ImageData>(initialImageData);

  return (
    <div className="min-h-screen p-4">
      <div className="flex items-center mb-4">
        <Button
          className="bg-[#330101] text-white text-sm px-3 py-2"
          size="sm"
          onClick={() => navigate('/teamleader/service-management')}
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="bg-white rounded-lg p-4">
        {/* General Information */}
        <h2 className="text-lg font-semibold mb-4">General Information</h2>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={formData.vehicleId}
              onChange={(e) => handleInputChange('vehicleId', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              disabled
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Vehicle ID</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.vehicleModel}
              onChange={(e) => handleInputChange('vehicleModel', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              disabled
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Vehicle Model</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.regoNumber}
              onChange={(e) => handleInputChange('regoNumber', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              disabled
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Rego #</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.serviceType}
              onChange={(e) => handleInputChange('serviceType', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              disabled
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Service Type</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.totalLabor}
              onChange={(e) => handleInputChange('totalLabor', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Total in Labour</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.totalParts}
              onChange={(e) => handleInputChange('totalParts', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Total in Parts</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.damages}
              onChange={(e) => handleInputChange('damages', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Damages</Label>
          </div>
          <div className="relative">
            <Input
              type="file"
              accept="image/*"
              multiple
              onChange={(e) => handleImageUpload('rightSideDoors', e.target.files, setImageData, toast)}
              className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Images
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Status</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {formData.status || 'Select Status'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {statusOptions.map((status) => (
                  <DropdownMenuItem
                    key={status}
                    onSelect={() => handleInputChange('status', status, setFormData)}
                    className={formData.status === status ? 'bg-amber-100 font-regular' : ''}
                  >
                    {status || 'Select Status'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="relative col-span-2">
            <Input
              type="text"
              value={formData.notes}
              onChange={(e) => handleInputChange('notes', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              disabled
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Notes</Label>
          </div>
        </div>

        {/* Vehicle Out Information */}
        <h2 className="text-lg font-semibold mb-4 mt-6">Vehicle Out Information</h2>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input
              type="date"
              value={formData.dateIn}
              onChange={(e) => handleInputChange('dateIn', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Date In</Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={formData.dateOut}
              onChange={(e) => handleInputChange('dateOut', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Date Out</Label>
          </div>
          <div className="relative">
            <Input
              type="time"
              value={formData.timeIn}
              onChange={(e) => handleInputChange('timeIn', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Time In</Label>
          </div>
          <div className="relative">
            <Input
              type="time"
              value={formData.timeOut}
              onChange={(e) => handleInputChange('timeOut', e.target.value, setFormData)}
              className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Time Out</Label>
          </div>
        </div>

        <div className="mb-4 relative">
          <Input
            type="text"
            value={formData.comments}
            onChange={(e) => handleInputChange('comments', e.target.value, setFormData)}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
          />
          <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Comments</Label>
        </div>

        {/* Save Button */}
        <div className="flex justify-end">
          <Button
            onClick={() => handleSave(navigate)}
            className="px-4 py-2 text-white rounded-md bg-[#330101] transition-colors text-sm"
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
}