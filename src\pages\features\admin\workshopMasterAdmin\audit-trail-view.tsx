import React from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export function AuditTrailView() {
    const { mechanicId } = useParams<{ mechanicId: string }>();
  const navigate = useNavigate();

  const auditData = {
    mechanicId: 'MC001',
    mechanicName: '<PERSON>',
    vehicle: 'Atom - 1PX 1ZR',
    serviceType: 'Accident Repair',
    dateTime: '21-02-2025 10:00 am',
    description: 'Set brakes and repair the doors in front of the vehicle',
  };

  return (
    <div className="min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <Button
          className='bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2'
          size="sm"
          onClick={() => navigate('/admin/workshopMasterAdmin/audit-trail')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="flex items-center mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl font-medium text-earth-dark">Audit & Trail</h1>
      </div>
      
      <div className="bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
        <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={auditData.mechanicId}
              className="bg-gray-300 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Mechanic ID
            </Label>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={auditData.mechanicName}
              className="bg-gray-300 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Mechanic Name
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={auditData.vehicle}
              className="bg-gray-300 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Vehicle
            </Label>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={auditData.serviceType}
              className="bg-gray-300 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Service Type
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={auditData.dateTime}
              className="bg-gray-300 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Date & Time
            </Label>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-3 sm:gap-6 md:gap-8">
          <div className="relative">
            <Input
              type="text"
              value={auditData.description}
              className="bg-gray-300 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base h-20 resize-none"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Description
            </Label>
          </div>
        </div>
      </div>
    </div>
  );
}