import { Dispatch, SetStateAction } from 'react';
import { useNavigate } from 'react-router-dom';
import { Reservation, RentalDetails, IncidentFormData, AccidentReportFields, AdditionalAccidentFields, BreakdownFields } from '../type/reception-type';

export const handleRegoChange = (
  selectedRego: string,
  setFormData: Dispatch<SetStateAction<IncidentFormData>>,
  reservationsData: Reservation[]
) => {
  setFormData((prev) => {
    const reservation = reservationsData.find((r) => r.rego === selectedRego);
    if (!reservation) return prev;
    return {
      ...prev,
      rego: selectedRego,
      rentalId: reservation.rentalId,
      pickupDate: reservation.pickupDate.toLocaleDateString(),
      pickupTime: reservation.pickupDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
      returnDate: reservation.returnDate.toLocaleDateString(),
      returnTime: reservation.returnDate.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit' }),
    };
  });
};

export const handlePhoneNumberChange = (
  phoneNumber: string,
  setFormData: Dispatch<SetStateAction<IncidentFormData>>,
  rentalDetailsData: RentalDetails[]
) => {
  setFormData((prev) => {
    const rentalDetails = rentalDetailsData.find((r) => r.phoneNumber === phoneNumber);
    if (!rentalDetails) return { ...prev, phoneNumber };
    return {
      ...prev,
      phoneNumber,
      customerName: rentalDetails.customerName,
      email: rentalDetails.email,
      agreementNo: rentalDetails.agreementNo,
    };
  });
};

export const handleReportTypeChange = (
  value: string,
  setFormData: Dispatch<SetStateAction<IncidentFormData>>,
  setAccidentFields: Dispatch<SetStateAction<AccidentReportFields>>,
  setAdditionalAccidentFields: Dispatch<SetStateAction<AdditionalAccidentFields>>,
  setBreakdownFields: Dispatch<SetStateAction<BreakdownFields>>,
  setShowAdditionalAccidentForm: Dispatch<SetStateAction<boolean>>,
  formData: IncidentFormData
) => {
  setFormData((prev) => ({ ...prev, reportType: value }));
  setShowAdditionalAccidentForm(false);
  setAccidentFields({
    payment: '',
    insuranceExcessCover: '',
    driverName: '',
    phoneNumber: '',
    email: '',
    birthday: undefined,
    address: '',
    postcode: '',
    country: '',
    drugsAlcoholIncident: '',
    licenseNumber: '',
    issueDate: undefined,
    expiryDate: undefined,
    conditions: '',
    frontView: null,
    backView: null,
    nextDue: undefined,
    obtainDetails: '',
    isDrugsAlcoholConsumed: undefined,
  });
  setAdditionalAccidentFields({
    accidentLocation: '',
    damageDescription: '',
    policeReport: null,
    witnessDetails: '',
  });
  setBreakdownFields({
    interiorImages: null,
    exteriorImages: null,
  });

  if (value === 'Accident') {
    setAccidentFields((prev) => ({
      ...prev,
      payment: 'No',
      insuranceExcessCover: 'Yes',
      driverName: formData.customerName || '',
      phoneNumber: formData.phoneNumber || '',
      email: formData.email || '',
      birthday: undefined,
      address: '',
      postcode: '',
      country: '',
      licenseNumber: '',
      issueDate: undefined,
      expiryDate: undefined,
      conditions: 'None',
    }));
  }
};

export const handleFileChange = <T extends AccidentReportFields | AdditionalAccidentFields | BreakdownFields>(
  field: keyof T,
  event: React.ChangeEvent<HTMLInputElement>,
  targetState: T,
  setTargetState: Dispatch<SetStateAction<T>>
) => {
  const file = event.target.files?.[0];
  if (file && (file.type.startsWith('image/') || file.type === 'application/pdf') && file.size < 5 * 1024 * 1024) { // Max 5MB
    setTargetState((prev) => ({ ...prev, [field]: file }));
  } else {
    alert('Please upload an image or PDF file smaller than 5MB.');
  }
};

export const handleDrugsAlcoholChange = (
  value: boolean,
  setAccidentFields: Dispatch<SetStateAction<AccidentReportFields>>
) => {
  setAccidentFields((prev) => ({
    ...prev,
    isDrugsAlcoholConsumed: value,
    payment: value ? 'Yes' : '',
    insuranceExcessCover: value ? 'Yes' : '',
  }));
};

export const handleInputChange = (
  field: keyof IncidentFormData,
  value: string,
  setFormData: Dispatch<SetStateAction<IncidentFormData>>
) => {
  setFormData((prev) => ({ ...prev, [field]: value }));
};

export const handleAccidentFieldChange = (
  field: keyof AccidentReportFields,
  value: string | Date | undefined,
  setAccidentFields: Dispatch<SetStateAction<AccidentReportFields>>
) => {
  setAccidentFields((prev) => ({ ...prev, [field]: value }));
};

export const handleAdditionalAccidentFieldChange = (
  field: keyof AdditionalAccidentFields,
  value: string,
  setAdditionalAccidentFields: Dispatch<SetStateAction<AdditionalAccidentFields>>
) => {
  setAdditionalAccidentFields((prev) => ({ ...prev, [field]: value }));
};

export const handleSubmit = (
  formData: IncidentFormData,
  accidentFields: AccidentReportFields,
  additionalAccidentFields: AdditionalAccidentFields,
  breakdownFields: BreakdownFields,
  showAdditionalAccidentForm: boolean,
  navigate: ReturnType<typeof useNavigate>
) => {
  const requiredFields = [
    formData.rego,
    formData.rentalId,
    formData.pickupDate,
    formData.pickupTime,
    formData.returnDate,
    formData.returnTime,
    formData.customerName,
    formData.email,
    formData.phoneNumber,
    formData.agreementNo,
    formData.reportType,
  ];
  const requiredAccidentFields = [
    accidentFields.driverName,
    accidentFields.phoneNumber,
    accidentFields.email,
    accidentFields.birthday,
    accidentFields.address,
    accidentFields.postcode,
    accidentFields.country,
    accidentFields.licenseNumber,
    accidentFields.issueDate,
    accidentFields.expiryDate,
    accidentFields.conditions,
    accidentFields.frontView,
    accidentFields.backView,
    accidentFields.isDrugsAlcoholConsumed !== undefined,
  ];
  const requiredAdditionalAccidentFields = [
    additionalAccidentFields.accidentLocation,
    additionalAccidentFields.damageDescription,
  ];
  const requiredBreakdownFields = [
    breakdownFields.interiorImages,
    breakdownFields.exteriorImages,
  ];

  if (requiredFields.some((field) => !field)) {
    alert('Please fill all required fields in Vehicle and Renter Details.');
    return;
  }
  if (formData.reportType === 'Accident' && !showAdditionalAccidentForm && requiredAccidentFields.some((field) => !field)) {
    alert('Please fill all required driver details.');
    return;
  }
  if (formData.reportType === 'Accident' && showAdditionalAccidentForm && requiredAdditionalAccidentFields.some((field) => !field)) {
    alert('Please fill all required additional accident details.');
    return;
  }
  if ((formData.reportType === 'Breakdown' || formData.reportType === 'Damage') && requiredBreakdownFields.some((field) => !field)) {
    alert('Please upload all required images.');
    return;
  }

  console.log('Incident Report submitted:', { formData, accidentFields, additionalAccidentFields, breakdownFields });
  navigate('/reception/reception-incidentReporting');
};