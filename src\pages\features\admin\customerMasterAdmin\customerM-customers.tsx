import React, { useState } from 'react';
import { Search, ChevronDown, Edit, Eye, RotateCcw } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';

interface Booking {
  name: string;
  phone: string;
  type: string;
  license: string;
  expireDate: string;
  reservationCount: string;
  status: 'Active' | 'Blacklist';
}

const bookingsData: Booking[] = [
  {
    name: '<PERSON>',
    phone: '0421 555 980',
    type: 'cooperate',
    license: '1234590',
    expireDate: '2025-07-10',
    reservationCount: '14',
    status: 'Active'
  },
  {
    name: '<PERSON>',
    phone: '0421 234 900',
    type: 'Normal',
    license: '1334500',
    expireDate: '2025-06-30',
    reservationCount: '5',
    status: 'Blacklist'
  },
  {
    name: '<PERSON>',
    phone: '0421 654 782',
    type: 'cooperate',
    license: '1224008',
    expireDate: '2026-07-10',
    reservationCount: '2',
    status: 'Active'
  },
  {
    name: 'Kumara Alwis',
    phone: '0421 560 111',
    type: 'cooperate',
    license: '2100332',
    expireDate: '2026-09-10',
    reservationCount: '11',
    status: 'Active'
  },
];

export function CustomerMCustomers() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const navigate = useNavigate();

  const handleCustomerNameClick = (name: string) => {
    // Encode the name to make it URL-safe (e.g., "Rose Fernando" -> "Rose%20Fernando")
    const encodedName = encodeURIComponent(name);
    navigate(`/admin/customerMasterAdmin/customerM-bookingSummery/${encodedName}`);
  };

  const filteredBookings = bookingsData.filter(booking => {
    const matchesSearch = booking.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.phone.includes(searchTerm);
    const matchesFilter = filterStatus === 'All' || booking.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const totalRecords = filteredBookings.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentBookings = filteredBookings.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const getStatusBadge = (status: string): string => {
    switch (status) {
      case 'Active':
        return 'bg-green-500 text-white px-2 xs:px-3 py-1 rounded text-xs font-medium';
      case 'Blacklist':
        return 'bg-black text-white px-2 xs:px-3 py-1 rounded text-xs font-medium';
      default:
        return '';
    }
  };

  return (
    <div className="p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 bg-white min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 xs:mb-4 sm:mb-6 gap-2 xs:gap-3 sm:gap-4">
        <div className="flex items-center">
          <RotateCcw className="w-4 h-4 xs:w-5 xs:h-5 sm:w-6 sm:h-6 mr-1 xs:mr-2 sm:mr-3 text-earth-dark" />
          <h1 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-bold text-earth-dark">Customers</h1>
        </div>
        <Button
          onClick={() => navigate('/admin/customerMasterAdmin/customerM-addnewcustomer')}
          className="px-2 xs:px-3 sm:px-4 py-1 xs:py-2 bg-[#330101] text-white rounded transition-colors text-xs xs:text-sm sm:text-base w-full sm:w-auto"
        >
          Add New Customer
        </Button>
      </div>

      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-1 xs:gap-2 sm:gap-4 mb-3 xs:mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterStatus}
            onValueChange={(value) => setFilterStatus(value)}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none text-xs xs:text-sm sm:text-base">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Blacklist">Blacklist</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-2 xs:left-3 top-1/2 transform -translate-y-1/2 w-3 xs:w-4 h-3 xs:h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-8 xs:pl-10 pr-3 xs:pr-4 py-1 xs:py-2 rounded-md focus:border-transparent text-xs xs:text-sm sm:text-base"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Mobile Cards View */}
      <div className="block sm:hidden space-y-2 xs:space-y-3">
        {currentBookings.map((booking) => (
          <div key={booking.license} className="(bg-white border border-gray-200 rounded-lg p-3 xs:p-4 shadow-sm relative max-w-md mx-auto w-full">
            <div className="flex justify-end mb-3 xs:mb-4">
              <span className={getStatusBadge(booking.status)}>{booking.status}</span>
            </div>
            <div className="grid grid-cols-2 gap-x-4 xs:gap-x-6 gap-y-3 xs:gap-y-4 text-[10px] xs:text-xs">
              <div>
                <span className="text-gray-500 block">Name</span>
                <span 
                  className="text-blue-600 hover:underline cursor-pointer"
                  onClick={() => handleCustomerNameClick(booking.name)}
                >
                  {booking.name}
                </span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Phone</span>
                <span className="text-gray-900">{booking.phone}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Type</span>
                <span className="text-gray-900">{booking.type}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">License</span>
                <span className="text-gray-900">{booking.license}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Expiration</span>
                <span className="text-gray-900">{booking.expireDate}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Reservations</span>
                <span className="text-gray-900">{booking.reservationCount}</span>
              </div>
            </div>
            <div className="flex justify-end gap-1 xs:gap-2 mt-3 xs:mt-4">
              <Button
                onClick={() => navigate('/admin/customerMasterAdmin/customerM-document')}
                variant="ghost"
                className="text-gray-600 hover:text-gray-800 p-1 xs:p-2"
              >
                <Eye className="w-3 xs:w-4 h-3 xs:h-4" />
              </Button>
              <Button
                onClick={() => navigate('/admin/customerMasterAdmin/customerM-editcustomer')}
                variant="ghost"
                className="text-gray-600 hover:text-gray-800 p-1 xs:p-2"
              >
                <Edit className="w-3 xs:w-4 h-3 xs:h-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden sm:block">
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className='bg-gray-50 uppercase'>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4">Customer Name</TableHead>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4">Phone No</TableHead>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4">Customer Type</TableHead>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4">License No #</TableHead>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4 hidden md:table-cell">Expiration Date</TableHead>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4 hidden md:table-cell">Reservation Count</TableHead>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4 hidden md:table-cell">Status</TableHead>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4 hidden md:table-cell">Documents</TableHead>
                <TableHead className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentBookings.map((booking) => (
                <TableRow key={booking.license}>
                  <TableCell 
                    className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4 cursor-pointer text-blue-600 hover:underline"
                    onClick={() => handleCustomerNameClick(booking.name)}
                  >
                    {booking.name}
                  </TableCell>
                  <TableCell className="text-xs sm:text-sm md:text-xs px-2 sm:px-3 md:px-4">{booking.phone}</TableCell>
                  <TableCell className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4">{booking.type}</TableCell>
                  <TableCell className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4">{booking.license}</TableCell>
                  <TableCell className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4 hidden md:table-cell">{booking.expireDate}</TableCell>
                  <TableCell className="text-xs sm:text-sm md:text-sm px-2 sm:px-3 md:px-4 hidden md:table-cell">{booking.reservationCount}</TableCell>
                  <TableCell className="px-2 sm:px-3 md:px-4">
                    <span className={getStatusBadge(booking.status)}>
                      {booking.status}
                    </span>
                  </TableCell>
                  <TableCell className="px-2 sm:px-3 md:px-4">
                    <div className="flex items-center">
                      <Button
                        onClick={() => navigate('/admin/customerMasterAdmin/customerM-document')}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                      >
                        <Eye className="w-3 sm:w-4 h-3 sm:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                  <TableCell className="px-2 sm:px-3 md:px-4">
                    <div className="flex items-center">
                      <Button
                        onClick={() => navigate('/admin/customerMasterAdmin/customerM-editcustomer')}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                      >
                        <Edit className="w-3 sm:w-4 h-3 sm:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination - Only for desktop */}
        <div className="hidden sm:block mt-3 xs:mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="text-xs xs:text-sm sm:text-base"
          />
        </div>
      </div>
    </div>
  );
}