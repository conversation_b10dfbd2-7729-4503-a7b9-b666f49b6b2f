import React from 'react';
import { Label } from '@/components/ui/label';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { ServiceTypeSectionProps } from '../type/reception-type';

const ServiceTypeSection: React.FC<ServiceTypeSectionProps> = ({
  formData,
  changeServiceType,
  setChangeServiceType,
  handleInputChange,
}) => (
  <div className="pt-6 space-y-4">
    <Label className="text-base font-medium">Do You Want to Change Service Type?</Label>
    <RadioGroup
      value={changeServiceType}
      onValueChange={(value: 'yes' | 'no') => setChangeServiceType(value)}
      className="flex gap-6"
    >
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="yes" id="yes" />
        <Label htmlFor="yes">Yes</Label>
      </div>
      <div className="flex items-center space-x-2">
        <RadioGroupItem value="no" id="no" />
        <Label htmlFor="no">No</Label>
      </div>
    </RadioGroup>

    {changeServiceType === 'yes' && (
      <div className="mt-6 space-y-2">
        <Label htmlFor="serviceType" className="absolute bg-white px-1 text-xs text-gray-600">
          Service Type
        </Label>
        <Select
          value={formData.serviceType}
          onValueChange={(value) => handleInputChange('serviceType', value)}
        >
          <SelectTrigger className="bg-white w-1/3 focus:border-transparent">
            <SelectValue placeholder="Select service type" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="Breakdown Service">Breakdown Service</SelectItem>
            <SelectItem value="Regular Maintenance">Regular Maintenance</SelectItem>
            <SelectItem value="Emergency Repair">Emergency Repair</SelectItem>
            <SelectItem value="Inspection">Inspection</SelectItem>
          </SelectContent>
        </Select>
      </div>
    )}
  </div>
);

export default ServiceTypeSection;
