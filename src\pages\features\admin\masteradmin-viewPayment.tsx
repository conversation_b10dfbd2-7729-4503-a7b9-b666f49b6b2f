import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';

interface Payment {
  id: string;
  reservationID: string;
  customerName: string;
  paymentConfirmation: string;
  pickupLocation: string;
  paymentMethod: string;
  paymentDate: string;
  amount: string;
  reference: string;
  last4Digits: string;
  paymentType: string;
  pickupDate: string;
  returnDate: string;
  vehicleClass: string;
  totalPrice: string;
  totalRevenue: string;
  totalPaid: string;
  totalRefunded: string;
  outstandingBalance: string;
  status: string;
  branch: string;
  walkIn: string;
  loanCustomer: string;
  vehicle: string;
}

const paymentData: Payment = {
  id: '57002',
  reservationID: '27689',
  customerName: 'Ikram Rahman',
  paymentConfirmation: 'Rental Payment',
  pickupLocation: 'Somerton',
  paymentMethod: 'Bank Card',
  paymentDate: '01-08-2025',
  amount: 'AU$271.00',
  reference: 'Rental',
  last4Digits: '5207',
  paymentType: 'Approved',
  pickupDate: '01-08-2025 17:41',
  returnDate: '03-08-2025 08:00',
  vehicleClass: 'Minibus 12 Seats - Premium',
  totalPrice: 'AU$477.00',
  totalRevenue: 'AU$456.37',
  totalPaid: 'AU$477.00',
  totalRefunded: 'AU$0.00',
  outstandingBalance: 'AU$0.00',
  status: 'Rental',
  branch: 'Lion Car Rental',
  walkIn: 'No',
  loanCustomer: 'No',
  vehicle: 'Car',
};

export function MasterAdminViewPayment() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const cleanId = id || paymentData.id;
/* 
  if (!paymentData) {
    return (
      <div className="p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8">
        <h2 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold text-red-600">Payment not found</h2>
        <p className="text-xs xs:text-sm sm:text-base">Could not find a payment with ID: {cleanId}</p>
        <Button
          className="mt-3 xs:mt-4 sm:mt-5 bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base"
          onClick={() => navigate('/admin/masterAdminPayments')}
        >
          Go Back
        </Button>
      </div>
    );
  } */

  return (
    <div className="min-h-screen px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
      <div className="py-3 xs:py-4 sm:py-6">
        <div className="flex justify-between items-center mb-4 xs:mb-5 sm:mb-6">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base flex items-center"
            size="sm"
            onClick={() => navigate('/admin/masteradmin-Payments')}
          >
            <ArrowLeft className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
            <span className="hidden sm:inline">Go Back</span>
          </Button>
        </div>
        <div className="flex items-center gap-1 xs:gap-2 sm:gap-3 mb-4 xs:mb-5 sm:mb-6 mt-3 xs:mt-4 sm:mt-5">
          <h1 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-semibold">View Payment - {paymentData.id}</h1>
        </div>

        <div className="space-y-3 xs:space-y-4 sm:space-y-5">
          <h3 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 mt-3 xs:mt-4 sm:mt-5 pb-1 xs:pb-2">Payment Information</h3>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="id" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">ID</Label>
              <Input
                value={paymentData.id}
                readOnly
                placeholder="ID"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="reservationID" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Reservation</Label>
              <Input
                value={paymentData.reservationID}
                readOnly
                placeholder="Reservation"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="customerName" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Customer</Label>
              <Input
                value={paymentData.customerName}
                readOnly
                placeholder="Customer"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="paymentConfirmation" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Payment Confirmation</Label>
              <Input
                value={paymentData.paymentConfirmation}
                readOnly
                placeholder="Payment Confirmation"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="pickupLocation" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Pickup Location</Label>
              <Input
                value={paymentData.pickupLocation}
                readOnly
                placeholder="Pickup Location"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="paymentMethod" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Payment Method</Label>
              <Input
                value={paymentData.paymentMethod}
                readOnly
                placeholder="Payment Method"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="paymentDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Payment Date</Label>
              <Input
                value={paymentData.paymentDate}
                readOnly
                placeholder="Payment Date"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="amount" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Amount</Label>
              <Input
                value={paymentData.amount}
                readOnly
                placeholder="Amount"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="reference" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Reference</Label>
              <Input
                value={paymentData.reference}
                readOnly
                placeholder="Reference"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="last4Digits" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Last 4 Digits</Label>
              <Input
                value={paymentData.last4Digits}
                readOnly
                placeholder="Last 4 Digits"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="paymentType" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Payment Type</Label>
              <Input
                value={paymentData.paymentType}
                readOnly
                placeholder="Payment Type"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="pickupDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Pickup Date</Label>
              <Input
                value={paymentData.pickupDate}
                readOnly
                placeholder="Pickup Date"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="returnDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Return Date</Label>
              <Input
                value={paymentData.returnDate}
                readOnly
                placeholder="Return Date"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="vehicleClass" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Vehicle Class</Label>
              <Input
                value={paymentData.vehicleClass}
                readOnly
                placeholder="Vehicle Class"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="totalPrice" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Total Price</Label>
              <Input
                value={paymentData.totalPrice}
                readOnly
                placeholder="Total Price"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="totalRevenue" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Total Revenue</Label>
              <Input
                value={paymentData.totalRevenue}
                readOnly
                placeholder="Total Revenue"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="totalPaid" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Total Paid</Label>
              <Input
                value={paymentData.totalPaid}
                readOnly
                placeholder="Total Paid"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="totalRefunded" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Total Refunded</Label>
              <Input
                value={paymentData.totalRefunded}
                readOnly
                placeholder="Total Refunded"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="outstandingBalance" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Outstanding Balance</Label>
              <Input
                value={paymentData.outstandingBalance}
                readOnly
                placeholder="Outstanding Balance"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="status" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Status</Label>
              <Input
                value={paymentData.status}
                readOnly
                placeholder="Status"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="branch" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Branch</Label>
              <Input
                value={paymentData.branch}
                readOnly
                placeholder="Branch"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="walkIn" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Walk In</Label>
              <Input
                value={paymentData.walkIn}
                readOnly
                placeholder="Walk In"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="loanCustomer" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Loan Customer</Label>
              <Input
                value={paymentData.loanCustomer}
                readOnly
                placeholder="Loan Customer"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
            <div className="relative">
              <Label htmlFor="vehicle" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Vehicle</Label>
              <Input
                value={paymentData.vehicle}
                readOnly
                placeholder="Vehicle"
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
              />
            </div>
          </div>

        </div>
      </div>
    </div>
  );
}