import { useState, useMemo } from "react";
import { AccidentService } from "../type/mechanictype"; // Reuse AccidentService type

const MOCK_DATA: AccidentService[] = [
  {
    vehicleId: "VID0001",
    vehicle: "Isuzu - DMS 101",
    insuranceClaimNumber: "D-123456",
    estimatedEndDate: "25/07/2025",
    actualEndDate: "26/07/2025",
    insuranceStatus: "Initial Review",
    status: "Pending",
    mechanicAssigned: "Alex Turner",
  },
  {
    vehicleId: "VID0002",
    vehicle: "Toyota - DMS 202",
    insuranceClaimNumber: "D-654321",
    estimatedEndDate: "15/06/2025",
    actualEndDate: "16/06/2025",
    insuranceStatus: "Accepted",
    status: "InProgress",
    mechanicAssigned: "<PERSON>",
  },
  {
    vehicleId: "VID0003",
    vehicle: "Ford - DMS 303",
    insuranceClaimNumber: "D-789012",
    estimatedEndDate: "10/06/2025",
    actualEndDate: "11/06/2025",
    insuranceStatus: "Declined",
    status: "Done",
    mechanicAssigned: "<PERSON>",
  },
];

export function useDamageService() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const filteredData = useMemo(() => {
    let data = MOCK_DATA;

    if (filterStatus !== "All") {
      data = data.filter((item) => item.status === filterStatus);
    }

    if (searchTerm.trim()) {
      data = data.filter(
        (item) =>
          item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.insuranceClaimNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return data;
  }, [searchTerm, filterStatus]);

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);

  const currentData = useMemo(() => {
    const start = (currentPage - 1) * recordsPerPage;
    return filteredData.slice(start, start + recordsPerPage);
  }, [filteredData, currentPage, recordsPerPage]);

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    currentData,
    totalPages,
    totalEntries,
  };
}