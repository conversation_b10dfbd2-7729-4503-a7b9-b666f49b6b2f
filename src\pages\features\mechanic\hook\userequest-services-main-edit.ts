import { useState, useEffect } from "react";
import { RequestServiceMain } from "../type/mechanictype";

const SERVICES_KEY = "mock_request_services_main";

function getServices(): RequestServiceMain[] {
  const data = localStorage.getItem(SERVICES_KEY);
  return data ? JSON.parse(data) : [];
}

function updateService(id: string, updated: Partial<RequestServiceMain>) {
  const services = getServices();
  const idx = services.findIndex((s) => s.serviceCode === id);
  if (idx !== -1) {
    services[idx] = { ...services[idx], ...updated };
    localStorage.setItem(SERVICES_KEY, JSON.stringify(services));
  }
}

export function useRequestServicesMainEdit({
  navigate,
  id,
}: {
  navigate: (path: string) => void;
  id?: string;
}) {
  const [serviceCode, setServiceCode] = useState("");
  const [serviceName, setServiceName] = useState("");
  const [description, setDescription] = useState("");

  useEffect(() => {
    const services = getServices();
    const service = services.find((s) => s.serviceCode === id);
    if (service) {
      setServiceCode(service.serviceCode);
      setServiceName(service.serviceName);
      setDescription(service.description);
    }
  }, [id]);

  const handleUpdate = () => {
    if (id) {
      updateService(id, { serviceName, description });
    }
    navigate("/mechanic/request-services-main");
  };

  const handleCancel = () => {
    navigate("/mechanic/request-services-main");
  };

  return {
    serviceCode,
    serviceName,
    setServiceName,
    description,
    setDescription,
    handleUpdate,
    handleCancel,
  };
}