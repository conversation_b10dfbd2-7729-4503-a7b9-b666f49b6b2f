import React from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useBreakdownServiceEdit } from "./hook/usebreakdown-service-edit";

export function BreakdownServiceEditPage() {
  const navigate = useNavigate();
  const { formData, handleInputChange } = useBreakdownServiceEdit();

  // Status badge helpers
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Pending":
        return "bg-gray-400 text-white";
      case "In Progress":
        return "bg-blue-500 text-white";
      case "Done":
        return "bg-green-500 text-white";
      default:
        return "bg-gray-200 text-gray-700";
    }
  };

  return (
    <div className="min-h-screen p-4 md:p-6">
      {/* Back Button - Desktop only */}
      <div className="hidden md:flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <Button
          className="w-full sm:w-auto px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center"
          size="sm"
          onClick={() => navigate('/mechanic/breakdown-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>

      {/* Mobile/Tablet Card View */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/mechanic/breakdown-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
        {/* Vehicle Details */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <h2 className="text-xl font-semibold mb-2">Vehicle Details</h2>
          <div className="space-y-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle ID</Label>
              <Input
                value={formData.vehicleId}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</Label>
              <Input
                value={formData.model}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Reg No</Label>
              <Input
                value={formData.regNo}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Incident Date</Label>
              <Input
                value={formData.incidentDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</Label>
              <Input
                value={formData.description}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        {/* Workshop Details */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <div className="flex justify-between items-start mb-2">
            <h2 className="text-xl font-semibold">Workshop Details</h2>
            <span className={`px-3 py-1 rounded text-sm font-medium w-28 flex justify-center items-center ${getStatusBadge(formData.status)}`}>
              {formData.status}
            </span>
          </div>
          <div className="space-y-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Branch</Label>
              <Input
                value={formData.branch}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Mechanic Assigned</Label>
              <Input
                value={formData.mechanicAssigned.join(", ")}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Estimated End Date</Label>
              <Input
                type="date"
                value={formData.estimatedEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Actual End Date</Label>
              <Input
                type="date"
                value={formData.actualEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
              <Input
                value={formData.status}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input
                value={formData.comment}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        {/* Mechanic Note */}
        <div className="bg-white rounded-lg shadow-sm p-4 mb-6">
          <h2 className="text-xl font-semibold mb-4">Mechanic Note</h2>
          <div className="space-y-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-700">Status</Label>
              <Select
                value={formData.mechanicNoteStatus}
                onValueChange={value => handleInputChange("mechanicNoteStatus", value)}
              >
                <SelectTrigger className="w-full p-2 border rounded focus:border-transparent text-sm mt-2">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Done">Done</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input value={formData.mechanicNoteComment} onChange={e => handleInputChange("mechanicNoteComment", e.target.value)} />
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex flex-row gap-4 mt-6">
          <Button className="bg-[#330101] text-white w-full" onClick={() => {/* handle update logic here */}}>Update</Button>
          <Button className="w-full" variant="outline" onClick={() => navigate("/mechanic/breakdown-service")}>Cancel</Button>
        </div>
      </div>

      {/* Desktop View (md and up) */}
      <div className="hidden md:block">
        <div className="bg-white rounded-lg shadow-sm p-4">
          {/* Vehicle Details */}
          <h2 className="text-xl font-semibold mb-4">Vehicle Details</h2>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle ID</Label>
              <Input
                value={formData.vehicleId}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</Label>
              <Input
                value={formData.model}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Reg No</Label>
              <Input
                value={formData.regNo}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>
          <div className="relative mb-4">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Incident Date</Label>
            <Input
              value={formData.incidentDate}
              readOnly
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
          </div>
          <div className="relative mb-4">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</Label>
            <Input
              value={formData.description}
              readOnly
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
          </div>

          {/* Workshop Details */}
          <div className="flex justify-between items-start mb-4 mt-6">
            <h2 className="text-xl font-semibold">Workshop Details</h2>
            
          </div>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Branch</Label>
              <Input
                value={formData.branch}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Mechanic Assigned</Label>
              <Input
                value={formData.mechanicAssigned.join(", ")}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Estimated End Date</Label>
              <Input
                type="date"
                value={formData.estimatedEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Actual End Date</Label>
              <Input
                type="date"
                value={formData.actualEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
              <Input
                value={formData.status}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="col-span-2 relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input
                value={formData.comment}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>

          {/* Mechanic Note */}
          <h2 className="text-xl font-semibold mb-4 mt-6">Mechanic Note</h2>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-700">Status</Label>
              <Select
                value={formData.mechanicNoteStatus}
                onValueChange={value => handleInputChange("mechanicNoteStatus", value)}
              >
                <SelectTrigger className="w-full p-2 border rounded focus:border-transparent text-sm mt-2">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Done">Done</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="col-span-2 relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input value={formData.mechanicNoteComment} onChange={e => handleInputChange("mechanicNoteComment", e.target.value)} />
            </div>
          </div>

          <div className="flex gap-4 mt-6">
            <Button className="bg-[#330101] text-white" onClick={() => {/* handle update logic here */}}>Update</Button>
            <Button variant="outline" onClick={() => navigate("/mechanic/breakdown-service")}>Cancel</Button>
          </div>
        </div>
      </div>
    </div>
  );
}