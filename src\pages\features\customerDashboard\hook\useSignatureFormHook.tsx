import { useState } from 'react';
import { signatureDefaultData } from '../common/mockdata';

export const useSignatureFormHook = () => {
  const [formData, setFormData] = useState({
    completedBy: signatureDefaultData.defaultCompletedBy,
    submissionDate: signatureDefaultData.defaultDate,
    submissionTime: signatureDefaultData.defaultTime,
  });

  const [showPopup, setShowPopup] = useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value,
    }));
  };

  const handleGoBack = () => {
    window.history.back();
  };

  const handleSubmit = () => {
    setShowPopup(true);
  };

  const handleClosePopup = () => {
    setShowPopup(false);
    setIsTermsAccepted(false);
  };

  const handleAcceptTerms = () => {
    if (isTermsAccepted) {
      console.log('Form submitted:', formData);
      alert('Accident report submitted successfully!');
      setShowPopup(false);
      window.location.href = '/customer/incident-reporting';
    }
  };

  return {
    formData,
    setFormData,
    showPopup,
    isTermsAccepted,
    setShowPopup,
    setIsTermsAccepted,
    handleInputChange,
    handleGoBack,
    handleSubmit,
    handleClosePopup,
    handleAcceptTerms,
  };
};