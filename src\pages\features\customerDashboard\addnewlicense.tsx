import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Camera, Calendar as CalendarIcon, ChevronRight, ChevronDown, Info, ArrowLeft } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';
import { useAddNewLicense } from './hook/useAddNewLicense';
import { mockNewLicense } from './common/mockdata';
import { AddNewLicenseProps } from './type/customer-type';

export const Addnewlicense: React.FC<AddNewLicenseProps> = () => {
  const navigate = useNavigate();
  const {
    permitNumber,
    setPermitNumber,
    issueCountry,
    setIssueCountry,
    issueDate,
    setIssueDate,
    expiryDate,
    setExpiryDate,
    licenseType,
    setLicenseType,
    frontView,
    setFrontView,
    backView,
    setBackView,
    showIssueCalendar,
    setShowIssueCalendar,
    showExpiryCalendar,
    setShowExpiryCalendar,
    handleFrontViewChange,
    handleBackViewChange,
    handleSubmit,
    handleBack,
  } = useAddNewLicense(mockNewLicense);

  const handleIssueDateClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowIssueCalendar(!showIssueCalendar);
  };

  const handleExpiryDateClick = (e: React.MouseEvent) => {
    e.preventDefault();
    setShowExpiryCalendar(!showExpiryCalendar);
  };

  return (
    <div className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8 p-2 xs:p-4 sm:p-6">
      <Button variant="outline" className="bg-[#330101] text-gray-100 px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base rounded" onClick={() => navigate('/customer/editProfile')}>
        <ArrowLeft className="mr-1 xs:mr-2 h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5 text-gray-100" /> Go Back
      </Button>

      <div className="flex items-center p-2 xs:p-3 sm:p-4 bg-[#F9F3E7] rounded-md">
        <Info className="mr-1 xs:mr-2 h-4 xs:h-5 sm:h-6 w-4 xs:w-5 sm:w-6 text-gray-600" />
        <p className="text-gray-800 text-[10px] xs:text-xs sm:text-sm md:text-base">If the License number you previously added has expired, you must add a New License number.
                                        Please add New License Number and details here and send the request to Reception.</p>
      </div>

      <div className="space-y-3 xs:space-y-4 sm:space-y-5">
        <h2 className="text-base xs:text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold text-gray-800">Driver's License</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Input
              type="text"
              id="permitNumber"
              value={permitNumber}
              onChange={(e) => setPermitNumber(e.target.value)}
              className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
              placeholder="Enter DL Number"
              disabled
              required
            />
            <label htmlFor="permitNumber" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
              DL Number*
            </label>
          </div>
          <div className="relative">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D]">
                  {issueCountry}
                  <ChevronRight className="ml-1 xs:ml-2 h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent>
                <DropdownMenuRadioGroup value={issueCountry} onValueChange={setIssueCountry}>
                  <DropdownMenuRadioItem value="Australia">Australia</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="USA">USA</DropdownMenuRadioItem>
                  <DropdownMenuRadioItem value="UK">UK</DropdownMenuRadioItem>
                </DropdownMenuRadioGroup>
              </DropdownMenuContent>
            </DropdownMenu>
            <label htmlFor="issueCountry" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
              Issue Country*
            </label>
          </div>
          <div className="relative">
            <Input
              type="text"
              id="issueDate"
              value={issueDate ? issueDate.toLocaleDateString() : ''}
              readOnly
              className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
              required
              onClick={handleIssueDateClick}
            />
            <label htmlFor="issueDate" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
              Issue Date*
            </label>
            <CalendarIcon
              className="absolute right-1 xs:right-2 top-1 xs:top-2 h-4 xs:h-5 sm:h-6 w-4 xs:w-5 sm:w-6 text-gray-600 cursor-pointer"
              onClick={handleIssueDateClick}
            />
            {showIssueCalendar && (
              <div className="mt-1 xs:mt-2">
                <Calendar
                  mode="single"
                  selected={issueDate}
                  onSelect={(date) => {
                    setIssueDate(date);
                    setShowIssueCalendar(false);
                  }}
                  onClickOutside={() => setShowIssueCalendar(false)}
                  className="border rounded-md p-1 xs:p-2"
                  initialFocus
                />
              </div>
            )}
          </div>
          <div className="relative">
            <Input
              type="text"
              id="expiryDate"
              value={expiryDate ? expiryDate.toLocaleDateString() : ''}
              readOnly
              className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
              required
              onClick={handleExpiryDateClick}
            />
            <label htmlFor="expiryDate" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
              Expiry Date*
            </label>
            <CalendarIcon
              className="absolute right-1 xs:right-2 top-1 xs:top-2 h-4 xs:h-5 sm:h-6 w-4 xs:w-5 sm:w-6 text-gray-600 cursor-pointer"
              onClick={handleExpiryDateClick}
            />
            {showExpiryCalendar && (
              <div className="mt-1 xs:mt-2">
                <Calendar
                  mode="single"
                  selected={expiryDate}
                  onSelect={(date) => {
                    setExpiryDate(date);
                    setShowExpiryCalendar(false);
                  }}
                  onClickOutside={() => setShowExpiryCalendar(false)}
                  className="border rounded-md p-1 xs:p-2"
                  initialFocus
                />
              </div>
            )}
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Input
              type="text"
              id="licenseType"
              value={licenseType}
              className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
              disabled
              required
            />
            <label
              htmlFor="licenseType"
              className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500"
            >
              Condition*
            </label>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="mt-2 xs:mt-3 sm:mt-4 relative">
            <label htmlFor="frontView" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
              Upload Front View*
            </label>
            <input
              type="file"
              id="frontView"
              accept="image/*"
              onChange={handleFrontViewChange}
              className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-gray-900 text-[10px] xs:text-xs sm:text-sm md:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
            />
            {frontView && <p className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600 mt-0.5 xs:mt-1">{frontView.name}</p>}
          </div>
          <div className="mt-2 xs:mt-3 sm:mt-4 relative">
            <label htmlFor="backView" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
              Upload Back View*
            </label>
            <input
              type="file"
              id="backView"
              accept="image/*"
              onChange={handleBackViewChange}
              className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-gray-900 text-[10px] xs:text-xs sm:text-sm md:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
            />
            {backView && <p className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600 mt-0.5 xs:mt-1">{backView.name}</p>}
          </div>
        </div>
      </div>

      <div className="mt-6 flex gap-4 justify-end">
        <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded" onClick={() => navigate('/customer/editProfile')}>
          Cancle
        </Button>
        <Button variant="default" className="bg-[#330101] text-white px-4 py-2 rounded" onClick={() => navigate('/customer/profile')}>
          Submit
        </Button>
      </div>
    </div>
  );
};