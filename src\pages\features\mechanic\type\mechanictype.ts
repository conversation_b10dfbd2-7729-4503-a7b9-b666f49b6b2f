export interface JobCardData {
  id: string;
  registration: string;
  make: string;
  model: string;
  modelSeries: string;
  colour: string;
  prodDate: string;
  nextService: string;
  regoDue: string;
  engineNumber: string;
  vin: string;
  buildDate: string;
  litres: string;
  trans: string;
  air: string;
  cyl: string;
  body: string;
  odo: string;
  hours: string;
  repairedBy: string;
  drainRefillEngineOil?: boolean;
  engineOilLitres?: string;
  replaceOilFilter?: boolean;
  replaceFuelFilter?: boolean;
  airFilterChecked?: boolean;
  airFilterReplaced?: boolean;
  cabinFilterChecked?: boolean;
  cabinFilterReplaced?: boolean;
  checkAdjustTyrePressure?: boolean;
  checkTopUpAllFluids?: boolean;
  checkCleanAdjustBreak?: boolean;
  checkCleanAdjustBreak2?: boolean;
  checkWiperBlades?: boolean;
  checkLights?: boolean;
  sparkPlugsChecked?: boolean;
  sparkPlugsReplaced?: boolean;
  checkCorrectOperation?: boolean;
  tyreConditionRF?: string;
  tyreConditionLF?: string;
  tyreConditionRR?: string;
  tyreConditionLR?: string;
  breakConditionFront?: string;
  breakDiscsFront?: string;
  breakConditionRear?: string;
  breakDiscsRear?: string;
  checkGPS?: boolean;
  roadTest?: boolean;
  comments?: string;
}

export interface JobCardFormData {
  // Vehicle Details
  registration: string
  make: string
  model: string
  modelSeries: string
  colour: string
  prodDate: string
  nextService: string
  regoDue: string
  engineNumber: string
  vin: string
  buildDate: string
  trans: string
  air: string
  cyl: string
  body: string
  odo: string
  hours: string
  repairedBy: string
  
  // Job Card Notes
  drainRefillEngineOil: boolean
  engineOilLitres: string
  replaceOilFilter: boolean
  replaceFuelFilter: boolean
  
  // Air Filter
  airFilterChecked: boolean
  airFilterReplaced: boolean
  
  // Cabin Filter
  cabinFilterChecked: boolean
  cabinFilterReplaced: boolean
  
  // Service items
  checkAdjustTyrePressure: boolean
  checkTopUpAllFluids: boolean
  checkCleanAdjustBreak: boolean
  checkCleanAdjustBreak2: boolean
  checkWiperBlades: boolean
  checkLights: boolean
  
  // Spark Plugs
  sparkPlugsChecked: boolean
  sparkPlugsReplaced: boolean
  
  checkCorrectOperation: boolean
  
  // Tyre Conditions
  tyreConditionRF: string
  tyreConditionLF: string
  tyreConditionRR: string
  tyreConditionLR: string

  // Brake Conditions
  breakConditionFront: string
  breakConditionRear: string
  breakDiscsRear: string
  breakDiscsFront: string
  
  checkGPS: boolean
  roadTest: boolean
  
  
  comments: string
}

export interface CustomerInfo {
  name: string;
  address: string;
  city: string;
  phone: string;
  email: string;
}

export interface JobCardFilters {
  status: string
  searchTerm: string
}

export interface JobCardEditData {
  id: string
  registration: string
  // All other fields from JobCardFormData for editing
  drainRefillEngineOil: boolean
  engineOilLitres: string
  replaceOilFilter: boolean
  replaceFuelFilter: boolean
  airFilterChecked: boolean
  airFilterReplaced: boolean
  cabinFilterChecked: boolean
  cabinFilterReplaced: boolean
  checkAdjustTyrePressure: boolean
  checkTopUpAllFluids: boolean
  checkCleanAdjustBreak: boolean
  checkCleanAdjustBreak2: boolean
  checkWiperBlades: boolean
  checkLights: boolean
  sparkPlugsChecked: boolean
  sparkPlugsReplaced: boolean
  checkCorrectOperation: boolean
  tyreConditionRF: string
  tyreConditionLF: string
  tyreConditionRR: string
  tyreConditionLR: string
  breakConditionFront: string
  breakConditionRear: string
  breakDiscsRear: string
  breakDiscsFront: string
  checkGPS: boolean
  roadTest: boolean
  comments: string
  status: string
}

export interface RequestPart {
  partNumber: string;
  partName: string;
  quantity: number;
  comment?: string;
}

export interface RequestPartMain {
  partNumber: string;
  partName: string;
  quantity: string;
  description: string;
}

export interface RequestService {
  serviceCode: string;
  serviceName: string;
  description?: string;
}

export interface RequestServiceMain {
  serviceCode: string;
  serviceName: string;
  description: string;
}

export interface JobCardAccidentRepairFormData {
  vehicleClass: string;
  rego: string;
  accidentDate: string;
  repairedBy: string;
  repairCompletionDate: string;
  comments?: string;
}

export interface JobCardAccidentRepairData {
  id: string;
  rego: string;
  vehicle: string;
  damageDescription: string;
  accidentDate: string;
  damageParts?: string;
  repairTasks?: string;
  repairedBy: string;
  repairCompletionDate: string;
  
}

export interface JobCardBreakdownServiceData {
  id: string;
  vehicleClass: string;
  rego: string;
  breakdownDate: string;
  repairedBy: string;
  repairCompletionDate: string;
  status: string;
}

export interface JobCardBreakdownServiceAddForm {
  rego: string;
  vehicleClass: string;
  description?: string;
  breakdownDate: string;
  partsReplaced?: string;
  repairTasksRequired?: string;
  repairedBy: string;
  repairCompletionDate: string;
  notes: {
    initialInspection: boolean;
    batteryBoost: boolean;
    towing: string;
    engineFault: boolean;
    engineRepair: boolean;
    checkRadiator: boolean;
    checkGPS: boolean;
    roadTest: boolean;
  };
}

export interface JobHistoryRow {
  rego: string;
  serviceType: string;
  serviceDate: string;
  status: string;
}

export interface Notification {
  type: string;
  message: string;
}

export interface ActivityLogEntry {
  type: string;
  message: string;
}

export interface ServiceManagementAll {
  vehicleId: string;
  vehicle: string;
  serviceTypes: string;
  EstimatedEndDate: string;
  ActualEndDate: string;
  status: 'Pending' | 'InProgress' | 'Done';
  vehicleStatus: string;
  dateIn: string;
  dateOut: string;
}

export interface AccidentService {
  vehicleId: string;
  vehicle: string;
  insuranceClaimNumber: string;
  estimatedEndDate: string;
  actualEndDate: string;
  insuranceStatus: "Initial Review" | "Accepted" | "Declined";
  status: "Pending" | "InProgress" | "Done";
  mechanicAssigned: string;
}

export interface BreakdownService {
  vehicleId: string; 
  vehicle: string;
  incidentDate: string;
  actualDate: string;
  estimatedDate: string;
  mechanicAssigned: string;
  status: "Pending" | "InProgress" | "Done";
}

export interface ServiceManagementAllViewData {
  vehicleId: string;
  vehicleModel: string;
  regoNumber: string;
  serviceType: string;
  totalParts: string;
  totalLabor: string;
  damages: string;
  notes: string;
  dateIn: string;
  dateOut: string;
  timeOut: string;
  timeIn: string;
  comments: string;
  reservation: string;
  fuelIn: string;
  fuelOut: string; 
  odometerIn: string;
  odometerOut: string;
  status: string;
  vehicleStatus: string;
}

export interface MaintainanceServiceEditData {
  vehicleId: string;
  model: string;
  regNo: string;
  maintenanceType: string;
  maintenanceTypeInterval: string;
  odometerAtDue: string;
  currentOdometer: string;
  vehicleCurrentRenter: string;
  vehicleCurrentLocation: string;
  description: string;
  branch: string;
  mechanicAssigned: string[];
  estimatedEndDate: string;
  actualEndDate: string;
  status: string;
  vehicleStatus: string;
  comment: string;
  mechanicNoteStatus: string;
  mechanicNoteComment: string;
}

export interface MechanicAccidentServiceData {
  vehicleId: string; 
  model: string;
  regNo: string;
  description: string;
  insuranceStatus: string;
  insuranceClaimNumber: string;
  estimatedEndDate: string;
  actualEndDate: string;
  status: string;
  images: {
    interiorImages: string[];
    exteriorImages: string[];
    leftSideDoors: string[];
    rightSideDoors: string[];
    frontSideImages: string[];
    backSideImages: string[];
    sideMirrors: string[];
    other: string[];
  };
  branch: string;
  mechanicAssigned: string[];
  comment: string;
  mechanicNoteStatus: string;
  mechanicNoteComment: string;
}

export interface MechanicBreakdownServiceData {
  vehicleId: string; 
  model: string;
  regNo: string;
  incidentDate: string;
  description: string;
  estimatedEndDate: string;
  actualEndDate: string;
  status: string;
  branch: string;
  mechanicAssigned: string[];
  comment: string;
  mechanicNoteStatus: string;
  mechanicNoteComment: string;
}
