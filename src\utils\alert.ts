// alert.ts
import { toast } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';

const alert = {
  success(message?: string) {
    toast.success(message || 'Success!', {
      toastId: 'success-toast',
    });
  },

  warn(message?: string) {
    toast.warn(message || 'Warning!', {
      toastId: 'warn-toast',
    });
  },

  error(message?: string) {
    toast.error(message || 'Error!', {
      toastId: 'error-toast',
    });
  },

  info(message?: string) {
    toast.info(message || 'Info!', {
      toastId: 'info-toast',
    });
  },

  clear() {
    toast.dismiss();
  },
};

export default alert;