import { useParams, useNavigate } from 'react-router-dom';
import { mockBookingData, mockInvoiceItems } from '../common/mockdata';

export const useBookingSummary = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const navigate = useNavigate();

  const getBookingById = (id) => {
    return {
      id: id,
      outstandingBalance: 594.00,
      pickupDate: '2024-01-15',
      pickupTime: '10:00 AM',
      returnDate: '2024-01-16',
      returnTime: '10:00 AM',
      vehicle: 'Toyota Camry',
      totalPrice: 532.52,
      loanVehicle: 'Yes',
    };
  };

  const booking = getBookingById(bookingId || '1');

  const handlePrintAgreement = () => {
    window.open(`/customer/booking-summary/agreement/${bookingId}`, '_blank');
  };

  const handleEmailAgreement = () => {
    alert('Agreement will be sent to the customer email address.');
  };

  const handlePrintReceipt = () => {
    window.open(`/customer/booking-summary/receipt/${bookingId}`, '_blank');
  };

  const handleEmailReceipt = () => {
    alert('Receipt will be sent to the customer email address.');
  };

  const bookingData = {
    id: booking.id,
    outstandingBalance: `AUD${booking.outstandingBalance.toFixed(2)}`,
    pickupDate: booking.pickupDate,
    pickupTime: booking.pickupTime,
    returnDate: booking.returnDate,
    returnTime: booking.returnTime,
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    branch: 'Lion Car Rental',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Eco Plus Car',
      features: ['Automatic Transmission', 'Air-conditioning', 'Fuel: 92 Petrol', 'Power Steering'],
      doors: '5 Doors',
      vehicleId: booking.vehicle,
      price: `AUD ${(booking.totalPrice / 14).toFixed(2)}`,
      priceNote: `AUD ${(booking.totalPrice / 14).toFixed(2)} (Incl GST) Daily excluding GST 10%`,
    },
    protections: [
      { name: 'Bond Compulsory - 500 (Mandatory)', price: 'AUD 500.00 One Time' },
      { name: 'Insurance-15', price: 'Yes AUD 15.00 One Time' },
    ],
    equipment: [
      { name: 'Child Seat', price: 'Yes AUD$40.00/Day' },
    ],
    customer: {
      type: 'Cooperate Customer',
      firstName: 'Pradeep',
      lastName: 'Testing',
      email: '<EMAIL>',
      phone: '0411 111 111',
      address: '2185 Hume Hwy',
      postcode: '2091',
      country: 'Australia',
      birthday: '02/01/1960',
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '12345678',
      issueDate: '20/04/2024',
      expireDate: '20/04/2028',
      country: 'Australia',
    },
    emergency: {
      name: 'Rose Perera',
      number: '0412 456 789',
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: 'AUD 38.18', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: 'AUD 500.00', days: '1 Day' },
      insurance: { description: 'Insurance Plus', amount: 'AUD 13.64', days: '1 Day' },
      childSeat: { description: 'Child Seat', amount: 'AUD 36.36', days: '1 Day' },
      subtotal: '588.18',
      gst: '8.82',
      discount: '3.00',
      total: '594.00',
      securityDeposit: '500.00',
      amountDue: '594.00',
    },
  };

  return {
    bookingId,
    navigate,
    booking,
    invoiceItems: mockInvoiceItems,
    handlePrintAgreement,
    handleEmailAgreement,
    handlePrintReceipt,
    handleEmailReceipt,
    bookingData,
  };
};