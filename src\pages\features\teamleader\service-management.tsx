import React, { useState } from 'react';
import { Search, AlertTriangle, Eye } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import { managementData } from './common/mockData';
import {
  handleVehicleIdClick,
  handleEditVehicleIdClick,
  getStatusColor,
} from './hook/useservice-management';

export function ServiceManagementPage() {
    const [searchTerm, setSearchTerm] = useState('');
    const [filterStatus, setFilterStatus] = useState('All');
    const [activeTab, setActiveTab] = useState('All');
    const [recordsPerPage, setRecordsPerPage] = useState(10);
    const [currentPage, setCurrentPage] = useState(1);

  // Get current date and calculate this week's range (Monday to Sunday)
  const today = new Date('2025-07-29T11:25:00+0530'); 
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); 
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6); 

  // Filter data based on search term, filter status, and due this week
  const filteredData = managementData.filter((item) =>
    (item.vehicleId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.status.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || item.status === filterStatus) &&
    (activeTab === 'Due This Week'
      ? (new Date(item.dateIn) >= startOfWeek && new Date(item.dateIn) <= endOfWeek) ||
        (new Date(item.dateOut) >= startOfWeek && new Date(item.dateOut) <= endOfWeek)
      : true)
  );

  // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  const navigate = useNavigate();

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Service Management</h1>
        </div>
      </div>

      {/* Tab Bar */}
      <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
              {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
                <Button
                  key={tab}
                  variant={activeTab === tab ? 'default' : 'outline'}
                  onClick={() => {
                    if (tab === 'Accident') {
                      navigate('/teamleader/service-accident');
                    } else {
                      setActiveTab(tab);
                    }
                    if (tab === 'General Maintenance') {
                      navigate('/teamleader/maintenance');
                    } else {
                      setActiveTab(tab);
                    }
                    if (tab === 'Breakdowns') {
                      navigate('/teamleader/service-breakdown');
                    } else {
                      setActiveTab(tab);
                    }
                    if (tab === 'Damage') {
                      navigate('/teamleader/service-damage');
                    } else {
                      setActiveTab(tab);
                    }
                  }}
                  className="text-sm md:text-base"
                >
                  {tab}
                </Button>
              ))}
            </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="InProgress">InProgress</SelectItem>
              <SelectItem value="Done">Done</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Table View */}
      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>ID</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Service Types</TableHead>
              <TableHead>Total in Parts</TableHead>
              <TableHead>Total in Labor</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {filteredData.map((item) => (
              <TableRow key={item.vehicleId} className="hover:bg-gray-50 transition-colors">
                <TableCell>
                  <span
                    className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                    onClick={() => handleEditVehicleIdClick(item.vehicleId, navigate)}
                  >
                    {item.vehicleId}
                  </span>
                </TableCell>
                <TableCell>{item.vehicle}</TableCell>
                <TableCell>{item.serviceTypes}</TableCell>
                <TableCell>{item.totalInParts}</TableCell>
                <TableCell>{item.totalInLabor}</TableCell>
                <TableCell>
                  <span className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </TableCell>
                <TableCell className="px-3 py-4">
                  <Button
                    onClick={() => handleVehicleIdClick(item.vehicleId, navigate)}
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
        setRecordsPerPage(records);
        setCurrentPage(1);
        }}
          className="mt-6"
      />
    </div>
  );
}