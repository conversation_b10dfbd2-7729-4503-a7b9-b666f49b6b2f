import { NavigateFunction } from 'react-router-dom';
import { CustomerFormData, Booking } from '../type/reception-type';
import { bookingsData } from '../common/mockData';

export const handleSave = (
  navigate: NavigateFunction,
  formData: CustomerFormData,
  frontViewFile: File | null,
  backViewFile: File | null,
  isBlacklisted: boolean
): void => {
  const index = bookingsData.findIndex(
    (booking) => booking.id === formData.customerId || booking.id === `#${formData.customerId}`
  );

  if (index !== -1) {
    bookingsData[index] = {
      ...bookingsData[index],
      customerName: `${formData.firstName} ${formData.lastName}`,
      customerPhone: formData.phone,
      type: formData.companyName ? 'Cooperate' : 'Normal',
      license: formData.dlNumber,
      expireDate: formData.expiryDate,
      status: isBlacklisted ? 'Blacklist' : 'Active',
      frontLicenseImage: frontViewFile
        ? `/assets/frontView${formData.customerId}.png`
        : bookingsData[index].frontLicenseImage,
      backLicenseImage: backViewFile
        ? `/assets/backView${formData.customerId}.png`
        : bookingsData[index].backLicenseImage,
    };
  }

  navigate('/reception/reception-customer');
};

export const handleBack = (navigate: NavigateFunction): void => {
  navigate('/reception/reception-customer');
};

