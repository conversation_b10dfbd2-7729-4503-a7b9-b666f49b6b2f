import { useState } from "react";
import { Notification } from "../type/mechanictype";

const MOCK_NOTIFICATIONS: Notification[] = [
  {
    type: "Assignment",
    message:
      "<PERSON> assigned Rego ACX 221 for Accident service at 11.00 am, on 28th of May 2025",
  },
  {
    type: "Assignment",
    message:
      "<PERSON> assigned Rego ACX 221 for breakdown service at 11.00 am, on 25th of May 2025",
  },
  {
    type: "Change",
    message:
      "<PERSON> changed the service type from breakdown service to General  Maintenance at 2.30 pm, on 21st of May 2025",
  },
  {
    type: "Comment",
    message:
      "<PERSON> left comment to Rego 1PX 1ZR at 4.00 pm, on 15th of May 2025",
  },
  {
    type: "Change",
    message:
      "<PERSON> changed the service type from breakdown service to General  Maintenance at 2.30 pm, on 21st of May 2025",
  },
];

export function useNotificationCenter() {
  const [search, setSearch] = useState("");
  const [filterType, setFilterType] = useState("All");

  const allTypes = ["All", ...Array.from(new Set(MOCK_NOTIFICATIONS.map(n => n.type)))];

  const filteredNotifications = MOCK_NOTIFICATIONS.filter((n) => {
    const matchesType = filterType === "All" || n.type === filterType;
    const matchesSearch = n.message.toLowerCase().includes(search.toLowerCase());
    return matchesType && matchesSearch;
  });

  return {
    search,
    setSearch,
    filterType,
    setFilterType,
    allTypes,
    filteredNotifications,
  };
}