import React from 'react';
import { useNavigate } from 'react-router-dom';
import { User } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { useEditProfile } from './hook/useEditProfile';
import { mockEditProfile } from './common/mockdata';
import { EditprofileProps } from './type/customer-type';

export const Editprofile: React.FC<EditprofileProps> = ({}) => {
  const navigate = useNavigate();
  const {
    profilePhoto,
    idPhoto,
    isCorporate,
    setIsCorporate,
    editAddress,
    setEditAddress,
    editPostCode,
    setEditPostCode,
    editCountry,
    setEditCountry,
    editBirthday,
    setEditBirthday,
    editphonenumber,
    setEditPhoneNumber,
    editcompany,
    setEditCompany,
    editEmergencyContactName,
    setEditEmergencyContactName,
    editEmergencyContactPhone,
    setEditEmergencyContactPhone,
    handleProfilePhotoChange,
    handleIdPhotoChange,
    handleBack,
    handleSubmit,
  } = useEditProfile(mockEditProfile);

  return (
    <div className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8">
      <div className="flex items-center">
  <User className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
  <h1 className="text-lg xs:text-xl sm:text-2xl md:text-3xl lg:text-3xl font-bold mb-5 xs:mb-4 md:mb-2 text-gray-800">Profile</h1>
</div>
      <div className="flex flex-row justify-between items-center mb-3 xs:mb-4 md:mb-6">
        <h1 className="text-base xs:text-lg sm:text-xl md:text-2xl lg:text-xl font-bold text-gray-800">Customer Information</h1>
       <Button variant="outline" className="bg-gray-200 text-gray-800 px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base rounded" onClick={() => navigate('/customer/profile')}>
          Back
      </Button>
      </div>

      <div className="mt-2 xs:mt-3 sm:mt-4">
        <Label className="text-[10px] xs:text-xs sm:text-sm md:text-base font-medium text-gray-700">Are you a corporate customer?*</Label>
        <div className="flex items-center gap-3 xs:gap-4 sm:gap-6 mt-1 xs:mt-2">
          <label className="flex items-center gap-1 xs:gap-2 text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600">
            <Input
              type="radio"
              name="corporate"
              checked={isCorporate === true}
              onChange={() => setIsCorporate(true)}
              className="w-3.5 xs:w-4 sm:w-5 h-3.5 xs:h-4 sm:h-5"
              disabled
            />
            Yes
          </label>
          <label className="flex items-center gap-1 xs:gap-2 text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600">
            <Input
              type="radio"
              name="corporate"
              checked={isCorporate === false}
              onChange={() => setIsCorporate(false)}
              className="w-3.5 xs:w-4 sm:w-5 h-3.5 xs:h-4 sm:h-5"
              disabled
            />
            No
          </label>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
        <div className="relative">
          <Input
            type="text"
            id="postCode"
            value={mockEditProfile.customerId}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="postCode" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Customer ID*
          </label>
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
        <div className="relative">
          <Input
            type="text"
            id="firstName"
            value={mockEditProfile.firstName}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="firstName" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            First Name*
          </label>
        </div>
        <div className="relative">
          <Input
            type="text"
            id="lastName"
            value={mockEditProfile.lastName}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="lastName" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Last Name*
          </label>
        </div>
      </div>

      <div className="mt-2 xs:mt-3 sm:mt-4 relative">
        <Input
          type="text"
          id="address"
          value={editAddress}
          onChange={(e) => setEditAddress(e.target.value)}
          className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
          disabled
          required
        />
        <label htmlFor="address" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
          Address*
        </label>
      </div>

      <div className="mt-2 xs:mt-3 sm:mt-4 grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
        <div className="relative">
          <Input
            type="text"
            id="postCode"
            value={editPostCode}
            onChange={(e) => setEditPostCode(e.target.value)}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="postCode" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Post Code*
          </label>
        </div>
        <div className="relative">
          <Input
            type="text"
            id="country"
            value={editCountry}
            onChange={(e) => setEditCountry(e.target.value)}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="country" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Country*
          </label>
        </div>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
      <div className="relative">
        <Input
          type="email"
          id="email"
          value={mockEditProfile.email}
          className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
          disabled
          required
        />
        <label htmlFor="email" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
          Email*
        </label>
      </div>
      </div>

       <div className="mt-2 xs:mt-3 sm:mt-4 grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
        <div className="relative">
          <Input
            type="text"
            id="birthday"
            value={editBirthday}
            onChange={(e) => setEditBirthday(e.target.value)}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="birthday" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Birthday*
          </label>
        </div>
        <div className="relative">
          <Input
            type="text"
            id="phonenumber"
            value={editphonenumber}
            onChange={(e) => setEditPhoneNumber(e.target.value)}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="phonenumber" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Phone Number*
          </label>
        </div>
      </div>

      
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
     <div className="relative">
          <Input
            type="text"
            id="companyName"
            value={editcompany}
            onChange={(e) => setEditCompany(e.target.value)}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="phonenumber" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Company Name*
          </label>
        </div>
      </div>

      <h2 className="text-base xs:text-lg sm:text-xl md:text-2xl lg:text-xl font-semibold mt-3 xs:mt-4 sm:mt-6 mb-2 xs:mb-3 sm:mb-4 text-gray-800">Emergency Contact Details</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
        <div className="relative">
          <Input
            type="text"
            id="emergencyContactName"
            value={editEmergencyContactName}
            onChange={(e) => setEditEmergencyContactName(e.target.value)}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            required
          />
          <label htmlFor="emergencyContactName" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Name*
          </label>
        </div>
        <div className="relative">
          <Input
            type="tel"
            id="emergencyContactPhone"
            value={editEmergencyContactPhone}
            onChange={(e) => setEditEmergencyContactPhone(e.target.value)}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            required
          />
          <label htmlFor="emergencyContactPhone" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Phone Number*
          </label>
        </div>
      </div>

      <div className="flex flex-row justify-between items-center mb-3 xs:mb-4 sm:mb-6">
        <h1 className="text-base xs:text-lg sm:text-xl md:text-2xl lg:text-xl font-semibold text-gray-800">Driver’s License</h1>
        <Button variant="outline" className="bg-[#330101] text-gray-100 px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base rounded" onClick={() => navigate('/customer/addnewlicense')}>
          Add New License
        </Button>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
        <div className="relative">
          <Input
            type="text"
            id="licenseNumber"
            value={mockEditProfile.licenseNumber}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="licenseNumber" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            DL Number*
          </label>
        </div>
        <div className="relative">
          <Input
            type="text"
            id="licenseIssueDate"
            value={mockEditProfile.licenseCountry}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="licenseIssueDate" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Issue Country*
          </label>
        </div>
        <div className="relative">
          <Input
            type="text"
            id="licenseIssueDate"
            value={mockEditProfile.licenseIssueDate}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="licenseIssueDate" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Issue Date*
          </label>
        </div>
        <div className="relative">
          <Input
            type="text"
            id="licenseExpiryDate"
            value={mockEditProfile.licenseExpiryDate}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="licenseExpiryDate" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Expiry Date*
          </label>
        </div>
        <div className="relative">
          <Input
            type="text"
            id="licenseType"
            value={mockEditProfile.licenseType}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-black text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-6 xs:pr-8 sm:pr-10"
            disabled
            required
          />
          <label htmlFor="licenseType" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Condition*
          </label>
        </div>
      </div>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
        <div className="mt-2 xs:mt-3 sm:mt-4 relative">
          <label htmlFor="profilePhoto" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Upload Front View*
          </label>
          <input
            type="file"
            id="profilePhoto"
            accept="image/*"
            onChange={handleProfilePhotoChange}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-gray-900 text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
          />
          {profilePhoto && <p className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600 mt-0.5 xs:mt-1">{profilePhoto.name}</p>}
        </div>

        <div className="mt-2 xs:mt-3 sm:mt-4 relative">
          <label htmlFor="idPhoto" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-600 peer-focus:text-blue-500">
            Upload Back View*
          </label>
          <input
            type="file"
            id="idPhoto"
            accept="image/*"
            onChange={handleIdPhotoChange}
            className="w-full border border-gray-500 rounded px-1 xs:px-2 sm:px-3 py-0.5 xs:py-1 sm:py-2 text-gray-900 text-[10px] xs:text-xs sm:text-sm md:text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
          />
          {idPhoto && <p className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600 mt-0.5 xs:mt-1">{idPhoto.name}</p>}
        </div>
      </div>

      <div className="mt-6 flex gap-4 justify-end">
        <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded" onClick={() => navigate('/customer/profile')}>
          Cancle
        </Button>
        <Button variant="default" className="bg-[#330101] text-white px-4 py-2 rounded" onClick={() => navigate('/customer/profile')}>
          Submit
        </Button>
      </div>
    </div>
  );
};