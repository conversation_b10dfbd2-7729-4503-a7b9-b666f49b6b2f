import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { mockRentalData } from '../common/mockdata';
import { FormData } from '../type/customer-type';

export const useAddRequestHook = () => {
  const navigate = useNavigate();

  const [formData, setFormData] = useState<FormData>({
    rego: '',
    rentalId: '',
    vehicleClass: '',
    pickupDate: '',
    pickupTime: '',
    returnDate: '',
    returnTime: '',
    requestType: '',
    extensionFromDate: '',
    extensionToDate: '',
    newReturnDate: '',
    newReturnTime: '',
    note: ''
  });

  const [errors, setErrors] = useState<{[key: string]: string}>({});

  const handleRegoChange = (selectedRego: string) => {
    const rentalInfo = mockRentalData.find(data => data.rego === selectedRego);
    
    if (rentalInfo) {
      setFormData(prev => ({
        ...prev,
        rego: selectedRego,
        rentalId: rentalInfo.rentalId,
        vehicleClass: rentalInfo.vehicleClass,
        pickupDate: rentalInfo.pickupDate,
        pickupTime: rentalInfo.pickupTime,
        returnDate: rentalInfo.returnDate,
        returnTime: rentalInfo.returnTime
      }));
    }
  };

  const handleRequestTypeChange = (requestType: string) => {
    setFormData(prev => ({
      ...prev,
      requestType,
      extensionFromDate: '',
      extensionToDate: '',
      newReturnDate: '',
      newReturnTime: ''
    }));
  };

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
    
    if (errors[field]) {
      setErrors(prev => ({
        ...prev,
        [field]: ''
      }));
    }
  };

  const validateForm = () => {
    const newErrors: {[key: string]: string} = {};

    if (!formData.rego) newErrors.rego = 'Please select a rego number';
    if (!formData.requestType) newErrors.requestType = 'Please select a request type';
    
    if (formData.requestType === 'Extension of Duration') {
      if (!formData.extensionFromDate) newErrors.extensionFromDate = 'Please select from date';
      if (!formData.extensionToDate) newErrors.extensionToDate = 'Please select to date';
      if (formData.extensionFromDate && formData.extensionToDate && 
          new Date(formData.extensionFromDate) >= new Date(formData.extensionToDate)) {
        newErrors.extensionToDate = 'To date must be after from date';
      }
    }
    
    if (formData.requestType === 'Early Termination') {
      if (!formData.newReturnDate) newErrors.newReturnDate = 'Please select new return date';
      if (!formData.newReturnTime) newErrors.newReturnTime = 'Please select new return time';
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  const handleSubmit = () => {
    if (validateForm()) {
      console.log('Form submitted:', formData);
      alert('Request submitted successfully!');
    }
  };

  const handleCancel = () => {
    setFormData({
      rego: '',
      rentalId: '',
      vehicleClass: '',
      pickupDate: '',
      pickupTime: '',
      returnDate: '',
      returnTime: '',
      requestType: '',
      extensionFromDate: '',
      extensionToDate: '',
      newReturnDate: '',
      newReturnTime: '',
      note: ''
    });
    setErrors({});
    navigate('/customer/change-requests');
  };

  return {
    formData,
    setFormData,
    errors,
    setErrors,
    handleRegoChange,
    handleRequestTypeChange,
    handleInputChange,
    validateForm,
    handleSubmit,
    handleCancel,
  };
};