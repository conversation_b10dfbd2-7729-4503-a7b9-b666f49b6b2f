import React, { useState } from 'react';
import { <PERSON><PERSON><PERSON>t, Eye, Wrench } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import ServiceTypeSection from './common/serviceTypeSection';
import CommentSection from './common/commentSection';
import { useHandlers } from './hook/usehandle'
import IncidentDetailsSection from './common/incidentDetailsSection';


export function EditBreakdownPage() {

  const {
  formData,
  images,
  handleInputChange,
  handleImageUpload,
  changeServiceType,
  setChangeServiceType,
  handleUpdate,
  handleCancel,
} = useHandlers('breakdown');


const ImageUploadField = ({ label, field, value }: 
  { label: string; field: keyof typeof images; value: File | null }) => (
    <div className="space-y-2 relative">
      <Label className="absolute bg-white px-2 text-xs text-gray-600">{label}</Label>
      <div className="flex items-center gap-2">
        <Input
          type="file"
          accept="image/*"
          onChange={(e) => handleImageUpload(field, e.target.files?.[0] || null)}
          className="flex-1"
        />
        <Button type="button" variant="outline" size="sm" className="px-3" disabled={!value}>
          <Eye className="h-4 w-4" />
        </Button>
      </div>
      {value && <p className="text-sm text-gray-500">{value.name}</p>}
    </div>
  );
 

  return (
    <div className="min-h-screen">
      <div className="mb-6">
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
          <span className="hidden md:inline">Go Back</span>
        </Button>

        <div className="flex items-center space-x-2 mt-4">
          <Wrench className="w-6 h-6 text-[#330101]" />
          <h1 className="text-2xl font-bold text-gray-900">Workshop - Edit</h1>
        </div>
      </div>

      <div className="bg-white p-6">
        {/* Vehicle Details */}         
        <h3 className="text-xl font-semibold text-gray-900 pb-3">Vehicle Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[
            { id: 'vehicleClass', label: 'Vehicle Class', value: formData.vehicleClass },
            { id: 'vehicleModel', label: 'Vehicle Model', value: formData.vehicleModel },
            { id: 'rego', label: 'Rego', value: formData.rego },
            { id: 'maintenanceType', label: 'Maintenance Type', value: formData.maintenanceType },
          ].map(({ id, label, value }) => (
            <div className="space-y-2 relative" key={id}>
              <Label htmlFor={id} className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
                {label}
              </Label>
              <Input
                id={id}
                value={value}
                onChange={(e) => handleInputChange(id, e.target.value)}
                className="bg-gray-100 cursor-not-allowed focus:outline-none focus:ring-0 focus:border-none"
                readOnly
              />
            </div>
          ))}
        </div>
             
        <h3 className="text-xl font-semibold text-gray-900 mt-6 pb-2">Upload Images</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-4">
            <ImageUploadField label="Interior Images" field="interior" value={images.interior} />
            <ImageUploadField label="Left Side Doors" field="leftSide" value={images.leftSide} />
            <ImageUploadField label="Front Side Images" field="frontSide" value={images.frontSide} />
            <ImageUploadField label="Side Mirrors" field="sideMirrors" value={images.sideMirrors} />
          </div>
          <div className="space-y-4">
            <ImageUploadField label="Exterior Images" field="exterior" value={images.exterior} />
            <ImageUploadField label="Right Side Doors" field="rightSide" value={images.rightSide} />
            <ImageUploadField label="Back Side Images" field="backSide" value={images.backSide} />
            <ImageUploadField label="Other" field="other" value={images.other} />
          </div>
        </div>

        {/* Change service type */}
        <ServiceTypeSection
        formData={formData}
        changeServiceType={changeServiceType}
        setChangeServiceType={setChangeServiceType}
        handleInputChange={handleInputChange}
      />

      {changeServiceType === 'yes' && (
        <IncidentDetailsSection
          formData={formData}
          handleInputChange={handleInputChange}
        />
      )}

        <CommentSection
        formData={formData}
        handleInputChange={handleInputChange}
      />
      </div>


      <div className="flex justify-end gap-4 mt-6">
        <Button variant="outline" onClick={handleCancel} className="px-8">
          Cancel
        </Button>
        <Button onClick={handleUpdate} className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]">
          Update
        </Button>
      </div>
    </div>
  );
}
