import React, { useState } from 'react';
import { Search, Edit, Cog, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';

interface Parts {
  serviceCode: string;
  vehicle: string;
  serviceName: string;
  vendorName: string;
  description?: string;
  mechanicName: string;
}

export const RequestedServices = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const parts: Parts[] = [
    {
      serviceCode: 'SC001',
      vehicle: 'Isuzu Npr 200  - AFS 009',
      serviceName: 'Wheel Alignments',
      vendorName: 'ARB Somerton',
      mechanicName: 'John Doe'
    },
    {
      serviceCode: 'SC002',
      vehicle: 'Atom - BBA 600',
      serviceName: 'Painting',
      vendorName: 'R<PERSON> Somerton',
      mechanicName: '<PERSON>'
    },
    {
      serviceCode: 'SC003',
      vehicle: 'Ford Mustang Match - QW5 P91',
      serviceName: 'Wheel Alignments',
      vendorName: 'Melbourne Car Removals',
      mechanicName: ''
    },
  ];

  const navigate = useNavigate();

  const handleServiceCodeClick = (serviceCode: string) => {
    navigate(`/admin/workshopMasterAdmin/requested-services-edit/${serviceCode}`);
  };

  const filteredParts = parts.filter(
    (services) =>
      services.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      services.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      services.mechanicName.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center mb-4 sm:mb-0">
            <Cog className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
            <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Requested Services</h1>
          </div>
          <Button
            onClick={() => navigate('/admin/workshopMasterAdmin/requested-services-add')}
            className="px-4 py-2 bg-[#330101] text-white rounded transition-colors w-full sm:w-auto"
          >
            Add New Service
          </Button>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          {/* Filter button - full width on mobile, chevron right aligned */}
          <div className="relative w-full sm:w-auto order-1">
            <button className="flex items-center justify-between gap-2 px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 text-sm w-full sm:w-auto">
              <span>Filter</span>
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
          {/* Search input - full width on mobile */}
          <div className="relative flex-1 order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing a name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>
          {/* Save button */}
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Service Code</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Service Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vendor</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Mechanic</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredParts.map((services) => (
                <TableRow key={services.serviceCode} className="hover:bg-gray-50">
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{services.serviceCode}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{services.vehicle}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{services.serviceName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{services.vendorName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{services.mechanicName || '-'}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    <Button
                      onClick={() => handleServiceCodeClick(services.serviceCode)}
                      variant="ghost"
                      className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                    >
                      <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="md:hidden space-y-4 mb-4">
        {filteredParts.length > 0 ? (
          filteredParts.map((services) => (
            <Card key={services.serviceCode} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
              {/* Card content */}
              <div className="mb-3">
                <span className="text-sm font-medium text-gray-700 break-words whitespace-pre-line">
                  {services.serviceName}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Service Code</div>
                  <div className="flex items-center text-sm">{services.serviceCode}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Vehicle</div>
                  <div className="flex items-center text-sm">{services.vehicle}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Vendor</div>
                  <div className="flex items-center text-sm">{services.vendorName}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Mechanic</div>
                  <div className="flex items-center text-sm">{services.mechanicName || '-'}</div>
                </div>
              </div>
              {/* Action button at the bottom */}
              <div className="flex justify-end mt-4">
                <Button
                  onClick={() => handleServiceCodeClick(services.serviceCode)}
                  variant="ghost"
                  className="text-gray-600 hover:text-gray-800 p-1"
                >
                  <Edit className="w-4 h-4" />
                  <span className="ml-2 text-xs">Edit</span>
                </Button>
              </div>
            </Card>
          ))
        ) : (
          <div className="text-center text-sm text-gray-500 py-4">No requested services found.</div>
        )}
      </div>
    </div>
  );
};