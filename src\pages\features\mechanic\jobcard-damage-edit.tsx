import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { ArrowLeft, AlertTriangle, ChevronDown, Camera } from 'lucide-react'; // <-- Import ChevronDown
import { useJobcardDamageEdit } from './hook/usejobcard-damage-edit';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

export function JobcardDamageEdit() {
  const {
    formData,
    isSubmitting,
    handleInputChange,
    handleSubmit,
    serviceChecks,
    handleServiceCheck,
    notesComment,
    setNotesComment
  } = useJobcardDamageEdit();
  const navigate = useNavigate();

  const handleCancel = () => {
    navigate('/mechanic/jobcard-damage');
  };

  // Mock vehicle options with rego, engineNumber, and accidentDate
  const vehicleOptions = [
    { rego: 'ACV 003', engineNumber: '#457815', accidentDate: '2024-07-01' },
    { rego: 'XYZ 789', engineNumber: '#123456', accidentDate: '2024-07-05' },
    { rego: 'DEF 456', engineNumber: '#987654', accidentDate: '2024-07-10' },
    { rego: 'GHI 321', engineNumber: '#654321', accidentDate: '2024-07-15' }
  ];
  const [showVehicleDropdown, setShowVehicleDropdown] = useState(false);

  const handleVehicleSelect = (vehicle: { rego: string; engineNumber: string; accidentDate: string }) => {
    handleInputChange('vehicle', `${vehicle.rego} (${vehicle.engineNumber})`);
    handleInputChange('accidentDate', vehicle.accidentDate);
    setShowVehicleDropdown(false);
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Back Button - Mobile only */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] flex items-center justify-center text-sm"
          size="sm"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>
      {/* Header - Desktop only */}
      <div className="hidden md:flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Vehicle Details */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Vehicle Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
              {/* Vehicle */}
              <div className="relative pt-3">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle</span>
                <div className="relative flex mt-2">
                  <Input
                    id="vehicle"
                    placeholder="Select"
                    value={formData.vehicle}
                    readOnly
                    onClick={() => setShowVehicleDropdown((prev) => !prev)}
                    className="text-sm h-8 w-full cursor-pointer pr-8 bg-gray-100"
                    required
                  />
                  <ChevronDown
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"
                  />
                  {showVehicleDropdown && (
                    <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow left-0 max-h-56 overflow-y-auto">
                      {vehicleOptions.map(option => (
                        <div
                          key={option.rego}
                          className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                          onClick={() => handleVehicleSelect(option)}
                        >
                          {option.rego} ({option.engineNumber})
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Damage Description */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Description</span>
                <Input
                  id="damageDescription"
                  value={formData.damageDescription}
                  onChange={(e) => handleInputChange('damageDescription', e.target.value)}
                  className="text-sm h-12 mt-2"
                  required
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:block">
            <div className="space-y-6">
              <div className="relative pt-3">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle</span>
                <div className="relative flex items-center">
                  <Input
                    id="vehicle"
                    placeholder="Select"
                    value={formData.vehicle}
                    readOnly
                    onClick={() => setShowVehicleDropdown((prev) => !prev)}
                    className="text-sm h-8 w-full cursor-pointer pr-8 bg-gray-100"
                    required
                  />
                  <ChevronDown
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"
                  />
                  {showVehicleDropdown && (
                    <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow left-0 max-h-56 overflow-y-auto">
                      {vehicleOptions.map(option => (
                        <div
                          key={option.rego}
                          className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                          onClick={() => handleVehicleSelect(option)}
                        >
                          {option.rego} ({option.engineNumber})
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Description</span>
                <Input
                  id="damageDescription"
                  value={formData.damageDescription}
                  onChange={(e) => handleInputChange('damageDescription', e.target.value)}
                  className="text-sm h-12 mt-2"
                  required
                />
              </div>
            </div>
          </div>
        </div>

        {/* Incident Details */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Incident Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-3">
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Parts Identified</span>
                <Input
                  id="damageParts"
                  placeholder=""
                  value={formData.damageParts}
                  onChange={(e) => handleInputChange('damageParts', e.target.value)}
                  className="text-sm h-8 mt-2"
                />
              </div>
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
                <Input
                  id="repairTasks"
                  placeholder=""
                  value={formData.repairTasks}
                  onChange={(e) => handleInputChange('repairTasks', e.target.value)}
                  className="text-sm h-8 mt-2"
                />
              </div>
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
                <Input
                  id="repairedBy"
                  placeholder="Select"
                  value={formData.repairedBy}
                  onChange={(e) => handleInputChange('repairedBy', e.target.value)}
                  className="text-sm h-8 mt-2"
                  required
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:block">
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="relative">
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Parts Identified</span>
                  <Input
                    id="damageParts"
                    placeholder=""
                    value={formData.damageParts}
                    onChange={(e) => handleInputChange('damageParts', e.target.value)}
                    className="text-sm h-8 mt-2"
                  />
                </div>
                <div className="relative">
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
                  <Input
                    id="repairTasks"
                    placeholder=""
                    value={formData.repairTasks}
                    onChange={(e) => handleInputChange('repairTasks', e.target.value)}
                    className="text-sm h-8 mt-2"
                  />
                </div>
                <div className="relative">
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
                  <Input
                    id="repairedBy"
                    placeholder="Select"
                    value={formData.repairedBy}
                    onChange={(e) => handleInputChange('repairedBy', e.target.value)}
                    className="text-sm h-8 mt-2"
                    required
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Upload Images */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Upload Images</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-6">
              {/*
                { id: "interiorImages", label: "Interior Images" },
                { id: "leftSideDoors", label: "Left Side Doors" },
                { id: "frontSideImages", label: "Front Side Images" },
                { id: "sideMirrors", label: "Side Mirrors" },
                { id: "exteriorImages", label: "Exterior Images" },
                { id: "rightSideDoors", label: "Right Side Doors" },
                { id: "backSideImages", label: "Back Side Images" },
                { id: "otherImages", label: "Other" },
              */}
              {Object.entries({
                interiorImages: "Interior Images",
                leftSideDoors: "Left Side Doors",
                frontSideImages: "Front Side Images",
                sideMirrors: "Side Mirrors",
                exteriorImages: "Exterior Images",
                rightSideDoors: "Right Side Doors",
                backSideImages: "Back Side Images",
                otherImages: "Other",
              }).map(([id, label]) => (
                <div className="relative" key={id}>
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">{label}</span>
                  <div className="border rounded px-4 py-3 flex items-center mt-2">
                    <Input type="file" id={id} accept="image/*" capture="environment" className="hidden" />
                    <label htmlFor={id} className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                      <Camera className="w-5 h-5 text-gray-500 mr-3 flex-shrink-0" />
                      <span className="flex-1 text-left">Add Photo</span>
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-6">
            <div className="space-y-6">
              {/*
                { id: "interiorImages", label: "Interior Images" },
                { id: "leftSideDoors", label: "Left Side Doors" },
                { id: "frontSideImages", label: "Front Side Images" },
                { id: "sideMirrors", label: "Side Mirrors" },
              */}
              {Object.entries({
                interiorImages: "Interior Images",
                leftSideDoors: "Left Side Doors",
                frontSideImages: "Front Side Images",
                sideMirrors: "Side Mirrors",
              }).map(([id, label]) => (
                <div className="relative" key={id}>
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">{label}</span>
                  <div className="border rounded px-4 py-3 flex items-center mt-2">
                    <Input type="file" id={id} accept="image/*" capture="environment" className="hidden" />
                    <label htmlFor={id} className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                      <Camera className="w-5 h-5 text-gray-500 mr-3 flex-shrink-0" />
                      <span className="flex-1 text-left">Add Photo</span>
                    </label>
                  </div>
                </div>
              ))}
            </div>
            <div className="space-y-6">
              {/*
                { id: "exteriorImages", label: "Exterior Images" },
                { id: "rightSideDoors", label: "Right Side Doors" },
                { id: "backSideImages", label: "Back Side Images" },
                { id: "otherImages", label: "Other" },
              */}
              {Object.entries({
                exteriorImages: "Exterior Images",
                rightSideDoors: "Right Side Doors",
                backSideImages: "Back Side Images",
                otherImages: "Other",
              }).map(([id, label]) => (
                <div className="relative" key={id}>
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">{label}</span>
                  <div className="border rounded px-4 py-3 flex items-center mt-2">
                    <Input type="file" id={id} accept="image/*" capture="environment" className="hidden" />
                    <label htmlFor={id} className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                      <Camera className="w-5 h-5 text-gray-500 mr-3 flex-shrink-0" />
                      <span className="flex-1 text-left">Add Photo</span>
                    </label>
                  </div>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Job Card Notes */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Job Card Notes</h2>
          <div className="font-semibold mb-2">The Service Requires;</div>
          {/* Mobile Card View */}
          <div className="block md:hidden">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-3">
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.damagedPanels} onChange={() => handleServiceCheck('damagedPanels')} />
                Remove and replace damaged body panels
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.realignFrame} onChange={() => handleServiceCheck('realignFrame')} />
                Remove dents and realign frame
              </label>
              <div className="flex flex-wrap items-center gap-2">
                Replace broken glass -
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.windscreen} onChange={() => handleServiceCheck('windscreen')} />
                  Windscreen
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.windows} onChange={() => handleServiceCheck('windows')} />
                  Windows
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.mirrors} onChange={() => handleServiceCheck('mirrors')} />
                  Mirrors
                </label>
              </div>
              <div className="flex flex-wrap items-center gap-2">
                Repair or replace damaged lights -
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.headlights} onChange={() => handleServiceCheck('headlights')} />
                  Headlights
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.tailLights} onChange={() => handleServiceCheck('tailLights')} />
                  Tail lights
                </label>
              </div>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.repaint} onChange={() => handleServiceCheck('repaint')} />
                Repaint affected areas
              </label>
              <div className="flex flex-wrap items-center gap-2">
                Replace/repair bumper -
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.bumperFront} onChange={() => handleServiceCheck('bumperFront')} />
                  Front
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.bumperRear} onChange={() => handleServiceCheck('bumperRear')} />
                  Rear
                </label>
              </div>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.grilleFenderBonnet} onChange={() => handleServiceCheck('grilleFenderBonnet')} />
                Replace damaged grille/fender/bonnet
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.airbags} onChange={() => handleServiceCheck('airbags')} />
                Inspect airbags and safety systems
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.wheelsSuspension} onChange={() => handleServiceCheck('wheelsSuspension')} />
                Realign wheels and suspension
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.wiring} onChange={() => handleServiceCheck('wiring')} />
                Electrical wiring check and repair
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.numberPlate} onChange={() => handleServiceCheck('numberPlate')} />
                Reattach number plate
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.bodyQuality} onChange={() => handleServiceCheck('bodyQuality')} />
                Perform final body quality check
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.cleanVehicle} onChange={() => handleServiceCheck('cleanVehicle')} />
                Clean vehicle exterior/interior post-repair
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.gps} onChange={() => handleServiceCheck('gps')} />
                Check GPS
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.roadTest} onChange={() => handleServiceCheck('roadTest')} />
                Road test
              </label>
              <div className="mt-4 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</span>
                <Textarea
                  id="notesComment"
                  placeholder=""
                  value={notesComment}
                  onChange={e => setNotesComment(e.target.value)}
                  className="text-sm min-h-8"
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:block">
            <div className="space-y-3 mb-4">
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.damagedPanels} onChange={() => handleServiceCheck('damagedPanels')} />
                Remove and replace damaged body panels
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.realignFrame} onChange={() => handleServiceCheck('realignFrame')} />
                Remove dents and realign frame
              </label>
              <div className="flex flex-wrap items-center gap-2">
                Replace broken glass -
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.windscreen} onChange={() => handleServiceCheck('windscreen')} />
                  Windscreen
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.windows} onChange={() => handleServiceCheck('windows')} />
                  Windows
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.mirrors} onChange={() => handleServiceCheck('mirrors')} />
                  Mirrors
                </label>
              </div>
              <div className="flex flex-wrap items-center gap-2">
                Repair or replace damaged lights -
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.headlights} onChange={() => handleServiceCheck('headlights')} />
                  Headlights
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.tailLights} onChange={() => handleServiceCheck('tailLights')} />
                  Tail lights
                </label>
              </div>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.repaint} onChange={() => handleServiceCheck('repaint')} />
                Repaint affected areas
              </label>
              <div className="flex flex-wrap items-center gap-2">
                Replace/repair bumper -
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.bumperFront} onChange={() => handleServiceCheck('bumperFront')} />
                  Front
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" checked={serviceChecks.bumperRear} onChange={() => handleServiceCheck('bumperRear')} />
                  Rear
                </label>
              </div>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.grilleFenderBonnet} onChange={() => handleServiceCheck('grilleFenderBonnet')} />
                Replace damaged grille/fender/bonnet
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.airbags} onChange={() => handleServiceCheck('airbags')} />
                Inspect airbags and safety systems
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.wheelsSuspension} onChange={() => handleServiceCheck('wheelsSuspension')} />
                Realign wheels and suspension
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.wiring} onChange={() => handleServiceCheck('wiring')} />
                Electrical wiring check and repair
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.numberPlate} onChange={() => handleServiceCheck('numberPlate')} />
                Reattach number plate
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.bodyQuality} onChange={() => handleServiceCheck('bodyQuality')} />
                Perform final body quality check
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.cleanVehicle} onChange={() => handleServiceCheck('cleanVehicle')} />
                Clean vehicle exterior/interior post-repair
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.gps} onChange={() => handleServiceCheck('gps')} />
                Check GPS
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={serviceChecks.roadTest} onChange={() => handleServiceCheck('roadTest')} />
                Road test
              </label>
              <div className="mt-4 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</span>
                <Textarea
                  id="notesComment"
                  placeholder=""
                  value={notesComment}
                  onChange={e => setNotesComment(e.target.value)}
                  className="text-sm min-h-8"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex flex-row flex-wrap justify-end gap-2 pt-4">
          <Button
            type="submit"
            className="bg-[#330101] hover:bg-gray-800 text-white px-8 py-2 sm:w-32 text-sm"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Updating...' : 'Update'}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="px-8 py-2 sm:w-32 text-sm"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}