import { useEffect, useState } from "react";
import { RequestServiceMain } from "../type/mechanictype";

const SERVICES_KEY = "mock_request_services_main";
const MOCK_DATA: RequestServiceMain[] = [
  {
    serviceCode: "SC001",
    serviceName: "Oil Change",
    description: "Engine oil and filter replacement.",
  },
  {
    serviceCode: "SC002",
    serviceName: "Brake Inspection",
    description: "Full brake system check and pad replacement.",
  },
  {
    serviceCode: "SC003",
    serviceName: "Tyre Rotation",
    description: "Rotate all tyres for even wear.",
  },
];

function getServices(): RequestServiceMain[] {
  const data = localStorage.getItem(SERVICES_KEY);
  return data ? JSON.parse(data) : [];
}

function setServices(services: RequestServiceMain[]) {
  localStorage.setItem(SERVICES_KEY, JSON.stringify(services));
}

export function useRequestServicesMain() {
  const [search, setSearch] = useState("");
  const [filterType, setFilterType] = useState("All");
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [services, setServicesState] = useState<RequestServiceMain[]>([]);

  useEffect(() => {
    let data = getServices();
    if (data.length === 0) {
      setServices(MOCK_DATA);
      data = MOCK_DATA;
    }
    setServicesState(data);
  }, []);

  // Get all unique service names for filter dropdown
  const allServiceNames = ["All", ...Array.from(new Set(services.map(d => d.serviceName)))];

  // Filter and search
  const filtered = services.filter(row => {
    const matchesType = filterType === "All" || row.serviceName === filterType;
    const matchesSearch =
      row.serviceCode.toLowerCase().includes(search.toLowerCase()) ||
      row.serviceName.toLowerCase().includes(search.toLowerCase()) ||
      row.description.toLowerCase().includes(search.toLowerCase());
    return matchesType && matchesSearch;
  });

  const totalRecords = filtered.length;
  const totalPages = Math.max(1, Math.ceil(totalRecords / pageSize));
  const paginated = filtered.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  return {
    search,
    setSearch,
    filterType,
    setFilterType,
    allServiceNames,
    pageSize,
    setPageSize,
    currentPage,
    setCurrentPage,
    totalPages,
    totalRecords,
    paginated,
  };
}