import { useState } from 'react'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table'
import { Pagination } from '../../../components/layout/Pagination'
import { Input } from '../../../components/ui/input'
import { Button } from '../../../components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import { Search, CarFront } from 'lucide-react'

interface FleetSummaryData {
  id: string
  category: string
  vehicle: string
  lastServiceType: string
  lastOdometer: number
  status: string
  mechanicAssigned: string
}

export function FleetSummaryPage() {
  const [currentPage, setCurrentPage] = useState(1)
  const [recordsPerPage, setRecordsPerPage] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('All')

  // Sample data - replace with actual data source
  const sampleData: FleetSummaryData[] = [
    {
      id: '1',
      category: 'Passenger',
      vehicle: 'Civic Hybrid Sedan - 1PX 12R',
      lastServiceType: 'General Maintenance',
      lastOdometer: 170333,
      status: 'Pending',
      mechanicAssigned: '-'
    },
    {
      id: '2',
      category: 'Passenger',
      vehicle: 'Atom - PCR 455',
      lastServiceType: 'Breakdown',
      lastOdometer: 176394,
      status: 'In Progress',
      mechanicAssigned: 'Mike Smith'
    },
    {
      id: '3',
      category: 'Commercial',
      vehicle: 'Ford Mustang Match - 1PY 2TR',
      lastServiceType: 'Accident Repair',
      lastOdometer: 172200,
      status: 'Awaiting Parts',
      mechanicAssigned: 'Jane Peter'
    },
    {
      id: '4',
      category: 'Passenger',
      vehicle: 'Atom - ASR 321',
      lastServiceType: 'Accident Repair',
      lastOdometer: 176204,
      status: 'Rentable',
      mechanicAssigned: '-'
    },
   
  ]

  // Filter and search data
  const filteredData = sampleData.filter((fleet) => {
    const matchesSearch = searchTerm === '' || 
      fleet.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fleet.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fleet.lastServiceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fleet.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fleet.mechanicAssigned.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'All' || fleet.status === filterStatus
    
    return matchesSearch && matchesFilter
  })
  
  const filteredTotalRecords = filteredData.length
  const filteredTotalPages = Math.ceil(filteredTotalRecords / recordsPerPage)
  
  // Calculate current page data from filtered results
  const startIndex = (currentPage - 1) * recordsPerPage
  const endIndex = startIndex + recordsPerPage
  const currentData = filteredData.slice(startIndex, endIndex)

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in progress':
        return 'bg-blue-500 text-white px-4 py-3 w-[120px] h-[5px]';
      case 'awaiting parts':
        return 'bg-orange-500 text-white px-6 py-3 w-[120px] h-[5px]';
      case 'rentable':
        return 'bg-green-500 text-white px-4 py-3 w-[120px] h-[5px]';
      case 'pending':
        return 'bg-yellow-500 text-white px-4 py-3 w-[120px] h-[5px]';
      default:
        return 'bg-gray-500 text-white px-4 py-3 w-[120px] h-[5px]';
    }
  };

  return (
    <div className="pt-0 px-4 pb-8 max-w-7xl mx-auto">
         {/* Title*/}
      <div className="flex items-center mb-6 sm:mb-8">
        < div className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <CarFront className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Summary</h1>
      </div>

        {/* Search and Filter Bar */}
                <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-6 mb-8 sm:mb-10">
                  <div className="relative w-full sm:w-auto">
                    <Select
                      value={filterStatus} 
                      onValueChange={(value) => setFilterStatus(value)}>
                      <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
                      <SelectValue placeholder="Filter" />
                      </SelectTrigger>
                      <SelectContent>
                      <SelectItem value="All">All</SelectItem>
                      <SelectItem value="Pending">Pending</SelectItem>
                      <SelectItem value="In Progress">In Progress</SelectItem>
                      <SelectItem value="Awaiting Parts">Awaiting Parts</SelectItem>
                      <SelectItem value="Rentable">Rentable</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  <div className="relative flex-1 order-3 sm:order-2">
                      <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
                      <Input
                        placeholder="Start typing here..."
                        value={searchTerm}
                        onChange={(e) => setSearchTerm(e.target.value)}
                        className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
                      />
                    </div>
                    
                    <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
                      <span className="hidden sm:inline">Save this search</span>
                      <span className="sm:hidden">Save</span>
                    </Button>
                  </div>
      
      {/* Desktop Table View - Aligned with pagination */}
      <div className="flex justify-center mb-12">
        <div className="w-full max-w-4xl">
          <div className="hidden md:block rounded-md border overflow-hidden shadow-sm">
            <div className="overflow-x-auto">
              <Table>
            <TableHeader>
              <TableRow className='bg-gray-50 uppercase'>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Category</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Service Type</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Odometer</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Status</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Mechanic Assigned</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length > 0 ? (
                currentData.map((fleet) => (
                  <TableRow key={fleet.id}>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{fleet.category}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 font-normal">{fleet.vehicle}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{fleet.lastServiceType}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                      <span className="text-xs lg:text-sm">
                        {fleet.lastOdometer.toLocaleString()}
                      </span>
                    </TableCell>
                    <TableCell>
                      <span
                    className={`inline-flex items-center justify-center w-[120px] h-[5px] rounded-md text-xs lg:text-sm font-sm  ${getStatusColor(
                      fleet.status
                    )} whitespace-nowrap`}
                  >
                    {fleet.status}
                  </span>
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                      {fleet.mechanicAssigned && fleet.mechanicAssigned !== '-' ? (
                        <span className="text-xs lg:text-sm">{fleet.mechanicAssigned}</span>
                      ) : (
                        <span className="text-gray-400">-</span>
                      )}
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={6} className="text-center py-8 text-gray-500">
                    No fleet summary data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
            </div>
          </div>
        </div>
      </div>

      {/* Pagination outside the table - More space */}
      <div className="flex justify-center mt-16 mb-8">
        <div className="w-full max-w-4xl">
          <Pagination
            currentPage={currentPage}
            totalPages={filteredTotalPages}
            totalRecords={filteredTotalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="text-sm sm:text-base"
          />
        </div>
      </div>
    </div>
  );
}
