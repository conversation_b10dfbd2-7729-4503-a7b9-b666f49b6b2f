import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface RefundFormData {
  id: string;
  reservation: string;
  customer: string;
  isSecurityDepositRefund: string;
  bondRefund: string;
  refundType: string;
  date: string;
  amount: string;
  reference: string;
  pickupDate: string;
  returnDate: string;
  pickupLocation: string;
  vehicleClass: string;
  totalPrice: string;
  totalRevenue: string;
  totalPaid: string;
  totalRefunded: string;
  outstandingBalance: string;
  status: string;
  branch: string;
  walkInCustomer: string;
  loanVehicle: string;
  totalDays: string;
}

const mockRefunds: RefundFormData[] = [
  {
    id: '19744',
    reservation: '27828',
    customer: '<PERSON><PERSON>',
    isSecurityDepositRefund: 'No',
    bondRefund: 'No',
    refundType: 'Bank Card',
    date: '01-08-2025',
    amount: 'AU$500.00',
    reference: 'Bond Refund',
    pickupDate: '01-08-2025 08:01',
    returnDate: '01-08-2025 17:44',
    pickupLocation: 'Somerton',
    vehicleClass: 'Moving Truck',
    totalPrice: 'AU$617.00',
    totalRevenue: 'AU$106.36',
    totalPaid: 'AU$617.00',
    totalRefunded: 'AU$500.00',
    outstandingBalance: 'AU$0.00',
    status: 'Completed',
    branch: 'Lion Car Rental',
    walkInCustomer: 'No',
    loanVehicle: 'No',
    totalDays: '1',
  },
  // ...add more mock data as needed
];

export function MasterAdminRefundView() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // Find the refund by reservation id
  const refund = mockRefunds.find(r => r.reservation === id);

  const [formData] = useState<RefundFormData>(
    refund || mockRefunds[0] // fallback to first if not found
  );

  return (
    <div className="min-h-screen p-4">
      <div className="flex items-center mb-4">
        <Button
          className='bg-[#330101] text-white text-sm px-3 py-2'
          size="sm"
          onClick={() => navigate('/admin/masterAdmin-refunds')}
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="bg-white rounded-lg p-4">
        <h2 className="text-lg font-semibold mb-4">Refund Details</h2>
        <form>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <FormField label="ID" value={formData.id} />
            <FormField label="Reservation" value={formData.reservation} />
            <FormField label="Customer" value={formData.customer} />
            <FormField label="Is Security Deposit Refund?" value={formData.isSecurityDepositRefund} />
            <FormField label="Bond Refund" value={formData.bondRefund} />
            <FormField label="Refund Type" value={formData.refundType} />
            <FormField label="Date" value={formData.date} />
            <FormField label="Amount" value={formData.amount} />
            <FormField label="Reference" value={formData.reference} />
            <FormField label="Pickup Date" value={formData.pickupDate} />
            <FormField label="Return Date" value={formData.returnDate} />
            <FormField label="Pickup Location" value={formData.pickupLocation} />
            <FormField label="Vehicle Class" value={formData.vehicleClass} />
            <FormField label="Total Price" value={formData.totalPrice} />
            <FormField label="Total Revenue" value={formData.totalRevenue} />
            <FormField label="Total Paid" value={formData.totalPaid} />
            <FormField label="Total Refunded" value={formData.totalRefunded} />
            <FormField label="Outstanding Balance" value={formData.outstandingBalance} />
            <FormField label="Status" value={formData.status} />
            <FormField label="Branch" value={formData.branch} />
            <FormField label="Walk in Customer?" value={formData.walkInCustomer} />
            <FormField label="Loan Vehicle" value={formData.loanVehicle} />
            <FormField label="Total Days" value={formData.totalDays} />
          </div>
        </form>
      </div>
    </div>
  );
}

function FormField({ label, value }: { label: string; value: string }) {
  return (
    <div className="relative">
      <Input
        type="text"
        value={value}
        className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
        readOnly
      />
      <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">{label}</Label>
    </div>
  );
}