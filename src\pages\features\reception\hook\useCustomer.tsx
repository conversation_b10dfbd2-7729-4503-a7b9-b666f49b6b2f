import { NavigateFunction } from 'react-router-dom';
import { Booking } from '../type/reception-type';

export const getStatusBadge = (status: string): string => {
  switch (status) {
    case 'Active':
      return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
    case 'Blacklist':
      return 'bg-black text-white px-3 py-1 rounded text-sm font-medium';
    default:
      return '';
  }
};

export const handleView = (customer: Booking, navigate: NavigateFunction) => {
  const cleanId = customer.id.replace('#', '');
  navigate(`/reception/reception-customer-documents/${cleanId}`);
};

export const handleEdit = (customer: Booking, navigate: NavigateFunction) => {
  const cleanId = customer.id.replace('#', '');
  navigate(`/reception/reception-customer-edit/${cleanId}`);
};