import React from 'react';
import { Search, ChevronDown, Edit, FileWarning, RotateCcw } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { useIncidentReporting } from './hook/useIncidentReporting';
import { IncidentReportingProps } from './type/customer-type';

export function IncidentReportingPage({}: IncidentReportingProps) {
  const {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    mobileBookings,
    currentBookings,
    totalPages,
    totalRecords,
    navigate,
  } = useIncidentReporting();

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <div className="flex items-center">
          <RotateCcw className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
          <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Incident Reporting</h1>
        </div>
        <Button
          onClick={() => navigate('/customer/incident-report-form')}
          className="px-3 sm:px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors text-sm sm:text-base w-full sm:w-auto"
        >
          Add Incident
        </Button>
      </div>
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterStatus}
            onValueChange={(value) => setFilterStatus(value)}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Accident">Accident</SelectItem>
              <SelectItem value="Breakdown">Breakdown</SelectItem>
              <SelectItem value="Damage">Damage</SelectItem>
              <SelectItem value="General Maintenance">General Maintenance</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>
      <div className="block sm:hidden">
        <div className="space-y-2">
          {currentBookings.map((booking) => (
            <div key={booking.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm relative">
              <div className="grid grid-cols-2 gap-4 text-sm mb-3">
                <div>
                  <p className="text-gray-500 font-medium">Rental Id</p>
                  <p className="text-gray-900">{booking.id}</p>
                </div>
                <div>
                  <p className="text-gray-500 font-medium">Vehicle</p>
                  <p className="text-gray-900">{booking.vehicle}</p>
                </div>
                <div>
                  <p className="text-gray-500 font-medium">Pickup Date</p>
                  <p className="text-gray-900">{booking.pickupDate}</p>
                  <p className="text-gray-600 text-xs">{booking.pickupTime}</p>
                </div>
                <div>
                  <p className="text-gray-500 font-medium">Return Date</p>
                  <p className="text-gray-900">{booking.returnDate}</p>
                  <p className="text-gray-600 text-xs">{booking.returnTime}</p>
                </div>
                <div>
                  <p className="text-gray-500 font-medium">Incident Date</p>
                  <p className="text-gray-900">{booking.incidentDate}</p>
                </div>
                <div>
                  <p className="text-gray-500 font-medium">Report Type</p>
                  <p className="text-gray-900">{booking.reportType}</p>
                </div>
                <div>
                  <p className="text-gray-500 font-medium">Vehicle Replaced</p>
                  <p className="text-gray-900">{booking.vehicleReplaced}</p>
                </div>
                <div>
                  <p className="text-gray-500 font-medium">Replaced Vehicle</p>
                  <p className="text-gray-900">{booking.replacedVehicle}</p>
                </div>
              </div>
              {booking.replacedDate !== '-' && (
                <div>
                  <p className="text-gray-500 font-medium text-sm">Replaced Date</p>
                  <p className="text-gray-900 text-sm">{booking.replacedDate}</p>
                </div>
              )}
              <Button
                onClick={() => navigate('/customer/incident-report-form')}
                variant="ghost"
                className="absolute bottom-2 right-2 text-gray-600 hover:text-gray-800 p-2"
              >
                <Edit className="w-4 h-4" />
              </Button>
            </div>
          ))}
        </div>
      </div>
      <div className="hidden sm:block">
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs sm:text-sm">Rental ID</TableHead>
                <TableHead className="text-xs sm:text-sm">Vehicle</TableHead>
                <TableHead className="text-xs sm:text-sm">Pickup Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Return Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Incident Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Report Type</TableHead>
                <TableHead className="text-xs sm:text-sm hidden md:table-cell">Vehicle Replaced</TableHead>
                <TableHead className="text-xs sm:text-sm hidden md:table-cell">Replaced Vehicle</TableHead>
                <TableHead className="text-xs sm:text-sm hidden md:table-cell">Replaced Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentBookings.map((booking) => (
                <TableRow key={booking.id}>
                  <TableCell className="text-xs sm:text-sm">{booking.id}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.vehicle}</TableCell>
                  <TableCell className="text-xs sm:text-sm">
                    <div>
                      <div>{booking.pickupDate}</div>
                      <div className="text-gray-500 text-xs">{booking.pickupTime}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-xs sm:text-sm">
                    <div>
                      <div>{booking.returnDate}</div>
                      <div className="text-gray-500 text-xs">{booking.returnTime}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.incidentDate}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.reportType}</TableCell>
                  <TableCell className="text-xs sm:text-sm hidden md:table-cell">{booking.vehicleReplaced}</TableCell>
                  <TableCell className="text-xs sm:text-sm hidden md:table-cell">{booking.replacedVehicle}</TableCell>
                  <TableCell className="text-xs sm:text-sm hidden md:table-cell">{booking.replacedDate}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Button
                        onClick={() => navigate('/customer/incident-report-form')}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalRecords}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-4 sm:mt-6"
      />
    </div>
  );
}