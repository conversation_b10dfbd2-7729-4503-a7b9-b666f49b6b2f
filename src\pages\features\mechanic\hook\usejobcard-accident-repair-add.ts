import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { JobCardAccidentRepairFormData, JobCardAccidentRepairData } from '../type/mechanictype';

export function useJobcardAccidentRepairAdd() {
  const navigate = useNavigate();
  const [isSubmitting, setIsSubmitting] = useState(false);

  const [formData, setFormData] = useState<JobCardAccidentRepairFormData>({
    vehicleClass: '',
    rego: '',
    accidentDate: '',
    repairedBy: '',
    repairCompletionDate: '',
    comments: ''
  });

  const handleInputChange = (field: keyof JobCardAccidentRepairFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const validateForm = () => {
    const requiredFields = ['vehicleClass', 'rego', 'accidentDate', 'repairedBy', 'repairCompletionDate'];
    const errors: string[] = [];

    requiredFields.forEach(field => {
      if (!formData[field as keyof JobCardAccidentRepairFormData]) {
        errors.push(`${field} is required`);
      }
    });

    if (errors.length > 0) {
      alert(`Please fill in the following fields: ${errors.join(', ')}`);
      return false;
    }

    return true;
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      // Get existing accident repair job cards from localStorage
      const existingData = localStorage.getItem('jobCardAccidentRepairData');
      const existingJobCards: JobCardAccidentRepairData[] = existingData ? JSON.parse(existingData) : [];

      // Create new job card entry
      const newJobCard: JobCardAccidentRepairData = {
        id: Date.now().toString(),
        vehicleClass: formData.vehicleClass,
        rego: formData.rego,
        accidentDate: formData.accidentDate,
        repairedBy: formData.repairedBy,
        repairCompletionDate: formData.repairCompletionDate,
        comments: formData.comments || ''
      };

      // Add new job card to existing data
      const updatedJobCards = [...existingJobCards, newJobCard];

      // Save to localStorage
      localStorage.setItem('jobCardAccidentRepairData', JSON.stringify(updatedJobCards));

      // Reset form
      setFormData({
        vehicleClass: '',
        rego: '',
        accidentDate: '',
        repairedBy: '',
        repairCompletionDate: '',
        comments: ''
      });

      alert('Accident repair job card added successfully!');
      navigate('/mechanic/jobcard-accident-repair');
    } catch (error) {
      console.error('Error saving accident repair job card:', error);
      alert('Error saving job card. Please try again.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/mechanic/jobcard-accident-repair');
  };

  return {
    formData,
    isSubmitting,
    handleInputChange,
    handleSubmit,
    handleCancel
  };
}