import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertTriangle, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Booking, FineFormData } from './type/reception-type';
import { bookingsData } from './common/mockData';
import { handleInputChange, handleFileChange, handleSubmit, handleCancel } from './hook/useEditFines';

export function ReceptionEditFinePage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const cleanId = `#${id}`;
  const booking = bookingsData.find(b => b.id === cleanId);

  // State for editable fields
  const [formData, setFormData] = useState<FineFormData>({
    obligationNo: booking?.obligationNo || '',
    penaltyAmount: booking?.penaltyAmount || 0,
    dueDate: booking?.dueDate || '',
    offenseDate: booking?.offenseDate || '',
    offenseTime: booking?.offenseTime || '',
    uploadedFile: null
  });

  if (!booking) {
    return <div>Fine not found</div>;
  }

  return (
    <div className="min-h-screen">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={() => navigate('/reception/reception-fines/')}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
        </div>
        <div className="flex items-center gap-2 mb-6 mt-4">
          <AlertTriangle className="w-6 h-6" />
          <h1 className="text-2xl font-semibold">Fines - Edit</h1>
        </div>

        <form onSubmit={(e) => handleSubmit(e, formData, navigate, cleanId)}>
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-900 mt-4 pb-2">Customer Information</h3>
            <div className="grid grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="rentalId" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rental ID</Label>
                <Input value={booking.id} readOnly placeholder="Rental ID" className="mt-1" />
              </div>
              <div className="relative">
                <Label htmlFor="customerName" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Customer Name</Label>
                <Input value={booking.customerName} readOnly placeholder="Customer Name" className="mt-1" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="phoneNumber" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Phone Number</Label>
                <Input value={booking.customerPhone} readOnly placeholder="Phone Number" className="mt-1" />
              </div>
              <div className="relative">
                <Label htmlFor="vehicle" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle</Label>
                <Input value={booking.vehicle} readOnly placeholder="Vehicle" className="mt-1" />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="obligationNumber" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Obligation Number</Label>
                <Input
                  name="obligationNo"
                  value={formData.obligationNo}
                  onChange={(e) => handleInputChange(e, setFormData)}
                  placeholder="Obligation Number"
                  className="mt-1"
                />
              </div>
              <div className="relative">
                <Label htmlFor="penaltyAmount" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Penalty Amount</Label>
                <Input
                  type="number"
                  name="penaltyAmount"
                  value={formData.penaltyAmount}
                  onChange={(e) => handleInputChange(e, setFormData)}
                  step="0.01"
                  placeholder="Penalty Amount"
                  className="mt-1"
                />
              </div>
            </div>

            <div>
              <Label className="text-sm font-medium text-gray-700">Due Date</Label>
              <div className="grid grid-cols-3 gap-4">
                <Input
                  type="number"
                  name="dueDateDay"
                  value={formData.dueDate.split('-')[0] || ''}
                  onChange={(e) => {
                    const parts = formData.dueDate.split('-');
                    setFormData(prev => ({
                      ...prev,
                      dueDate: `${e.target.value}-${parts[1] || '01'}-${parts[2] || '2025'}`
                    }));
                  }}
                  placeholder="Day"
                  className="mt-1"
                />
                <Input
                  type="number"
                  name="dueDateMonth"
                  value={formData.dueDate.split('-')[1] || ''}
                  onChange={(e) => {
                    const parts = formData.dueDate.split('-');
                    setFormData(prev => ({
                      ...prev,
                      dueDate: `${parts[0] || '01'}-${e.target.value}-${parts[2] || '2025'}`
                    }));
                  }}
                  placeholder="Month"
                  className="mt-1"
                />
                <Input
                  type="number"
                  name="dueDateYear"
                  value={formData.dueDate.split('-')[2] || ''}
                  onChange={(e) => {
                    const parts = formData.dueDate.split('-');
                    setFormData(prev => ({
                      ...prev,
                      dueDate: `${parts[0] || '01'}-${parts[1] || '01'}-${e.target.value}`
                    }));
                  }}
                  placeholder="Year"
                  className="mt-1"
                />
              </div>
            </div>
          </div>

          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-900 mt-4 pb-2">Traffic Offense</h3>
            <div className="grid grid-cols-4 gap-4">
              <Input
                type="number"
                name="offenseDateDay"
                value={formData.offenseDate.split('-')[0] || ''}
                onChange={(e) => {
                  const parts = formData.offenseDate.split('-');
                  setFormData(prev => ({
                    ...prev,
                    offenseDate: `${e.target.value}-${parts[1] || '01'}-${parts[2] || '2025'}`
                  }));
                }}
                placeholder="Day"
                className="mt-1"
              />
              <Input
                type="number"
                name="offenseDateMonth"
                value={formData.offenseDate.split('-')[1] || ''}
                onChange={(e) => {
                  const parts = formData.offenseDate.split('-');
                  setFormData(prev => ({
                    ...prev,
                    offenseDate: `${parts[0] || '01'}-${e.target.value}-${parts[2] || '2025'}`
                  }));
                }}
                placeholder="Month"
                className="mt-1"
              />
              <Input
                type="number"
                name="offenseDateYear"
                value={formData.offenseDate.split('-')[2] || ''}
                onChange={(e) => {
                  const parts = formData.offenseDate.split('-');
                  setFormData(prev => ({
                    ...prev,
                    offenseDate: `${parts[0] || '01'}-${parts[1] || '01'}-${e.target.value}`
                  }));
                }}
                placeholder="Year"
                className="mt-1"
              />
              <Input
                type="time"
                name="offenseTime"
                value={formData.offenseTime}
                onChange={(e) => handleInputChange(e, setFormData)}
                placeholder="Time"
                className="mt-1"
              />
            </div>
          </div>

          <div>
            <Label className="block text-sm font-medium text-gray-700 mt-4">Uploaded File</Label>
            <div className="flex items-center gap-2">
              <Button className="bg-gray-200 text-gray-700 px-4 py-2 rounded">View</Button>
              <Input
                type="file"
                accept="application/pdf"
                onChange={(e) => handleFileChange(e, setFormData)}
                className="mt-1 flex-1"
              />
            </div>
            {formData.uploadedFile && (
              <p className="mt-2 text-sm text-gray-600">
                Selected file: {formData.uploadedFile.name}
              </p>
            )}
          </div>

          <div className="flex justify-end space-x-4 mt-8">
            <Button
              variant="outline"
              onClick={() => handleCancel(navigate)}
              className="px-6 py-2"
            >
              Cancel
            </Button>
            <Button
              type="submit"
              className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]"
            >
              Submit
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
}