import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; 
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface CustomerFormData {
  customerId: string;
  firstName: string;
  lastName: string;
  address: string;
  postCode: string;
  country: string;
  email: string;
  phone: string;
  birthday: {
    day: string;
    month: string;
    year: string;
  };
  companyName: string;
  emergencyContactName: string;
  emergencyContactNumber: string;
  code: string;
  dlNumber: string;
  issueCountry: string;
  issueDate: string;
  expiryDate: string;
  conditions: string;
  password: string;
  isCorporate: string;
  discount: string;
  discountCode: string;
}

export function CustomerMEditcustomer() {
  const navigate = useNavigate(); 
  const handleSave = (): void => {
    navigate('');
  };

  const [formData, setFormData] = useState<CustomerFormData>({
    customerId: 'CID004',
    firstName: 'Pradeep',
    lastName: 'Testing',
    address: '2/85 Hume Hwy, Somerton VIC',
    postCode: '3062',
    country: 'Australia',
    email: '<EMAIL>',
    phone: '0421 234 567',
    birthday: { day: '02', month: '01', year: '1960' },
    companyName: 'Lion Car Rentals',
    emergencyContactName: 'Smith Peter',
    emergencyContactNumber: '0421 234 567',
    code: 'Mobile',
    dlNumber: '12345678',
    issueCountry: 'Australia',
    issueDate: '02/02/2021',
    expiryDate: '02/02/2026',
    conditions: 'Light Vehicles',
    password: 'Pradeep@123',
    isCorporate: '',
    discount: '',
    discountCode: 'LION001'
  });

  const [frontViewFile, setFrontViewFile] = useState<File | null>(null);
  const [backViewFile, setBackViewFile] = useState<File | null>(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);

  const handleFrontViewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFrontViewFile(file);
  };

  const handleBackViewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setBackViewFile(file);
  };

  const handleInputChange = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleBirthdayChange = (field: keyof CustomerFormData['birthday'], value: string) => {
    setFormData(prev => ({
      ...prev,
      birthday: { ...prev.birthday, [field]: value }
    }));
  };

  const conditionsOptions: string[] = ['', 'Light Vehicles', 'Heavy Vehicles', 'Motorcycles'];
  const countryOptions: string[] = ['', 'Australia', 'New Zealand', 'United Kingdom', 'United Status'];
  const codeOptions: string[] = ['', 'Land'];
  const discountOptions: string[] = ['', 'Percentage', 'Amount'];

  return (
    <div className="min-h-screen">
      <div className="flex items-center mb-3 xs:mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2'
          size="sm"
          onClick={() => navigate('/admin/customerMasterAdmin/customerM-customers')}
        >
          <ArrowLeft className="w-3 xs:w-4 h-3 xs:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="bg-white rounded-lg shadow-sm p-3 xs:p-4 sm:p-6 md:p-8 lg:p-10">
        <h2 className="text-base xs:text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold mb-3 xs:mb-4 sm:mb-6 md:mb-8">Customer Information</h2>

        {/* Customer ID */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="customerId"
              value={formData.customerId}
              onChange={(e) => handleInputChange('customerId', e.target.value)}
              className="bg-gray-200 w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Customer ID
            </Label>
          </div>
        </div>

        {/* Name Fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              First Name
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Last Name
            </Label>
          </div>
        </div>

        {/* Address */}
        <div className="mb-3 xs:mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
            required
          />
          <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Address
          </Label>
        </div>

        {/* Post Code and Country */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.postCode}
              onChange={(e) => handleInputChange('postCode', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Post Code
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Country</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border border-gray-300 text-xs xs:text-sm sm:text-base">
                  {formData.country || 'Select Country'}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {countryOptions.map((country) => (
                  <DropdownMenuItem
                    key={country}
                    onSelect={() => handleInputChange('country', country)}
                    className={formData.country === country ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {country || 'Select Country'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Email */}
        <div className="mb-3 xs:mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
            required
          />
          <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Email
          </Label>
        </div>

        {/* Phone and Birthday */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Phone Number
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={`${formData.birthday.year}-${formData.birthday.month.padStart(2, '0')}-${formData.birthday.day.padStart(2, '0')}`}
              onChange={(e) => {
                const [year, month, day] = e.target.value.split('-');
                setFormData(prev => ({
                  ...prev,
                  birthday: { day, month, year }
                }));
              }}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Birthday
            </Label>
          </div>
        </div>

        {/* Company Name */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.companyName}
              onChange={(e) => handleInputChange('companyName', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Company Name
            </Label>
          </div>
        </div>

        {/* Emergency Contact Details */}
        <h3 className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl font-semibold mb-3 xs:mb-4 sm:mb-6 md:mb-8">Emergency Contact Details</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.emergencyContactName}
              onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Emergency Contact Person's Name
            </Label>
          </div>
          <div className="relative">
            <div className="flex">
              <div className="relative">
                <div className="relative">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between border border-gray-300 text-xs xs:text-sm sm:text-base">
                        {formData.code || 'Mobile'}
                        <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full">
                      {codeOptions.map((code) => (
                        <DropdownMenuItem
                          key={code}
                          onSelect={() => handleInputChange('code', code)}
                          className={formData.code === code ? 'bg-amber-100 font-semibold' : ''}
                        >
                          {code || 'Mobile'}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              <Input
                type="tel"
                value={formData.emergencyContactNumber}
                onChange={(e) => handleInputChange('emergencyContactNumber', e.target.value)}
                className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-r-md border-l-0 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              />
            </div>
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Emergency Contact Number
            </Label>
          </div>
        </div>

        {/* Corporate Customer Section */}
        <h3 className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl font-semibold mb-3 xs:mb-4 sm:mb-6 md:mb-8">Corporate Customer</h3>
        <div className="mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="flex items-center space-x-2 xs:space-x-3 sm:space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isCorporate}
                onChange={(e) => handleInputChange('isCorporate', e.target.checked)}
                className="mr-1 xs:mr-2"
              />
              <span className="text-xs xs:text-sm sm:text-base">Yes</span>
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={!formData.isCorporate}
                onChange={(e) => handleInputChange('isCorporate', !e.target.checked)}
                className="mr-1 xs:mr-2"
              />
              <span className="text-xs xs:text-sm sm:text-base">No</span>
            </label>
          </div>
        </div>

        {/* Add Discount Code */}
        <div className="mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
            <div className="relative">
              <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Discount Type</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between border border-gray-300 text-xs xs:text-sm sm:text-base">
                    {formData.discount || 'Select'}
                    <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  {discountOptions.map((discount) => (
                    <DropdownMenuItem
                      key={discount}
                      onSelect={() => handleInputChange('discount', discount)}
                      className={formData.discount === discount ? 'bg-amber-100 font-semibold' : ''}
                    >
                      {discount || 'Select'}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="relative">
              <Input
                type="text"
                className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              />
              <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                Percentage
              </Label>
            </div>
          </div>

          <div className="mb-3 xs:mb-4 sm:mb-6 md:mb-8 relative">
            <Button className="mt-2 xs:mt-3 sm:mt-4 px-2 xs:px-3 sm:px-4 py-1 xs:py-2 bg-[#330101] text-white rounded-md text-xs xs:text-sm sm:text-base">
              Generate Code
            </Button>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mt-2 xs:mt-3 sm:mt-4">
            <div className="relative">
              <Input
                type="text"
                className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              />
              <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                Discount Code
              </Label>
            </div>
            <div className="flex items-end space-x-1 xs:space-x-2 sm:space-x-3">
              <div className="mb-3 xs:mb-4 sm:mb-0">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={false}
                    onChange={() => {}}
                    className="mr-1 xs:mr-2"
                  />
                  <span className="text-xs xs:text-sm sm:text-base">Send Email to Customer</span>
                </label>
              </div>
              <Button className="px-3 xs:px-4 sm:px-6 py-1 xs:py-2 bg-[#330101] text-white rounded-md text-xs xs:text-sm sm:text-base">
                Copy
              </Button>
            </div>
          </div>
        </div>

        {/* Driver's License */}
        <h3 className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl font-semibold mb-3 xs:mb-4 sm:mb-6 md:mb-8">Driver's License</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.dlNumber}
              onChange={(e) => handleInputChange('dlNumber', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              DL Number
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Country</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border border-gray-300 text-xs xs:text-sm sm:text-base">
                  {formData.country || 'Select Country'}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {countryOptions.map((country) => (
                  <DropdownMenuItem
                    key={country}
                    onSelect={() => handleInputChange('country', country)}
                    className={formData.country === country ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {country || 'Select Country'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="date"
              value={formData.issueDate}
              onChange={(e) => handleInputChange('issueDate', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Issue Date
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={formData.expiryDate}
              onChange={(e) => handleInputChange('expiryDate', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Expiry Date
            </Label>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Conditions</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border border-gray-300 text-xs xs:text-sm sm:text-base">
                  {formData.conditions || 'Select Conditions'}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {conditionsOptions.map((condition) => (
                  <DropdownMenuItem
                    key={condition}
                    onSelect={() => handleInputChange('conditions', condition)}
                    className={formData.conditions === condition ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {condition || 'Select Conditions'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Upload Files */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <div className="flex">
              <Input
                type="text"
                value={frontViewFile?.name || 'No file selected'}
                readOnly
                className="flex-1 p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-l-md bg-gray-50 text-xs xs:text-sm sm:text-base"
              />
              <Label className="flex items-center px-2 xs:px-3 sm:px-4 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                <span className="text-xs xs:text-sm sm:text-base">Choose File</span>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleFrontViewChange}
                  className="hidden"
                />
              </Label>
            </div>
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Upload Front View
            </Label>
          </div>
          <div className="relative">
            <div className="flex">
              <Input
                type="text"
                value={backViewFile?.name || 'No file selected'}
                readOnly
                className="flex-1 p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-l-md bg-gray-50 text-xs xs:text-sm sm:text-base"
              />
              <Label className="flex items-center px-2 xs:px-3 sm:px-4 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                <span className="text-xs xs:text-sm sm:text-base">Choose File</span>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleBackViewChange}
                  className="hidden"
                />
              </Label>
            </div>
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Upload Back View
            </Label>
          </div>
        </div>

        {/* Password */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.password}
              onChange={(e) => handleInputChange('password', e.target.value)}
              className="bg-gray-200 w-full p-1 xs:p-2 sm:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-xs xs:text-sm sm:text-base"
              disabled
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Password
            </Label>
          </div>
        </div>

        <div className="mt-3 xs:mt-4 sm:mt-6">
          <label className="flex items-center">
            <input
              type="checkbox"
              onChange={(e) => {
                if (e.target.checked) {
                  setIsPopupOpen(true);
                } else {
                  setIsPopupOpen(false);
                }
              }}
              className="mr-1 xs:mr-2"
            />
            <span className="text-xs xs:text-sm sm:text-base">Do you need to blacklist this customer?</span>
          </label>
        </div>

        {isPopupOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-4 xs:p-5 sm:p-6 rounded-lg shadow-lg w-[90%] sm:w-[400px] max-w-[90%]">
              <p className="text-sm xs:text-base sm:text-lg mb-3 xs:mb-4 sm:mb-6">Are you sure you’re blacklisting this customer?</p>
              <div className="flex justify-end gap-2 xs:gap-3 sm:gap-4">
                <Button
                  onClick={() => setIsPopupOpen(false)}
                  className="px-3 xs:px-4 sm:px-6 py-1 xs:py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600 text-xs xs:text-sm sm:text-base"
                >
                  Yes
                </Button>
                <Button
                  onClick={() => setIsPopupOpen(false)}
                  className="px-3 xs:px-4 sm:px-6 py-1 xs:py-2 bg-red-500 text-white rounded-md hover:bg-red-600 text-xs xs:text-sm sm:text-base"
                >
                  No
                </Button>
              </div>
            </div>
          </div>
        )}

      </div>

      <div className="flex justify-end">
        <Button 
          onClick={handleSave}
          className="px-2 xs:px-3 sm:px-4 py-1 xs:py-2 bg-[#330101] text-white rounded-md transition-colors text-xs xs:text-sm sm:text-base"
        >
          Save Customer
        </Button>
      </div>
    </div>
  );
}