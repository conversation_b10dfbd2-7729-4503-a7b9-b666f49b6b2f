import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { Lock, Eye } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Logo } from '../components/auth/logo';
import { BackButton } from '../components/auth/back-button';

export function ResetPassword() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);

  const handleLogin = () => {
    navigate('/login');
  };

  return (
    <div className="auth-container">
         <BackButton />
      <div className="auth-form">
        <Logo />
        <h1 className="auth-heading">Reset Password</h1>
        <div className="space-y-6">
          {/* New Password */}
          <div className="grid grid-cols-1 gap-4">
            <div className="relative">
              <Input
                type={showPassword ? 'text' : 'password'}
                id="newPassword"
                placeholder="••••••••••••••"
                className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                required
              />
              <label htmlFor="newPassword" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                New Password*
              </label>
              <Lock className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              <button
                type="button"
                onClick={() => setShowPassword(!showPassword)}
                className="absolute right-12 top-3.5"
                aria-label={showPassword ? 'Hide password' : 'Show password'}
              >
                {showPassword ? <Eye className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
              </button>
            </div>
          </div>

          {/* Confirm Password */}
          <div className="grid grid-cols-1 gap-4">
            <div className="relative">
              <Input
                type={showConfirmPassword ? 'text' : 'password'}
                id="confirmPassword"
                placeholder="••••••••••••••"
                className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                required
              />
              <label htmlFor="confirmPassword" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                Confirm Password*
              </label>
              <Lock className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              <button
                type="button"
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className="absolute right-12 top-3.5"
                aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
              >
                {showConfirmPassword ? <Eye className="h-5 w-5 text-gray-400" /> : <Eye className="h-5 w-5 text-gray-400" />}
              </button>
            </div>
          </div>

          {/* conditions*/}
          <div className="text-xs text-gray-600 space-y-1">
            <p>Password must contain:</p>
            <p>- At least 8 characters</p>
            <p>- One uppercase letter</p>
            <p>- One number</p>
          </div>
        </div>
        <p className="text-xs text-gray-500 mb-4">*All Fields Are Required</p>
        <Button className="auth-button bg-yellow-500 text-white w-full">Reset Password</Button>
        <div className="text-center text-sm mt-4">
          Remember your password?{' '}
          <button onClick={handleLogin} className="auth-link">
            Login
          </button>
        </div>
        <div className="text-center text-xs text-gray-600 mt-8">
          DEVELOPED BY: <span className="text-yellow-700">WEBS R US PTY LTD</span>
        </div>
      </div>
    </div>
  );
}