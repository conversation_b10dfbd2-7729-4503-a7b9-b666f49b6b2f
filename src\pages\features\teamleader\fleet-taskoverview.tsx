import { useState, useEffect } from 'react'
import { useNavigate } from 'react-router-dom'
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table'
import { Pagination } from '../../../components/layout/Pagination'
import { Input } from '../../../components/ui/input'
import { Button } from '../../../components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import { Search, Eye, FileWarning } from 'lucide-react'
import { FleetTaskData } from './type/teamleadertype'

export function FleeTaskoverview() {
  const navigate = useNavigate()
  const [currentPage, setCurrentPage] = useState(1)
  const [recordsPerPage, setRecordsPerPage] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('All')

  // Sample data - replace with actual data source
  const initialData: FleetTaskData[] = [
    {
      id: '1',
      taskId: 'TO001',
      rego: '1PX 12R',
      lastService: 'Oil Change',
      lastServiceDate: '2025-07-10',
      status: 'Rentable'
    },
    {
      id: '2',
      taskId: 'TO002',
      rego: 'PCR 455',
      lastService: 'Brake Service',
      lastServiceDate: '2025-07-08',
      status: 'Pending'
    },
    {
      id: '3',
      taskId: 'TO003',
      rego: '1PY 2TR',
      lastService: 'Full Service',
      lastServiceDate: '2025-07-06',
      status: 'Pending'
    },
    {
      id: '4',
      taskId: 'TO004',
      rego: 'ASR 321',
      lastService: 'Tire Rotation',
      lastServiceDate: '2025-07-09',
      status: 'Rentable'
    },
    {
      id: '5',
      taskId: 'TO005',
      rego: 'QBV 233',
      lastService: 'Engine Check',
      lastServiceDate: '2025-07-05',
      status: 'Pending'
    },
    {
      id: '6',
      taskId: 'TO006',
      rego: 'MNO 789',
      lastService: 'Battery Check',
      lastServiceDate: '2025-07-07',
      status: 'Pending'
    },
    {
      id: '7',
      taskId: 'TO007',
      rego: 'XYZ 456',
      lastService: 'Air Filter',
      lastServiceDate: '2025-07-04',
      status: 'Pending'
    }
  ]

  // Load data from localStorage or use initial data
  const [sampleData, setSampleData] = useState<FleetTaskData[]>(() => {
    try {
      const savedData = localStorage.getItem('fleetTaskData')
      if (savedData) {
        return JSON.parse(savedData)
      } else {
        // Initialize localStorage with initial data
        localStorage.setItem('fleetTaskData', JSON.stringify(initialData))
        return initialData
      }
    } catch (error) {
      console.error('Error loading task data:', error)
      return initialData
    }
  })

  // Refresh data when component mounts or when returning from other pages
  useEffect(() => {
    try {
      const savedData = localStorage.getItem('fleetTaskData')
      if (savedData) {
        setSampleData(JSON.parse(savedData))
      }
    } catch (error) {
      console.error('Error refreshing task data:', error)
    }
  }, [])

  // Filter and search data
  const filteredData = sampleData.filter((task) => {
    const matchesSearch = searchTerm === '' || 
      task.rego.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.lastService.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.lastServiceDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      task.status.toLowerCase().includes(searchTerm.toLowerCase())
    
    const matchesFilter = filterStatus === 'All' || task.status === filterStatus
    
    return matchesSearch && matchesFilter
  })
  
  const filteredTotalRecords = filteredData.length
  const filteredTotalPages = Math.ceil(filteredTotalRecords / recordsPerPage)
  
  // Calculate current page data from filtered results
  const startIndex = (currentPage - 1) * recordsPerPage
  const endIndex = startIndex + recordsPerPage
  const currentData = filteredData.slice(startIndex, endIndex)

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'rentable':
        return 'text-xs font-medium rounded bg-green-500 text-white px-2 py-0.5'
      case 'pending':
        return 'text-xs font-medium rounded bg-blue-500 text-white px-2 py-0.5'
      default:
        return 'text-xs font-medium rounded bg-gray-200 text-gray-700 px-2 py-0.5'
    }
  }

  const handleViewClick = (taskId: string) => {
    // Navigate to task overview view page using React Router
    console.log('Viewing task:', taskId)
    navigate(`/teamleader/fleet-taskoverview-view/${taskId}`)
  }

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex items-center mb-4 sm:mb-6">
        <FileWarning className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Task Overview</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterStatus} 
            onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
            <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
            <SelectItem value="All">All</SelectItem>
            <SelectItem value="Rentable">Rentable</SelectItem>
            <SelectItem value="Pending">Pending</SelectItem>
            <SelectItem value="Overdue">Overdue</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1 order-3 sm:order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing here..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>
          
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>

      {/* Mobile Cards View */}
      <div className="block md:hidden space-y-2">
        {currentData.map((task) => (
          <div key={task.id} className="bg-white border border-gray-200 rounded-lg p-4 shadow-sm relative max-w-md mx-auto w-full">
            <div className="grid grid-cols-2 gap-x-6 gap-y-4 text-sm mb-3">
              <div>
                <span className="text-gray-500 font-medium block">Rego</span>
                <span className="text-gray-900">{task.rego}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Last Service</span>
                <span className="text-gray-900">{task.lastService}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Service Date</span>
                <span className="text-gray-900">{task.lastServiceDate}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Status</span>
                <span className={getStatusColor(task.status)}>
                  {task.status}
                </span>
              </div>
            </div>
            <Button
              onClick={() => handleViewClick(task.taskId)}
              variant="ghost"
              className="absolute bottom-2 right-2 text-gray-600 hover:text-gray-800 p-2">
              <Eye className="w-4 h-4" />
            </Button>
          </div>
        ))}
      </div>


      {/* Desktop Table View */}
      <div className="hidden md:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className='bg-gray-50 uppercase'>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Rego</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Service</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Service Date</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Status</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length > 0 ? (
                currentData.map((task) => (
                  <TableRow key={task.id}>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{task.rego}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{task.lastService}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{task.lastServiceDate}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                      <span className={getStatusColor(task.status)}>
                        {task.status}
                      </span>
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                      <div className="flex items-center">
                        <Button
                          onClick={() => handleViewClick(task.taskId)}
                          variant="ghost"
                          className="text-gray-600 hover:text-gray-800 p-1 lg:p-2">
                          <Eye className="w-3 h-3 lg:w-4 lg:h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                    No fleet task data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="mt-4 sm:mt-6">

        <Pagination
          currentPage={currentPage}
          totalPages={filteredTotalPages}
          totalRecords={filteredTotalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="text-sm sm:text-base"
        />
      </div>
    </div>
  );
}
