import { useState, useEffect } from 'react'
import { useNavigate, useParams } from 'react-router-dom'
import { JobCardEditData, JobCardData } from '../type/mechanictype'

export const useJobcardRegularMaintainanceEdit = () => {
  const navigate = useNavigate()
  const { id } = useParams<{ id: string }>()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [jobCards, setJobCards] = useState<JobCardData[]>([])
  const [selectedJobCard, setSelectedJobCard] = useState<JobCardData | null>(null)
  
  const [formData, setFormData] = useState<JobCardEditData>({
    id: '',
    registration: '',
    drainRefillEngineOil: false,
    engineOilLitres: '',
    replaceOilFilter: false,
    replaceFuelFilter: false,
    airFilterChecked: false,
    airFilterReplaced: false,
    cabinFilterChecked: false,
    cabinFilterReplaced: false,
    checkAdjustTyrePressure: false,
    checkTopUpAllFluids: false,
    checkCleanAdjustBreak: false,
    checkCleanAdjustBreak2: false,
    checkWiperBlades: false,
    checkLights: false,
    sparkPlugsChecked: false,
    sparkPlugsReplaced: false,
    checkCorrectOperation: false,
    tyreConditionRF: '',
    tyreConditionLF: '',
    tyreConditionRR: '',
    tyreConditionLR: '',
    breakConditionFront: '',
    breakConditionRear: '',
    breakDiscsRear: '',
    breakDiscsFront: '',
    checkGPS: false,
    roadTest: false,
    comments: '',
    status: 'Pending'
  })

  // Load job cards on component mount
  useEffect(() => {
    const savedData = localStorage.getItem('jobCardData')
    if (savedData) {
      const jobCardsData = JSON.parse(savedData)
      setJobCards(jobCardsData)
      
      // If editing existing job card, load its data
      if (id) {
        const existingJobCard = jobCardsData.find((card: JobCardData) => card.id === id)
        if (existingJobCard) {
          setSelectedJobCard(existingJobCard)
          
          // Load full form data if available
          const fullFormData = localStorage.getItem('jobCardFullData')
          if (fullFormData) {
            const fullData = JSON.parse(fullFormData)
            const existingFullData = fullData.find((data: any) => data.id === id)
            if (existingFullData) {
              setFormData({
                id: existingFullData.id,
                registration: existingJobCard.registration,
                drainRefillEngineOil: existingFullData.drainRefillEngineOil || false,
                engineOilLitres: existingFullData.engineOilLitres || '',
                replaceOilFilter: existingFullData.replaceOilFilter || false,
                replaceFuelFilter: existingFullData.replaceFuelFilter || false,
                airFilterChecked: existingFullData.airFilterChecked || false,
                airFilterReplaced: existingFullData.airFilterReplaced || false,
                cabinFilterChecked: existingFullData.cabinFilterChecked || false,
                cabinFilterReplaced: existingFullData.cabinFilterReplaced || false,
                checkAdjustTyrePressure: existingFullData.checkAdjustTyrePressure || false,
                checkTopUpAllFluids: existingFullData.checkTopUpAllFluids || false,
                checkCleanAdjustBreak: existingFullData.checkCleanAdjustBreak || false,
                checkCleanAdjustBreak2: existingFullData.checkCleanAdjustBreak2 || false,
                checkWiperBlades: existingFullData.checkWiperBlades || false,
                checkLights: existingFullData.checkLights || false,
                sparkPlugsChecked: existingFullData.sparkPlugsChecked || false,
                sparkPlugsReplaced: existingFullData.sparkPlugsReplaced || false,
                checkCorrectOperation: existingFullData.checkCorrectOperation || false,
                tyreConditionRF: existingFullData.tyreConditionRF || '',
                tyreConditionLF: existingFullData.tyreConditionLF || '',
                tyreConditionRR: existingFullData.tyreConditionRR || '',
                tyreConditionLR: existingFullData.tyreConditionLR || '',
                breakConditionFront: existingFullData.breakConditionFront || '',
                breakConditionRear: existingFullData.breakConditionRear || '',
                breakDiscsRear: existingFullData.breakDiscsRear || '',
                breakDiscsFront: existingFullData.breakDiscsFront || '',
                checkGPS: existingFullData.checkGPS || false,
                roadTest: existingFullData.roadTest || false,
                comments: existingFullData.comments || '',
                status: existingJobCard.status
              })
            } else {
              // Set default values with existing job card data
              setFormData(prev => ({
                ...prev,
                id: existingJobCard.id,
                registration: existingJobCard.registration,
                status: existingJobCard.status
              }))
            }
          }
        }
      }
    }
  }, [id])

  const handleRegistrationChange = (registrationNumber: string) => {
    const selectedCard = jobCards.find(card => card.registration === registrationNumber)
    if (selectedCard) {
      setSelectedJobCard(selectedCard)
      setFormData(prev => ({
        ...prev,
        id: selectedCard.id,
        registration: selectedCard.registration,
        status: selectedCard.status
      }))
    }
  }

  const handleInputChange = (field: keyof JobCardEditData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }))
  }

  const handleCheckboxChange = (field: keyof JobCardEditData, checked: boolean) => {
    setFormData(prev => ({ ...prev, [field]: checked }))
  }

  const handleStatusChange = (newStatus: string) => {
    setFormData(prev => ({ ...prev, status: newStatus }))
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!selectedJobCard) {
      alert('Please select a job card to edit')
      return
    }

    setIsSubmitting(true)

    try {
      // Update job card data
      const jobCardData = localStorage.getItem('jobCardData')
      if (jobCardData) {
        const jobCards = JSON.parse(jobCardData)
        const updatedJobCards = jobCards.map((card: JobCardData) =>
          card.id === selectedJobCard.id 
            ? { ...card, status: formData.status }
            : card
        )
        localStorage.setItem('jobCardData', JSON.stringify(updatedJobCards))
      }

      // Update full form data
      const fullFormData = localStorage.getItem('jobCardFullData')
      if (fullFormData) {
        const fullData = JSON.parse(fullFormData)
        const updatedFullData = fullData.map((data: any) =>
          data.id === selectedJobCard.id 
            ? { ...data, ...formData }
            : data
        )
        localStorage.setItem('jobCardFullData', JSON.stringify(updatedFullData))
      } else {
        // Create new full data entry
        const newFullData = [{ ...formData }]
        localStorage.setItem('jobCardFullData', JSON.stringify(newFullData))
      }

      alert('Job card updated successfully!')
      navigate('/mechanic/services')
      
    } catch (error) {
      console.error('Error updating job card:', error)
      alert('Error updating job card. Please try again.')
    } finally {
      setIsSubmitting(false)
    }
  }

  const handleCancel = () => {
    navigate('/mechanic/services')
  }

  return {
    formData,
    selectedJobCard,
    jobCards,
    isSubmitting,
    handleRegistrationChange,
    handleInputChange,
    handleCheckboxChange,
    handleStatusChange,
    handleSubmit,
    handleCancel
  }
}
