import React from 'react';
import { useNavigate } from 'react-router-dom';
import { ArrowLeft, Calendar as CalendarIcon } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useIncidentReportForm } from './hook/useIncidentReportForm';
import { IncidentReportFormProps , Reservation } from './type/customer-type';
import { mockReservations } from './common/mockdata';

export const IncidentReportForm: React.FC<IncidentReportFormProps> = () => {
  const navigate = useNavigate();
  const {
    rego,
    setRego,
    rentalId,
    setRentalId,
    pickupDate,
    setPickupDate,
    returnDate,
    setReturnDate,
    agreementNo,
    setAgreementNo,
    reportType,
    setReportType,
    showPickupCalendar,
    setShowPickupCalendar,
    showReturnCalendar,
    setShowReturnCalendar,
    showIncidentCalendar,
    setShowIncidentCalendar,
    showAdditionalAccidentForm,
    setShowAdditionalAccidentForm,
    accidentFields,
    setAccidentFields,
    additionalAccidentFields,
    setAdditionalAccidentFields,
    breakdownFields,
    setBreakdownFields,
    profilePhoto,
    setProfilePhoto,
    idPhoto,
    setIdPhoto,
    handleRegoChange,
    handleReportTypeChange,
    handleFrontViewChange,
    handleBackViewChange,
    handleInteriorImagesChange,
    handleExteriorImagesChange,
    handleDrugsAlcoholChange,
    handleProfilePhotoChange,
    handleIdPhotoChange,
    handleIncidentDateChange,
    handleOdometerChange,
    handleFuelGaugeChange,
    handleNoteChange,
    handleAccidentLocationChange,
    handleDamageDescriptionChange,
    handleWitnessDetailsChange,
    handlePoliceReportChange,
    handleSubmit,
    handleBack,
    handleNext,
    
    
  } = useIncidentReportForm();

  return (
    <div className="space-y-6 p-6">
      <Button
        className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
        size="sm"
        onClick={handleBack}
      >
        <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
        <span className="hidden md:inline">Go Back</span>
      </Button>
      <h2 className="text-xl font-semibold text-gray-800">Incident Reporting</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="relative">
          <Select value={rego} onValueChange={handleRegoChange}>
            <SelectTrigger className="w-full h-[48px] justify-between border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-transparent">
              <SelectValue placeholder="Rego" />
            </SelectTrigger>
            <SelectContent>
              {mockReservations.map((reservation: Reservation) => (
                <SelectItem key={reservation.rego} value={reservation.rego}>
                  {reservation.rego}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <Label htmlFor="rego" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
            Rego*
          </Label>
        </div>
        <div className="relative">
          <Input
            type="text"
            id="rentalId"
            value={rentalId}
            readOnly
            className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
            required
          />
          <Label htmlFor="rentalId" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
            Rental ID*
          </Label>
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="relative">
          <Input
            type="text"
            id="pickupDate"
            value={pickupDate ? pickupDate.toLocaleString('en-US', { dateStyle: 'medium', timeStyle: 'short' }) : ''}
            readOnly
            className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
            required
            onClick={() => setShowPickupCalendar(true)}
          />
          <Label htmlFor="pickupDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
            Pickup Date*
          </Label>
          {showPickupCalendar && (
            <div className="mt-2">
              <Calendar
                mode="single"
                selected={pickupDate}
                onSelect={(date) => {
                  setPickupDate(date);
                  setShowPickupCalendar(false);
                }}
                onClickOutside={() => setShowPickupCalendar(false)}
                className="border rounded-md p-2"
                initialFocus
              />
            </div>
          )}
        </div>
        <div className="relative">
          <Input
            type="text"
            id="returnDate"
            value={returnDate ? returnDate.toLocaleString('en-US', { dateStyle: 'medium', timeStyle: 'short' }) : ''}
            readOnly
            className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
            required
            onClick={() => setShowReturnCalendar(true)}
          />
          <Label htmlFor="returnDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
            Return Date*
          </Label>
          {showReturnCalendar && (
            <div className="mt-2">
              <Calendar
                mode="single"
                selected={returnDate}
                onSelect={(date) => {
                  setReturnDate(date);
                  setShowReturnCalendar(false);
                }}
                onClickOutside={() => setShowReturnCalendar(false)}
                className="border rounded-md p-2"
                initialFocus
              />
            </div>
          )}
        </div>
      </div>
      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
        <div className="relative">
          <Input
            type="text"
            id="agreementNo"
            value={agreementNo}
            readOnly
            className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
            required
          />
          <Label htmlFor="agreementNo" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
            Agreement No*
          </Label>
        </div>
        <div className="relative">
          <Select value={reportType} onValueChange={handleReportTypeChange}>
            <SelectTrigger className="w-full h-[48px] justify-between border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-transparent">
              <SelectValue placeholder="Report Type" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="Accident Repair">Accident</SelectItem>
              <SelectItem value="Breakdown Service">Breakdown Service</SelectItem>
              <SelectItem value="Regular Maintenance">Regular Maintenance</SelectItem>
              <SelectItem value="Damage">Damage</SelectItem>
            </SelectContent>
          </Select>
          <Label htmlFor="reportType" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
            Report Type*
          </Label>
        </div>
      </div>
      {reportType === 'Accident Repair' && !showAdditionalAccidentForm && (
        <div className="space-y-4">
          <h2 className="text-xl font-semibold text-gray-800">Driver Details</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="text"
                id="driverName"
                value={accidentFields.driverName}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Enter Driver Name"
              />
              <Label htmlFor="driverName" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Driver Name*
              </Label>
            </div>
            <div className="relative">
              <Input
                type="tel"
                id="phoneNumber"
                value={accidentFields.phoneNumber}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Enter Phone Number"
              />
              <Label htmlFor="phoneNumber" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Phone Number*
              </Label>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="email"
                id="email"
                value={accidentFields.email}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Enter Email"
              />
              <Label htmlFor="email" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Email*
              </Label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="birthday"
                value={accidentFields.birthday ? accidentFields.birthday.toLocaleDateString() : ''}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                onClick={() => setAccidentFields({ ...accidentFields, birthday: new Date() })}
              />
              <Label htmlFor="birthday" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Birthday*
              </Label>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="text"
                id="address"
                value={accidentFields.address}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Enter Address"
              />
              <Label htmlFor="address" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Address*
              </Label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="postcode"
                value={accidentFields.postcode}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Enter Postcode"
              />
              <Label htmlFor="postcode" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Postcode*
              </Label>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="text"
                id="country"
                value={accidentFields.country}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Enter Country"
              />
              <Label htmlFor="country" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Country*
              </Label>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Label className="text-xs sm:text-sm font-medium text-gray-700">Drugs or alcohol consumed in the 12 hours prior to the incident:*</Label>
              <div className="flex items-center gap-4 mt-2 sm:gap-6">
                <label className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
                  <Input
                    type="radio"
                    name="drugsAlcohol"
                    checked={accidentFields.isDrugsAlcoholConsumed === true}
                    onChange={() => handleDrugsAlcoholChange(true)}
                    className="w-4 h-4"
                  />
                  Yes
                </label>
                <label className="flex items-center gap-2 text-xs sm:text-sm text-gray-600">
                  <Input
                    type="radio"
                    name="drugsAlcohol"
                    checked={accidentFields.isDrugsAlcoholConsumed === false}
                    onChange={() => handleDrugsAlcoholChange(false)}
                    className="w-4 h-4"
                  />
                  No
                </label>
              </div>
            </div>
          </div>
          <div className="flex justify-between items-center mb-6">
            <h1 className="text-2xl font-semibold text-gray-800">Driver’s License</h1>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="text"
                id="licenseNumber"
                value="********"
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                disabled
                required
              />
              <label htmlFor="licenseNumber" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                DL Number*
              </label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="licenseIssueCountry"
                value="Australia"
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                disabled
                required
              />
              <label htmlFor="licenseIssueCountry" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                Issue Country*
              </label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="licenseIssueDate"
                value="01/01/2020"
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                disabled
                required
              />
              <label htmlFor="licenseIssueDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                Issue Date*
              </label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="licenseExpiryDate"
                value="01/01/2030"
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                disabled
                required
              />
              <label htmlFor="licenseExpiryDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                Expiry Date*
              </label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="licenseType"
                value="Full License"
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                disabled
                required
              />
              <label htmlFor="licenseType" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                Condition*
              </label>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="mt-4 relative">
              <label htmlFor="profilePhoto" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                Upload Front View*
              </label>
              <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                {accidentFields.frontView && (
                  <img
                    src={URL.createObjectURL(accidentFields.frontView)}
                    alt="Front view preview"
                    className="max-w-full max-h-32 object-contain mb-2"
                  />
                )}
                <input
                  type="file"
                  id="profilePhoto"
                  accept="image/*"
                  onChange={handleProfilePhotoChange}
                  className="w-full mt-2"
                />
              </div>
              {accidentFields.frontView && <p className="text-sm text-gray-600 mt-1">{accidentFields.frontView.name}</p>}
            </div>
            <div className="mt-4 relative">
              <label htmlFor="idPhoto" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                Upload Back View*
              </label>
              <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                {accidentFields.backView && (
                  <img
                    src={URL.createObjectURL(accidentFields.backView)}
                    alt="Back view preview"
                    className="max-w-full max-h-32 object-contain mb-2"
                  />
                )}
                <input
                  type="file"
                  id="idPhoto"
                  accept="image/*"
                  onChange={handleIdPhotoChange}
                  className="w-full mt-2"
                />
              </div>
              {accidentFields.backView && <p className="text-sm text-gray-600 mt-1">{accidentFields.backView.name}</p>}
            </div>
          </div>
          <div className="mt-6 flex gap-4 justify-end">
            <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded" onClick={handleBack}>
              Cancel
            </Button>
            <Button variant="default" className="bg-[#330101] text-white px-4 py-2 rounded" onClick={() => navigate('/customer/accident-form')}>
              Next
            </Button>
          </div>
        </div>
      )}
      {(reportType === 'Breakdown Service' || reportType === 'Regular Maintenance' || reportType === 'Damage') && (
        <div className="space-y-4">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="text"
                id="incidentDate"
                value={breakdownFields.incidentDate ? breakdownFields.incidentDate.toLocaleString('en-US', { dateStyle: 'medium', timeStyle: 'short' }) : ''}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                onClick={() => setShowIncidentCalendar(true)}
              />
              <Label htmlFor="incidentDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Incident Date*
              </Label>
              {showIncidentCalendar && (
                <div className="mt-2">
                  <Calendar
                    mode="single"
                    selected={breakdownFields.incidentDate}
                    onSelect={(date) => {
                      handleIncidentDateChange(date);
                      setShowIncidentCalendar(false);
                    }}
                    onClickOutside={() => setShowIncidentCalendar(false)}
                    className="border rounded-md p-2"
                    initialFocus
                  />
                </div>
              )}
            </div>
            <div className="relative">
              <Input
                type="number"
                id="daysLeft"
                value={breakdownFields.daysLeft}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Days Left"
              />
              <Label htmlFor="daysLeft" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Days Left*
              </Label>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="text"
                id="odometerReading"
                value={breakdownFields.odometerReading}
                onChange={handleOdometerChange}
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Enter Odometer Reading"
              />
              <Label htmlFor="odometerReading" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Odometer Reading*
              </Label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="fuelGauge"
                value={breakdownFields.fuelGauge}
                onChange={handleFuelGaugeChange}
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                placeholder="Enter Fuel Gauge"
              />
              <Label htmlFor="fuelGauge" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Fuel Gauge*
              </Label>
            </div>
          </div>
          <div className="relative">
            <Input
              type="text"
              id="note"
              value={breakdownFields.note}
              onChange={handleNoteChange}
              className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
              placeholder="Enter Note"
            />
            <Label htmlFor="note" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
              Note*
            </Label>
          </div>
          {(reportType === 'Breakdown Service' || reportType === 'Damage') && (
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <label htmlFor="interiorImages" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Interior Images*
                </label>
                <input
                  type="file"
                  id="interiorImages"
                  accept="image/*"
                  onChange={handleInteriorImagesChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                {breakdownFields.interiorImages && <p className="text-sm text-gray-600 mt-1">{breakdownFields.interiorImages.name}</p>}
              </div>
              <div className="relative">
                <label htmlFor="exteriorImages" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Exterior Images*
                </label>
                <input
                  type="file"
                  id="exteriorImages"
                  accept="image/*"
                  onChange={handleExteriorImagesChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                {breakdownFields.exteriorImages && <p className="text-sm text-gray-600 mt-1">{breakdownFields.exteriorImages.name}</p>}
              </div>
            </div>
          )}
          <div className="mt-6 flex gap-4 justify-end">
            <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded" onClick={handleBack}>
              Cancel
            </Button>
            <Button variant="default" className="bg-[#330101] text-white px-4 py-2 rounded" onClick={handleSubmit}>
              Submit
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}