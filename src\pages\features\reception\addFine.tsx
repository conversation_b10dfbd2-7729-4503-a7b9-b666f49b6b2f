import React, { useState } from 'react';
import { Alert<PERSON><PERSON>gle, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';
import { RentalData, AddFineFormData } from './type/reception-type';
import {
  handleRentalIdChange,
  handleSuggestionSelect,
  handleFileUpload,
  handleSubmit,
  handleCancel
} from './hook/useAddFine';

export function ReceptionAddFinePage() {
  const [formData, setFormData] = useState<AddFineFormData>({
    rentalId: '',
    customerName: '',
    phoneNumber: '',
    vehicle: '',
    obligationNo: '',
    penaltyAmount: '',
    dueDate: '',
    offenseDate: '',
    offenseTime: '00:00',
    uploadedFile: null
  });
  const [suggestions, setSuggestions] = useState<RentalData[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedRental, setSelectedRental] = useState<RentalData | null>(null);

  const navigate = useNavigate();

  return (
    <div className="p-6">
      {/* Go Back Button */}
      <Button
        className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
        size="sm"
        onClick={() => navigate('/reception/reception-fines/')}
      >
        <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
        <span className="hidden md:inline">Go Back</span>
      </Button>

      {/* Title */}
      <div className="flex items-center gap-2 mb-6 mt-4">
        <AlertTriangle className="w-6 h-6" />
        <h1 className="text-2xl font-semibold">Fines - Add</h1>
      </div>

      <Card className="mb-6">
        <CardContent className="space-y-6">
          {/* Customer Information */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-900 mt-4 pb-2">Customer Information</h3>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="rentalId" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rental ID</Label>
                <Input
                  id="rentalId"
                  value={formData.rentalId}
                  onChange={(e) => handleRentalIdChange(e.target.value, formData, setFormData, setSuggestions, setShowSuggestions, setSelectedRental)}
                  placeholder="eg: RENT001"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                />
                {showSuggestions && suggestions.length > 0 && (
                  <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-48 overflow-y-auto">
                    {suggestions.map((rental) => (
                      <div
                        key={rental.id}
                        className="px-3 py-2 cursor-pointer hover:bg-gray-100"
                        onClick={() => handleSuggestionSelect(rental, formData, setFormData, setShowSuggestions, setSelectedRental)}
                      >
                        <div className="font-medium">{rental.id}</div>
                        <div className="text-sm text-gray-600">{rental.customerName}</div>
                      </div>
                    ))}
                  </div>
                )}
              </div>
              <div className="relative">
                <Label htmlFor="customerName" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Customer Name</Label>
                <Input
                  id="customerName"
                  value={formData.customerName}
                  onChange={(e) => setFormData(prev => ({ ...prev, customerName: e.target.value }))}
                  readOnly={selectedRental !== null}
                  className={`mt-1 focus:outline-none focus:ring-0 focus:border-none ${selectedRental ? 'bg-gray-100' : ''}`}
                  placeholder="eg: Suresh Perera"
                />
              </div>
            </div>
            
            <div className="grid grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="phoneNumber" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Phone Number</Label>
                <Input
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={(e) => setFormData(prev => ({ ...prev, phoneNumber: e.target.value }))}
                  readOnly={selectedRental !== null}
                  className={`mt-1 focus:outline-none focus:ring-0 focus:border-none ${selectedRental ? 'bg-gray-100' : ''}`}
                  placeholder="eg: 0421 475 120"
                />
              </div>
              <div className="relative">
                <Label htmlFor="vehicle" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle</Label>
                <Input
                  id="vehicle"
                  value={formData.vehicle}
                  onChange={(e) => setFormData(prev => ({ ...prev, vehicle: e.target.value }))}
                  readOnly={selectedRental !== null}
                  className={`mt-1 focus:outline-none focus:ring-0 focus:border-none ${selectedRental ? 'bg-gray-100' : ''}`}
                  placeholder="eg: Atom - PCR 455"
                />
              </div>
            </div>

            <div className="grid grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="obligationNo" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Obligation Number</Label>
                <Input
                  id="obligationNo"
                  value={formData.obligationNo}
                  onChange={(e) => setFormData(prev => ({ ...prev, obligationNo: e.target.value }))}
                  placeholder="eg: 234567891023"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                />
              </div>
              <div className="relative">
                <Label htmlFor="penaltyAmount" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Penalty Amount</Label>
                <Input
                  id="penaltyAmount"
                  type="number"
                  value={formData.penaltyAmount}
                  onChange={(e) => setFormData(prev => ({ ...prev, penaltyAmount: e.target.value }))}
                  step="0.01"
                  placeholder="eg: 100.00"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="dueDate" className="text-sm font-medium text-gray-700">Due Date</Label>
              <Input
                id="dueDate"
                type="date"
                value={formData.dueDate}
                onChange={(e) => setFormData(prev => ({ ...prev, dueDate: e.target.value }))}
                className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
              />
            </div>
          </div>

          {/* Traffic Offense */}
          <div className="space-y-4">
            <h3 className="text-xl font-semibold text-gray-900 mt-4 pb-2">Traffic Offense</h3>
            
            <div>
              <Label className="text-sm font-medium text-gray-700">Offense Date & Time</Label>
              <div className="grid grid-cols-2 gap-2 mt-1">
                <Input
                  type="date"
                  value={formData.offenseDate}
                  onChange={(e) => setFormData(prev => ({ ...prev, offenseDate: e.target.value }))}
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                />
                <Input
                  type="time"
                  value={formData.offenseTime}
                  onChange={(e) => setFormData(prev => ({ ...prev, offenseTime: e.target.value }))}
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                />
              </div>
            </div>

            <div>
              <Label htmlFor="fileUpload" className="text-sm font-medium text-gray-700">Upload File</Label>
              <div className="mt-1 flex items-center gap-2">
                <Button type="button" variant="outline" onClick={() => document.getElementById('fileUpload')?.click()}>
                  Choose File
                </Button>
                <span className="text-sm text-gray-600">
                  {formData.uploadedFile ? formData.uploadedFile.name : 'infringement.pdf'}
                </span>
                <input
                  id="fileUpload"
                  type="file"
                  className="hidden"
                  onChange={(e) => handleFileUpload(e, formData, setFormData)}
                  accept=".pdf,.jpg,.jpeg,.png"
                />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Buttons */}
      <div className="flex justify-end space-x-4 mt-8">
        <Button
          variant="outline"
          onClick={() => handleCancel(formData, setFormData, setSuggestions, setShowSuggestions, setSelectedRental, navigate)}
          className="px-6 py-2"
        >
          Cancel
        </Button>
        <Button
          onClick={() => handleSubmit(formData, setFormData, setSuggestions, setShowSuggestions, setSelectedRental, navigate)}
          className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]"
        >
          Submit
        </Button>
      </div>
    </div>
  );
}