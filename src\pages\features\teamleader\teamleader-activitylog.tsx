import React, { useState } from 'react';
import { Search, ChevronDown, ScrollText } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Activities } from './type/teamleadertype';
import { activitiesData } from './common/mockData';

export function TeamleaderActivitylog() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const isDateInRange = (notificationDate: string, filter: string): boolean => {
    const today = new Date();
    const notifDate = new Date(notificationDate);
    
    switch (filter) {
      case 'Today':
        return notifDate.toDateString() === today.toDateString();
      
      case 'This Week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        weekStart.setHours(0, 0, 0, 0);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        
        return notifDate >= weekStart && notifDate <= weekEnd;
      
      case 'This Month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);
        
        return notifDate >= monthStart && notifDate <= monthEnd;
      
      case 'Last 30 Days':
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        thirtyDaysAgo.setHours(0, 0, 0, 0);
        
        return notifDate >= thirtyDaysAgo && notifDate <= today;
      
      default:
        return true;
    }
  };

  // Filter notifications based on search and date filter
  const filteredActivities = activitiesData.filter(activities => {
    const matchesSearch = activities.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activities.id.toString().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || isDateInRange(activities.date, filterStatus);
    return matchesSearch && matchesFilter;
  });

  // Pagination
  const totalRecords = filteredActivities.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentNotifications = filteredActivities.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    }
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title */}
      <div className="flex items-center mb-4 sm:mb-6">
        <ScrollText className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Activity Log</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <select 
            value={filterStatus}
            onChange={(e) => {
              setFilterStatus(e.target.value);
              setCurrentPage(1);
            }}
            className="appearance-none bg-white border border-gray-300 rounded-md px-3 sm:px-4 py-2 w-full sm:w-auto text-sm sm:text-base focus:outline-none focus:ring-2 focus:ring-yellow-500 focus:border-transparent"
          >
            <option value="All">Filter</option>
            <option value="Today">Today</option>
            <option value="This Week">This Week</option>
            <option value="This Month">This Month</option>
            <option value="Last 30 Days">Last 30 Days</option>
          </select>
          <ChevronDown className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none" />
        </div>
        
        <div className="relative flex-1 w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm sm:text-base rounded-md focus:border-transparent"
          />
        </div>
        
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 w-full sm:w-auto text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Results Summary */}
      <div className="mb-3 sm:mb-4">
        <p className="text-xs sm:text-sm text-gray-600">
          Showing {currentNotifications.length} of {totalRecords} notifications
          {filterStatus !== 'All' && (
            <span className="ml-1">
              filtered by <span className="font-medium">{filterStatus}</span>
            </span>
          )}
        </p>
      </div>

      {/* Activity Log List */}
      <div className="space-y-3 sm:space-y-4">
        {currentNotifications.length === 0 ? (
          <div className="text-center py-6 sm:py-8">
            <p className="text-gray-500 text-sm sm:text-base">No notifications found matching your criteria.</p>
          </div>
        ) : (
          currentNotifications.map((activities) => (
            <div
              key={activities.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-2 sm:space-x-3">
                <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#4A0000] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 sm:mt-0">
                  <span className="text-white text-xs sm:text-sm font-medium">i</span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-gray-800 text-sm sm:text-base leading-relaxed break-words">
                    {activities.message}
                  </p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalRecords > 0 && (
        <div className="mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="overflow-x-auto"
          />
        </div>
      )}
    </div>
  );
}