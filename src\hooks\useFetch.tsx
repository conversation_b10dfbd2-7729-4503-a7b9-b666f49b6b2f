// useFetch.tsx
import { useState, useCallback } from 'react';
import axios, { AxiosRequestConfig, AxiosResponse } from 'axios';
import Cookies from 'js-cookie';
import alert from '../utils/alert';

const BASE_URL = (import.meta.env.VITE_BASE_URL as string) || 'http://localhost:3000'; 
const DEFAULT_ERROR_MESSAGE = 'An unexpected error occurred.';

interface ApiResponse<T = unknown> {
  status?: boolean;
  success?: boolean;
  message?: string;
  code?: number;
  data?: T;
  token?: string; // Add token field
  user?: {
    id: number;
    email: string;
    role: string;
    clientProfile?: Record<string, any>;
  }; // Add user field
  errors?: { field: string; message: string }[];
}

interface FetchOptions {
  method?: 'GET' | 'POST' | 'PUT' | 'DELETE' | 'PATCH';
  accessToken?: string;
  data?: unknown;
  silent?: boolean;
  successMessage?: string;
  config?: AxiosRequestConfig;
  contentType?: string;
  file?: File;
  image?: File;
  endpoint?: string;
}

interface UseFetchReturn<T = unknown> {
  data: ApiResponse<T> | null;
  loading: boolean;
  error: string | null;
  responseCode: number | null;
  fetchData: (overrideOptions?: FetchOptions) => Promise<ApiResponse<T> | null>;
}

const useFetch = <T = unknown>(
  initialEndpoint = '', 
  options: FetchOptions = {}
): UseFetchReturn<T> => {
  const [data, setData] = useState<ApiResponse<T> | null>(null);
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [responseCode, setResponseCode] = useState<number | null>(null);

  const handleError = useCallback((message: string = DEFAULT_ERROR_MESSAGE, code?: number): void => {
    if (code === 401) {
      Cookies.remove('accessToken');
      window.location.reload();
    } else {
      setError(message);
      // Only show warning if silent is false (already checked in caller)
      alert.warn(error || "operation failed");
    }
  }, [error]);

  const fetchData = useCallback(async (overrideOptions: FetchOptions = {}): Promise<ApiResponse<T> | null> => {
    const {
      method = 'GET',
      accessToken,
      data: requestData,
      silent = false,
      successMessage,
      config,
      contentType = 'application/json',
      file,
      image,
      endpoint = initialEndpoint,
    } = { ...options, ...overrideOptions };

    setLoading(true);
    setError(null);

    try {
      let response: AxiosResponse<ApiResponse<T>>;
      const headers: Record<string, string> = {
        ...(accessToken && { Authorization: `Bearer ${accessToken}` }),
        ...(config?.headers as Record<string, string>),
      };

      let finalData: unknown = requestData;
      const finalHeaders: Record<string, string> = { 
        'Content-Type': contentType, 
        ...headers 
      };

      if (contentType === 'multipart/form-data' || file || image) {
        const formData = new FormData();
        
        // If requestData is already FormData, use it directly
        if (requestData instanceof FormData) {
          finalData = requestData;
        } else {
          // Handle file/image uploads
          if (file) formData.append('file', file);
          if (image) formData.append('image', image);
          
          // Handle other form data
          if (requestData && typeof requestData === 'object') {
            Object.entries(requestData).forEach(([key, value]) => {
              // Handle File objects specially
              if (value instanceof File) {
                formData.append(key, value);
              } 
              // Handle arrays
              else if (Array.isArray(value)) {
                value.forEach((item: string | Blob) => formData.append(key, item));
              }
              // Handle nested objects
              else if (typeof value === 'object' && value !== null) {
                formData.append(key, JSON.stringify(value));
              }
              // Handle other values
              else if (value !== undefined && value !== null) {
                formData.append(key, String(value));
              }
            });
          }
          finalData = formData;
        }
        // Remove Content-Type header for FormData to let browser set it with boundary
        delete finalHeaders['Content-Type'];
      } else if (contentType === 'application/json' && requestData) {
        // Only stringify if the data isn't already a string
        finalData = typeof requestData === 'string' ? requestData : JSON.stringify(requestData);
      }

      const requestConfig: AxiosRequestConfig = {
        headers: finalHeaders,
        ...config,
      };

      const url = `${BASE_URL}${endpoint}`;

      switch (method.toUpperCase()) {
        case 'GET':
          response = await axios.get<ApiResponse<T>>(url, {
            params: requestData,
            ...requestConfig,
          });
          break;
        case 'POST':
          response = await axios.post<ApiResponse<T>>(url, finalData, requestConfig);
          break;
        case 'PUT':
          response = await axios.put<ApiResponse<T>>(url, finalData, requestConfig);
          break;
        case 'DELETE':
          response = await axios.delete<ApiResponse<T>>(url, {
            data: finalData,
            ...requestConfig,
          });
          break;
        case 'PATCH':
          response = await axios.patch<ApiResponse<T>>(url, finalData, requestConfig);
          break;
        default:
          throw new Error(`Unsupported HTTP method: ${method}`);
      }

      setResponseCode(response.status);
      const responseData = response.data;

      if (responseData.code) setResponseCode(responseData.code);

      if (responseData.status === true || response.status === 200 || response.status === 201) {
        setData(responseData);
        // Only show success alert if silent is explicitly false
        if (!silent) {
          alert.success(successMessage || responseData.message || 'Operation successful!');
        }
        
        // Return response data for chaining
        return responseData;
      } else {
        if (responseData.code === 401 && endpoint === '/chat/ask/question') {
          window.location.href = '/';
          return null;
        } else if (responseData.code === 403) {
          Cookies.remove('accessToken');
          window.location.href = '/';
          return null;
        } else {
          setData(responseData);
          // Only handle error with alert if silent is false
          if (!silent) {
            handleError(responseData.message, responseData.code);
          } else {
            setError(responseData.message || 'An error occurred');
          }
          return responseData;
        }
      }
    } catch (error: unknown) {
      const axiosError = error as { response?: { status?: number; data?: { message?: string } }; message?: string };
      setResponseCode(axiosError.response?.status || 500);
      const errorMessage = axiosError.response?.data?.message || axiosError.message || DEFAULT_ERROR_MESSAGE;
      // Only show error alert if silent is false
      if (!silent) {
        handleError(errorMessage);
      } else {
        setError(errorMessage);
      }
      return null;
    } finally {
      setLoading(false);
    }
  }, [initialEndpoint, options, handleError]);

  return { data, loading, error, responseCode, fetchData };
};

export default useFetch;