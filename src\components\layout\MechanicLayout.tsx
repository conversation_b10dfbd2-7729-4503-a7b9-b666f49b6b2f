import { Outlet } from 'react-router-dom';
import { Header } from './Header';
import { MechanicSidebar } from './MechanicSidebar';
import React, { useState } from 'react';

export function MechanicLayout() {
  const [sidebarOpen, setSidebarOpen] = useState(false);

  return (
    <div className="flex flex-col min-h-screen">
      <Header 
        onMenuToggle={() => setSidebarOpen(open => !open)} 
        isSidebarOpen={sidebarOpen}
      />
      <div className="flex flex-1">
        <MechanicSidebar 
          isOpen={sidebarOpen} 
          onClose={() => setSidebarOpen(false)} 
        />
        <main className="flex-1 p-6 bg-white">
          <Outlet />
        </main>
      </div>
    </div>
  );
}
