import { NavigateFunction } from 'react-router-dom';
import { FineFormData } from '../type/reception-type';
import { bookingsData } from '../common/mockData';

export const handleInputChange = (
  e: React.ChangeEvent<HTMLInputElement>,
  setFormData: React.Dispatch<React.SetStateAction<FineFormData>>
) => {
  const { name, value } = e.target;
  setFormData(prev => ({
    ...prev,
    [name]: name === 'penaltyAmount' ? parseFloat(value) || 0 : value
  }));
};

export const handleFileChange = (
  e: React.ChangeEvent<HTMLInputElement>,
  setFormData: React.Dispatch<React.SetStateAction<FineFormData>>
) => {
  const file = e.target.files?.[0];
  if (file && file.type === 'application/pdf') {
    setFormData(prev => ({
      ...prev,
      uploadedFile: file
    }));
  } else {
    alert('Please upload a valid PDF file');
  }
};

export const handleSubmit = (
  e: React.FormEvent,
  formData: FineFormData,
  navigate: NavigateFunction,
  bookingId: string
) => {
  e.preventDefault();
  const index = bookingsData.findIndex(b => b.id === bookingId);
  if (index !== -1) {
    bookingsData[index] = {
      ...bookingsData[index],
      obligationNo: formData.obligationNo,
      penaltyAmount: formData.penaltyAmount,
      dueDate: formData.dueDate,
      offenseDate: formData.offenseDate,
      offenseTime: formData.offenseTime
    };
  }
  console.log('Updated data:', formData);
  navigate('/reception/reception-fines/');
};

export const handleCancel = (navigate: NavigateFunction) => {
  navigate('/reception/reception-fines/');
};