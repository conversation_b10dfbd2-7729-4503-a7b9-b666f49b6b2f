import React, { useState } from 'react';
import { Eye, Search, ClipboardPenLine } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';

export function AuditTrail() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data for the table based on the image
  const auditData = [
    {
      mechanicId: 'MC001',
      mechanic: '<PERSON>',
      vehicle: 'Atom - 1PX 1ZR',
      serviceType: 'Accident Repair',
      dateTime: '21-02-2025 10:00 am',
    },
    {
      mechanicId: 'MC002',
      mechanic: 'Mike Smith',
      vehicle: 'Isuzu Npr 200 - AFS 009',
      serviceType: 'General Maintenance',
      dateTime: '21-02-2025 11:00 am',
    },
  ];

  // Filter data based on search term and filter status
  const filteredData = auditData.filter((item) =>
    (item.mechanicId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.mechanic.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.serviceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.dateTime.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || item.serviceType === filterStatus)
  );

    const handleViewVehicle = (mechanicId: string) => {
    navigate(`/admin/workshopMasterAdmin/audit-trail-view/${mechanicId.replace('#', '')}`);
  };

  // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <ClipboardPenLine className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Audit & Trail</h1>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Accident Repair">Accident Repair</SelectItem>
              <SelectItem value="General Maintenance">General Maintenance</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Table View */}
      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>Mechanic ID</TableHead>
              <TableHead>Mechanic Assigned</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Service Type</TableHead>
              <TableHead>Date & Time</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item) => (
              <TableRow key={item.mechanicId} className="hover:bg-gray-50 transition-colors">
                <TableCell>
                 {item.mechanicId}
                </TableCell>
                <TableCell>{item.mechanic}</TableCell>
                <TableCell>{item.vehicle}</TableCell>
                <TableCell>{item.serviceType}</TableCell>
                <TableCell>{item.dateTime}</TableCell>
                <TableCell>
                   <Button
                      variant="ghost"
                      onClick={() => handleViewVehicle(item.mechanicId)}
                      className="text-gray-600 hover:text-gray-800 transition-colors"
                      title="View Vehicle"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-6"
      />
    </div>
  );
}