import React, { useState } from 'react';
import { Search, Eye, Edit } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination } from '@/components/layout/Pagination';
import { Card } from '@/components/ui/card';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useNavigate } from 'react-router-dom';

export function ServiceDamage() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('Damage');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data for the table
  const accidentData = [
    {
      vehicleId: 'VID0012',
      vehicle: 'Atom - 1PX 5ZP',
      insuranceClaimNumber: '1234569',
      actualDate: '22/07/2025',
      estimatedDate: '21/07/2025',
      insuranceStatus: 'Accepted',
      mechanicAssigned: 'Anne Katrina',
      status: 'Inprogress'
    },
    {
      vehicleId: 'VID0013',
      vehicle: 'BMW 330 d Coupe - 191',
      insuranceClaimNumber: '-',
      actualDate: '08/06/2025',
      estimatedDate: '08/06/2025',
      insuranceStatus: 'Declined',
      mechanicAssigned: '-',
      status: 'Done'
    },
    {
      vehicleId: 'VID0014',
      vehicle: 'Isuzu - AAB 004',
      insuranceClaimNumber: '-',
      actualDate: '10/06/2025',
      estimatedDate: '12/06/2025',
      insuranceStatus: 'Initial Review',
      mechanicAssigned: 'Mike Smith',
      status: 'Pending'
    },
  ];

  // Filter data based on search term and status
  const filteredData = accidentData.filter((item) =>
    (item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.insuranceClaimNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.actualDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.estimatedDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.mechanicAssigned.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || item.status === filterStatus)
  );

  const handleVehicleIdClick = (vehicleId: string) => {
    navigate(`/admin/workshopMasterAdmin/service-damage-edit/${vehicleId}`);
  };

  // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'initial review':
        return 'bg-yellow-500 text-white';
      case 'accepted':
        return 'bg-green-500 text-white';
      case 'declined':
        return 'bg-red-500 text-white';
      case 'done':
        return 'bg-green-500 text-white';
      case 'pending':
        return 'bg-gray-500 text-white';
      case 'inprogress':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-300 text-gray-700';
    }
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Damage</h1>
      </div>

      {/* Tab Bar */}
      <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
        {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
          <Button
            key={tab}
            variant={activeTab === tab ? 'default' : 'outline'}
            onClick={() => {
              if (tab === 'All') {
                navigate('/admin/workshopMasterAdmin/service-management');
              } else if (tab === 'General Maintenance') {
                navigate('/admin/workshopMasterAdmin/service-maintenance');
              } else if (tab === 'Breakdowns') {
                navigate('/admin/workshopMasterAdmin/service-breakdown');
              } else if (tab === 'Accident') {
                navigate('/admin/workshopMasterAdmin/service-accident');
              } else {
                setActiveTab(tab);
              }
            }}
            className="text-sm md:text-base"
          >
            {tab}
          </Button>
        ))}
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        {/* Filter dropdown - always first */}
        <div className="relative w-full sm:w-auto order-1">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="Inprogress">Inprogress</SelectItem>
              <SelectItem value="Done">Done</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* Search input - always second */}
        <div className="relative flex-1 order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        {/* Save button - always third */}
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Responsive Card View for xs, sm, md (below lg) */}
      <div className="lg:hidden space-y-4 mb-4">
        {filteredData.length === 0 && (
          <div className="text-center text-gray-500 py-8">No records found.</div>
        )}
        {filteredData.map((item, index) => (
          <Card key={index} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
            {/* Status badges - top right, one per line */}
            <div className="absolute top-4 right-4 flex flex-col items-end gap-1 min-w-[120px]">
              <span
                className={`inline-flex flex-col items-center justify-center rounded px-3 py-1.5 text-xs font-semibold w-[120px] text-center break-words whitespace-normal leading-tight ${getStatusColor(
                  item.insuranceStatus
                )}`}
              >
                <span className="block">Insurance:</span>
                <span className="block">{item.insuranceStatus}</span>
              </span>
              <span
                className={`inline-flex items-center justify-center rounded px-3 py-1.5 text-xs font-semibold w-[120px] text-center ${getStatusColor(
                  item.status
                )} whitespace-nowrap`}
              >
                {item.status}
              </span>
            </div>
            {/* Card content */}
            <div className="mb-3 pr-[110px]">
              <span
                className="text-blue-600 hover:text-blue-800 font-semibold cursor-pointer text-base"
                onClick={() => handleVehicleIdClick(item.vehicleId)}
              >
                {item.vehicleId}
              </span>
            </div>
            {/* Vehicle name under status badges, left aligned */}
            <div className="mb-2 mt-8 pl-0">
              <span className="text-gray-900 font-medium text-left block">{item.vehicle}</span>
            </div>
            <div className="mb-2 pl-0">
              <div className="text-xs text-gray-500 uppercase font-medium mb-1">Insurance Claim Number</div>
              <div className="text-sm">{item.insuranceClaimNumber}</div>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-2">
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Estimated End Date</div>
                <div className="text-sm">{item.estimatedDate}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Actual End Date</div>
                <div className="text-sm">{item.actualDate}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Mechanic Assigned</div>
                <div className="text-sm">{item.mechanicAssigned}</div>
              </div>
            </div>
            {/* Actions at the bottom */}
            <div className="flex justify-end gap-2 mt-4">
              {/* <Button variant="ghost" className="text-gray-600 hover:text-gray-800 mr-2"
                onClick={() => navigate(`/teamleader/service-viewdamage/${item.vehicle.split(' - ')[1]}`)}>
                <Eye className="w-4 h-4" />
              </Button> */}
              <Button variant="ghost" className="text-gray-600 hover:text-gray-800"
                onClick={() => handleVehicleIdClick(item.vehicleId)}>
                <Edit className="w-5 h-5" />
                <span className="ml-2 text-xs">Edit</span>
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Table View for lg and up */}
      <div className="hidden lg:block rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>Vehicle ID</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Insurance Claim Number</TableHead>
              <TableHead>Estimated End Date</TableHead>
              <TableHead>Actual End Date</TableHead>
              <TableHead>Insurance Status</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Mechanic Assigned</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item, index) => (
              <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                <TableCell>{item.vehicleId}</TableCell>
                <TableCell>{item.vehicle}</TableCell>
                <TableCell>{item.insuranceClaimNumber}</TableCell>
                <TableCell>{item.estimatedDate}</TableCell>
                <TableCell>{item.actualDate}</TableCell>
                <TableCell>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.insuranceStatus)}`}>
                    {item.insuranceStatus}
                  </span>
                </TableCell>
                <TableCell>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </TableCell>
                <TableCell>{item.mechanicAssigned}</TableCell>
                <TableCell>
                  {/* <Button variant="ghost" className="text-gray-600 hover:text-gray-800 mr-2"
                    onClick={() => navigate(`/teamleader/service-viewdamage/${item.vehicle.split(' - ')[1]}`)}>
                    <Eye className="w-4 h-4" />
                  </Button> */}
                  <Button variant="ghost" className="text-gray-600 hover:text-gray-800"
                    onClick={() => handleVehicleIdClick(item.vehicleId)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination only for lg and up */}
      <div className="mt-6 hidden lg:block">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
        />
      </div>
    </div>
  );
}