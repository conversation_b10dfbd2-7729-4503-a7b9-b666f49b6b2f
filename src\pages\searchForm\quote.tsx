import React, { useRef } from 'react';
import { useNavigate } from 'react-router-dom'; 
import SearchHeader from '@/components/layout/SearchHeader';
import { Download } from 'lucide-react';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import economyCar from '@/assets/economyCar.png';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableHeader, TableRow } from '@/components/ui/table';

export function QuotePage() {
  const LionLogo = () => (
    <img src="/logo.png" alt="Lion Logo" className="h-10 xs:h-12 sm:h-14 md:h-16 w-auto" />
  );

  const navigate = useNavigate(); 
  const handleBack = () => {
    navigate('/search/extras');
  };

  const invoiceItems = [
    { description: 'Economy Plus Car', rate: 'AUD 38.18 Per Day', days: '1 Day', amount: '38.18' },
    { description: 'Bond Compulsory - 500', rate: '', days: '', amount: '500.00' },
    { description: 'Insurance - 15', rate: '', days: '', amount: '13.64' },
    { description: 'Additional Driver', rate: 'AUD 40.00 Per Day', days: '1 Day', amount: '36.36' },
    { description: 'GST 10%', rate: '', days: '', amount: '8.82' },
  ];

  // Reference to the quote for PDF generation
  const quoteRef = useRef<HTMLDivElement>(null);
  const handleDownload = async () => {
    if (!quoteRef.current) return;

    const canvas = await html2canvas(quoteRef.current, {
      scale: 2,
      useCORS: true,
    });

    const imgData = canvas.toDataURL('image/png');
    const pdf = new jsPDF('p', 'mm', 'a4');
    const pdfWidth = pdf.internal.pageSize.getWidth();
    const pdfHeight = (canvas.height * pdfWidth) / canvas.width;

    pdf.addImage(imgData, 'PNG', 0, 0, pdfWidth, pdfHeight);
    pdf.save('Lion_Rentals_Quote.pdf');
  };

  return (
    <div className="min-h-screen">
      <SearchHeader />
      <div className="p-2 xs:p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12 min-h-screen bg-gray-50">
        <div className="max-w-[95%] xs:max-w-[90%] sm:max-w-3xl md:max-w-4xl lg:max-w-[794px] xl:max-w-[850px] mx-auto">
          <div ref={quoteRef} className="bg-white border border-gray-200 rounded-lg shadow-sm" style={{ minHeight: '1122px' }}>
            <div className="p-4 xs:p-6 sm:p-8">
              
              {/* Company Details & Logo */}
              <div className="flex flex-row items-center justify-between mb-4 xs:mb-5 sm:mb-6">
                <div className="flex-1">
                  <h1 className="text-xs xs:text-sm sm:text-base md:text-lg">LION RENTALS PTY LTD</h1>
                  <p className="text-[10px] xs:text-xs sm:text-sm text-gray-600 mb-0.5 xs:mb-1">ABN: 11*********</p>
                  <p className="text-[10px] xs:text-xs sm:text-sm text-gray-600 mb-0.5 xs:mb-1">ACN: *********</p>
                  <p className="text-[10px] xs:text-xs sm:text-sm text-gray-600 mb-0.5 xs:mb-1">2/85 Hume Hwy, Somerton, VIC, 3062</p>
                  <p className="text-[10px] xs:text-xs sm:text-sm text-gray-600">03 9303 7447 | <EMAIL></p>
                </div>
                <div className="ml-2 xs:ml-3 sm:ml-4">
                  <LionLogo />
                </div>
              </div>

              <h2 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-bold text-center mb-4 xs:mb-5 sm:mb-6">Quote</h2>

              {/* Customer's booking details */}
              <div className="mb-6 xs:mb-7 sm:mb-8 text-sm xs:text-sm sm:text-base">
                <div className="grid grid-cols-2 xs:grid-cols-1 gap-x-4 xs:gap-x-6 sm:gap-x-8 gap-y-2 xs:gap-y-3 mb-3 xs:mb-4">
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Customer Name</span>
                    <span className="text-gray-600">: Pradeep Testing</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Customer Email</span>
                    <span className="text-gray-600">: <EMAIL></span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Customer Phone</span>
                    <span className="text-gray-600">: 0411111111</span>
                  </div>
                </div>

                <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-3 gap-x-4 xs:gap-x-6 sm:gap-x-8 gap-y-2 xs:gap-y-3 mb-3 xs:mb-4">
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Pickup Date</span>
                    <span className="text-gray-600">: 13-06-2025</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Pickup Time</span>
                    <span className="text-gray-600">: 11:30 am</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Pickup Location</span>
                    <span className="text-gray-600">: Somerton</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Return Date</span>
                    <span className="text-gray-600">: 14-06-2025</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Return Time</span>
                    <span className="text-gray-600">: 10:30 pm</span>
                  </div>
                  <div className="flex">
                    <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Return Location</span>
                    <span className="text-gray-600">: Somerton</span>
                  </div>
                </div>

                <div className="flex">
                  <span className="font-semibold text-gray-900 w-24 xs:w-28 sm:w-32">Branch</span>
                  <span className="text-gray-600">: Lion Car Rental</span>
                </div>
              </div>

              {/* Vehicle Class */}
              <div className="flex items-center mb-6 xs:mb-7 sm:mb-8">
                <div className="w-16 xs:w-18 sm:w-20 md:w-24 h-16 xs:h-18 sm:h-20 md:h-24 flex items-center justify-center mr-2 xs:mr-3 sm:mr-4">
                  <img src={economyCar} alt="Vehicle Class" className="max-h-full w-auto" />
                </div>
                <span className="text-base xs:text-lg sm:text-xl md:text-2xl font-medium text-gray-900">Economy Plus Car</span>
              </div>

              {/* Qoute Items */}
              <div className="border-2 border-dashed border-gray-400 p-2 sm:p-4 mb-4 sm:mb-6 md:mb-8">
                  <Table className="w-full">
                    <TableHeader>
                      <tr className="border-b border-gray-300">
                        <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2">DESCRIPTION</th>
                        <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2 hidden sm:table-cell"></th>
                        <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2 hidden md:table-cell"></th>
                        <th className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2">AMOUNT (AUD)</th>
                      </tr>
                    </TableHeader>
                    <TableBody>
                      {invoiceItems.map((item, index) => (
                        <TableRow key={index} className="border-b border-gray-200">
                          <td className="text-[10px] sm:text-xs md:text-sm lg:text-base py-1 sm:py-2">
                            <div>{item.description}</div>
                            {(item.rate || item.days) && (
                              <div className="text-[9px] sm:text-[10px] md:hidden">
ս
                                <span>{item.rate} {item.days}</span>
                              </div>
                            )}
                          </td>
                          <td className="text-left text-[9px] sm:text-[10px] md:text-xs lg:text-sm py-1 sm:py-2 hidden sm:table-cell">
                            {item.rate && <div>{item.rate}</div>}
                          </td>
                          <td className="text-left text-[9px] sm:text-[10px] md:text-xs lg:text-sm py-1 sm:py-2 hidden md:table-cell">
                            {item.days && <div>{item.days}</div>}
                          </td>
                          <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 sm:py-2">{item.amount}</td>
                        </TableRow>
                      ))}
                      <tr>
                        <td className="py-4 xs:py-5" colSpan={3}></td>
                      </tr>
                    
                      <TableRow className="border-b border-gray-400 hidden md:table-row">
                        <td className="py-1 sm:py-2" colSpan={2}></td>
                        <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Discount</td>
                        <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">3.00</td>
                      </TableRow>
                      <TableRow className="border-b border-gray-400 hidden md:table-row">
                        <td className="py-1 sm:py-2" colSpan={2}></td>
                        <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Rent AUD</td>
                        <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">594.00</td>
                      </TableRow>
                      <TableRow className="border-b border-gray-400 hidden md:table-row">
                        <td className="py-1 sm:py-2" colSpan={2}></td>
                        <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Security Deposit</td>
                        <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">100.00</td>
                      </TableRow>
                      <TableRow className="hidden md:table-row">
                        <td className="py-1 sm:py-2" colSpan={2}></td>
                        <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-bold hidden md:table-cell">Total</td>
                        <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-bold hidden md:table-cell">594.00</td>
                      </TableRow>
                      <br></br>
                      {/* Mobile totals */}
                      <TableRow className="md:hidden">
                        <td className="py-1 sm:py-2" colSpan={4}>
                          <div className="text-[10px] sm:text-xs font-semibold">Discount: 3.00</div>
                          <div className="text-[10px] sm:text-xs font-semibold">Rent AUD: 594.00</div>
                          <div className="text-[10px] sm:text-xs font-semibold">Security Deposit: 100.00</div>
                          <div className="text-[10px] sm:text-xs font-bold">Total: 494.00</div>
                        </td>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
            </div>
          </div>

          {/* Download & Back Buttons */}
          <div className="flex flex-col sm:flex-row justify-center sm:justify-between gap-2 xs:gap-3 sm:gap-4 py-2 xs:py-3 px-2">
            <Button
              onClick={handleBack}
              className="bg-gray-300 hover:bg-gray-400 text-gray-800 font-medium px-3 xs:px-4 sm:px-5 py-1 xs:py-2 text-xs xs:text-sm sm:text-base w-full sm:w-auto"
            >
              Back
            </Button>
            <Button
              className="bg-[#330101] hover:bg-amber-800 text-white font-medium px-3 xs:px-4 sm:px-5 py-1 xs:py-2 text-xs xs:text-sm sm:text-base w-full sm:w-auto"
              onClick={handleDownload}
            >
              <Download className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 mr-1 xs:mr-2" />
              Download Quote
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}