import React from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { ArrowLeft, Download, MessageSquare } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader } from '@/components/ui/card';
import { interactions } from './common/mockData';
import { getStatusBadge } from './hook/useInteraction';

export function InteractionDetailsPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Find the interaction by ID
  const interaction = interactions.find((item) => item.id === id);

  // Handle case where interaction is not found
  if (!interaction) {
    return (
      <div className="min-h-screen p-6">
        <div className="max-w-4xl mx-auto">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={() => navigate('/reception/interaction-management')}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
          <div className="mt-6 text-center text-gray-900 text-lg">Interaction not found</div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen p-6">
      {/* Header Section */}
      <div className="mb-6">
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={() => navigate('/reception/interaction-management')}
        >
          <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
          <span className="hidden md:inline">Go Back</span>
        </Button>

        <div className="flex items-center justify-between space-x-2 mt-4">
          <div className="flex items-center gap-2">
            <MessageSquare className="h-6 w-6 text-gray-600" />
            <h1 className="text-2xl font-bold text-gray-900">Interaction Details</h1>
          </div>
          {/* <Button className="bg-amber-900 hover:bg-amber-800 text-white">
            <Download className="w-4 h-4 mr-2" />
            Download Report
          </Button> */}
        </div>
      </div>

      <div className="max-w-4xl mx-auto">
        {/* Main Content Card */}
        <Card className="border border-gray-300">
          <CardHeader className="pb-4">
            <div className="flex justify-end">
              <span className={getStatusBadge(interaction.status)}>
                {interaction.status}
              </span>
            </div>
          </CardHeader>

          <CardContent className="space-y-6">
            {/* Interaction Details Grid */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              {/* Interaction ID */}
              <div className="flex flex-col space-y-2">
                <label className="text-sm font-medium text-gray-700">Interaction ID:</label>
                <span className="text-gray-900">{interaction.id}</span>
              </div>

              {/* Interaction Mode */}
              <div className="flex flex-col space-y-2">
                <label className="text-sm font-medium text-gray-700">Interaction Mode:</label>
                <span className="text-gray-900">{interaction.mode}</span>
              </div>

              {/* Conditional Email Address for Email mode */}
              {interaction.mode === 'Email' && (
                <div className="flex flex-col space-y-2">
                  <label className="text-sm font-medium text-gray-700">Email Address:</label>
                  <span className="text-gray-900">{interaction.email || '-'}</span>
                </div>
              )}

              {/* Conditional Call Type and Duration for Call mode */}
              {interaction.mode === 'Call' && (
                <>
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium text-gray-700">Call Type:</label>
                    <span className="text-gray-900">{interaction.callType || '-'}</span>
                  </div>
                  <div className="flex flex-col space-y-2">
                    <label className="text-sm font-medium text-gray-700">Duration (min):</label>
                    <span className="text-gray-900">{interaction.callDuration ? `${interaction.callDuration} min` : '-'}</span>
                  </div>
                </>
              )}

              {/* Handled By */}
              <div className="flex flex-col space-y-2">
                <label className="text-sm font-medium text-gray-700">Handled By:</label>
                <span className="text-gray-900">Eric</span>
              </div>

              {/* Date & Time */}
              <div className="flex flex-col space-y-2">
                <label className="text-sm font-medium text-gray-700">Date & Time:</label>
                <span className="text-gray-900">{interaction.dateTime}</span>
              </div>

              {/* Customer Name */}
              <div className="flex flex-col space-y-2">
                <label className="text-sm font-medium text-gray-700">Customer Name:</label>
                <span className="text-gray-900">{interaction.customerName}</span>
              </div>

              {/* Customer Phone Number */}
              <div className="flex flex-col space-y-2">
                <label className="text-sm font-medium text-gray-700">Customer Phone Number:</label>
                <span className="text-gray-900">{interaction.phoneNumber}</span>
              </div>

              {/* Note */}
              <div className="flex flex-col space-y-2 md:col-span-2">
                <label className="text-sm font-medium text-gray-700">Note:</label>
                <span className="text-gray-900">{interaction.purpose || '-'}</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  );
}