import React, { useState } from 'react';
import { ArrowLeft, FileText, ChevronDown } from 'lucide-react';
import { useNavigate, useParams } from 'react-router-dom';

// Mock data for the request
const requestData = {
  customerName: '<PERSON><PERSON>',
  rentalId: '#0001',
  customerPhone: '0423 850 117',
  vehicleClass: 'Economy Plus',
  vehicle: 'Civic Hybrid Sedan - 1PX 1ZR',
  pickupDate: '28-06-2025',
  returnDate: '30-06-2025',
  dateRange: '28-06-2025 to 30-06-2025',
  requestType: 'Extension of Duration',
  note: 'I want this vehicle for 2 more days'
};

export function ReceptionViewrequest() {
    const navigate = useNavigate();
    const { rentalId } = useParams<{ rentalId: string }>();
  const [status, setStatus] = useState('Select Action');
  const [isDropdownOpen, setIsDropdownOpen] = useState(false);

  const statusOptions = [
    { value: 'Approved', label: 'Approved', color: 'bg-red-900 text-white' },
    { value: 'In-Progress', label: 'In-Progress', color: 'bg-gray-600 text-white' },
    { value: 'Cancel', label: 'Cancel', color: 'bg-gray-400 text-white' }
  ];

  const handleStatusChange = (newStatus: string) => {
    setStatus(newStatus);
    setIsDropdownOpen(false);
  };

  const getCurrentStatusColor = () => {
    const currentStatus = statusOptions.find(option => option.value === status);
    return currentStatus ? currentStatus.color : 'bg-red-900 text-white';
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <button className="flex items-center space-x-2 text-white bg-red-900 px-4 py-2 rounded hover:bg-red-800"
            onClick={() => navigate('/reception/reception-changerequest')}>
              <ArrowLeft className="w-4 h-4" />
              <span>Go Back</span>
            </button>
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <FileText className="w-6 h-6 text-red-900" />
            <h1 className="text-2xl font-bold text-gray-800">Change Request</h1>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="bg-white rounded-lg p-8">
          {/* Request Details */}
          <div className="space-y-8">
            {/* Customer Name */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Customer Name :
              </label>
              <span className="text-sm text-gray-900">{requestData.customerName}</span>
            </div>

            {/* Rental ID */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Rental ID :
              </label>
              <span className="text-sm text-gray-900">{requestData.rentalId}</span>
            </div>

            {/* Customer Phone Number */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Customer Phone Number :
              </label>
              <span className="text-sm text-gray-900">{requestData.customerPhone}</span>
            </div>

            {/* Vehicle Class */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Vehicle Class :
              </label>
              <span className="text-sm text-gray-900">{requestData.vehicleClass}</span>
            </div>

            {/* Vehicle */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Vehicle :
              </label>
              <span className="text-sm text-gray-900">{requestData.vehicle}</span>
            </div>

            {/* Pickup Date */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Pickup Date :
              </label>
              <span className="text-sm text-gray-900">{requestData.pickupDate}</span>
            </div>

            {/* Return Date */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Return Date :
              </label>
              <span className="text-sm text-gray-900">{requestData.returnDate}</span>
            </div>

            {/* Date Range */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Date Range :
              </label>
              <span className="text-sm text-gray-900">{requestData.dateRange}</span>
            </div>

            {/* Request Type */}
            <div className="flex items-center">
              <label className="w-1/3 text-sm font-medium text-gray-700">
                Request Type :
              </label>
              <span className="text-sm text-gray-900">{requestData.requestType}</span>
            </div>

            {/* Note */}
            <div className="flex items-start">
              <label className="w-1/3 text-sm font-medium text-gray-700 pt-1">
                Note :
              </label>
              <div className="flex-1 flex justify-between items-start">
                <span className="text-sm text-gray-900">{requestData.note}</span>
                
                {/* Status Dropdown */}
                <div className="relative ml-4">
                  <button
                    onClick={() => setIsDropdownOpen(!isDropdownOpen)}
                    className={`flex items-center space-x-2 px-4 py-2 rounded text-sm font-medium ${getCurrentStatusColor()} hover:opacity-90 transition-opacity`}
                  >
                    <span>{status}</span>
                    <ChevronDown className="w-4 h-4" />
                  </button>
                  
                  {/* Dropdown Menu */}
                  {isDropdownOpen && (
                    <div className="absolute right-0 mt-2 w-32 bg-white rounded-md shadow-lg border border-gray-200 z-10">
                      {statusOptions.map((option) => (
                        <button
                          key={option.value}
                          onClick={() => handleStatusChange(option.value)}
                          className={`w-full text-left px-4 py-2 text-sm first:rounded-t-md last:rounded-b-md ${
                            option.value === status 
                              ? `${option.color}` 
                              : 'text-gray-700 hover:bg-gray-50'
                          }`}
                        >
                          {option.label}
                        </button>
                      ))}
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Click outside to close dropdown */}
      {isDropdownOpen && (
        <div 
          className="fixed inset-0 z-0" 
          onClick={() => setIsDropdownOpen(false)}
        />
      )}
    </div>
  );
}