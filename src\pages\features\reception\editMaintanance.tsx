import React from 'react';
import { ArrowLeft, Wrench } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';

import ServiceTypeSection from './common/serviceTypeSection';
import IncidentDetailsSection from './common/incidentDetailsSection';
import CommentSection from './common/commentSection';
import { useHandlers } from './hook/usehandle'

export function EditMaintenancePage() {
  const {
    formData,
    changeServiceType,
    setChangeServiceType,
    handleInputChange,
    handleUpdate,
    handleCancel,
  } = useHandlers('maintenance');

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
          <span className="hidden md:inline">Go Back</span>
        </Button>
        <div className="flex items-center space-x-2 mt-4">
          <Wrench className="w-6 h-6 text-[#330101]" />
          <h1 className="text-2xl font-bold text-gray-900">Workshop - Edit</h1>
        </div>
      </div>

      {/* Vehicle Details */}
      <div className="bg-white p-6">
        <h3 className="text-xl font-semibold text-gray-900 pb-3">Vehicle Details</h3>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
          {[
            { id: 'vehicleClass', label: 'Vehicle Class', value: formData.vehicleClass },
            { id: 'vehicleModel', label: 'Vehicle Model', value: formData.vehicleModel },
            { id: 'rego', label: 'Rego', value: formData.rego },
            { id: 'maintenanceType', label: 'Maintenance Type', value: formData.maintenanceType },
          ].map(({ id, label, value }) => (
            <div className="space-y-2 relative" key={id}>
              <Label htmlFor={id} className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
                {label}
              </Label>
              <Input
                id={id}
                value={value}
                onChange={(e) => handleInputChange(id, e.target.value)}
                className="bg-gray-100 cursor-not-allowed focus:outline-none focus:ring-0 focus:border-none"
                readOnly
              />
            </div>
          ))}
        </div>
      </div>

     {/* Change service type */}
      <ServiceTypeSection
        formData={formData}
        changeServiceType={changeServiceType}
        setChangeServiceType={setChangeServiceType}
        handleInputChange={handleInputChange}
      />

      {changeServiceType === 'yes' && (
        <IncidentDetailsSection
          formData={formData}
          handleInputChange={handleInputChange}
        />
      )}

      <CommentSection
        formData={formData}
        handleInputChange={handleInputChange}
      />

      {/* Actions */}
      <div className="flex justify-end gap-4 mt-8">
        <Button variant="outline" onClick={handleCancel} className="px-8">
          Cancel
        </Button>
        <Button onClick={handleUpdate} className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]">
          Update
        </Button>
      </div>
    </div>
  );
}
