import React from 'react';
import { ArrowLeft, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate } from 'react-router-dom';

interface DocumentData {
  customerName: string;
  frontLicenseImage?: string;
  backLicenseImage?: string;
}

export const CustomerMDocument: React.FC = () => {
  const documents: DocumentData[] = [
    {
      customerName: "Rose Fernando",
      frontLicenseImage: '/assets/frontView.png',
      backLicenseImage: '/assets/backView.png' 
    }
  ];

  const navigate = useNavigate(); 


  return (
    <div className="min-h-screen px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
      <div className="flex items-center mb-3 xs:mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2'
          size="sm"
          onClick={() => navigate('/admin/customerMasterAdmin/customerM-customers')}
        >
          <ArrowLeft className="w-3 xs:w-4 h-3 xs:h-4 mr-1" />
          Go Back
        </Button>
      </div>

      {/* Main Content */}
      <div className="p-2 xs:p-3 sm:p-5">
        {/* Documents Title */}
        <div className="flex items-center gap-2 mb-4 xs:mb-6 sm:mb-8">
          <FileText size={10} className="text-gray-700 xs:size-8 sm:size-8" />
          <h1 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold text-gray-800">Documents</h1>
        </div>

        {/* Mobile Cards View */}
        <div className="block sm:hidden space-y-2 xs:space-y-3">
          {documents.map((document, index) => (
            <div key={index} className="bg-white border border-gray-200 rounded-lg p-3 xs:p-4 shadow-sm max-w-md mx-auto w-full">
              <div className="text-xs xs:text-sm sm:text-base text-gray-600 font-medium mb-2 xs:mb-3 text-center">
                {document.customerName}
              </div>
              <div className="grid grid-cols-1 gap-2 xs:gap-3">
                <div>
                  <span className="text-xs xs:text-sm font-medium text-gray-500 block text-center">Driving License Front View</span>
                  {document.frontLicenseImage ? (
                    <img 
                      src={document.frontLicenseImage} 
                      alt="Driving License Front"
                      className="w-24 xs:w-28 sm:w-30 h-auto rounded border shadow-sm mx-auto"
                    />
                  ) : (
                    <div className="w-24 xs:w-28 sm:w-30 h-16 xs:h-18 sm:h-20 bg-gradient-to-br from-green-500 to-green-600 rounded flex items-center justify-center text-white text-[10px] xs:text-xs text-center shadow-sm mx-auto">
                      DRIVING LICENSE<br />FRONT VIEW
                    </div>
                  )}
                </div>
                <div>
                  <span className="text-xs xs:text-sm font-medium text-gray-500 block text-center">Driving License Back View</span>
                  {document.backLicenseImage ? (
                    <img 
                      src={document.backLicenseImage} 
                      alt="Driving License Back"
                      className="w-24 xs:w-28 sm:w-30 h-auto rounded border shadow-sm mx-auto"
                    />
                  ) : (
                    <div className="w-24 xs:w-28 sm:w-30 h-16 xs:h-18 sm:h-20 bg-gradient-to-br from-gray-600 to-gray-700 rounded flex items-center justify-center text-white text-[10px] xs:text-xs text-center shadow-sm mx-auto">
                      DRIVING LICENSE<br />BACK VIEW
                    </div>
                  )}
                </div>
              </div>
            </div>
          ))}
        </div>

        {/* Desktop Table View */}
        <div className="hidden sm:block">
          <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
            <table className="w-full">
              <thead className="bg-gray-50 border-b border-gray-200">
                <tr>
                  <th className="px-2 xs:px-3 sm:px-4 py-3 xs:py-4 text-center text-xs xs:text-sm sm:text-base font-semibold text-gray-700">
                    Customer Name
                  </th>
                  <th className="px-2 xs:px-3 sm:px-4 py-3 xs:py-4 text-center text-xs xs:text-sm sm:text-base font-semibold text-gray-700">
                    Driving License<br />Front View
                  </th>
                  <th className="px-2 xs:px-3 sm:px-4 py-3 xs:py-4 text-center text-xs xs:text-sm sm:text-base font-semibold text-gray-700">
                    Driving License<br />Back View
                  </th>
                </tr>
              </thead>
              <tbody>
                {documents.map((document, index) => (
                  <tr key={index} className="border-b border-gray-100 last:border-b-0">
                    <td className="px-2 xs:px-3 sm:px-4 py-4 xs:py-5 text-center">
                      <div className="text-xs xs:text-sm sm:text-base text-gray-600 font-medium">{document.customerName}</div>
                    </td>
                    <td className="px-2 xs:px-3 sm:px-4 py-4 xs:py-5 text-center">
                      {document.frontLicenseImage ? (
                        <img 
                          src={document.frontLicenseImage} 
                          alt="Driving License Front"
                          className="w-24 xs:w-28 sm:w-30 h-auto rounded border shadow-sm mx-auto"
                        />
                      ) : (
                        <div className="w-24 xs:w-28 sm:w-30 h-16 xs:h-18 sm:h-20 bg-gradient-to-br from-green-500 to-green-600 rounded flex items-center justify-center text-white text-xs xs:text-sm text-center shadow-sm mx-auto">
                          DRIVING LICENSE<br />FRONT VIEW
                        </div>
                      )}
                    </td>
                    <td className="px-2 xs:px-3 sm:px-4 py-4 xs:py-5 text-center">
                      {document.backLicenseImage ? (
                        <img 
                          src={document.backLicenseImage} 
                          alt="Driving License Back"
                          className="w-24 xs:w-28 sm:w-30 h-auto rounded border shadow-sm mx-auto"
                        />
                      ) : (
                        <div className="w-24 xs:w-28 sm:w-30 h-16 xs:h-18 sm:h-20 bg-gradient-to-br from-gray-600 to-gray-700 rounded flex items-center justify-center text-white text-xs xs:text-sm text-center shadow-sm mx-auto">
                          DRIVING LICENSE<br />BACK VIEW
                        </div>
                      )}
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  );
};