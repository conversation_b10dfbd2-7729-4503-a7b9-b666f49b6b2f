import React, { useState } from 'react';
import { Search, Edit, Wrench, ChevronDown } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';

interface Parts {
  partNo: string;
  partName: string;
  quantity: string;
  vendorName: string;
  mechanicName?: string;
}

export const RequestedParts = () => {
  const [searchTerm, setSearchTerm] = useState('');

  const parts: Parts[] = [
    {
      partNo: 'PN001',
      partName: 'Tyre 205/50-17',
      quantity: '06',
      vendorName: 'ARB Somerton',
      mechanicName: '<PERSON> Doe'
    },
    {
      partNo: 'PN002',
      partName: 'Air Filter',
      quantity: '04',
      vendorName: 'R<PERSON> Somerton',
      mechanicName: '<PERSON>'
    },
    {
      partNo: 'PN003',
      partName: 'Wiper Plates',
      quantity: '02',
      vendorName: 'Melbourne Car Removals',
      mechanicName: ''
    },
  ];

  const navigate = useNavigate();

  const handlePartNoClick = (partNo: string) => {
    navigate(`/admin/workshopMasterAdmin/requested-parts-edit/${partNo}`);
  };

  const filteredParts = parts.filter(
    (parts) =>
      parts.partName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      parts.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      parts.partNo.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center mb-4 sm:mb-0">
            <Wrench className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
            <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Requested Parts</h1>
          </div>
          <Button
            onClick={() => navigate('/admin/workshopMasterAdmin/requested-parts-add')}
            className="px-4 py-2 bg-[#330101] text-white rounded transition-colors w-full sm:w-auto"
          >
            Add New Part
          </Button>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          {/* Filter button - full width on mobile, chevron right aligned */}
          <div className="relative w-full sm:w-auto order-1">
            <button className="flex items-center justify-between gap-2 px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 text-sm w-full sm:w-auto">
              <span>Filter</span>
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
          {/* Search input - full width on mobile */}
          <div className="relative flex-1 order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing a name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>
          {/* Save button */}
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
      </div>

      {/* Desktop Table */}
      <div className="hidden md:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Part No</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Part Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Quantity</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vendor</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Mechanic</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredParts.map((parts) => (
                <TableRow key={parts.partNo} className="hover:bg-gray-50">
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{parts.partNo}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{parts.partName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{parts.quantity}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{parts.vendorName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{parts.mechanicName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    <Button
                      onClick={() => handlePartNoClick(parts.partNo)}
                      variant="ghost"
                      className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                    >
                      <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="md:hidden space-y-4 mb-4">
        {filteredParts.length > 0 ? (
          filteredParts.map((parts) => (
            <Card key={parts.partNo} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
              {/* Card content */}
              <div className="mb-3">
                <span className="text-sm font-medium text-gray-700 break-words whitespace-pre-line">
                  {parts.partName}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Part No</div>
                  <div className="flex items-center text-sm">{parts.partNo}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Quantity</div>
                  <div className="flex items-center text-sm">{parts.quantity}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Vendor</div>
                  <div className="flex items-center text-sm">{parts.vendorName}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Mechanic</div>
                  <div className="flex items-center text-sm">{parts.mechanicName || '-'}</div>
                </div>
              </div>
              {/* Action button at the bottom */}
              <div className="flex justify-end mt-4">
                <Button
                  onClick={() => handlePartNoClick(parts.partNo)}
                  variant="ghost"
                  className="text-gray-600 hover:text-gray-800 p-1"
                >
                  <Edit className="w-4 h-4" />
                  <span className="ml-2 text-xs">Edit</span>
                </Button>
              </div>
            </Card>
          ))
        ) : (
          <div className="text-center text-sm text-gray-500 py-4">No requested parts found.</div>
        )}
      </div>
    </div>
  );
};