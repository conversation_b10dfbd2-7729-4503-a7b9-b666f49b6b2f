import { useState, useMemo } from "react";
import { ServiceManagementAll } from "../type/mechanictype";
import { useNavigate } from "react-router-dom";

const MOCK_DATA: ServiceManagementAll[] = [
  {
    vehicleId: "VID0001",
    vehicle: "Civic Hybrid Sedan - 1PX 1ZR",
    serviceTypes: "General Maintenance",
    status: "Pending",
    vehicleStatus: "",
    dateIn: "28/07/2025", // Example: today
    dateOut: "30/07/2025"
  },
  {
    vehicleId: "VID0002",
    vehicle: "Atom - 1PX 5ZP",
    serviceTypes: "Accident",
    status: "Pending",
    vehicleStatus: "",
    dateIn: "20/07/2025",
    dateOut: "22/07/2025"
  },
  {
    vehicleId: "VID0003",
    vehicle: "BMW 330 d Coupe - 191",
    serviceTypes: "Breakdown",
    status: "InProgress",
    vehicleStatus: "",
    dateIn: "29/07/2025",
    dateOut: "02/08/2025"
  },
  {
    vehicleId: "VID0004",
    vehicle: "Holden CTS H2 Automatic - 181",
    serviceTypes: "Damage",
    status: "Done",
    vehicleStatus: "",
    dateIn: "01/08/2025",
    dateOut: "03/08/2025"
  },
];

// Utility function to check if a date is in this week
function isDateInThisWeek(dateStr: string) {
  if (!dateStr) return false;
  const [day, month, year] = dateStr.split("/");
  const date = new Date(`${year}-${month}-${day}`);
  const now = new Date();
  const startOfWeek = new Date(now);
  startOfWeek.setDate(now.getDate() - now.getDay());
  startOfWeek.setHours(0, 0, 0, 0);
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6);
  endOfWeek.setHours(23, 59, 59, 999);
  return date >= startOfWeek && date <= endOfWeek;
}

export function useServiceManagementAll() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [activeTab, setActiveTab] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const navigate = useNavigate();

  const filteredData = useMemo(() => {
    let data = MOCK_DATA;

    // This is the key part for "Due This Week"
    if (activeTab === "Due This Week") {
      data = data.filter(
        (item) => isDateInThisWeek(item.dateIn) || isDateInThisWeek(item.dateOut)
      );
    } else if (activeTab !== "All") {
      data = data.filter((item) =>
        item.serviceTypes.toLowerCase().includes(activeTab.toLowerCase())
      );
    }

    if (filterStatus !== "All") {
      data = data.filter((item) => item.status === filterStatus);
    }

    if (searchTerm.trim()) {
      data = data.filter(
        (item) =>
          item.vehicleId.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.serviceTypes.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return data;
  }, [searchTerm, filterStatus, activeTab]);

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);

  const currentData = useMemo(() => {
    const start = (currentPage - 1) * recordsPerPage;
    return filteredData.slice(start, start + recordsPerPage);
  }, [filteredData, currentPage, recordsPerPage]);

  const handleRowClick = (vehicleId: string) => {
    navigate(`/mechanic/service-management-all-view/${vehicleId}`);
  };

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    activeTab,
    setActiveTab,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    filteredData,
    totalPages,
    totalEntries,
    currentData,
    handleRowClick,
  };
}

export function ServiceManagementAllViewPage() {
  // Component logic for viewing a single service management entry
}