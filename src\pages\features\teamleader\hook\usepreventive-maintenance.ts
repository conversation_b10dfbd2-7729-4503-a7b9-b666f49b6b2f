import { NavigateFunction } from 'react-router-dom';
import { PreventiveRecord } from '../type/teamleadertype';

export const handleSearchTermChange = (
  value: string,
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>
): void => {
  setSearchTerm(value);
};

export const handleRecordsPerPageChange = (
  records: number,
  setRecordsPerPage: React.Dispatch<React.SetStateAction<number>>,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>
): void => {
  setRecordsPerPage(records);
  setCurrentPage(1);
};

export const handlePageChange = (
  page: number,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>
): void => {
  setCurrentPage(page);
};

export const handleVehicleIdClick = (
  vehicleId: string,
  navigate: NavigateFunction
): void => {
  navigate(`/teamleader/maintenance-history/${vehicleId}`);
};

export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'upcoming':
      return 'bg-gray-500 text-white';
    case 'delay':
      return 'bg-red-500 text-white';
    case 'overdue':
      return 'bg-yellow-500 text-white';
    default:
      return 'bg-gray-200 text-gray-800';
  }
};