import { useState } from "react";
import { JobCardAccidentRepairData } from "../type/mechanictype";

const mockJobCards: JobCardAccidentRepairData[] = [
  {
    id: "1",
    rego: "ABC123",
    vehicle: "Isuzu - DMS 101",
    damageDescription: "Front bumper dented",
    accidentDate: "2025-07-01",
    damageParts: "Front bumper",
    repairTasks: "Replace bumper",
    repairedBy: "John Doe",
    repairCompletionDate: "2025-07-10",
  },
  {
    id: "2",
    rego: "XYZ789",
    vehicle: "Toyota - DMS 202",
    damageDescription: "Left door scratched",
    accidentDate: "2025-06-15",
    damageParts: "Left door",
    repairTasks: "Paint and polish",
    repairedBy: "<PERSON>",
    repairCompletionDate: "2025-06-25",
  },
];

export function useJobcardDamage() {
  const [jobCards] = useState(mockJobCards);
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);

  const totalRecords = jobCards.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);

  return {
    jobCards,
    currentPage,
    recordsPerPage,
    totalPages,
    totalRecords,
    setCurrentPage,
    setRecordsPerPage,
  };
}