import { NavigateFunction } from 'react-router-dom';

export const handleGoBack = (rentalId: string, navigate: NavigateFunction): void => {
  const cleanId = rentalId.replace('#', '');
  navigate(`/reception/reception-reservantionmanagement/reception-bookingsummary/${cleanId}`);
};

export const handleCancel = (rentalId: string, navigate: NavigateFunction): void => {
  const cleanId = rentalId.replace('#', '');
  navigate(`/reception/reception-reservantionmanagement/reception-bookingsummary/${cleanId}`);
};

export const handleSubmit = (rentalId: string, navigate: NavigateFunction): void => {
  const cleanId = rentalId.replace('#', '');
  navigate(`/reception/reception-reservantionmanagement/reception-bookingsummary/${cleanId}`);
};

export const handleImageView = (
  imageSrc: string,
  title: string,
  setSelectedImage: React.Dispatch<React.SetStateAction<{ src: string; title: string } | null>>
): void => {
  setSelectedImage({ src: imageSrc, title });
};

export const closeModal = (
  setSelectedImage: React.Dispatch<React.SetStateAction<{ src: string; title: string } | null>>
): void => {
  setSelectedImage(null);
};