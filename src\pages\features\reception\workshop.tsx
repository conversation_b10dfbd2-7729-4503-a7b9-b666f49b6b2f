import React, { useState, useMemo } from 'react';
import { <PERSON>ting<PERSON>, Search, Eye, Edit, ChevronDown, Wrench } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card, CardContent } from '@/components/ui/card';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';

// Interface for data items
interface DataItem {
  id: number | string;
  vehicleID?: string;
  serviceTypes?: string;
  totalInParts?: string;
  totalInLabor?: string;
  incidentDate?: string;
  vehicleClass?: string;
  vehicleRego?: string;
  reportType?: string;
  vehicle?: string;
  maintenanceType?: string;
  vehicleCurrentRenter?: string;
  vehicleStatus?: string;
  nextService?: string;
  rego?: string;
  make?: string;
  model?: string;
  status?: string;
  incident?: string;
  location?: string;
  insuranceClaimed?: string;
  insuranceCompany?: string;
  claimedNumber?: string;
  damageType?: string;
  severity?: string;
  estimatedCost?: string;
}

// Mock data
const mockData: Record<string, DataItem[]> = {
  all: [
    {
      id: 1,
      vehicleID: "VID0001",
      vehicle: 'Civic Hybrid Sedan - 1PX 1ZR',
      serviceTypes: 'General Maintenance',
      totalInParts: 'AUD 0.00',
      totalInLabor: 'AUD 0.00',
      status: 'Pending'
    },
    {
      id: 2,
      vehicleID: 'VID0002',
      vehicle: 'Atom - 1PX 5ZP',
      serviceTypes: 'Accident',
      totalInParts: 'AUD 60.00',
      totalInLabor: 'AUD 0.00',
      status: 'Pending'
    },
    {
      id: 3,
      vehicleID: 'VID0003',
      vehicle: 'BMW 330 d Coupe - 191',
      serviceTypes: 'Breakdown',
      totalInParts: 'AUD 120.00',
      totalInLabor: 'AUD 100.0',
      status: 'In-Progress'
    },
    {
      id: 4,
      vehicleID: 'VID0004',
      vehicle: 'Holden CTS H2 Automatic - 181',
      serviceTypes: 'Damage',
      totalInParts: 'AUD 80.00',
      totalInLabor: 'AUD 140.00',
      status: 'Done'
    }
  ],
  generalMaintenance: [
    {
      id: 'GM0001',
      vehicle: 'Toyota Corolla - T71 230',
      maintenanceType: 'General Maintenance every 5000 km',
      vehicleCurrentRenter:'Not Assigned',
      status: 'Pending',
      vehicleStatus:'Dirty'
    },
    {
      id: 'GM0002',
      vehicle: 'Toyota Hiace - AZR 207',
      maintenanceType: 'General Maintenance every 2500 km',
      vehicleCurrentRenter:'Pradeep',
      status: 'In-Progress',
      vehicleStatus:'Dirty'
    },
    {
      id: 'GM0003',
      vehicle: 'Honda Civic - QW5 P91',
      maintenanceType: 'General Maintenance every 5000 km',
      vehicleCurrentRenter:'Not Assigned',
      status: 'Done',
      vehicleStatus:'Cleaned'
    }
  ],
  accident: [
    {
      id: 1,
      incidentDate: '03/05/2025',
      vehicle: 'Isuzu - AFS 009',
      status: 'Initial Review'
    },
    {
      id: 2,
      incidentDate: '25/04/2025',
      vehicle: 'Isuzu - AAB 004',
      status: 'Insurance Accepted'
    },
    {
      id: 3,
      incidentDate: '10/03/2025',
      vehicle: 'Toyota - BCS 903',
      status: 'Insurance Declined'
    }
  ],
  breakdown: [
    {
      id: 'B001',
      vehicle: 'Toyota Corolla-T71 230',
      incident: 'Engine Failure',
      location: 'Colombo',
      insuranceClaimed: 'Yes',
      insuranceCompany: 'AIA Insurance',
      claimedNumber: 'CL2025001'
    },
    {
      id: 'B002',
      vehicle: 'Toyota Hiace-AZR 207',
      incident: 'Transmission Issue',
      location: 'Galle Road',
      insuranceClaimed: 'No',
      insuranceCompany: '-',
      claimedNumber: '-'
    },
    {
      id: 'B003',
      vehicle: 'Honda Civic-QW5 P91',
      incident: 'Brake Failure',
      location: 'Kandy Road',
      insuranceClaimed: 'Yes',
      insuranceCompany: 'Ceylinco Insurance',
      claimedNumber: 'CL2025002'
    }
  ],
  damage: [
    {
      id: 'D001',
      vehicle: 'Toyota Corolla-T71 230',
      damageType: 'Exterior Damage',
      severity: 'Minor',
      estimatedCost: 'Rs. 25,000',
      status: 'Pending Assessment'
    },
    {
      id: 'D002',
      vehicle: 'Toyota Hiace-AZR 207',
      damageType: 'Interior Damage',
      severity: 'Major',
      estimatedCost: 'Rs. 150,000',
      status: 'Under Repair'
    },
    {
      id: 'D003',
      vehicle: 'Honda Civic-QW5 P91',
      damageType: 'Mechanical Damage',
      severity: 'Severe',
      estimatedCost: 'Rs. 300,000',
      status: 'Completed'
    }
  ]
};

const filterOptions = [
  { value: 'all', label: 'All' },
  { value: 'generalMaintenance', label: 'General Maintenance' },
  { value: 'accident', label: 'Accident' },
  { value: 'breakdown', label: 'Breakdown' },
  { value: 'damage', label: 'Damage' }
];

export function ReceptionWorkshop() {
  const [selectedFilter, setSelectedFilter] = useState<string>('all');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const navigate = useNavigate();

  const getStatusBadgeColor = (status: string): string => {
    switch (status) {
      case 'Initial Review':
      case 'Pending Assessment':
        return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Insurance Accepted':
      case 'Completed':
        return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Insurance Declined':
        return 'bg-red-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Under Repair':
        return 'bg-orange-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Done':
        return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Pending':
        return 'bg-gray-400 text-white px-3 py-1 rounded text-sm font-medium';
      case 'In-Progress':
        return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Dirty':
        return 'bg-red-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Cleaned':
        return 'bg-yellow-500 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };

  const filteredData = useMemo(() => {
    const data = mockData[selectedFilter as keyof typeof mockData] || mockData.all;
    if (!searchTerm) return data;

    const lowerSearchTerm = searchTerm.toLowerCase();
    return data.filter((item: DataItem) => {
      switch (selectedFilter) {
        case 'all':
          return (
            item.status?.toLowerCase().includes(lowerSearchTerm) ||
            item.vehicleID?.toLowerCase().includes(lowerSearchTerm) ||
            item.serviceTypes?.toLowerCase().includes(lowerSearchTerm) ||
            item.vehicle?.toLowerCase().includes(lowerSearchTerm)
          );
        case 'generalMaintenance': {
          const rego = item.vehicle?.split('-')[1]?.trim();
          return (
            item.id.toString().toLowerCase().includes(lowerSearchTerm) ||
            item.vehicle?.toLowerCase().includes(lowerSearchTerm) ||
            rego?.toLowerCase().includes(lowerSearchTerm) ||
            item.maintenanceType?.toLowerCase().includes(lowerSearchTerm) ||
            item.vehicleCurrentRenter?.toLowerCase().includes(lowerSearchTerm)||
            item.vehicleStatus?.toLowerCase().includes(lowerSearchTerm) ||
            item.status?.toLowerCase().includes(lowerSearchTerm)
          );
        }
        case 'accident':
          return (
            item.rego?.toLowerCase().includes(lowerSearchTerm) ||
            item.make?.toLowerCase().includes(lowerSearchTerm) ||
            item.model?.toLowerCase().includes(lowerSearchTerm) ||
            item.status?.toLowerCase().includes(lowerSearchTerm)
          );
        case 'breakdown':
          return (
            item.id.toString().toLowerCase().includes(lowerSearchTerm) ||
            item.rego?.toLowerCase().includes(lowerSearchTerm) ||
            item.vehicle?.toLowerCase().includes(lowerSearchTerm) ||
            item.incident?.toLowerCase().includes(lowerSearchTerm) ||
            item.insuranceClaimed?.toLowerCase().includes(lowerSearchTerm) ||
            item.insuranceCompany?.toLowerCase().includes(lowerSearchTerm)
          );
        case 'damage':
          return (
            item.id.toString().toLowerCase().includes(lowerSearchTerm) ||
            item.rego?.toLowerCase().includes(lowerSearchTerm) ||
            item.vehicle?.toLowerCase().includes(lowerSearchTerm) ||
            item.damageType?.toLowerCase().includes(lowerSearchTerm) ||
            item.severity?.toLowerCase().includes(lowerSearchTerm) ||
            item.status?.toLowerCase().includes(lowerSearchTerm)
          );
        default:
          return true;
      }
    });
  }, [selectedFilter, searchTerm]);

  const totalRecords = filteredData.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const paginatedData = useMemo(() => {
    const startIndex = (currentPage - 1) * recordsPerPage;
    return filteredData.slice(startIndex, startIndex + recordsPerPage);
  }, [filteredData, currentPage, recordsPerPage]);

  const renderTableHeaders = () => {
    switch (selectedFilter) {
      case 'all':
        return (
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Vehicle</TableHead>
            <TableHead>Service Types</TableHead>
            <TableHead>Total in Parts</TableHead>
            <TableHead>Total in Labor</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        );
      case 'generalMaintenance':
        return (
          <TableRow>
            <TableHead>Vehicle</TableHead>
            <TableHead>Maintenance Type</TableHead>
            <TableHead>Current Renter</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Vehicle Status</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        );
      case 'accident':
        return (
          <TableRow>
            <TableHead>Incident Date</TableHead>
            <TableHead>Vehicle</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        );
      case 'breakdown':
        return (
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Vehicle</TableHead>
            <TableHead>Incident</TableHead>
            <TableHead>Location</TableHead>
            <TableHead>Insurance Claimed</TableHead>
            <TableHead>Insurance Company</TableHead>
            <TableHead>Claimed Number</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        );
      case 'damage':
        return (
          <TableRow>
            <TableHead>ID</TableHead>
            <TableHead>Vehicle</TableHead>
            <TableHead>Damage Type</TableHead>
            <TableHead>Severity</TableHead>
            <TableHead>Estimated Cost</TableHead>
            <TableHead>Status</TableHead>
            <TableHead>Action</TableHead>
          </TableRow>
        );
      default:
        return null;
    }
  };

  const renderTableRows = () => {
    return paginatedData.map((item: DataItem) => (
      <TableRow key={item.id}>
        {selectedFilter === 'all' && (
          <>
            <TableCell>
              <span
                className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                onClick={() => navigate('/teamleader/service-management-edit')}
              >
                {item.vehicleID}
              </span>
            </TableCell>
            <TableCell>{item.vehicle}</TableCell>
            <TableCell>{item.serviceTypes}</TableCell>
            <TableCell>{item.totalInParts}</TableCell>
            <TableCell>{item.totalInLabor}</TableCell>
            <TableCell>
              <span className={`inline-flex px-2 py-1 text-xs font-semibold rounded ${getStatusBadgeColor(item.status || '')}`}>
                {item.status}
              </span> 
            </TableCell>
            <TableCell className="px-3 py-4">
              <Button
                onClick={() => navigate('/reception/reception-workshops/all')}
                variant="ghost"
                size="sm"
                className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
              >
                <Eye className="w-4 h-4" />
              </Button>
            </TableCell>
          </>
        )}
        {selectedFilter === 'generalMaintenance' && (
          <>
            <TableCell>{item.vehicle}</TableCell>
            <TableCell>{item.maintenanceType}</TableCell>
            <TableCell>{item.vehicleCurrentRenter}</TableCell>
            <TableCell>
              <span className={getStatusBadgeColor(item.status || '')}>
                {item.status}
              </span>
            </TableCell>
            <TableCell>
              <span className={getStatusBadgeColor(item.vehicleStatus || '')}>
                {item.vehicleStatus}
              </span>
            </TableCell>
            <TableCell>
              <div className="flex items-center">
                <Button variant="ghost" onClick={() => navigate(`edit-maintenance/${item.id}`)}>
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </>
        )}
        {selectedFilter === 'accident' && (
          <>
            <TableCell>{item.incidentDate}</TableCell>
            <TableCell>{item.vehicle}</TableCell>
            <TableCell>
              <span className={getStatusBadgeColor(item.status || '')}>
                {item.status}
              </span>
            </TableCell>
            <TableCell>
              <div className="flex items-center">
                <Button variant="ghost" onClick={()=> navigate(`edit-accident/${item.id}`)}>
                  <Edit className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </>
        )}
        {selectedFilter === 'breakdown' && (
          <>
            <TableCell>{item.id}</TableCell>
            <TableCell>{item.vehicle}</TableCell>
            <TableCell>{item.incident}</TableCell>
            <TableCell>{item.location}</TableCell>
            <TableCell>
              <span className={`px-3 py-1 rounded text-sm font-medium ${
                item.insuranceClaimed === 'Yes' ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800'
              }`}>
                {item.insuranceClaimed}
              </span>
            </TableCell>
            <TableCell>{item.insuranceCompany}</TableCell>
            <TableCell>{item.claimedNumber}</TableCell>
            <TableCell>
              <div className="flex items-center">
                <Button variant="ghost" onClick={()=> navigate(`edit-breakdown/${item.id}`)} >
                  <Edit className="w-4 h-4" />
                </Button>
                <Button variant="ghost">
                  <Eye className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </>
        )}
        {selectedFilter === 'damage' && (
          <>
            <TableCell>{item.id}</TableCell>
            <TableCell>{item.vehicle}</TableCell>
            <TableCell>{item.damageType}</TableCell>
            <TableCell>
              <span className={`px-2 py-1 rounded-full text-xs font-medium ${
                item.severity === 'Minor' ? 'bg-yellow-100 text-yellow-800' :
                item.severity === 'Major' ? 'bg-orange-100 text-orange-800' :
                'bg-red-100 text-red-800'
              }`}>
                {item.severity}
              </span>
            </TableCell>
            <TableCell>{item.estimatedCost}</TableCell>
            <TableCell>
              <span className={getStatusBadgeColor(item.status || '')}>
                {item.status}
              </span>
            </TableCell>
            <TableCell>
              <div className="flex items-center">
                <Button variant="ghost">
                  <Edit className="w-4 h-4" />
                </Button>
                <Button variant="ghost">
                  <Eye className="w-4 h-4" />
                </Button>
              </div>
            </TableCell>
          </>
        )}
      </TableRow>
    ));
  };

  const getSearchPlaceholder = () => {
    switch (selectedFilter) {
      case 'all':
        return 'Search by date, class, rego, or type...';
      case 'generalMaintenance':
        return 'Search by ID, class, rego, or maintenance type...';
      case 'accident':
        return 'Search by rego, make, model, or status...';
      case 'breakdown':
        return 'Search by ID, rego, vehicle, incident, claim, or insurance...';
      case 'damage':
        return 'Search by ID, rego, vehicle, damage type, severity, or status...';
      default:
        return 'Search...';
    }
  };

  return (
    <div className="min-h-screen">
      <div className="flex">
        <div className="flex-1 p-6">
          <div className="flex items-center gap-2 mb-6">
            <Wrench className="w-6 h-6" />
            <h1 className="text-2xl font-semibold">Workshop</h1>
          </div>

          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
            <div className="relative w-full sm:w-auto">
              <Select value={selectedFilter} onValueChange={setSelectedFilter}>
                <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
                  <SelectValue />
                </SelectTrigger>
                <SelectContent>
                  {filterOptions.map((option) => (
                    <SelectItem key={option.value} value={option.value}>
                      {option.label}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="flex-1 relative">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Input
                placeholder={getSearchPlaceholder()}
                value={searchTerm}
                onChange={(e) => {
                  setSearchTerm(e.target.value);
                  setCurrentPage(1);
                }}
                className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
              />
            </div>

            <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
              Save this search
            </Button>
          </div>

          <Card>
            <CardContent className="p-0">
              <div className="overflow-x-auto">
                <Table>
                  <TableHeader className="bg-gray-50 uppercase">
                    {renderTableHeaders()}
                  </TableHeader>
                  <TableBody>
                    {renderTableRows()}
                  </TableBody>
                </Table>
              </div>
            </CardContent>
          </Card>

          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="mt-6"
          />
        </div>
      </div>
    </div>
  );
}