import React, { useState, useMemo } from 'react';
import { Search, UserCheck } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Pagination } from '@/components/layout/Pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card } from '@/components/ui/card';

interface Teamleader {
  id: string;
  name: string;
  phone: string;
  address: string;
  enabled: boolean;
}

export const Teamleaders = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const teamleaders: Teamleader[] = [
    { id: 'TL006', name: '<PERSON>', phone: '0421 452 953', address: '147 Birch Way, Southside', enabled: false },
    { id: 'TL005', name: 'Jessica Taylor', phone: '0421 789 903', address: '258 Walnut St, Central', enabled: true },
    { id: 'TL004', name: 'Mike Smith', phone: '0421 589 200', address: '789 Pine Rd, Uptown', enabled: false },
    { id: 'TL003', name: 'Emily Davis', phone: '0421 550 126', address: '321 Elm St, Westside', enabled: true },
    { id: 'TL002', name: 'Robert Brown', phone: '0421 439 002', address: '654 Maple Dr, Eastside', enabled: true },
    { id: 'TL001', name: 'Lisa Noe', phone: '0421 500 321', address: '987 Cedar Ln, Northside', enabled: false },
  ];

  const filteredTeamleaders = useMemo(() => {
    return teamleaders
      .filter((teamleader) =>
        teamleader.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
        teamleader.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
        teamleader.address.toLowerCase().includes(searchTerm.toLowerCase())
      )
      .filter((teamleader) =>
        filterStatus === 'All' ? true : teamleader.enabled === (filterStatus === 'Yes')
      );
  }, [searchTerm, filterStatus]);

  const totalEntries = filteredTeamleaders.length;
  const paginatedTeamleaders = filteredTeamleaders.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  );

  // Helper for status badge color
  const getStatusColor = (enabled: boolean) =>
    enabled ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800';

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center mb-4 sm:mb-0">
            <UserCheck className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
            <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Team Leaders</h1>
          </div>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          {/* On mobile: All filter first, then search input, then Save button */}
          <div className="relative w-full sm:w-auto order-1">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-gray-300">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All</SelectItem>
                <SelectItem value="Yes">Enabled</SelectItem>
                <SelectItem value="No">Disabled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="relative flex-1 order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing a name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md border-gray-200 focus:border-gray-300 text-sm sm:text-base"
            />
          </div>
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
      </div>

      {/* Table for Desktop */}
      <div className="hidden md:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Full Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Phone Number</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Address</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Enabled</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedTeamleaders.length > 0 ? (
                paginatedTeamleaders.map((teamleader) => (
                  <TableRow key={teamleader.id} className="hover:bg-gray-50">
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{teamleader.name}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{teamleader.phone}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{teamleader.address}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-medium rounded w-[90px] justify-center ${getStatusColor(teamleader.enabled)}`}
                      >
                        {teamleader.enabled ? 'Yes' : 'No'}
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center text-sm text-gray-500 py-4">
                    No team leaders found
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="md:hidden space-y-4 mb-4">
        {filteredTeamleaders.length > 0 ? (
          filteredTeamleaders.map((teamleader) => (
            <Card key={teamleader.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
              {/* Status badge - top right, fixed width */}
              <div className="absolute top-4 right-4 flex flex-col items-end gap-2">
                <span
                  className={`inline-flex items-center justify-center rounded px-3 py-1 text-xs font-semibold w-[90px] text-center ${getStatusColor(
                    teamleader.enabled
                  )} whitespace-nowrap`}
                >
                  {teamleader.enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              {/* Card content */}
              <div className="mb-3 pr-[100px]">
                <span className="text-sm font-medium text-gray-700 break-words whitespace-pre-line">
                  {teamleader.name}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Phone Number</div>
                  <div className="flex items-center text-sm">{teamleader.phone}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Address</div>
                  <div className="flex items-center text-sm">{teamleader.address}</div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          <div className="text-center text-sm text-gray-500 py-4">No team leaders found</div>
        )}
      </div>

      {/* Pagination only for md and up */}
      <div className="mt-4 sm:mt-6 hidden md:block">
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalEntries / entriesPerPage)}
          totalRecords={totalEntries}
          recordsPerPage={entriesPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setEntriesPerPage(records);
            setCurrentPage(1);
          }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4"
        />
      </div>
    </div>
  );
};