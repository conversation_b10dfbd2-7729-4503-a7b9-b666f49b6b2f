import React, { useState, useCallback } from 'react';
import { useNavigate } from 'react-router-dom';
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Calendar } from '@/components/ui/calendar';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

interface Reservation {
  rego: string;
  rentalId: string;
  pickupDate: Date;
  returnDate: Date;
}

interface RentalDetails {
  phoneNumber: string;
  email: string;
  customerName: string;
  agreementNo: string;
}

interface AccidentReportFields {
  payment: string;
  insuranceExcessCover: string;
  driverName: string;
  phoneNumber: string;
  email: string;
  birthday: Date | undefined;
  address: string;
  postcode: string;
  country: string;
  drugsAlcoholIncident: string;
  licenseNumber: string;
  issueDate: Date | undefined;
  expiryDate: Date | undefined;
  conditions: string;
  frontView: File | null;
  backView: File | null;
  nextDue: Date | undefined;
  obtainDetails: string;
  isDrugsAlcoholConsumed: boolean | undefined;
}

interface AdditionalAccidentFields {
  accidentLocation: string;
  damageDescription: string;
  policeReport: File | null;
  witnessDetails: string;
}

interface BreakdownFields {
  interiorImages: File | null;
  exteriorImages: File | null;
}

interface FormData {
  rego: string;
  rentalId: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  customerName: string;
  email: string;
  phoneNumber: string;
  agreementNo: string;
  reportType: string;
  replacementRequested: string;
  vehicleDamaged: string;
  payingExcess: string;
  estimatedAmount: string;
  actualPaid: string;
  totalAmount: string;
  escalateToManager: string;
}

export default function IncidentReportingEditPage() {
  const navigate = useNavigate();
  const [rego, setRego] = useState<string>('');
  const [rentalId, setRentalId] = useState<string>('');
  const [pickupDate, setPickupDate] = useState<Date | undefined>(undefined);
  const [returnDate, setReturnDate] = useState<Date | undefined>(undefined);
  const [agreementNo, setAgreementNo] = useState<string>('');
  const [reportType, setReportType] = useState<string>('');
  const [showPickupCalendar, setShowPickupCalendar] = useState(false);
  const [showReturnCalendar, setShowReturnCalendar] = useState(false);
  const [showAdditionalAccidentForm, setShowAdditionalAccidentForm] = useState(false);

  const [formData, setFormData] = useState<FormData>({
    rego: '',
    rentalId: '',
    pickupDate: '',
    pickupTime: '',
    returnDate: '',
    returnTime: '',
    customerName: '',
    email: '',
    phoneNumber: '',
    agreementNo: '',
    reportType: '',
    replacementRequested: '',
    vehicleDamaged: '',
    payingExcess: '',
    estimatedAmount: '',
    actualPaid: '',
    totalAmount: '',
    escalateToManager: '',
  });

  const [accidentFields, setAccidentFields] = useState<AccidentReportFields>({
    payment: '',
    insuranceExcessCover: '',
    driverName: '',
    phoneNumber: '',
    email: '',
    birthday: undefined,
    address: '',
    postcode: '',
    country: '',
    drugsAlcoholIncident: '',
    licenseNumber: '',
    issueDate: undefined,
    expiryDate: undefined,
    conditions: '',
    frontView: null,
    backView: null,
    nextDue: undefined,
    obtainDetails: '',
    isDrugsAlcoholConsumed: undefined,
  });

  const [additionalAccidentFields, setAdditionalAccidentFields] = useState<AdditionalAccidentFields>({
    accidentLocation: '',
    damageDescription: '',
    policeReport: null,
    witnessDetails: '',
  });

  const [breakdownFields, setBreakdownFields] = useState<BreakdownFields>({
    interiorImages: null,
    exteriorImages: null,
  });

  // Dummy reservation data
  const dummyReservations: Reservation[] = [
    { rego: 'SQA 758', rentalId: '#0145', pickupDate: new Date('2025-05-14T14:45:00'), returnDate: new Date('2025-05-20T14:45:00') },
    { rego: 'ABC 123', rentalId: '#0146', pickupDate: new Date('2025-06-01T09:00:00'), returnDate: new Date('2025-06-07T09:00:00') },
    { rego: 'XYZ 789', rentalId: '#0147', pickupDate: new Date('2025-07-01T12:00:00'), returnDate: new Date('2025-07-08T12:00:00') },
  ];

  // Dummy renter details (for demonstration)
  const dummyRentalDetails: RentalDetails[] = [
    { phoneNumber: '0421 800 566', email: '<EMAIL>', customerName: 'John Doe', agreementNo: 'AGR001' },
    { phoneNumber: '0421 322 400', email: '<EMAIL>', customerName: 'Peter Smith', agreementNo: 'AGR002' },
  ];

  // Handle Rego selection
  const handleRegoChange = useCallback((selectedRego: string) => {
    setRego(selectedRego);
    const reservation = dummyReservations.find((r) => r.rego === selectedRego); 
    if (reservation) {
      setRentalId(reservation.rentalId);
      setPickupDate(reservation.pickupDate);
      setReturnDate(reservation.returnDate);
      setFormData((prev) => ({
        ...prev,
        rego: selectedRego,
        rentalId: reservation.rentalId,
        pickupDate: reservation.pickupDate.toLocaleDateString(),
        returnDate: reservation.returnDate.toLocaleDateString(),
      }));
    }
  }, []);

  // Handle Phone Number change and fetch renter details
  const handlePhoneNumberChange = useCallback((phoneNumber: string) => {
    setFormData((prev) => ({ ...prev, phoneNumber }));
    const rentalDetails = dummyRentalDetails.find((r) => r.phoneNumber === phoneNumber);
    if (rentalDetails) {
      setFormData((prev) => ({
        ...prev,
        customerName: rentalDetails.customerName,
        email: rentalDetails.email,
        agreementNo: rentalDetails.agreementNo,
        phoneNumber: phoneNumber,
      }));
      setAgreementNo(rentalDetails.agreementNo);
    }
  }, []);

  // Handle Report Type change
  const handleReportTypeChange = useCallback((value: string) => {
    setReportType(value);
    setShowAdditionalAccidentForm(false);
    setAccidentFields({
      payment: '',
      insuranceExcessCover: '',
      driverName: '',
      phoneNumber: '',
      email: '',
      birthday: undefined,
      address: '',
      postcode: '',
      country: '',
      drugsAlcoholIncident: '',
      licenseNumber: '',
      issueDate: undefined,
      expiryDate: undefined,
      conditions: '',
      frontView: null,
      backView: null,
      nextDue: undefined,
      obtainDetails: '',
      isDrugsAlcoholConsumed: undefined,
    });
    setAdditionalAccidentFields({
      accidentLocation: '',
      damageDescription: '',
      policeReport: null,
      witnessDetails: '',
    });
    setBreakdownFields({
      interiorImages: null,
      exteriorImages: null,
    });

    if (value === 'Accident Repair') {
      setAccidentFields((prev) => ({
        ...prev,
        payment: 'No',
        insuranceExcessCover: 'Yes',
        driverName: formData.customerName || '',
        phoneNumber: formData.phoneNumber || '',
        email: formData.email || '',
        birthday: undefined,
        address: '',
        postcode: '',
        country: '',
        licenseNumber: '',
        issueDate: undefined,
        expiryDate: undefined,
        conditions: 'None',
      }));
    }
  }, [formData]);

  // Handle file uploads with validation
  const handleFileChange = useCallback((field: keyof AccidentReportFields | keyof BreakdownFields | keyof AdditionalAccidentFields, event: React.ChangeEvent<HTMLInputElement>, targetState: any, setTargetState: React.Dispatch<any>) => {
    const file = event.target.files?.[0];
    if (file && file.type.startsWith('image/') && file.size < 5 * 1024 * 1024) { // Max 5MB
      setTargetState((prev: any) => ({ ...prev, [field]: file }));
    } else {
      alert('Please upload an image file smaller than 5MB.');
    }
  }, []);

  // Handle drugs/alcohol radio button change
  const handleDrugsAlcoholChange = useCallback((value: boolean) => {
    setAccidentFields((prev) => ({
      ...prev,
      isDrugsAlcoholConsumed: value,
      payment: value ? 'Yes' : '',
      insuranceExcessCover: value ? 'Yes' : '',
    }));
  }, []);

  // Handle form input changes
  const handleInputChange = useCallback((field: keyof FormData, value: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  }, []);

  // Handle accident field changes
  const handleAccidentFieldChange = useCallback((field: keyof AccidentReportFields, value: string | Date | undefined) => {
    setAccidentFields((prev) => ({ ...prev, [field]: value }));
  }, []);

  // Handle additional accident field changes
  const handleAdditionalAccidentFieldChange = useCallback((field: keyof AdditionalAccidentFields, value: string) => {
    setAdditionalAccidentFields((prev) => ({ ...prev, [field]: value }));
  }, []);

  // Form submission with basic validation
  const handleSubmit = useCallback(async () => {
    if (!rego || !rentalId || !reportType) {
      alert('Please fill all required fields: Rego, Rental ID, and Report Type.');
      return;
    }
    if (reportType === 'Accident Repair' && (!accidentFields.driverName || !accidentFields.phoneNumber || !accidentFields.email)) {
      alert('Please fill all required driver details.');
      return;
    }
    console.log('Incident Report submitted:', { rego, rentalId, pickupDate, returnDate, agreementNo, reportType, formData, accidentFields, additionalAccidentFields, breakdownFields });
    
    navigate('/reception/reception-incidentReporting');
  }, [rego, rentalId, reportType, formData, accidentFields, additionalAccidentFields, breakdownFields, navigate]);

   const handleBack = () => {
    navigate('/teamleader/incidentReporting');
  };

  const handleNext = () => {
    navigate('/teamleader/accidentForm');
  };

  return (
    <div className="p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex items-center mb-6">
        <Button size="default" className="bg-[#330101] text-white px-4 py-2 rounded" onClick={handleBack}>
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go Back
        </Button>
      </div>

      {/* Form */}
      <div className="max-w-4xl">
        {/* Vehicle Details Section */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Vehicle Details</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            <div className="relative">
              <Select value={rego} onValueChange={handleRegoChange}>
                <SelectTrigger className="w-full h-[48px] justify-between border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-transparent">
                  <SelectValue placeholder="Rego" />
                </SelectTrigger>
                <SelectContent>
                  {dummyReservations.map((reservation) => (
                    <SelectItem key={reservation.rego} value={reservation.rego}>
                      {reservation.rego}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
              <Label htmlFor="rego" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Rego*
              </Label>
            </div>

            <div className="relative">
              <Input
                type="text"
                id="rentalId"
                value={rentalId}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                required
              />
              <Label htmlFor="rentalId" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Rental ID*
              </Label>
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                type="text"
                id="pickupDate"
                value={pickupDate ? pickupDate.toLocaleString('en-US', { dateStyle: 'medium', timeStyle: 'short' }) : ''}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                required
                onClick={() => setShowPickupCalendar(true)}
              />
              <Label htmlFor="pickupDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Pickup Date*
              </Label>
              {showPickupCalendar && (
                <div className="mt-2">
                  <Calendar
                    mode="single"
                    selected={pickupDate}
                    onSelect={(date) => {
                      setPickupDate(date);
                      setFormData((prev) => ({ ...prev, pickupDate: date?.toLocaleDateString() || '' }));
                      setShowPickupCalendar(false);
                    }}
                    onClickOutside={() => setShowPickupCalendar(false)}
                    className="border rounded-md p-2"
                    initialFocus
                  />
                </div>
              )}
            </div>

            <div className="relative">
              <Input
                type="text"
                id="returnDate"
                value={returnDate ? returnDate.toLocaleString('en-US', { dateStyle: 'medium', timeStyle: 'short' }) : ''}
                readOnly
                className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                required
                onClick={() => setShowReturnCalendar(true)}
              />
              <Label htmlFor="returnDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                Return Date*
              </Label>
              {showReturnCalendar && (
                <div className="mt-2">
                  <Calendar
                    mode="single"
                    selected={returnDate}
                    onSelect={(date) => {
                      setReturnDate(date);
                      setFormData((prev) => ({ ...prev, returnDate: date?.toLocaleDateString() || '' }));
                      setShowReturnCalendar(false);
                    }}
                    onClickOutside={() => setShowReturnCalendar(false)}
                    className="border rounded-md p-2"
                    initialFocus
                  />
                </div>
              )}
            </div>
          </div>
        </div>

        {/* Renter Details Section */}
        <div className="mb-8">
          <h2 className="text-lg font-semibold mb-4">Renter Details</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-6">
            <div className="space-y-6">
              <div className="relative">
                <Input
                  type="tel"
                  id="phoneNumber"
                  value={formData.phoneNumber}
                  onChange={(e) => handlePhoneNumberChange(e.target.value)}
                  className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  placeholder="Enter Phone Number"
                  required
                />
                <Label className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Phone Number*
                </Label>
              </div>

              <div className="relative">
                <Input
                  type="text"
                  id="customerName"
                  value={formData.customerName}
                  onChange={(e) => handleInputChange('customerName', e.target.value)}
                  className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                />
                <Label className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Customer Name*
                </Label>
              </div>

              <div className="relative">
                <Select value={reportType} onValueChange={handleReportTypeChange}>
                  <SelectTrigger className="w-full h-[48px] justify-between border border-gray-500 rounded px-3 py-2 focus:outline-none focus:border-transparent">
                    <SelectValue placeholder="Report Type" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="Accident Repair">Accident</SelectItem>
                    <SelectItem value="Breakdown Service">Breakdown Service</SelectItem>
                    <SelectItem value="Regular Maintenance">Regular Maintenance</SelectItem>
                    <SelectItem value="Damage">Damage</SelectItem>
                  </SelectContent>
                </Select>
                <Label htmlFor="reportType" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Report Type*
                </Label>
              </div>
            </div>

            <div className="space-y-6">
              <div className="relative">
                <Input
                  type="email"
                  id="email"
                  value={formData.email}
                  onChange={(e) => handleInputChange('email', e.target.value)}
                  className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                />
                <Label className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Email*
                </Label>
              </div>

              <div className="relative">
                <Input
                  type="text"
                  id="agreementNo"
                  value={formData.agreementNo}
                  onChange={(e) => handleInputChange('agreementNo', e.target.value)}
                  className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                />
                <Label className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Agreement Number*
                </Label>
              </div>
            </div>
          </div>
        </div>

        {/* Conditional Fields based on Report Type */}
        {reportType === 'Accident Repair' && !showAdditionalAccidentForm && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800">Driver Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="driverName"
                  value={accidentFields.driverName}
                  onChange={(e) => handleAccidentFieldChange('driverName', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Driver Name"
                  required
                />
                <Label htmlFor="driverName" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Driver Name*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="tel"
                  id="phoneNumber"
                  value={accidentFields.phoneNumber}
                  onChange={(e) => handleAccidentFieldChange('phoneNumber', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Phone Number"
                  required
                />
                <Label htmlFor="phoneNumber" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Phone Number*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="email"
                  id="email"
                  value={accidentFields.email}
                  onChange={(e) => handleAccidentFieldChange('email', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Email"
                  required
                />
                <Label htmlFor="email" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Email*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="birthday"
                  value={accidentFields.birthday ? accidentFields.birthday.toLocaleDateString() : ''}
                  onClick={() => setAccidentFields((prev) => ({ ...prev, birthday: new Date() }))}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Select Birthday"
                  required
                />
                <Label htmlFor="birthday" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Birthday*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="address"
                  value={accidentFields.address}
                  onChange={(e) => handleAccidentFieldChange('address', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Address"
                  required
                />
                <Label htmlFor="address" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Address*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="postcode"
                  value={accidentFields.postcode}
                  onChange={(e) => handleAccidentFieldChange('postcode', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Postcode"
                  required
                />
                <Label htmlFor="postcode" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Postcode*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="country"
                  value={accidentFields.country}
                  onChange={(e) => handleAccidentFieldChange('country', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Country"
                  required
                />
                <Label htmlFor="country" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Country*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Label className="text-sm font-medium text-gray-700">Drugs or alcohol consumed in the 12 hours prior to the incident:*</Label>
                <div className="flex items-center gap-6 mt-2">
                  <label className="flex items-center gap-2 text-sm text-gray-600">
                    <Input
                      type="radio"
                      name="drugsAlcohol"
                      checked={accidentFields.isDrugsAlcoholConsumed === true}
                      onChange={() => handleDrugsAlcoholChange(true)}
                      className="w-4 h-4"
                    />
                    Yes
                  </label>
                  <label className="flex items-center gap-2 text-sm text-gray-600">
                    <Input
                      type="radio"
                      name="drugsAlcohol"
                      checked={accidentFields.isDrugsAlcoholConsumed === false}
                      onChange={() => handleDrugsAlcoholChange(false)}
                      className="w-4 h-4"
                    />
                    No
                  </label>
                </div>
              </div>
            </div>

            {/* Driver’s License Section */}
            <div className="flex justify-between items-center mb-6">
              <h1 className="text-2xl font-semibold text-gray-800">Driver’s License</h1>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="licenseNumber"
                  value={accidentFields.licenseNumber}
                  onChange={(e) => handleAccidentFieldChange('licenseNumber', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter DL Number"
                  required
                />
                <Label htmlFor="licenseNumber" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  DL Number*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="licenseIssueCountry"
                  value={accidentFields.country}
                  onChange={(e) => handleAccidentFieldChange('country', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Issue Country"
                  required
                />
                <Label htmlFor="licenseIssueCountry" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Issue Country*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="licenseIssueDate"
                  value={accidentFields.issueDate ? accidentFields.issueDate.toLocaleDateString() : ''}
                  onClick={() => setAccidentFields((prev) => ({ ...prev, issueDate: new Date() }))}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Select Issue Date"
                  required
                />
                <Label htmlFor="licenseIssueDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Issue Date*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="licenseExpiryDate"
                  value={accidentFields.expiryDate ? accidentFields.expiryDate.toLocaleDateString() : ''}
                  onClick={() => setAccidentFields((prev) => ({ ...prev, expiryDate: new Date() }))}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Select Expiry Date"
                  required
                />
                <Label htmlFor="licenseExpiryDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Expiry Date*
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="licenseType"
                  value={accidentFields.conditions}
                  onChange={(e) => handleAccidentFieldChange('conditions', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Conditions"
                  required
                />
                <Label htmlFor="licenseType" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Conditions*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="mt-4 relative">
                <Label htmlFor="frontView" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Upload Front View*
                </Label>
                <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                  {accidentFields.frontView && (
                    <img
                      src={URL.createObjectURL(accidentFields.frontView)}
                      alt="Front view preview"
                      className="max-w-full max-h-32 object-contain mb-2"
                    />
                  )}
                  <input
                    type="file"
                    id="frontView"
                    accept="image/*"
                    onChange={(e) => handleFileChange('frontView', e, accidentFields, setAccidentFields)}
                    className="w-full mt-2"
                  />
                </div>
                {accidentFields.frontView && <p className="text-sm text-gray-600 mt-1">{accidentFields.frontView.name}</p>}
              </div>

              <div className="mt-4 relative">
                <Label htmlFor="backView" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Upload Back View*
                </Label>
                <div className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent">
                  {accidentFields.backView && (
                    <img
                      src={URL.createObjectURL(accidentFields.backView)}
                      alt="Back view preview"
                      className="max-w-full max-h-32 object-contain mb-2"
                    />
                  )}
                  <input
                    type="file"
                    id="backView"
                    accept="image/*"
                    onChange={(e) => handleFileChange('backView', e, accidentFields, setAccidentFields)}
                    className="w-full mt-2"
                  />
                </div>
                {accidentFields.backView && <p className="text-sm text-gray-600 mt-1">{accidentFields.backView.name}</p>}
              </div>
            </div>

            <div className="mt-6 flex gap-4 justify-end">
              <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded" onClick={handleBack}>
                Cancel
              </Button>
              <Button variant="default" className="bg-[#330101] text-white px-4 py-2 rounded" onClick={handleNext}>
                Next
              </Button>
            </div>
          </div>
        )}

        {(reportType === 'Breakdown Service' || reportType === 'Regular Maintenance' || reportType === 'Damage') && (
          <div className="space-y-4">
            {reportType === 'Breakdown Service' && (
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="relative">
                  <Label htmlFor="interiorImages" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                    Interior Images*
                  </Label>
                  <input
                    type="file"
                    id="interiorImages"
                    accept="image/*"
                    onChange={(e) => handleFileChange('interiorImages', e, breakdownFields, setBreakdownFields)}
                    className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  />
                  {breakdownFields.interiorImages && <p className="text-sm text-gray-600 mt-1">{breakdownFields.interiorImages.name}</p>}
                </div>
                <div className="relative">
                  <Label htmlFor="exteriorImages" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                    Exterior Images*
                  </Label>
                  <input
                    type="file"
                    id="exteriorImages"
                    accept="image/*"
                    onChange={(e) => handleFileChange('exteriorImages', e, breakdownFields, setBreakdownFields)}
                    className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  />
                  {breakdownFields.exteriorImages && <p className="text-sm text-gray-600 mt-1">{breakdownFields.exteriorImages.name}</p>}
                </div>
              </div>
            )}

            <div className="mt-6 flex gap-4 justify-end">
              <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded" onClick={handleBack}>
                Cancel
              </Button>
              <Button variant="default" className="bg-[#330101] text-white px-4 py-2 rounded" onClick={handleSubmit}>
                Submit 
              </Button>
            </div>
          </div>
        )}

        {showAdditionalAccidentForm && (
          <div className="space-y-4">
            <h2 className="text-xl font-semibold text-gray-800">Additional Accident Details</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="accidentLocation"
                  value={additionalAccidentFields.accidentLocation}
                  onChange={(e) => handleAdditionalAccidentFieldChange('accidentLocation', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Accident Location"
                  required
                />
                <Label htmlFor="accidentLocation" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Accident Location*
                </Label>
              </div>
              <div className="relative">
                <Textarea
                  id="damageDescription"
                  value={additionalAccidentFields.damageDescription}
                  onChange={(e) => handleAdditionalAccidentFieldChange('damageDescription', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  placeholder="Describe Damage"
                  required
                />
                <Label htmlFor="damageDescription" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Damage Description*
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="policeReport" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Police Report
                </Label>
                <input
                  type="file"
                  id="policeReport"
                  accept="image/*,.pdf"
                  onChange={(e) => handleFileChange('policeReport', e, additionalAccidentFields, setAdditionalAccidentFields)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                {additionalAccidentFields.policeReport && <p className="text-sm text-gray-600 mt-1">{additionalAccidentFields.policeReport.name}</p>}
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="witnessDetails"
                  value={additionalAccidentFields.witnessDetails}
                  onChange={(e) => handleAdditionalAccidentFieldChange('witnessDetails', e.target.value)}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-black text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  placeholder="Enter Witness Details"
                />
                <Label htmlFor="witnessDetails" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Witness Details
                </Label>
              </div>
            </div>

            <div className="mt-6 flex gap-4 justify-end">
              <Button variant="outline" className="bg-gray-200 text-gray-800 px-4 py-2 rounded" onClick={handleBack}>
                Cancel
              </Button>
              <Button variant="default" className="bg-[#330101] text-white px-4 py-2 rounded" onClick={handleSubmit}>
                Submit
              </Button>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}