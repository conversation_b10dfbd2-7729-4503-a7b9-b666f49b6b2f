/* import { NavLink, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  Car, 
  Calendar, 
  BarChart3, 
  UserCheck, 
  DollarSign, 
  FileSearch, 
  Settings,
  Shield,
  ChevronRight,
  Podcast,
  CalendarDays,
  UserSearch,
  Bell,
  ClipboardList,
  ClipboardPenLineIcon,
  CalendarCheck,
  Clock,
  FileText,
  CreditCard,
  ListChecks,
  BarChart2
} from 'lucide-react';
import { useState } from 'react';
import path from 'path';

export function AdminSidebar() {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>({
    'Customer Management': false,
    'Reception Management': false,
    'Workshop Management': false,
    'Staff Management': false,
    'Fleet': false,
    'Car Rental':false, // Added for nested Fleet dropdown
  });

  const navItems = [
    {
      path: '/admin/dashboard',
      label: 'Dashboard Overview',
      icon: <LayoutDashboard className="w-5 h-5 mr-3" />
    },
      {
      path: '',
      label: 'Car Rental',
      icon: <Users className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/admin/masterAdmin/carrental-reservations', label: 'Reservations', icon: <CalendarCheck className="w-4 h-4 mr-2" /> },
        { path: '/admin/masterAdmin/carrental-reservatioAttempts', label: 'Reservation Attempts', icon: <Clock className="w-4 h-4 mr-2" /> },
        { path: '/admin/masterAdmin/carrental-quotes', label: 'Quotes', icon: <FileText className="w-4 h-4 mr-2" />},
        { path: '/admin/masterAdmin/carrental-payments', label: 'Payments', icon: <CreditCard className="w-4 h-4 mr-2" /> },
        { path: '/admin/masterAdmin/carrental-externalCharges', label: 'External Charges', icon: <DollarSign className="w-4 h-4 mr-2" /> },
        { path: '/admin/masterAdmin/carrental-calendar', label: 'Calendar', icon: <CalendarDays className="w-4 h-4 mr-2" /> },
      ],
    },
      {
      path: '/admin/check-list',
      label: 'Check List',
      icon: <ListChecks className="w-4 h-4 mr-2" />
    },
 
    {
      path: '',
      label: 'Customer Management',
      icon: <Users className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/admin/customerMasterAdmin/customerM-overview', label: 'Dashboard Overview', icon: <LayoutDashboard className="w-4 h-4 mr-2" /> },
        { path: '/admin/customerMasterAdmin/customerM-customers', label: 'Customers', icon: <Users className="w-4 h-4 mr-2" /> },
        { path: '/admin/customerMasterAdmin/customerM-fines', label: 'Fines', icon: <DollarSign className="w-4 h-4 mr-2" /> },
        { path: '/admin/customerMasterAdmin/customerM-invoices', label: 'Invoices', icon: <FileSearch className="w-4 h-4 mr-2" /> },
        { path: '/admin/customerMasterAdmin/customerM-notificationCentre', label: 'Notification Centre', icon: <BarChart3 className="w-4 h-4 mr-2" /> },
        { path: '/admin/customerMasterAdmin/customerM-ActivityLog', label: 'Activity Log', icon: <Calendar className="w-4 h-4 mr-2" /> },
      ],
    },
    {
      path: '',
      label: 'Reception Management',
      icon: <UserCheck className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/admin/receptionMasterAdmin/dashboard', label: 'Dashboard Overview', icon: <LayoutDashboard className="w-4 h-4 mr-2" /> },
        { path: '/admin/receptionMasterAdmin/reception-management', label: 'Reception Management', icon: <UserSearch className="w-4 h-4 mr-2" /> },
        { path: '/admin/receptionMasterAdmin/interactions', label: 'Interaction Management', icon: <Podcast className="w-4 h-4 mr-2" /> },
        { path: '/admin/receptionMasterAdmin/reservations', label: 'Reservation Management', icon: <CalendarDays className="w-4 h-4 mr-2" /> },
        {
          path: '',
          label: 'Service Management',
          icon: <Settings className="w-4 h-4 mr-2" />,
          subItems: [
            {path: '/admin/receptionMasterAdmin/incident-reporting', label: 'Incident Reporting', icon: <FileSearch className="w-4 h-4 mr-2" /> },  
            {path: '/admin/receptionMasterAdmin/workshops', label: 'Workshop', icon: <Settings className="w-4 h-4 mr-2" /> },
          ],
        },
        { path: '/admin/receptionMasterAdmin/change-request', label: 'Change Request', icon: <ClipboardPenLineIcon className="w-4 h-4 mr-2" /> },
        {
          path: '',
          label: 'Fleet',
          icon: <Car className="w-4 h-4 mr-2" />,
          subItems: [
            { path: '/admin/receptionMasterAdmin/fleet/rates', label: 'Rates', icon: <DollarSign className="w-4 h-4 mr-2" /> },
            { path: '/admin/receptionMasterAdmin/fleet/vehicles', label: 'Vehicles', icon: <Car className="w-4 h-4 mr-2" /> },
          ]
        },
        { path: '/admin/receptionMasterAdmin/notifications', label: 'Notifications Centre', icon: <Bell className="w-4 h-4 mr-2" /> },
        { path: '/admin/receptionMasterAdmin/activity-log', label: 'Activity Log', icon: <ClipboardList className="w-4 h-4 mr-2" /> },
      ],
    },
    {
      path: '',
      label: 'Workshop Management',
      icon: <Car className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/admin/workshop/dashboard', label: 'Workshop Management', icon: <LayoutDashboard className="w-4 h-4 mr-2" /> },
        { path: '/admin/workshop/fleet', label: 'Fleet', icon: <Car className="w-4 h-4 mr-2" /> },
        { path: '/admin/workshop/teamleaders', label: 'Team Leaders', icon: <UserCheck className="w-4 h-4 mr-2" /> },
        { path: '/admin/workshop/mechanics', label: 'Mechanics', icon: <Users className="w-4 h-4 mr-2" /> },
        { path: '/admin/workshop/panelbeaters', label: 'Panel Beaters', icon: <Car className="w-4 h-4 mr-2" /> },
        { path: '/admin/workshop/vendors', label: 'Vendors', icon: <DollarSign className="w-4 h-4 mr-2" /> },
        { path: '/admin/workshop/parts', label: 'Parts and Services', icon: <FileSearch className="w-4 h-4 mr-2" /> },
        { path: '/admin/workshop/preventive', label: 'Preventive Maintenance', icon: <Settings className="w-4 h-4 mr-2" /> },
        { path: '/admin/workshop/services', label: 'Service Management', icon: <Car className="w-4 h-4 mr-2" /> },
      ],
    },
    {
      path: '',
      label: 'Staff Management',
      icon: <UserCheck className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/admin/staff/roles', label: 'User Roles', icon: <Shield className="w-4 h-4 mr-2" /> },
        { path: '/admin/staff/access', label: 'Access Roles', icon: <Settings className="w-4 h-4 mr-2" /> },
        { path: '/admin/staff/users', label: 'Users', icon: <Users className="w-4 h-4 mr-2" /> },
      ],
    },
         {
      path: '/admin/reports',
      label: 'Reports',
      icon: <BarChart2 className="w-4 h-4 mr-2" />
    },
         {
      path: '/admin/settings',
      label: 'Settings',
      icon: <Settings className="w-4 h-4 mr-2" />
    },
  ];

  const toggleExpand = (label: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  return (
    <aside className="bg-earth-cream w-64 min-h-screen flex flex-col">
      <nav className="flex-1">
        <div className="p-4 border-b border-gold-lighter">
          <div className="flex items-center">
            <Shield className="w-6 h-6 mr-2 text-earth-dark" />
            <h2 className="text-lg font-semibold text-earth-dark">Admin Portal</h2>
          </div>
        </div>
        <ul className="space-y-1 pt-4">
          {navItems.map(item => (
            <li key={item.path || item.label}>
              {item.subItems ? (
                <div>
                  <div
                    className={`flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors cursor-pointer ${
                      item.subItems.some(sub => sub.path && location.pathname.startsWith(sub.path)) || 
                      item.subItems.some(sub => sub.subItems?.some(subSub => subSub.path && location.pathname.startsWith(subSub.path))) 
                        ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                    }`}
                    onClick={() => toggleExpand(item.label)}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                    <ChevronRight
                      className={`w-4 h-4 ml-auto transition-transform ${
                        expandedItems[item.label] ? 'rotate-90' : ''
                      }`}
                    />
                  </div>
                  {expandedItems[item.label] && (
                    <ul className="ml-6 space-y-1">
                      {item.subItems.map(subItem => (
                        <li key={subItem.path || subItem.label}>
                          {subItem.subItems ? (
                            <div>
                              <div
                                className={`flex items-center px-4 py-2 text-earth-dark hover:bg-gold-lighter transition-colors cursor-pointer ${
                                  subItem.subItems.some(subSub => subSub.path && location.pathname.startsWith(subSub.path)) 
                                    ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                                }`}
                                onClick={() => toggleExpand(subItem.label)}
                              >
                                {subItem.icon}
                                <span>{subItem.label}</span>
                                <ChevronRight
                                  className={`w-4 h-4 ml-auto transition-transform ${
                                    expandedItems[subItem.label] ? 'rotate-90' : ''
                                  }`}
                                />
                              </div>
                              {expandedItems[subItem.label] && (
                                <ul className="ml-6 space-y-1">
                                  {subItem.subItems.map(subSubItem => (
                                    <li key={subSubItem.path}>
                                      <NavLink
                                        to={subSubItem.path}
                                        className={({ isActive }) =>
                                          `flex items-center px-4 py-2 text-earth-dark hover:bg-gold-lighter transition-colors ${
                                            isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                                          }`
                                        }
                                      >
                                        {subSubItem.icon}
                                        <span>{subSubItem.label}</span>
                                      </NavLink>
                                    </li>
                                  ))}
                                </ul>
                              )}
                            </div>
                          ) : (
                            <NavLink
                              to={subItem.path}
                              className={({ isActive }) =>
                                `flex items-center px-4 py-2 text-earth-dark hover:bg-gold-lighter transition-colors ${
                                  isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                                }`
                              }
                            >
                              {subItem.icon}
                              <span>{subItem.label}</span>
                            </NavLink>
                          )}
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors ${
                      isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                    }`
                  }
                >
                  {item.icon}
                  <span>{item.label}</span>
                </NavLink>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
} */


import React, { useState } from 'react';
import { NavLink, useLocation } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Users, 
  UserCircle,
  Car, 
  Calendar, 
  BarChart3, 
  UserCheck, 
  DollarSign, 
  FileSearch, 
  Settings,
  Shield,
  ChevronRight,
  Podcast,
  CalendarDays,
  UserSearch,
  Bell,
  ClipboardList,
  ClipboardPenLineIcon,
  CalendarCheck,
  Clock,
  FileText,
  CreditCard,
  ListChecks,
  BarChart2,
  CarFront,
  CalendarPlus,
  Users2,
  Cog,
  Wrench,
  AlertTriangle,
  FileWarning,
  ClipboardPenLine,
  ScrollText
} from 'lucide-react';

export function AdminSidebar({ isOpen, onClose }) {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState({
    'Customer Management': false,
    'Reception Management': false,
    'Workshop Management': false,
    'Staff Management': false,
    'Fleet': false,
    'Car Rental': false,
  });

  const navItems = [
    {
      path: '/admin/dashboard',
      label: 'Dashboard Overview',
      icon: <LayoutDashboard className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '',
      label: 'Car Rental',
      icon: <Users className="w-5 h-5 mr-3 flex-shrink-0" />,
      subItems: [
        { path: '/admin/masterAdmin/carrental-reservations', label: 'Reservations', icon: <CalendarCheck className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/masterAdmin/carrental-reservatioAttempts', label: 'Reservation Attempts', icon: <Clock className="w-4 h-4 mr-2 flex-shrink-0" /> },

        { path: '/admin/masterAdmin-quotes', label: 'Quotes', icon: <FileText className="w-4 h-4 mr-2 flex-shrink-0" />},
        { path: '/admin/masteradmin-payments', label: 'Payments', icon: <CreditCard className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/masterAdmin-refunds', label: 'Refunds', icon: <DollarSign className="w-4 h-4 mr-2 flex-shrink-0" />  },
       
        { path: '/admin/masterAdmin/carrental-calendar', label: 'Calendar', icon: <CalendarDays className="w-4 h-4 mr-2 flex-shrink-0" /> },
         { path: '/admin/masterAdmin/carrental-somerton', label: 'Somerton', icon: <CalendarDays className="w-4 h-4 mr-2 flex-shrink-0" /> },
      ],
    },
    {
      path: '/admin/check-list',
      label: 'Check List',
      icon: <ListChecks className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '',
      label: 'Customer Management',
      icon: <Users className="w-5 h-5 mr-3 flex-shrink-0" />,
      subItems: [
        { path: '/admin/customerMasterAdmin/customerM-overview', label: 'Dashboard Overview', icon: <LayoutDashboard className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/customerMasterAdmin/customerM-customers', label: 'Customers', icon: <Users className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/customerMasterAdmin/customerM-fines', label: 'Fines', icon: <DollarSign className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/customerMasterAdmin/customerM-invoices', label: 'Invoices', icon: <FileSearch className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/customerMasterAdmin/customerM-notificationCentre', label: 'Notification Centre', icon: <BarChart3 className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/customerMasterAdmin/customerM-ActivityLog', label: 'Activity Log', icon: <Calendar className="w-4 h-4 mr-2 flex-shrink-0" /> },
      ],
    },
    
    {
      path: '',
      label: 'Reception Management',
      icon: <UserCheck className="w-5 h-5 mr-3 flex-shrink-0" />,
      subItems: [
        { path: '/admin/receptionMasterAdmin/dashboard', label: 'Dashboard Overview', icon: <LayoutDashboard className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/receptionMasterAdmin/reception-management', label: 'Reception Management', icon: <UserSearch className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/receptionMasterAdmin/interactions', label: 'Interaction Management', icon: <Podcast className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/receptionMasterAdmin/reservations', label: 'Reservation Management', icon: <CalendarDays className="w-4 h-4 mr-2 flex-shrink-0" /> },
        {
          path: '',
          label: 'Service Management',
          icon: <Settings className="w-4 h-4 mr-2 flex-shrink-0" />,
          subItems: [
            {path: '/admin/receptionMasterAdmin/incident-reporting', label: 'Incident Reporting', icon: <FileSearch className="w-4 h-4 mr-2 flex-shrink-0" /> },  
            {path: '/admin/receptionMasterAdmin/workshops', label: 'Workshop', icon: <Settings className="w-4 h-4 mr-2 flex-shrink-0" /> },
          ],
        },
        { path: '/admin/receptionMasterAdmin/change-request', label: 'Change Request', icon: <ClipboardPenLineIcon className="w-4 h-4 mr-2 flex-shrink-0" /> },
        {
          path: '',
          label: 'Fleet',
          icon: <Car className="w-4 h-4 mr-2 flex-shrink-0" />,
          subItems: [
            { path: '/admin/receptionMasterAdmin/fleet/rates', label: 'Rates', icon: <DollarSign className="w-4 h-4 mr-2 flex-shrink-0" /> },
            { path: '/admin/receptionMasterAdmin/fleet/vehicles', label: 'Vehicles', icon: <Car className="w-4 h-4 mr-2 flex-shrink-0" /> },
          ]
        },
        { path: '/admin/receptionMasterAdmin/notifications', label: 'Notifications Centre', icon: <Bell className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/receptionMasterAdmin/activity', label: 'Activity Log', icon: <ClipboardList className="w-4 h-4 mr-2 flex-shrink-0" /> },
      ],
    },


    {
      path: '',
      label: 'Workshop Management',
      icon: <Car className="w-5 h-5 mr-3 flex-shrink-0" />,
      subItems: [
        { path: '/admin/workshopMasterAdmin/workshopM-overview', label: 'Dashboard Overview', icon: <LayoutDashboard className="w-4 h-4 mr-2 flex-shrink-0" /> },
        {
          path: '',
          label: 'Fleet',
          icon: <Car className="w-4 h-4 mr-2 flex-shrink-0" />,
          subItems: [
            { path: '/admin/workshopMasterAdmin/fleet-summary', label: 'Summary', icon: <CarFront className="w-4 h-4 mr-2 flex-shrink-0" /> },
            { path: '/admin/workshopMasterAdmin/fleet-quality', label: 'Quality', icon: <CalendarCheck className="w-4 h-4 mr-2 flex-shrink-0" /> },
            { path: '/admin/workshopMasterAdmin/fleet-task', label: 'Task Overview', icon: <FileWarning className="w-4 h-4 mr-2 flex-shrink-0" /> },
          ]
        },
        { path: '/admin/workshopMasterAdmin/teamleaders', label: 'Team Leaders', icon: <UserCheck className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/workshopMasterAdmin/mechanics', label: 'Mechanics', icon: <UserCircle className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/workshopMasterAdmin/panel-beater', label: 'Panel Beaters', icon: <Users className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/workshopMasterAdmin/vendor', label: 'Vendors', icon: <Users2 className="w-4 h-4 mr-2 flex-shrink-0" /> },
        {
          path: '',
          label: 'Parts & Services',
          icon: <Settings className="w-4 h-4 mr-2 flex-shrink-0" />,
          subItems: [
            { path: '/admin/workshopMasterAdmin/requested-parts', label: 'Requested Parts', icon: <Wrench className="w-4 h-4 mr-2 flex-shrink-0" /> },
            { path: '/admin/workshopMasterAdmin/requested-services', label: 'Requested-Services', icon: <Cog className="w-4 h-4 mr-2 flex-shrink-0" /> },
          ]
        },
        { path: '/admin/workshopMasterAdmin/incident-reporting', label: 'Incident Reporting', icon: <ClipboardList className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/workshopMasterAdmin/service-management', label: 'Service Management', icon: <AlertTriangle className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/workshopMasterAdmin/preventive-maintenance', label: 'Preventive Maintenance', icon: <CalendarPlus className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/workshopMasterAdmin/audit-trail', label: 'Audit & Trail', icon: <ClipboardPenLine className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/workshopMasterAdmin/notification', label: 'Notifications Centre', icon: <Bell className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/workshopMasterAdmin/activity', label: 'Activity Log', icon: <ScrollText className="w-4 h-4 mr-2 flex-shrink-0" /> },
      ],
    },

    
    {
      path: '',
      label: 'Staff Management',
      icon: <UserCheck className="w-5 h-5 mr-3 flex-shrink-0" />,
      subItems: [
        { path: '/admin/staff/roles', label: 'User Roles', icon: <Shield className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/staff/access', label: 'Access Roles', icon: <Settings className="w-4 h-4 mr-2 flex-shrink-0" /> },
        { path: '/admin/staff/users', label: 'Users', icon: <Users className="w-4 h-4 mr-2 flex-shrink-0" /> },
      ],
    },
    {
      path: '/admin/reports',
      label: 'Reports',
      icon: <BarChart2 className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/admin/settings',
      label: 'Settings',
      icon: <Settings className="w-5 h-5 mr-3 flex-shrink-0" />
    },
  ];

  const toggleExpand = (label) => {
    setExpandedItems(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  const handleNavClick = () => {
    // Close sidebar on mobile when nav item is clicked
    if (window.innerWidth < 1024) {
      onClose();
    }
  };

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}

      {/* Sidebar */}
      <aside className={`
        bg-earth-cream flex flex-col
        fixed lg:static inset-y-0 left-0 z-50
        w-64 lg:w-64
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <nav className="flex-1 overflow-y-auto">
          <div className="p-4 border-b border-gold-lighter">
            <div className="flex items-center">
              <Shield className="w-6 h-6 mr-2 text-earth-dark flex-shrink-0" />
              <h2 className="text-lg font-semibold text-earth-dark">Admin Portal</h2>
            </div>
          </div>
          <ul className="space-y-1 pt-4 pb-4">
            {navItems.map(item => (
              <li key={item.path || item.label}>
                {item.subItems ? (
                  <div>
                    <div
                      className={`flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors cursor-pointer ${
                        item.subItems.some(sub => sub.path && location.pathname.startsWith(sub.path)) || 
                        item.subItems.some(sub => sub.subItems?.some(subSub => subSub.path && location.pathname.startsWith(subSub.path))) 
                          ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                      }`}
                      onClick={() => toggleExpand(item.label)}
                    >
                      {item.icon}
                      <span className="text-sm lg:text-base truncate flex-1">{item.label}</span>
                      <ChevronRight
                        className={`w-4 h-4 transition-transform flex-shrink-0 ${
                          expandedItems[item.label] ? 'rotate-90' : ''
                        }`}
                      />
                    </div>
                    {expandedItems[item.label] && (
                      <ul className="ml-6 space-y-1">
                        {item.subItems.map(subItem => (
                          <li key={subItem.path || subItem.label}>
                            {subItem.subItems ? (
                              <div>
                                <div
                                  className={`flex items-center px-4 py-2 text-earth-dark hover:bg-gold-lighter transition-colors cursor-pointer ${
                                    subItem.subItems.some(subSub => subSub.path && location.pathname.startsWith(subSub.path)) 
                                      ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                                  }`}
                                  onClick={() => toggleExpand(subItem.label)}
                                >
                                  {subItem.icon}
                                  <span className="text-sm lg:text-base truncate flex-1">{subItem.label}</span>
                                  <ChevronRight
                                    className={`w-4 h-4 transition-transform flex-shrink-0 ${
                                      expandedItems[subItem.label] ? 'rotate-90' : ''
                                    }`}
                                  />
                                </div>
                                {expandedItems[subItem.label] && (
                                  <ul className="ml-6 space-y-1">
                                    {subItem.subItems.map(subSubItem => (
                                      <li key={subSubItem.path}>
                                        <NavLink
                                          to={subSubItem.path}
                                          onClick={handleNavClick}
                                          className={({ isActive }) =>
                                            `flex items-center px-4 py-2 text-earth-dark hover:bg-gold-lighter transition-colors ${
                                              isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                                            }`
                                          }
                                        >
                                          {subSubItem.icon}
                                          <span className="text-sm lg:text-base truncate">{subSubItem.label}</span>
                                        </NavLink>
                                      </li>
                                    ))}
                                  </ul>
                                )}
                              </div>
                            ) : (
                              <NavLink
                                to={subItem.path}
                                onClick={handleNavClick}
                                className={({ isActive }) =>
                                  `flex items-center px-4 py-2 text-earth-dark hover:bg-gold-lighter transition-colors ${
                                    isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                                  }`
                                }
                              >
                                {subItem.icon}
                                <span className="text-sm lg:text-base truncate">{subItem.label}</span>
                              </NavLink>
                            )}
                          </li>
                        ))}
                      </ul>
                    )}
                  </div>
                ) : (
                  <NavLink
                    to={item.path}
                    onClick={handleNavClick}
                    className={({ isActive }) =>
                      `flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors ${
                        isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                      }`
                    }
                  >
                    {item.icon}
                    <span className="text-sm lg:text-base truncate">{item.label}</span>
                  </NavLink>
                )}
              </li>
            ))}
          </ul>
        </nav>
      </aside>
    </>
  );
}