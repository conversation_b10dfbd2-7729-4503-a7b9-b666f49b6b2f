import React, { useState } from 'react';
import { ArrowLeft, Eye, Upload, ChevronDown } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useNavigate } from 'react-router-dom';
import { AssignVehicleFormData, ImageModalProps } from './type/reception-type';
import { handleGoBack, handleCancel, handleSubmit, handleImageView, closeModal } from './hook/useAssignVehicle';
import { mockAssignVehicleFormData, mockImageCategories, mockVehicles } from './common/mockData';

const ImageModal: React.FC<ImageModalProps> = ({ isOpen, onClose, imageSrc, imageTitle }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="max-w-2xl">
        <DialogHeader>
          <DialogTitle>{imageTitle}</DialogTitle>
        </DialogHeader>
        <div className="flex justify-center p-4">
          <img 
            src={imageSrc} 
            alt={imageTitle} 
            className="max-w-full max-h-96 object-contain"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export function AssignVehiclePage() {
  const navigate = useNavigate();
  const [selectedImage, setSelectedImage] = useState<{ src: string; title: string } | null>(null);
  const [formData, setFormData] = useState<AssignVehicleFormData>(mockAssignVehicleFormData[0]);
  const [selectedRentalId, setSelectedRentalId] = useState<string>(mockAssignVehicleFormData[0].rentalId);

  const handleRentalChange = (rentalId: string) => {
    const selectedReservation = mockAssignVehicleFormData.find(res => res.rentalId === rentalId);
    if (selectedReservation) {
      setFormData(selectedReservation);
      setSelectedRentalId(rentalId);
    }
  };

  const handleVehicleChange = (vehicleId: string) => {
    const selectedVehicle = mockAssignVehicleFormData.find(res => res.vehicle.vehicleId === vehicleId)?.vehicle;
    if (selectedVehicle) {
      setFormData(prev => ({ ...prev, vehicle: selectedVehicle }));
    }
  };

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  const handleSelectChange = (name: string, value: string) => {
    setFormData(prev => ({ ...prev, [name]: value }));
  };

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="flex-1 p-6">
        {/* Top Navigation */}
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Button
                className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
                size="sm"
                onClick={() => handleGoBack(formData.rentalId, navigate)}
              >
                <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
                <span className="hidden md:inline">Go Back</span>
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                className="bg-[#330101] text-white hover:bg-[#ffde5c]"
                onClick={() => handleSubmit(formData.rentalId, navigate)}
              >
                Assign Vehicle
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white hover:text-white"
                onClick={() => navigate('/reception/reception-returnVehicle')}
              >
                Return Vehicle
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white"
              >
                Agreement
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white"
              >
                Receipt
                <ChevronDown className="h-4 w-4 ml-2" />
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white"
              >
                <ChevronDown className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="space-y-6 mt-6">
          {/* Basic Information */}
          <div className="grid grid-cols-3 gap-4">
            <div className="relative">
              <Label htmlFor="rentalId" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rental ID</Label>
              <Input
                id="rentalId"
                value={formData.rentalId}
                className="bg-gray-100"
                readOnly
              />
              {/* <Select value={selectedRentalId} onValueChange={handleRentalChange}>
                <SelectTrigger id="rentalId" className="h-12 focus:outline-none focus:ring-0 focus:border-none" readOnly>
                  <SelectValue placeholder="Select Reservation" />
                </SelectTrigger>
                <SelectContent>
                  {mockAssignVehicleFormData.map(res => (
                    <SelectItem key={res.rentalId} value={res.rentalId}>
                      {res.rentalId}
                    </SelectItem>
                  ))}
                  
                </SelectContent>
              </Select> */}
            </div>
            <div className="relative">
              <Label htmlFor="vehicle" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle</Label>
              <Select value={formData.vehicle.vehicleId} onValueChange={handleVehicleChange}>
                <SelectTrigger id="vehicle" className="h-12 focus:outline-none focus:ring-0 focus:border-none">
                  <SelectValue placeholder="Select vehicle" />
                </SelectTrigger>
                <SelectContent>
                  {mockVehicles.map(vehicle => (
                    <SelectItem key={vehicle} value={vehicle}>
                      {vehicle}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label htmlFor="vehicleClass" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Class</Label>
              <Input
                id="vehicleClass"
                value={formData.vehicle.class}
                className="bg-gray-100"
                readOnly
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 mt-4">
            <div className="relative">
              <Label htmlFor="pickupLocation" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Pickup Location</Label>
              <Input
                id="pickupLocation"
                value={formData.pickupLocation}
                className="bg-gray-100"
                readOnly
              />
            </div>
            <div className="relative">
              <Label htmlFor="pickupDate" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Pickup Date</Label>
              <div className="flex space-x-2">
                <Input
                  placeholder="DD"
                  value={formData.pickupDate.split('-')[0]}
                  className="w-16 bg-gray-100"
                  readOnly
                />
                <Input
                  placeholder="MM"
                  value={formData.pickupDate.split('-')[1]}
                  className="w-16 bg-gray-100"
                  readOnly
                />
                <Input
                  placeholder="YYYY"
                  value={formData.pickupDate.split('-')[2]}
                  className="w-20 bg-gray-100"
                  readOnly
                />
                <Input
                  placeholder="Time"
                  value={formData.pickupTime}
                  className="flex-1 bg-gray-100"
                  readOnly
                />
              </div>
            </div>
            <div className="relative">
              <Label htmlFor="returnLocation" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Return Location</Label>
              <Input
                id="returnLocation"
                value={formData.returnLocation}
                className="bg-gray-100"
                readOnly
              />
            </div>
          </div>

          <div className="grid grid-cols-3 gap-4 mt-4">
            <div className="relative">
              <Label htmlFor="returnDate" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Return Date</Label>
              <div className="flex space-x-2">
                <Input
                  placeholder="DD"
                  value={formData.returnDate.split('-')[0]}
                  className="w-16 bg-gray-100"
                  readOnly
                />
                <Input
                  placeholder="MM"
                  value={formData.returnDate.split('-')[1]}
                  className="w-16 bg-gray-100"
                  readOnly
                />
                <Input
                  placeholder="YYYY"
                  value={formData.returnDate.split('-')[2]}
                  className="w-20 bg-gray-100"
                  readOnly
                />
                <Input
                  placeholder="Time"
                  value={formData.returnTime}
                  className="flex-1 bg-gray-100"
                  readOnly
                />
              </div>
            </div>
            <div className="relative">
              <Label htmlFor="odometerAtPickup" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Odometer at Pickup</Label>
              <Input
                id="odometerAtPickup"
                name="odometerAtPickup"
                value={formData.odometerAtPickup}
                onChange={handleInputChange}
              />
            </div>
            <div className="relative">
              <Label htmlFor="fuelLevelAtPickup" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Fuel Level at Pickup</Label>
              <Select
                value={formData.fuelLevelAtPickup}
                onValueChange={(value) => handleSelectChange('fuelLevelAtPickup', value)}
              >
                <SelectTrigger id="fuelLevelAtPickup" className="h-12 focus:outline-none focus:ring-0 focus:border-none">
                  <SelectValue placeholder="Select fuel level" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Full">Full</SelectItem>
                  <SelectItem value="3/4">3/4</SelectItem>
                  <SelectItem value="1/2">1/2</SelectItem>
                  <SelectItem value="1/4">1/4</SelectItem>
                  <SelectItem value="Empty">Empty</SelectItem>
                </SelectContent>
              </Select>
            </div>
          </div>

          {/* Proof Images */}
          <div>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Proof Images</h3>
              <div className="flex space-x-4">
                <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  <img src="/src/assets/odometre.png" alt="Speedometer" className="w-full h-full object-cover rounded" />
                </div>
                <div className="w-24 h-24 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  <div
                    className="text-center cursor-pointer"
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = 'image/*';
                      input.onchange = (event: Event) => {
                        const target = event.target as HTMLInputElement;
                        const file = target.files?.[0];
                        if (file) {
                          console.log('File selected:', file);
                        }
                      };
                      input.click();
                    }}
                  >
                    <Upload className="h-8 w-8 mx-auto mb-2 text-gray-400" />
                    <span className="text-xs text-gray-500">Upload Image</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Images of the vehicle */}
          <div>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Images of the vehicle</h3>
              <div className="grid grid-cols-4 gap-6">
                {mockImageCategories.map((category, index) => (
                  <div key={index} className="relative">
                    <Label className="text-sm font-medium text-gray-700 absolute left-2 top-[-2px] bg-white px-1">{category.title}</Label>
                    <div className="mt-2 space-y-2">
                      {category.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex items-center justify-between p-2 border rounded">
                          <span className="text-sm">{item}</span>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleImageView(item, `${category.title} - ${item}`, setSelectedImage)}
                          >
                            <Eye className="h-4 w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Vehicle Conditions */}
          <div>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Vehicle Conditions</h3>
              <div className="relative mt-6">
                <Label htmlFor="comment" className="absolute left-2 top-[-9px] bg-white px-1 text-xs text-gray-600">Comment</Label>
                <Textarea
                  id="comment"
                  name="comment"
                  value={formData.comment}
                  onChange={handleInputChange}
                  rows={4}
                />
              </div>
            </div>
          </div>

          {/* Payment & Insurance */}
          {/* <div>
            <div className="p-6">
              <h3 className="text-lg font-semibold mb-4">Payment & Insurance</h3>
              <div className="grid grid-cols-2 gap-4">
                <div className="relative">
                  <Label htmlFor="paymentDue" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Payment Due</Label>
                  <Input
                    id="paymentDue"
                    name="paymentDue"
                    value={formData.paymentDue}
                    onChange={handleInputChange}
                  />
                </div>
                <div className="relative">
                  <Label htmlFor="excessCoverObtained" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Excess Cover Obtained?</Label>
                  <Select
                    value={formData.excessCoverObtained}
                    onValueChange={(value) => handleSelectChange('excessCoverObtained', value)}
                  >
                    <SelectTrigger id="excessCoverObtained" className="h-12 focus:outline-none focus:ring-0 focus:border-none">
                      <SelectValue placeholder="Select option" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Yes">Yes</SelectItem>
                      <SelectItem value="No">No</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </div>
          </div> */}

          {/* Action Buttons */}
          <div className="flex justify-end gap-4 mt-6">
            <Button variant="outline" onClick={() => handleCancel(formData.rentalId, navigate)} className="px-8">
              Cancel
            </Button>
            <Button onClick={() => handleSubmit(formData.rentalId, navigate)} className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]">
              Submit
            </Button>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          isOpen={!!selectedImage}
          onClose={() => closeModal(setSelectedImage)}
          imageSrc={selectedImage.src}
          imageTitle={selectedImage.title}
        />
      )}
    </div>
  );
}