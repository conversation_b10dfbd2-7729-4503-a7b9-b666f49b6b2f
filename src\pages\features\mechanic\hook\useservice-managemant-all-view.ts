import { useEffect, useState } from "react";
import { useParams } from "react-router-dom";
import { ServiceManagementAllViewData } from "../type/mechanictype";

// Place your mock data array here:
const SERVICE_MANAGEMENT_ALL_VIEW_MOCK: ServiceManagementAllViewData[] = [
  {
    vehicleId: "VID0001",
    vehicleModel: "Civic Hybrid Sedan",
    regoNumber: "1PX 1ZR",
    serviceType: "General Maintenance",
    totalParts: "02",
    totalLabor: "03",
    damages: "Minor scratches on rear bumper",
    notes: "Customer requested quick turnaround.",
    dateIn: "02/07/2025",
    dateOut: "02/08/2025",
    timeOut: "01:43 PM",
    timeIn: "09:00 AM",
    comments: "All tasks completed successfully.",
    reservation: "RES12345",
    fuelIn: "Full",
    fuelOut: "Half",
    odometerIn: "12000",
    odometerOut: "12100",
    status: "Pending",
    vehicleStatus: "Ready",
  },
  {
    vehicleId: "VID0002",
    vehicleModel: "Atom",
    regoNumber: "1PX 5ZP",
    serviceType: "Accident",
    totalParts: "05",
    totalLabor: "08",
    damages: "Front bumper and left headlight damaged",
    notes: "Insurance claim filed.",
    dateIn: "02/06/2025",
    dateOut: "02/08/2025",
    timeOut: "03:00 PM",
    timeIn: "10:00 AM",
    comments: "Awaiting insurance approval.",
    reservation: "RES54321",
    fuelIn: "Three Quarters",
    fuelOut: "Quarter",
    odometerIn: "22000",
    odometerOut: "22050",
    status: "Pending",
    vehicleStatus: "In Repair",
  },
  {
    vehicleId: "VID0003",
    vehicleModel: "BMW 330 d Coupe",
    regoNumber: "191",
    serviceType: "Breakdown",
    totalParts: "01",
    totalLabor: "02",
    damages: "Engine overheating",
    notes: "Towed to workshop.",
    dateIn: "02/05/2025",
    dateOut: "02/08/2025",
    timeOut: "11:00 AM",
    timeIn: "08:00 AM",
    comments: "Radiator replaced.",
    reservation: "RES67890",
    fuelIn: "Half",
    fuelOut: "Half",
    odometerIn: "50000",
    odometerOut: "50010",
    status: "InProgress",
    vehicleStatus: "Under Maintenance",
  },
  {
    vehicleId: "VID0004",
    vehicleModel: "Holden CTS H2 Automatic",
    regoNumber: "181",
    serviceType: "Damage",
    totalParts: "00",
    totalLabor: "01",
    damages: "Small dent on right door",
    notes: "No urgent action required.",
    dateIn: "02/04/2025",
    dateOut: "02/08/2025",
    timeOut: "04:00 PM",
    timeIn: "12:00 PM",
    comments: "Scheduled for next week.",
    reservation: "RES24680",
    fuelIn: "Full",
    fuelOut: "Full",
    odometerIn: "35000",
    odometerOut: "35005",
    status: "Done",
    vehicleStatus: "Ready",
  },
];

export function useServiceManagementAllView() {
  const { vehicleId } = useParams<{ vehicleId: string }>();
  const [formData, setFormData] = useState<ServiceManagementAllViewData | null>(null);

  useEffect(() => {
    if (vehicleId) {
      const found = SERVICE_MANAGEMENT_ALL_VIEW_MOCK.find(item => item.vehicleId === vehicleId);
      setFormData(found || null);
    }
  }, [vehicleId]);

  return { formData, setFormData };
}

