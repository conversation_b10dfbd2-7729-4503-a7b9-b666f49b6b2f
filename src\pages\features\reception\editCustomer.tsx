import React, { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { bookingsData, conditionsOptions, countryOptions, phoneOptions, discountOptions } from './common/mockData';
import { handleSave } from './hook/useEditCustomer';
import { CustomerFormData } from './type/reception-type';

export function ReceptionCustomerEditPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const [formData, setFormData] = useState<CustomerFormData>({
    customerId: '',
    firstName: '',
    lastName: '',
    address: '',
    postCode: '',
    country: '',
    email: '',
    phone: '',
    birthday: { day: '', month: '', year: '' },
    companyName: '',
    emergencyContactName: '',
    emergencyContactNumber: '',
    code: 'Mobile',
    dlNumber: '',
    issueCountry: '',
    issueDate: '',
    expiryDate: '',
    conditions: '',
    password: '',
    isCorporate: '',
    discount: '',
    discountCode: ''
  });
  const [frontViewFile, setFrontViewFile] = useState<File | null>(null);
  const [backViewFile, setBackViewFile] = useState<File | null>(null);
  const [isPopupOpen, setIsPopupOpen] = useState(false);
  const [sendEmail, setSendEmail] = useState(false);

  useEffect(() => {
    const customer = bookingsData.find(
      (booking) => booking.id === id || booking.id === `#${id}`
    );
    if (customer) {
      const [firstName, ...lastNameParts] = customer.customerName.split(' ');
      setFormData({
        customerId: customer.id,
        firstName: firstName || '',
        lastName: lastNameParts.join(' ') || '',
        address: '',
        postCode: '',
        country: '',
        email: '',
        phone: customer.customerPhone,
        birthday: { day: '', month: '', year: '' },
        companyName: customer.type === 'Cooperate' ? 'Unknown Company' : '',
        emergencyContactName: '',
        emergencyContactNumber: '',
        code: 'Mobile',
        dlNumber: customer.license,
        issueCountry: '',
        issueDate: '',
        expiryDate: customer.expireDate,
        conditions: '',
        password: '',
        isCorporate: customer.type === 'Cooperate' ? 'true' : '',
        discount: '',
        discountCode: ''
      });
    }
  }, [id]);

  const handleFrontViewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFrontViewFile(file);
  };

  const handleBackViewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setBackViewFile(file);
  };

  const handleInputChange = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleBirthdayChange = (field: keyof CustomerFormData['birthday'], value: string) => {
    setFormData(prev => ({
      ...prev,
      birthday: { ...prev.birthday, [field]: value }
    }));
  };

  return (
    <div className="min-h-screen">
      <div className="flex items-center mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2'
          size="sm"
          onClick={() => navigate('/reception/reception-customer')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="bg-white rounded-lg shadow-sm p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
        <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl font-semibold mb-2 sm:mb-4 md:mb-6">Customer Information</h2>

        {/* Customer ID */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.customerId}
            onChange={(e) => handleInputChange('customerId', e.target.value)}
            className="bg-gray-200 w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            required
            disabled
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Customer ID
          </Label>
        </div>

        {/* Name Fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              First Name
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Last Name
            </Label>
          </div>
        </div>

        {/* Address */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            required
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Address
          </Label>
        </div>

        {/* Post Code and Country */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.postCode}
              onChange={(e) => handleInputChange('postCode', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Post Code
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Country</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-b text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {formData.country || 'Select Country'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {countryOptions.map((country) => (
                  <DropdownMenuItem
                    key={country}
                    onSelect={() => handleInputChange('country', country)}
                    className={formData.country === country ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {country || 'Select Country'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Email */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="email"
            value={formData.email}
            onChange={(e) => handleInputChange('email', e.target.value)}
            className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            required
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Email
          </Label>
        </div>

        {/* Phone and Birthday */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Phone Number
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={`${formData.birthday.year}-${formData.birthday.month.padStart(2, '0')}-${formData.birthday.day.padStart(2, '0')}`}
              onChange={(e) => {
                const [year, month, day] = e.target.value.split('-');
                setFormData(prev => ({
                  ...prev,
                  birthday: { day, month, year }
                }));
              }}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Birthday
            </Label>
          </div>
        </div>

        {/* Company Name */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.companyName}
            onChange={(e) => handleInputChange('companyName', e.target.value)}
            className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Company Name
          </Label>
        </div>

        {/* Emergency Contact Details */}
        <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Emergency Contact Details</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.emergencyContactName}
              onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Emergency Contact Person's Name
            </Label>
          </div>
          <div className="relative">
            <div className="flex">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between border-b text-[10px] sm:text-xs md:text-sm lg:text-base">
                    {formData.code || 'Mobile'}
                    <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  {phoneOptions.map((code) => (
                    <DropdownMenuItem
                      key={code}
                      onSelect={() => handleInputChange('code', code)}
                      className={formData.code === code ? 'bg-amber-100 font-semibold' : ''}
                    >
                      {code || 'Mobile'}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
              <Input
                type="tel"
                value={formData.emergencyContactNumber}
                onChange={(e) => handleInputChange('emergencyContactNumber', e.target.value)}
                className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-r-md border-l-0 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
            </div>
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Emergency Contact Number
            </Label>
          </div>
        </div>

        {/* Corporate Customer Section */}
        <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Corporate Customer</h3>
        <div className="mb-4 sm:mb-6 md:mb-8">
          <div className="flex items-center space-x-4">
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isCorporate === 'true'}
                onChange={(e) => handleInputChange('isCorporate', e.target.checked ? 'true' : '')}
                className="mr-2"
              />
              Yes
            </label>
            <label className="flex items-center">
              <input
                type="checkbox"
                checked={formData.isCorporate !== 'true'}
                onChange={(e) => handleInputChange('isCorporate', e.target.checked ? '' : 'true')}
                className="mr-2"
              />
              No
            </label>
          </div>
        </div>

        {/* Add Discount Code */}
        <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Discount Code</h3>
        <div className="mb-4 sm:mb-6 md:mb-8">
          <div className="grid grid-cols-2 gap-2 sm:gap-4 md:gap-6">
            <div className="relative">
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Discount Type</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between border-b text-[10px] sm:text-xs md:text-sm lg:text-base">
                    {formData.discount || 'Select'}
                    <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  {discountOptions.map((discount) => (
                    <DropdownMenuItem
                      key={discount}
                      onSelect={() => handleInputChange('discount', discount)}
                      className={formData.discount === discount ? 'bg-amber-100 font-semibold' : ''}
                    >
                      {discount || 'Select'}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="relative">
              <Input
                type="text"
                value={formData.discountCode}
                onChange={(e) => handleInputChange('discountCode', e.target.value)}
                className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                Discount Code
              </Label>
            </div>
          </div>
          <div className="mb-4 sm:mb-6 md:mb-8 relative">
            <Button className="mt-2 px-4 py-2 bg-[#330101] text-white rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
              Generate Code
            </Button>
          </div>
          <div className="grid grid-cols-2 gap-2 sm:gap-4 md:gap-6 mt-2">
            <div className="relative">
              <Input
                type="text"
                value={formData.discountCode}
                readOnly
                className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md bg-gray-50 text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                Discount Code
              </Label>
            </div>
            <div className="flex items-end space-x-2">
              <div className="mb-4">
                <label className="flex items-center">
                  <input
                    type="checkbox"
                    checked={sendEmail}
                    onChange={(e) => setSendEmail(e.target.checked)}
                    className="mr-2"
                  />
                  Send Email to Customer
                </label>
              </div>
              <Button className="px-6 py-2 bg-[#330101] text-white rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
                Copy
              </Button>
            </div>
          </div>
        </div>

        {/* Driver's License */}
        <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Driver's License</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.dlNumber}
              onChange={(e) => handleInputChange('dlNumber', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              DL Number
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Country</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-b text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {formData.issueCountry || 'Select Country'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {countryOptions.map((country) => (
                  <DropdownMenuItem
                    key={country}
                    onSelect={() => handleInputChange('issueCountry', country)}
                    className={formData.issueCountry === country ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {country || 'Select Country'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="date"
              value={formData.issueDate}
              onChange={(e) => handleInputChange('issueDate', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Issue Date
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={formData.expiryDate}
              onChange={(e) => handleInputChange('expiryDate', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Expiry Date
            </Label>
          </div>
        </div>
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Conditions</Label>
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full justify-between border-b text-[10px] sm:text-xs md:text-sm lg:text-base">
                {formData.conditions || 'Select Conditions'}
                <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full">
              {conditionsOptions.map((condition) => (
                <DropdownMenuItem
                  key={condition}
                  onSelect={() => handleInputChange('conditions', condition)}
                  className={formData.conditions === condition ? 'bg-amber-100 font-semibold' : ''}
                >
                  {condition || 'Select Conditions'}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Upload Files */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <div className="flex">
              <Input
                type="text"
                value={frontViewFile?.name || 'No file selected'}
                readOnly
                className="flex-1 p-2 sm:p-3 border border-gray-300 rounded-l-md bg-gray-50 text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="flex items-center px-3 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Choose File</span>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleFrontViewChange}
                  className="hidden"
                />
              </Label>
            </div>
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Upload Front View
            </Label>
          </div>
          <div className="relative">
            <div className="flex">
              <Input
                type="text"
                value={backViewFile?.name || 'No file selected'}
                readOnly
                className="flex-1 p-2 sm:p-3 border border-gray-300 rounded-l-md bg-gray-50 text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="flex items-center px-3 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Choose File</span>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleBackViewChange}
                  className="hidden"
                />
              </Label>
            </div>
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Upload Back View
            </Label>
          </div>
        </div>

        {/* Password */}
        <div className="mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            className="bg-gray-200 w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
            disabled
          />
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Password
          </Label>
        </div>

        <div className="mt-4">
          <label className="flex items-center">
            <input
              type="checkbox"
              checked={isPopupOpen}
              onChange={(e) => setIsPopupOpen(e.target.checked)}
              className="mr-2"
            />
            Do you need to blacklist this customer?
          </label>
        </div>

        {isPopupOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-6 rounded-lg shadow-lg">
              <p className="text-lg mb-4">Are you sure you’re blacklisting this customer?</p>
              <div className="flex justify-end gap-4">
                <Button
                  onClick={() => {
                    setIsPopupOpen(false);
                    handleSave(navigate, formData, frontViewFile, backViewFile, true);
                  }}
                  className="px-6 py-2 bg-blue-500 text-white rounded-md hover:bg-blue-600"
                >
                  Yes
                </Button>
                <Button
                  onClick={() => setIsPopupOpen(false)}
                  className="px-6 py-2 bg-red-500 text-white rounded-md hover:bg-red-600"
                >
                  No
                </Button>
              </div>
            </div>
          </div>
        )}

        <Button 
          onClick={() => handleSave(navigate, formData, frontViewFile, backViewFile, isPopupOpen)}
          className="px-2 sm:px-3 md:px-4 py-1 sm:py-2 bg-[#330101] text-white rounded-md transition-colors text-[10px] sm:text-xs md:text-sm lg:text-base"
        >
          Update
        </Button>
      </div>
    </div>
  );
}