import { NavigateFunction } from 'react-router-dom';

export const getStatusBadge = (status: string) => {
  switch (status) {
    case 'In Progress':
      return 'bg-blue-600 px-3 py-1 text-white rounded-md';
    case 'Resolved':
      return 'bg-green-600 px-3 py-1 text-white rounded-md';
    case 'Escalated':
      return 'bg-red-500 px-3 py-1 text-white rounded-md';
    default:
      return 'bg-gray-100 px-3 py-1 text-black rounded-md';
  }
};

export const handleViewInteraction = (id: string, navigate: NavigateFunction) => {
  navigate(`/reception/interaction-management/view-interaction/${id}`);
};