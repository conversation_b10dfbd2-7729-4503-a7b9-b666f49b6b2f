import { useState, useEffect } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { JobCardAccidentRepairData } from '../type/mechanictype';

export function useJobcardDamageEdit() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [formData, setFormData] = useState<JobCardAccidentRepairData>({
    id: '',
    vehicle: '',
    damageDescription: '',
    accidentDate: '',
    damageParts: '',
    repairTasks: '',
    repairedBy: '',
    repairCompletionDate: '',
  });

  const [serviceChecks, setServiceChecks] = useState({
    damagedPanels: false,
    realignFrame: false,
    windscreen: false,
    windows: false,
    mirrors: false,
    headlights: false,
    tailLights: false,
    repaint: false,
    bumperFront: false,
    bumperRear: false,
    grilleFenderBonnet: false,
    airbags: false,
    wheelsSuspension: false,
    wiring: false,
    numberPlate: false,
    bodyQuality: false,
    cleanVehicle: false,
    gps: false,
    roadTest: false,
  });

  const [notesComment, setNotesComment] = useState('');

  useEffect(() => {
    const existingData = localStorage.getItem('jobCardAccidentRepairData');
    const jobCards: JobCardAccidentRepairData[] = existingData ? JSON.parse(existingData) : [];
    const jobCard = jobCards.find(card => card.id === id);
    if (jobCard) {
      setFormData(jobCard);
      // Optionally set serviceChecks and notesComment if stored
    }
  }, [id]);

  const handleInputChange = (field: keyof JobCardAccidentRepairData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleServiceCheck = (key: string) => {
    setServiceChecks(prev => ({ ...prev, [key]: !prev[key] }));
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);

    try {
      const existingData = localStorage.getItem('jobCardAccidentRepairData');
      let jobCards: JobCardAccidentRepairData[] = existingData ? JSON.parse(existingData) : [];
      jobCards = jobCards.map(card => card.id === id ? { ...formData } : card);
      localStorage.setItem('jobCardAccidentRepairData', JSON.stringify(jobCards));
      alert('Job card updated successfully!');
      navigate('/mechanic/jobcard-accident-repair');
    } catch (error) {
      alert('Error updating job card.');
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleCancel = () => {
    navigate('/mechanic/jobcard-accident-repair');
  };

  return {
    formData,
    isSubmitting,
    handleInputChange,
    handleSubmit,
    handleCancel,
    serviceChecks,
    handleServiceCheck,
    notesComment,
    setNotesComment
  };
}