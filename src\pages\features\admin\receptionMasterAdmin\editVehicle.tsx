import React, { useState } from 'react';
import { Eye, Upload, X, Plus, ArrowLeft } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Textarea } from '@/components/ui/textarea';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useNavigate, useParams } from 'react-router-dom';

interface ImageUpload {
  id: string;
  name: string;
  url?: string;
}

export function ReceptionEditVehiclePage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [images, setImages] = useState<ImageUpload[]>([
    { id: '1', name: 'Interior Images', url: '/placeholder-interior.jpg' },
    { id: '2', name: 'Exterior Images', url: '/placeholder-exterior.jpg' },
    { id: '3', name: 'Front Side Images', url: '/placeholder-frontSide.jpg' },
    { id: '4', name: 'Right Side Images', url: '/placeholder-rightSide.jpg' },
    { id: '5', name: 'Left Side Images', url: '/placeholder-leftSide.jpg' },
    { id: '6', name: 'Back Side Images', url: '/placeholder-backSide.jpg' },
    { id: '7', name: 'Side Mirror', url: '/placeholder-sideMirror.jpg' },
    { id: '8', name: 'Others', url: '/placeholder-others.jpg' }
  ]);

  const [selectedImage, setSelectedImage] = useState<string | null>(null);
  const [isImageDialogOpen, setIsImageDialogOpen] = useState(false);

  const handleImageUpload = (imageId: string, file: File) => {
    const url = URL.createObjectURL(file);
    setImages(prev => prev.map(img => 
      img.id === imageId ? { ...img, url } : img
    ));
  };

  const handleViewImage = (imageUrl: string) => {
    setSelectedImage(imageUrl);
    setIsImageDialogOpen(true);
  };

  const handleGoBack = () => {
    if (!id) {
      console.error('Vehicle ID is undefined for go back');
      navigate('/receptionMasterAdmin/fleet/vehicles');
      return;
    }
    navigate(`/receptionMasterAdmin/fleet/vehicles`);
  };

  const handleTabClick = (tab: string) => {
    if (!id) {
      console.error('Vehicle ID is undefined for tab navigation');
      navigate('/receptionMasterAdmin/fleet/vehicles');
      return;
    }

    const tabRoutes: { [key: string]: string } = {
      'Edit': `/admin/receptionMasterAdmin/fleet/vehicles/edit-vehicle/${id}`,
      'Reservations': `/admin/receptionMasterAdmin/fleet/vehicles/reservations/${id}`,
      'Damages': `/admin/receptionMasterAdmin/fleet/vehicles/${id}/damages`,
      'Block Periods': `/admin/receptionMasterAdmin/fleet/vehicles/blocked-periods/${id}`,
      'Expenses': `/admin/receptionMasterAdmin/fleet/vehicles/expenses/${id}`,
      'Relocations': `/admin/receptionMasterAdmin/fleet/vehicles/relocations/${id}`,
      'Repair Orders': `/admin/receptionMasterAdmin/fleet/vehicles/repair-orders/${id}`,
      'Files': `/receptionMasterAdmin/fleet/vehicles/${id}/files`,
      'Check List': `/receptionMasterAdmin/fleet/vehicles/${id}/check-list`
    };

    navigate(tabRoutes[tab]);
  };

  return (
    <div className="p-6 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        
        <h1 className="text-2xl font-semibold mt-4">Mercedes-Benz E 280 CDI Classic - {id || 'Unknown'}</h1>
        <div className="flex gap-2 mt-4 text-sm">
          {['Edit', 'Reservations', 'Damages', 'Block Periods', 'Expenses', 'Relocations', 'Repair Orders', 'Files', 'Check List'].map((tab) => (
            <Button
              key={tab}
              className={`px-3 py-1 rounded text-sm ${
                tab === 'Edit' 
                  ? 'bg-white text-gray-900 border border-gray-300' 
                  : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
              }`}
              variant="ghost"
              onClick={() => handleTabClick(tab)}
            >
              {tab}
            </Button>
          ))}
        </div>
      </div>

      <div className="grid grid-cols-3 gap-6">
        {/* Left Column - Main Form */}
        <div className="col-span-2 space-y-6">
          {/* Fleet Section */}
          <Card>
            <CardHeader>
              <CardTitle>Fleet</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-2 gap-4">
                <div className="relative">
                  <Label htmlFor="fleet" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Fleet</Label>
                  <Input id="fleet" placeholder="Passenger" />
                </div>
                <div className="relative">
                  <Label htmlFor="class" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Class</Label>
                  <Select>
                    <SelectTrigger className="h-12">
                      <SelectValue />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="economy">Economy</SelectItem>
                      <SelectItem value="premium">Premium</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Purchase Section */}
          <Card>
            <CardHeader>
              <CardTitle>Purchase</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="purchaseDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Purchase Date</Label>
                  <Input id="purchaseDate" defaultValue="29/04/2022" />
                </div>
                <div className="relative">
                  <Label htmlFor="supplier" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Supplier</Label>
                  <Input id="supplier" defaultValue="Sun Car" />
                </div>
                <div className="relative">
                  <Label htmlFor="owner" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Owner</Label>
                  <Input id="owner" defaultValue="Dinul Zoysa" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="mileage" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Mileage</Label>
                  <Input id="mileage" defaultValue="1900000" />
                </div>
                <div className="relative">
                  <Label htmlFor="financeBank" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Finance Bank</Label>
                  <Input id="financeBank" defaultValue="Commercial" />
                </div>
                <div className="relative">
                  <Label htmlFor="noOfRepay" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">No of Repay</Label>
                  <Input id="noOfRepay" defaultValue="02" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="monthlyPay" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Monthly Pay</Label>
                  <Input id="monthlyPay" defaultValue="1900000" />
                </div>
                <div className="relative">
                  <Label htmlFor="balloon" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Balloon</Label>
                  <Input id="balloon" defaultValue="1900000" />
                </div>
                <div className="relative">
                  <Label htmlFor="priceIncGST" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Price-inc GST</Label>
                  <Input id="priceIncGST" defaultValue="150000" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="wdv" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">WDV</Label>
                  <Input id="wdv" defaultValue="300000" />
                </div>
                <div className="relative">
                  <Label htmlFor="purchasePrice" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Purchase Price</Label>
                  <Input id="purchasePrice" defaultValue="1900000" />
                </div>
                <div className="relative">
                  <Label htmlFor="expectedRestValue" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Expected Rest Value</Label>
                  <Input id="expectedRestValue" defaultValue="3000000" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="odometerAtPurchase" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Odometer at Purchase</Label>
                  <Input id="odometerAtPurchase" className="max-w-xs" />
                </div>
                <div className="md:col-span-1 relative">
                  <Label htmlFor="depreciationTime" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Depreciation Time (In Months)</Label>
                  <Input id="depreciationTime" className="max-w-xs" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Details Section */}
          <Card>
            <CardHeader>
              <CardTitle>Vehicle Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="vehicleKey" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Vehicle Key</Label>
                  <Input id="vehicleKey" defaultValue="ZCTM948843" />
                </div>
                <div className="relative">
                  <Label htmlFor="oldRegistration" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Old Rego</Label>
                  <Input id="oldRegistration" defaultValue="RCA 445" />
                </div>
                <div className="relative">
                  <Label htmlFor="make" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Make</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Toyota" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="toyota">Toyota</SelectItem>
                      <SelectItem value="mercedes">Mercedes</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="model" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Model</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Camry" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="camry">Camry</SelectItem>
                      <SelectItem value="e280">E 280 CDI</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="relative">
                  <Label htmlFor="vin" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">VIN</Label>
                  <Input id="vin" defaultValue="MERTKNJNCHAM2000156" />
                </div>
                <div className="relative">
                  <Label htmlFor="type" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Type</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Car" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="car">Car</SelectItem>
                      <SelectItem value="van">Van</SelectItem>
                      <SelectItem value="truck">Truck</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="yom" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">YOM</Label>
                  <Input id="yom" defaultValue="2006/1990" />
                </div>
                <div className="relative">
                  <Label htmlFor="lastMileage" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Last Mileage</Label>
                  <Input id="lastMileage" defaultValue="1900000" />
                </div>
                <div className="relative">
                  <Label htmlFor="transmiss" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Transmiss</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Auto" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="auto">Auto</SelectItem>
                      <SelectItem value="manual">Manual</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="colour" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Colour</Label>
                  <Input id="colour" defaultValue="White" />
                </div>
                <div className="relative">
                  <Label htmlFor="fuel" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Fuel</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="8" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="1">1</SelectItem>
                      <SelectItem value="2">2</SelectItem>
                      <SelectItem value="3">3</SelectItem>
                      <SelectItem value="4">4</SelectItem>
                      <SelectItem value="5">5</SelectItem>
                      <SelectItem value="6">6</SelectItem>
                      <SelectItem value="7">7</SelectItem>
                      <SelectItem value="8">8</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="relative">
                  <Label htmlFor="recordDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Record Date</Label>
                  <Input id="recordDate" defaultValue="29/08/2025" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="capacity" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Capacity</Label>
                  <Input id="capacity" defaultValue="5" />
                </div>
                <div className="relative">
                  <Label htmlFor="dimension" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Dimensions</Label>
                  <Input id="dimension" defaultValue="4 x 4 x 4 foot" />
                </div>
                <div className="relative">
                  <Label htmlFor="availableDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Available Date</Label>
                  <Input id="availableDate" defaultValue="26/06/2025" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="availableUntil" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Available Until</Label>
                  <Input id="availableUntil" defaultValue="26/06/2025" />
                </div>
                <div className="relative">
                  <Label htmlFor="vehicleEntryDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Vehicle Entry Date</Label>
                  <Input id="vehicleEntryDate" defaultValue="26/06/2025" />
                </div>
                <div className="relative">
                  <Label htmlFor="vehicleExitDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Vehicle Exit Date</Label>
                  <Input id="vehicleExitDate" defaultValue="26/06/2025" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="status" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Status</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Available" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="Available">Available</SelectItem>
                      <SelectItem value="Unavailable">Unavailable</SelectItem>
                      <SelectItem value="Maintenance">Maintenance</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Rates Section */}
          <Card>
            <CardHeader>
              <CardTitle>Rates</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="relative">
                  <Label htmlFor="rate1" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Rates 1-3</Label>
                  <Input id="rate1" defaultValue="$37.00" />
                </div>
                <div className="relative">
                  <Label htmlFor="rate4" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Rates 4-6</Label>
                  <Input id="rate4" defaultValue="$35.00" />
                </div>
                <div className="relative">
                  <Label htmlFor="rate7" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Rates 7+</Label>
                  <Input id="rate7" defaultValue="$32.00" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4 mt-4">
                <div className="relative">
                  <Label htmlFor="longTerm" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Long Term</Label>
                  <Input id="longTerm" defaultValue="$10.00" />
                </div>
                <div className="relative">
                  <Label htmlFor="rb" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">RB</Label>
                  <Input id="rb" defaultValue="$37.00" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Vehicle Further Details */}
          <Card>
            <CardHeader>
              <CardTitle>Vehicle Further Details</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="location" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Location</Label>
                  <Input id="location" defaultValue="Sunnathani" />
                </div>
                <div className="relative">
                  <Label htmlFor="regoExpiry" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Rego Expiry</Label>
                  <Input id="regoExpiry" defaultValue="08/08/2025" />
                </div>
                <div className="relative">
                  <Label htmlFor="rfid" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Insurance Class</Label>
                  <Input id="rfid" defaultValue="SDP" />
                </div>
              </div>
              <div className="grid grid-cols-3 gap-4">
                <div className="relative">
                  <Label htmlFor="roadSide" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Road Side</Label>
                  <Input id="roadSide" />
                </div>
                <div className="relative">
                  <Label htmlFor="fuelType" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Fuel Type</Label>
                  <Input id="fuelType" />
                </div>
                <div className="relative">
                  <Label htmlFor="serviceFrequency" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Service Frequency</Label>
                  <Input id="serviceFrequency" defaultValue="5000Km" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Upload Images Section */}
          <Card>
            <CardHeader>
              <CardTitle>Upload Images</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-4 gap-4">
                {images.map((image) => (
                  <div key={image.id} className="flex items-center justify-between p-3 border rounded">
                    <span className="text-sm">{image.name}</span>
                    <div className="flex gap-2">
                      {image.url && (
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => handleViewImage(image.url!)}
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </CardContent>
          </Card>

          {/* Insurance Providers */}
          <Card>
            <CardHeader>
              <CardTitle>Insurance Providers</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="relative w-1/2">
                <Label htmlFor="companyName" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Company Name</Label>
                <Input id="companyName" placeholder="eg. SLIC etc" />
              </div>
              <div className="mt-4 w-1/2">
                <Label>Available Policies</Label>
                <div className="mt-2 relative">
                  <Label htmlFor="policy" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Policy</Label>
                  <div className="flex gap-2">
                    <Input id="policy" placeholder="eg. Accident, Damage etc" />
                    <Button variant="outline" size="sm">
                      <Plus className="w-4 h-4" />
                    </Button>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* GPS Section */}
          <Card>
            <CardHeader>
              <CardTitle>GPS</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4">
                <div className="relative">
                  <Label htmlFor="gpsStatus" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">GPS Status</Label>
                  <Input id="gpsStatus" />
                </div>
                <div className="relative">
                  <Label htmlFor="gpsDevice" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">GPS - Device</Label>
                  <Input id="gpsDevice" />
                </div>
                <div className="relative">
                  <Label htmlFor="gpsPhoneNo" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">GPS - Phone No</Label>
                  <Input id="gpsPhoneNo" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Quality Section */}
          <Card>
            <CardHeader>
              <CardTitle>Quality</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="relative">
                  <Label htmlFor="lastDetailClean" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Last Detail Clean</Label>
                  <Input id="lastDetailClean" className="w-full" />
                </div>
                <div className="relative">
                  <Label htmlFor="lastService" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Last Service</Label>
                  <Input id="lastService" className="w-full" />
                </div>
                <div className="relative">
                  <Label htmlFor="lastInspection" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Last Inspection</Label>
                  <Input id="lastInspection" className="w-full" />
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Disposal Section */}
          <Card>
            <CardHeader>
              <CardTitle>Disposal</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="grid grid-cols-3 gap-4 mb-4">
                <div className="relative">
                  <Label htmlFor="disposalDate" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Date</Label>
                  <Input id="disposalDate" placeholder="eg. 26/06/2022" />
                </div>
                <div className="relative">
                  <Label htmlFor="value" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Value</Label>
                  <Input id="value" placeholder="eg. fire/old" />
                </div>
                <div className="relative">
                  <Label htmlFor="byuer" className="absolute left-3 top-[-7px] bg-white px-2 text-xs text-gray-600 z-10">Byuer</Label>
                  <Input id="byuer" />
                </div>
              </div>
              <div>
                <Label htmlFor="disposalNotes">Notes</Label>
                <Textarea id="disposalNotes" />
              </div>
            </CardContent>
          </Card>

          {/* Note Section */}
          <Card>
            <CardHeader>
              <CardTitle>Note</CardTitle>
            </CardHeader>
            <CardContent>
              <div>
                <Label htmlFor="notes">Notes</Label>
                <Textarea id="notes" rows={4} />
              </div>
            </CardContent>
          </Card>

          {/* Update Button */}
          <div className="flex justify-end">
            <Button className="bg-red-600 hover:bg-red-700 text-white">
              Update
            </Button>
          </div>
        </div>

        {/* Right Column - Maintenance History */}
        <div>
          <Card>
            <CardHeader>
              <CardTitle>Maintenance History</CardTitle>
            </CardHeader>
            <CardContent>
              <div className="space-y-4">
                <div className="flex justify-between items-center text-sm">
                  <span>Type</span>
                  <span>Last Service At</span>
                  <span>Next Service At</span>
                  <span>Action</span>
                </div>
                <div className="flex justify-between items-center text-sm">
                  {/* <span>Basic Maintenance Every 5000 Km</span>
                  <span>0:59:04am</span>
                  <span>17:04:04am</span> */}
                  <Button variant="outline" size="sm">
                    <Eye className="w-4 h-4" />
                  </Button>
                </div>
                <div className="mt-4">
                  <Button variant="outline" size="sm">
                    Add+
                  </Button>
                </div>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>

      {/* Image View Dialog */}
      <Dialog open={isImageDialogOpen} onOpenChange={setIsImageDialogOpen}>
        <DialogContent className="max-w-4xl">
          <DialogHeader>
            <DialogTitle>View Image</DialogTitle>
          </DialogHeader>
          <div className="flex justify-center">
            {selectedImage ? (
              <img 
                src={selectedImage} 
                alt="Vehicle" 
                className="max-w-full max-h-96 object-contain"
              />
            ) : (
              <div className="w-full h-64 bg-gray-100 flex items-center justify-center">
                <span className="text-gray-500">No image available</span>
              </div>
            )}
          </div>
          <div className="flex justify-center gap-4 mt-4">
            <Button
              variant="outline"
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.onchange = (e) => {
                  const file = (e.target as HTMLInputElement).files?.[0];
                  if (file) {
                    const url = URL.createObjectURL(file);
                    setSelectedImage(url);
                  }
                };
                input.click();
              }}
            >
              <Upload className="w-4 h-4 mr-2" />
              Upload New Image
            </Button>
            <Button variant="outline" onClick={() => setIsImageDialogOpen(false)}>
              Close
            </Button>
          </div>
        </DialogContent>
      </Dialog>
    </div>
  );
}