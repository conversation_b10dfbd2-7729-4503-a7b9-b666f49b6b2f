export function StaffDashboard() {
  return (
    <div className="p-6">
      <div className="mb-6">
        <h1 className="text-3xl font-bold text-gray-900">Staff Dashboard</h1>
        <p className="text-gray-600 mt-2">Welcome to the Lion Rentals Staff Portal</p>
      </div>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6 mb-8">
        {/* Stats Cards */}
        <div className="bg-white rounded-lg shadow p-6 border-l-4 border-blue-500">
          <div className="flex items-center">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">Pending Tasks</h3>
              <p className="text-3xl font-bold text-blue-600">12</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 border-l-4 border-green-500">
          <div className="flex items-center">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">Completed Today</h3>
              <p className="text-3xl font-bold text-green-600">8</p>
            </div>
          </div>
        </div>
        
        <div className="bg-white rounded-lg shadow p-6 border-l-4 border-orange-500">
          <div className="flex items-center">
            <div className="flex-1">
              <h3 className="text-lg font-semibold text-gray-900">Support Tickets</h3>
              <p className="text-3xl font-bold text-orange-600">5</p>
            </div>
          </div>
        </div>
      </div>
      
      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        {/* Today's Tasks */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Today's Tasks</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-blue-50 rounded border-l-4 border-blue-400">
              <div>
                <span className="text-sm font-medium text-gray-900">Vehicle Inspection - BMW X3</span>
                <p className="text-xs text-gray-600">License: ABC-123</p>
              </div>
              <span className="text-xs text-blue-600 font-medium">High Priority</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-green-50 rounded border-l-4 border-green-400">
              <div>
                <span className="text-sm font-medium text-gray-900">Customer Support - John Doe</span>
                <p className="text-xs text-gray-600">Booking inquiry</p>
              </div>
              <span className="text-xs text-green-600 font-medium">Normal</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-yellow-50 rounded border-l-4 border-yellow-400">
              <div>
                <span className="text-sm font-medium text-gray-900">Maintenance Schedule Review</span>
                <p className="text-xs text-gray-600">Weekly maintenance check</p>
              </div>
              <span className="text-xs text-yellow-600 font-medium">Medium</span>
            </div>
          </div>
        </div>
        
        {/* Recent Activities */}
        <div className="bg-white rounded-lg shadow p-6">
          <h3 className="text-lg font-semibold text-gray-900 mb-4">Recent Activities</h3>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm text-gray-600">Completed vehicle inspection for Toyota Camry</span>
              <span className="text-xs text-gray-400">1 hour ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm text-gray-600">Resolved customer support ticket #1234</span>
              <span className="text-xs text-gray-400">2 hours ago</span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded">
              <span className="text-sm text-gray-600">Updated maintenance schedule for Ford Explorer</span>
              <span className="text-xs text-gray-400">3 hours ago</span>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}
