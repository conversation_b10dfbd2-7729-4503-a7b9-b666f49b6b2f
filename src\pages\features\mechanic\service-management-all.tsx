import React, { useEffect } from 'react';
import { Search, RotateCcw, Eye } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate, useLocation } from 'react-router-dom';
import { useServiceManagementAll } from './hook/useservice-management-all';
import { ServiceManagementAll } from './type/mechanictype';
import { Card } from '@/components/ui/card';

export function ServiceManagementAllPage() {
  const navigate = useNavigate();
  const location = useLocation();
  const {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    activeTab,
    setActiveTab,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    currentData,
    totalPages,
    totalEntries,
    filteredData, // Add this to get all filtered data for mobile
  } = useServiceManagementAll();

  useEffect(() => {
    if (location.state && location.state.tab) {
      setActiveTab(location.state.tab);
    } else if (!activeTab) {
      setActiveTab("All");
    }
    // eslint-disable-next-line
  }, [location.key]);

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Done':
        return 'bg-green-500 text-white';
      case 'Pending':
        return 'bg-gray-400 text-white';
      case 'InProgress':
        return 'bg-blue-500 text-white';
      default:
        return '';
    }
  };

  // Tab navigation handler
  const handleTabClick = (tab: string) => {
    if (tab === "All" || tab === "Due This Week") {
      navigate("/mechanic/service-management-all", { state: { tab } });
    } else if (tab === "General Maintenance") {
      navigate("/mechanic/maintainance-service");
    } else if (tab === "Accident") {
      navigate("/mechanic/accident-service");
    } else if (tab === "Breakdowns") {
      navigate("/mechanic/breakdown-service");
    } else if (tab === "Damage") {
      navigate("/mechanic/damage-service");
    }
  };

  // Responsive Card for Mobile/Tablet
  const ServiceCard = ({ item }: { item: ServiceManagementAll }) => (
    <Card className="relative mb-4 p-4 shadow-md border border-gray-200 rounded-lg">
      {/* Header: Vehicle ID and Status */}
      <div className="flex justify-between items-start mb-4">
        <span className="text-lg font-semibold text-blue-600">{item.vehicleId}</span>
        <div className="flex flex-col gap-2 items-center">
          <span
            className={`px-3 py-1 rounded text-sm font-medium w-24 flex justify-center items-center ${getStatusColor(item.status)}`}
          >
            {item.status}
          </span>
          {/* If you want to add a second badge, add it here */}
          {/* <span className="...">Vehicle Status: ...</span> */}
        </div>
      </div>
      {/* Vehicle Name */}
      <div className="flex items-center mb-3">
        <span className="text-sm font-medium text-gray-700">{item.vehicle}</span>
      </div>
      {/* Service Type */}
      <div className="mb-2">
        <span className="text-xs text-gray-500 uppercase font-medium">Service</span>
        <div className="text-sm">{item.serviceTypes}</div>
      </div>
      {/* Date In/Out */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium mb-1">Date In</div>
          <div className="text-sm">{item.dateIn}</div>
        </div>
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium mb-1">Date Out</div>
          <div className="text-sm">{item.dateOut}</div>
        </div>
      </div>
      {/* Actions at the bottom */}
      <div className="flex justify-end gap-2 pt-3 border-t border-gray-100">
        <Button
          onClick={() => navigate(`/mechanic/service-management-all-view/${item.vehicleId}`)}
          variant="ghost"
          size="sm"
          className="text-gray-600 hover:text-gray-800"
        >
          <Eye className="w-4 h-4 mr-1" />
          View
        </Button>
      </div>
    </Card>
  );

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <RotateCcw className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Service Management (All)</h1>
        </div>
      </div>

      {/* Tab Bar */}
      <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
        {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
          <Button
            key={tab}
            variant={activeTab === tab ? 'default' : 'outline'}
            onClick={() => handleTabClick(tab)}
            className="text-sm md:text-base"
          >
            {tab}
          </Button>
        ))}
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="InProgress">InProgress</SelectItem>
              <SelectItem value="Done">Done</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="block md:hidden">
        {filteredData.length > 0 ? (
          filteredData.map((item: ServiceManagementAll) => (
            <ServiceCard key={item.vehicleId} item={item} />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            No records found matching your search criteria.
          </div>
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block">
        <div className="rounded-md border bg-white">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead>Vehicle ID</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Service Types</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((item: ServiceManagementAll) => (
                <TableRow key={item.vehicleId} className="hover:bg-gray-50 transition-colors">
                  <TableCell>
                    <span className="text-gray-800 font-medium">{item.vehicleId}</span>
                  </TableCell>
                  <TableCell>{item.vehicle}</TableCell>
                  <TableCell>{item.serviceTypes}</TableCell>
                  <TableCell>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.status)}`}>
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell className="px-3 py-4">
                    <Button
                      onClick={() => navigate(`/mechanic/service-management-all-view/${item.vehicleId}`)}
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                    >
                      <Eye className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        {/* Pagination for desktop only */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-6"
        />
      </div>
    </div>
  );
}