import React, { useState, useEffect } from 'react';
import { ArrowLeft, Car, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useNavigate, useParams } from 'react-router-dom';

// Define the interface for fleet rates
interface FleetRate {
  id: string;
  vehicleClass: string;
  fleet: string;
  rate1_3: number;
  rate4_6: number;
  rate6_7: number;
  sat: string;
  longTerm: number;
  perDayKM: number;
  additionalKM: number;
  fuelLevel: string;
  serviceFrequency: string;
  status: string;
}

interface RateData {
  vehicleClass: string; // Store the id (e.g., 'economyPlus')
  fleet: string;
  rate1_3: string;
  rate4_6: string;
  rate6_7: string;
  longTerm: string;
  perDayKm: string;
  additionalKm: string;
}

export function ReceptionEditRatePage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  // Mock data for fleet rates
  const mockFleetRates: FleetRate[] = [
    { id: '#FR001', vehicleClass: 'Economy', fleet: 'Passenger', rate1_3: 45.00, rate4_6: 42.00, rate6_7: 40.00, sat: '$ -', longTerm: 1000.00, perDayKM: 150, additionalKM: 0.25, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
    { id: '#FR002', vehicleClass: 'Economy Plus', fleet: 'Passenger', rate1_3: 55.00, rate4_6: 52.00, rate6_7: 50.00, sat: '$ -', longTerm: 1200.00, perDayKM: 200, additionalKM: 0.30, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
    { id: '#FR003', vehicleClass: 'Mid-size', fleet: 'Commercial', rate1_3: 65.00, rate4_6: 62.00, rate6_7: 60.00, sat: '$ -', longTerm: 1400.00, perDayKM: 250, additionalKM: 0.35, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Inactive' },
    { id: '#FR004', vehicleClass: 'SUV', fleet: 'Passenger', rate1_3: 80.00, rate4_6: 77.00, rate6_7: 75.00, sat: '$ -', longTerm: 1600.00, perDayKM: 300, additionalKM: 0.40, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
    { id: '#FR005', vehicleClass: 'Luxury', fleet: 'Passenger', rate1_3: 120.00, rate4_6: 115.00, rate6_7: 110.00, sat: '$ -', longTerm: 2000.00, perDayKM: 200, additionalKM: 0.50, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
    { id: '#FR006', vehicleClass: 'Compact', fleet: 'Passenger', rate1_3: 40.00, rate4_6: 38.00, rate6_7: 36.00, sat: '$ -', longTerm: 900.00, perDayKM: 150, additionalKM: 0.20, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Inactive' },
    { id: '#FR007', vehicleClass: 'Van', fleet: 'Commercial', rate1_3: 90.00, rate4_6: 85.00, rate6_7: 80.00, sat: '$ -', longTerm: 1800.00, perDayKM: 250, additionalKM: 0.45, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
    { id: '#FR008', vehicleClass: 'Truck', fleet: 'Commercial', rate1_3: 110.00, rate4_6: 105.00, rate6_7: 100.00, sat: '$ -', longTerm: 1900.00, perDayKM: 300, additionalKM: 0.50, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
    { id: '#FR009', vehicleClass: 'Sedan', fleet: 'Passenger', rate1_3: 60.00, rate4_6: 57.00, rate6_7: 55.00, sat: '$ -', longTerm: 1300.00, perDayKM: 200, additionalKM: 0.30, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Inactive' },
    { id: '#FR010', vehicleClass: 'Hybrid', fleet: 'Passenger', rate1_3: 70.00, rate4_6: 67.00, rate6_7: 65.00, sat: '$ -', longTerm: 1500.00, perDayKM: 250, additionalKM: 0.35, fuelLevel: '2.00', serviceFrequency: '5000km', status: 'Active' },
   ];

  const [rateData, setRateData] = useState<RateData>({
    vehicleClass: '',
    fleet: '',
    rate1_3: '',
    rate4_6: '',
    rate6_7: '',
    longTerm: '',
    perDayKm: '',
    additionalKm: ''
  });

  useEffect(() => {
    const rateId = `#${id}`;
    const rateToEdit = mockFleetRates.find(rate => rate.id === rateId);
    if (rateToEdit) {
      // Map the vehicleClass to its corresponding id (e.g., 'economyPlus' for 'Economy Plus')
      const vehicleClassId = vehicleClasses.find(vc => vc.name === rateToEdit.vehicleClass)?.id || rateToEdit.vehicleClass.toLowerCase().replace(/ /g, '');
      setRateData({
        vehicleClass: vehicleClassId,
        fleet: rateToEdit.fleet,
        rate1_3: rateToEdit.rate1_3.toString(),
        rate4_6: rateToEdit.rate4_6.toString(),
        rate6_7: rateToEdit.rate6_7.toString(),
        longTerm: rateToEdit.longTerm.toString(),
        perDayKm: rateToEdit.perDayKM.toString(),
        additionalKm: rateToEdit.additionalKM.toString()
      });
    }
  }, [id]);

  const handleVehicleClassChange = (value: string) => {
    const selectedClassName = vehicleClasses.find(vc => vc.id === value)?.name || value;
    const selectedRate = mockFleetRates.find(rate => rate.vehicleClass.toLowerCase() === selectedClassName.toLowerCase());
    if (selectedRate) {
      setRateData(prev => ({
        ...prev,
        vehicleClass: value,
        fleet: selectedRate.fleet,
        rate1_3: selectedRate.rate1_3.toString(),
        rate4_6: selectedRate.rate4_6.toString(),
        rate6_7: selectedRate.rate6_7.toString(),
        longTerm: selectedRate.longTerm.toString(),
        perDayKm: selectedRate.perDayKM.toString(),
        additionalKm: selectedRate.additionalKM.toString()
      }));
    }
  };

  const handleInputChange = (field: keyof RateData, value: string) => {
    setRateData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = () => {
    console.log('Rate data updated:', rateData);
    // Handle form submission/update logic here (e.g., update mockFleetRates or API call)
    navigate('/admin/receptionMasterAdmin/fleet/rates');
  };

  const handleCancel = () => {
    navigate('/admin/receptionMasterAdmin/fleet/rates');
  };

  const vehicleClasses = [
    { id: 'economy', name: 'Economy' },
    { id: 'economyPlus', name: 'Economy Plus' },
    { id: 'mid-size', name: 'Mid-size' },
    { id: 'suv', name: 'SUV' },
    { id: 'luxury', name: 'Luxury' },
    { id: 'compact', name: 'Compact' },
    { id: 'van', name: 'Van' },
    { id: 'truck', name: 'Truck' },
    { id: 'sedan', name: 'Sedan' },
    { id: 'hybrid', name: 'Hybrid' }
  ];

  return (
    <div className="p-6 min-h-screen">
      <div className="mb-6">
        {/* Go Back Button */}
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={() => navigate('/admin/receptionMasterAdmin/fleet/rates')}
        >
          <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
          <span className="hidden md:inline">Go Back</span>
        </Button>

        {/* Title */}
        <div className="flex items-center space-x-2 mt-4">
          <Car className="w-6 h-6 text-[#330101]" />
          <h1 className="text-2xl font-bold text-gray-900">Rates - Edit</h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Vehicle Class */}
          <div className="relative space-y-2">
            <Label htmlFor="vehicle-class" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Vehicle Class
            </Label>
            <Select value={rateData.vehicleClass} onValueChange={handleVehicleClassChange}>
              <SelectTrigger id="vehicle-class" className="h-12">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {vehicleClasses.map((vc) => (
                  <SelectItem key={vc.id} value={vc.id}>
                    {vc.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Fleet - Read-only Input */}
          <div className="relative space-y-2">
            <Label htmlFor="fleet" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Fleet
            </Label>
            <Input
              id="fleet"
              value={rateData.fleet}
              readOnly
              className="h-12 bg-gray-50"
              placeholder="Select a vehicle class"
            />
          </div>

          {/* Rate 1 - 3 */}
          <div className="relative space-y-2">
            <Label htmlFor="rate1-3" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Rate 1 - 3
            </Label>
            <Input
              id="rate1-3"
              placeholder="eg. $ 27.00"
              className="h-12"
              value={rateData.rate1_3}
              onChange={(e) => handleInputChange('rate1_3', e.target.value)}
            />
          </div>

          {/* Rate 4 - 6 */}
          <div className="relative space-y-2">
            <Label htmlFor="rate4-6" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Rate 4 - 6
            </Label>
            <Input
              id="rate4-6"
              placeholder="eg. $ 34.00"
              className="h-12"
              value={rateData.rate4_6}
              onChange={(e) => handleInputChange('rate4_6', e.target.value)}
            />
          </div>

          {/* Rate 6 - 7 */}
          <div className="relative space-y-2">
            <Label htmlFor="rate6-7" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Rate 6 - 7
            </Label>
            <Input
              id="rate6-7"
              placeholder="eg. $ 30.00"
              className="h-12"
              value={rateData.rate6_7}
              onChange={(e) => handleInputChange('rate6_7', e.target.value)}
            />
          </div>

          {/* Long Term */}
          <div className="relative space-y-2">
            <Label htmlFor="long-term" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Long Term
            </Label>
            <Input
              id="long-term"
              placeholder="eg. $ 182.00"
              className="h-12"
              value={rateData.longTerm}
              onChange={(e) => handleInputChange('longTerm', e.target.value)}
            />
          </div>

          {/* Per Day km */}
          <div className="relative space-y-2">
            <Label htmlFor="per-day-km" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Per Day km
            </Label>
            <Input
              id="per-day-km"
              placeholder="eg. 250/km"
              className="h-12"
              value={rateData.perDayKm}
              onChange={(e) => handleInputChange('perDayKm', e.target.value)}
            />
          </div>

          {/* Additional km (per km) */}
          <div className="relative space-y-2">
            <Label htmlFor="additional-km" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Additional km (per km)
            </Label>
            <Input
              id="additional-km"
              placeholder="eg. $0.20"
              className="h-12"
              value={rateData.additionalKm}
              onChange={(e) => handleInputChange('additionalKm', e.target.value)}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 mt-8">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="px-6 py-2"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]"
          >
            Save
          </Button>
        </div>
      </div>
    </div>
  );
}