import React, { useState } from 'react';
import { Search, Eye, UserSearch } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';

interface ReceptionData {
  receptionId: string;
  name: string;
  contactNumber: string;
  location: string;
  status: 'Active' | 'Inactive';
}

const receptionData: ReceptionData[] = [
  {
    receptionId: 'RS0001',
    name: '<PERSON> Sophia',
    contactNumber: '+61 4 1234 5678',
    location: 'Somerton Head Office',
    status: 'Active'
  },
  {
    receptionId: 'RS0002',
    name: '<PERSON>',
    contactNumber: '+61 4 1234 5678',
    location: 'Somerton Head Office',
    status: 'Active'
  },
  {
    receptionId: 'RS0003',
    name: '<PERSON><PERSON>',
    contactNumber: '+61 4 1234 5678',
    location: 'Somerton Head Office',
    status: 'Active'
  },
  {
    receptionId: 'RS0004',
    name: 'Harry Jhosaph',
    contactNumber: '+61 4 1234 5678',
    location: 'Somerton Head Office',
    status: 'Active'
  },
  {
    receptionId: 'RS0005',
    name: 'Hash Fernando',
    contactNumber: '+61 4 1234 5678',
    location: 'Somerton Head Office',
    status: 'Inactive'
  }
];

export function ReceptionManagementPage() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  // Filter bookings based on search and filter
  const filteredReceptions = receptionData.filter((reception) => {
    const matchesSearch = 
      reception.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      reception.contactNumber.includes(searchTerm) ||
      reception.receptionId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || reception.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  // For mobile view - show all filtered requests (no pagination)
  const mobileBookings = filteredReceptions;

  // For desktop view - paginated requests
  const totalRecords = filteredReceptions.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentBookings = filteredReceptions.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const getStatusBadge = (status: string): string => {
    switch (status) {
      case 'Active':
        return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Inactive':
        return 'bg-red-500 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };

  const navigate = useNavigate();

  const handleReception = (receptionId: string) => {
    navigate(`/reception/reception-dashboard/${receptionId}`);
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex items-center space-x-2 mb-4">
        <UserSearch className="h-5 w-5 text-gray-600" />
        <h1 className="text-2xl font-semibold text-gray-900">Reception Management</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterStatus}
            onValueChange={(value) => setFilterStatus(value)}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Active">Active</SelectItem>
              <SelectItem value="Inactive">Inactive</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Mobile View */}
      <div className="block md:hidden">
        {mobileBookings.length === 0 ? (
          <p className="text-gray-500 text-center">No receptions found.</p>
        ) : (
          mobileBookings.map((item) => (
            <div key={item.receptionId} className="border-b py-4">
              <div className="flex justify-between">
                <div>
                  <p className="font-medium">{item.name}</p>
                  <p className="text-sm text-gray-500">{item.receptionId}</p>
                  <p className="text-sm text-gray-500">{item.contactNumber}</p>
                  <p className="text-sm text-gray-500">{item.location}</p>
                </div>
                <div className="flex items-center space-x-2">
                  <span className={getStatusBadge(item.status)}>{item.status}</span>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleReception(item.receptionId)}
                  >
                    <Eye className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Desktop Data Table */}
      <div className="hidden md:block">
        <div className="rounded-md border bg-white">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead>Reception ID</TableHead>
                <TableHead>Name</TableHead>
                <TableHead>Contact Number</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentBookings.map((item) => (
                <TableRow key={item.receptionId}>
                  <TableCell>{item.receptionId}</TableCell>
                  <TableCell>{item.name}</TableCell>
                  <TableCell>{item.contactNumber}</TableCell>
                  <TableCell>{item.location}</TableCell>
                  <TableCell>
                    <span className={getStatusBadge(item.status)}>
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => handleReception(item.receptionId)}
                    >
                      <Eye className="h-4 w-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-4 sm:mt-6"
        />
      </div>
    </div>
  );
}