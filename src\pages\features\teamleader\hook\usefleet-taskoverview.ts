import { useState, useEffect, useMemo } from 'react'
import { useNavigate, useLocation } from 'react-router-dom'
import { FleetTaskData } from '../type/teamleadertype'

export function useFleetTaskOverview() {
  const navigate = useNavigate()
  const location = useLocation()
  const [currentPage, setCurrentPage] = useState(1)
  const [recordsPerPage, setRecordsPerPage] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('All')

  // Function to get initial data from localStorage or return default data
  const getInitialData = (): FleetTaskData[] => {
    // Try to get data from localStorage first, fallback to default data
    const savedData = localStorage.getItem('fleetTaskData')
    if (savedData) {
      try {
        const parsedData = JSON.parse(savedData)
        // Check if the data has taskId field, if not, reset to default
        if (parsedData.length > 0 && !parsedData[0].taskId) {
          console.log('Resetting localStorage data to include taskId field')
          localStorage.removeItem('fleetTaskData')
          return getDefaultData()
        }
        return parsedData
      } catch (error) {
        console.error('Error parsing localStorage data:', error)
        localStorage.removeItem('fleetTaskData')
        return getDefaultData()
      }
    }
    
    return getDefaultData()
  }

  // Function to get default data structure
  const getDefaultData = (): FleetTaskData[] => {
    return [
      {
        id: '1',
        taskId: 'TO001',
        rego: '1PX 12R',
        lastService: 'Oil Change',
        lastServiceDate: '2025-07-10',
        status: 'Pending'
      },
      {
        id: '2',
        taskId: 'TO002',
        rego: 'PCR 455',
        lastService: 'Brake Service',
        lastServiceDate: '2025-07-08',
        status: 'Pending'
      },
      {
        id: '3',
        taskId: 'TO003',
        rego: '1PY 2TR',
        lastService: 'Full Service',
        lastServiceDate: '2025-07-06',
        status: 'Pending'
      },
      {
        id: '4',
        taskId: 'TO004',
        rego: 'ASR 321',
        lastService: 'Tire Rotation',
        lastServiceDate: '2025-07-09',
        status: 'Completed'
      },
      {
        id: '5',
        taskId: 'TO005',
        rego: 'QBV 233',
        lastService: 'Engine Check',
        lastServiceDate: '2025-07-05',
        status: 'Pending'
      },
      {
        id: '6',
        taskId: 'TO006',
        rego: 'MNO 789',
        lastService: 'Battery Check',
        lastServiceDate: '2025-07-07',
        status: 'Pending'
      },
      {
        id: '7',
        taskId: 'TO007',
        rego: 'XYZ 456',
        lastService: 'Air Filter',
        lastServiceDate: '2025-07-04',
        status: 'Pending'
      },
    ]
  }

  const [sampleData, setSampleData] = useState<FleetTaskData[]>(getInitialData)

  // Function to generate next task ID
  const generateNextTaskId = (): string => {
    const existingTaskIds = sampleData.map(task => task.taskId)
    const numbers = existingTaskIds
      .filter(id => id.startsWith('TO'))
      .map(id => parseInt(id.substring(2), 10))
      .filter(num => !isNaN(num))
    
    const maxNumber = numbers.length > 0 ? Math.max(...numbers) : 0
    const nextNumber = maxNumber + 1
    return `TO${nextNumber.toString().padStart(3, '0')}`
  }

  // Function to add a new task
  const addNewTask = (taskData: Omit<FleetTaskData, 'id' | 'taskId'>) => {
    const newId = (sampleData.length + 1).toString()
    const newTaskId = generateNextTaskId()
    
    const newTask: FleetTaskData = {
      id: newId,
      taskId: newTaskId,
      ...taskData
    }
    
    const updatedData = [...sampleData, newTask]
    setSampleData(updatedData)
    localStorage.setItem('fleetTaskData', JSON.stringify(updatedData))
  }

  // Function to reset localStorage (for debugging)
  const resetLocalStorage = () => {
    localStorage.removeItem('fleetTaskData')
    setSampleData(getDefaultData())
    console.log('localStorage reset with new data structure')
  }

  // Handle status updates from confirmation page
  useEffect(() => {
    if (location.state?.taskId && location.state?.newStatus) {
      const { taskId, newStatus } = location.state
      setSampleData(prevData => {
        const updatedData = prevData.map(task => 
          task.id === taskId 
            ? { ...task, status: newStatus }
            : task
        )
        // Save updated data to localStorage
        localStorage.setItem('fleetTaskData', JSON.stringify(updatedData))
        return updatedData
      })
      // Clear the location state to prevent re-triggering
      navigate(location.pathname, { replace: true })
    }
  }, [location.state, navigate, location.pathname])

  // Filter and search data using useMemo for performance
  const filteredData = useMemo(() => {
    return sampleData.filter((task) => {
      const matchesSearch = searchTerm === '' || 
        task.rego.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.lastService.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.lastServiceDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
        task.status.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesFilter = filterStatus === 'All' || task.status === filterStatus
      
      return matchesSearch && matchesFilter
    })
  }, [sampleData, searchTerm, filterStatus])
  
  const filteredTotalRecords = filteredData.length
  const filteredTotalPages = Math.ceil(filteredTotalRecords / recordsPerPage)
  
  // Calculate current page data from filtered results
  const currentData = useMemo(() => {
    const startIndex = (currentPage - 1) * recordsPerPage
    const endIndex = startIndex + recordsPerPage
    return filteredData.slice(startIndex, endIndex)
  }, [filteredData, currentPage, recordsPerPage])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'text-xs font-medium rounded bg-green-500 text-white px-2 py-1.5'
      case 'pending':
        return 'text-xs font-medium rounded bg-blue-800 text-white px-2 py-1.5'
      default:
        return 'text-xs font-medium rounded bg-gray-200 text-gray-700 px-2 py-1.5'
    }
  }

  const handleViewClick = (taskId: string) => {
    // Find the task to get its formatted taskId
    const task = sampleData.find(t => t.id === taskId)
    const formattedTaskId = task?.taskId || taskId
    
    // Debug logging
    console.log('handleViewClick called with:', taskId)
    console.log('Found task:', task)
    console.log('Formatted task ID:', formattedTaskId)
    console.log('Navigating to:', `/teamleader/fleet-taskoverview-view/${formattedTaskId}`)
    
    // Navigate to task overview view page with formatted task ID in URL
    navigate(`/teamleader/fleet-taskoverview-view/${formattedTaskId}`, { 
      state: { taskId: taskId, formattedTaskId: formattedTaskId } 
    })
  }

  const handleRecordsPerPageChange = (records: number) => {
    setRecordsPerPage(records)
    setCurrentPage(1) // Reset to first page when changing records per page
  }

  return {
    // State
    currentPage,
    recordsPerPage,
    searchTerm,
    filterStatus,
    
    // Computed values
    currentData,
    filteredTotalRecords,
    filteredTotalPages,
    
    // Functions
    setCurrentPage,
    setSearchTerm,
    setFilterStatus,
    getStatusColor,
    handleViewClick,
    handleRecordsPerPageChange,
    addNewTask,
    generateNextTaskId,
    resetLocalStorage,
  }
}
