import { Routes, Route } from 'react-router-dom'

import { SearchFormPage } from '@/pages/searchForm/search-form'
import { VehicleClass } from '@/pages/searchForm/vehicle-class'
import { CustomerInformationPage } from '@/pages/searchForm/customer-information'
import { ConfirmPage } from '@/pages/searchForm/confirm'
import { QuotePage } from '@/pages/searchForm/quote'
import { Extras } from '@/pages/searchForm/extras'
import { Payment } from '@/pages/searchForm/payment'
import { Receipt } from '@/pages/searchForm/receipt-invoice'



export const SearchRoutes = () => {
  return (
    <Routes>
      <Route path="/" element={<SearchFormPage />} /> 
      <Route path="/vehicle-class" element={<VehicleClass />} />
      <Route path="/customer-information" element={<CustomerInformationPage />} />
      <Route path="/confirm" element={<ConfirmPage />} />
      <Route path="/quote" element={<QuotePage />} />
      <Route path="/extras" element={<Extras/>} />
      <Route path="/payment" element={<Payment/>} />
      <Route path="/receipt-invoice" element={<Receipt/>} />
      
     

      
    </Routes>
  )
}