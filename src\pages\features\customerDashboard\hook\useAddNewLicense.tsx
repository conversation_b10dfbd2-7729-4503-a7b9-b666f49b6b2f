import { useState } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { mockNewLicense } from '../common/mockdata';

export const useAddNewLicense = (initialData: any = mockNewLicense) => {
  const [permitNumber, setPermitNumber] = useState(initialData.permitNumber);
  const [issueCountry, setIssueCountry] = useState(initialData.issueCountry);
  const [issueDate, setIssueDate] = useState<Date | undefined>(initialData.issueDate);
  const [expiryDate, setExpiryDate] = useState<Date | undefined>(initialData.expiryDate);
  const [licenseType, setLicenseType] = useState(initialData.licenseType);
  const [frontView, setFrontView] = useState<File | null>(initialData.frontView);
  const [backView, setBackView] = useState<File | null>(initialData.backView);
  const [showIssueCalendar, setShowIssueCalendar] = useState(false);
  const [showExpiryCalendar, setShowExpiryCalendar] = useState(false);

  const handleFrontViewChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setFrontView(event.target.files[0]);
    }
  };

  const handleBackViewChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setBackView(event.target.files[0]);
    }
  };

  const handleSubmit = (navigate: NavigateFunction) => {
    console.log('License added:', { permitNumber, issueCountry, issueDate, expiryDate, licenseType, frontView, backView });
    navigate('/customer/editprofile');
  };

  const handleBack = (navigate: NavigateFunction) => {
    navigate('/customer/editprofile');
  };

  return {
    permitNumber,
    setPermitNumber,
    issueCountry,
    setIssueCountry,
    issueDate,
    setIssueDate,
    expiryDate,
    setExpiryDate,
    licenseType,
    setLicenseType,
    frontView,
    setFrontView,
    backView,
    setBackView,
    showIssueCalendar,
    setShowIssueCalendar,
    showExpiryCalendar,
    setShowExpiryCalendar,
    handleFrontViewChange,
    handleBackViewChange,
    handleSubmit,
    handleBack,
  };
};