import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { mockIncidentReports } from '../common/mockdata';

export const useIncidentReporting = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const navigate = useNavigate();

  const filteredBookings = mockIncidentReports.filter(booking => {
    const matchesSearch = booking.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         booking.id.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || booking.reportType === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const mobileBookings = filteredBookings;

  const totalRecords = filteredBookings.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentBookings = filteredBookings.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    mobileBookings,
    currentBookings,
    totalPages,
    totalRecords,
    navigate,
  };
};