import { ChangeRequest, Invoice } from "../type/customer-type";


export const mockCustomerProfile = {
  customerId: 'CID0001',
  firstName: 'Pradeep',
  lastName: 'Testing',
  email: '<EMAIL>',
  address: '2/85 Hume Hwy, Somerton VIC 3062',
  postCode: '3062',
  country: 'Australia',
  birthday: '02/01/1960',
  phonenumber: '0421 234 567',
  companyName: 'Lion car Rentals',
  emergencyContactName: '<PERSON>',
  emergencyContactPhone: '+**********',
  licenseNumber: '********',
  licenseIssueDate: '01/01/2020',
  licenseExpiryDate: '01/01/2030',
  licenseCountry: 'Australia',
  licenseType: 'Full License',
  isCorporate: true,
};

export const mockEditProfile = {
  customerId: 'CID0001',
  firstName: 'Pradeep',
  lastName: 'Testing',
  email: '<EMAIL>',
  address: '2/85 Hume Hwy, Somerton VIC 3062',
  postCode: '3062',
  country: 'Australia',
  birthday: '02/01/1960',
  phonenumber: '0421 234 567',
  companyName: 'Lion Car Rentals',
  emergencyContactName: '<PERSON>',
  emergencyContactPhone: '+**********',
  licenseNumber: '********',
  licenseIssueDate: '01/01/2020',
  licenseExpiryDate: '01/01/2030',
  licenseCountry: 'Australia',
  licenseType: 'Full License',
  isCorporate: true,
};

export const mockNewLicense = {
  permitNumber: '12345678',
  issueCountry: 'Australia',
  issueDate: new Date('2022-01-01'),
  expiryDate: new Date('2027-01-01'),
  licenseType: 'Full License',
  frontView: null,
  backView: null,
};

export const mockBookingsData = [
  {
    id: '#0026',
    pickupDate: '10-07-2025',
    pickupTime: '10:30',
    returnDate: '11-07-2025',
    returnTime: '10:30',
    vehicle: 'PCR 455',
    totalPrice: 592.00,
    outstandingBalance: 492.00,
    status: 'Rental',
    loanVehicle: 'Yes',
  },
  {
    id: '#0025',
    pickupDate: '13-07-2025',
    pickupTime: '08:16',
    returnDate: '14-07-2025',
    returnTime: '08:16',
    vehicle: 'Isuzu Nmr 45 150 - 2A05QR',
    totalPrice: 550.00,
    outstandingBalance: 450.00,
    status: 'Open',
    loanVehicle: 'No',
  },
  {
    id: '#0024',
    pickupDate: '11-01-2025',
    pickupTime: '21:16',
    returnDate: '24-01-2025',
    returnTime: '21:16',
    vehicle: 'Toyota Hiace Commuter - 2BH88D',
    totalPrice: 592.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No',
  },
  {
    id: '#0023',
    pickupDate: '20-12-2024',
    pickupTime: '21:16',
    returnDate: '24-12-2024',
    returnTime: '21:16',
    vehicle: 'DEF 456',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No',
  },
  {
    id: '#0022',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No',
  },
  {
    id: '#0021',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No',
  },
  {
    id: '#0020',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No',
  },
  {
    id: '#0019',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No',
  },
  {
    id: '#0018',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No',
  },
  {
    id: '#0017',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No',
  },
];

export const mockBookingData = {
  id: '1',
  outstandingBalance: 594.00,
  pickupDate: '2024-01-15',
  pickupTime: '10:00 AM',
  returnDate: '2024-01-16',
  returnTime: '10:00 AM',
  vehicle: 'Toyota Camry',
  totalPrice: 532.52,
  loanVehicle: 'Yes',
};

export const mockInvoiceItems = [
  { description: 'Eco Plus Car', rate: 'AUD 38.18 Per Day', days: '1 Day', amount: '38.18' },
  { description: 'Bond Compulsory - 500', rate: '', days: '', amount: '500.00' },
  { description: 'Insurance', rate: '', days: '', amount: '13.64' },
  { description: 'Child Seat', rate: 'AUD 36.36 Per Day', days: '1 Day', amount: '36.36' },
];

export const mockIncidentReports = [
  {
    id: '#0036',
    vehicle: 'PCR 455',
    pickupDate: '09-07-2025',
    pickupTime: '10:30',
    returnDate: '10-07-2025',
    returnTime: '11:30',
    incidentDate: '10-07-2025',
    reportType: 'Breakdown',
    vehicleReplaced: 'Yes',
    replacedVehicle: 'ACD 300',
    replacedDate: '10-07-2025',
  },
  {
    id: '#0034',
    vehicle: 'XBB 211',
    pickupDate: '08-07-2025',
    pickupTime: '12.30',
    returnDate: '10-07-2025',
    returnTime: '10.00',
    incidentDate: '10-07-2025',
    reportType: 'Accident',
    vehicleReplaced: 'No',
    replacedVehicle: '-',
    replacedDate: '-',
  },
  {
    id: '#0033',
    vehicle: 'PPR 255',
    pickupDate: '07-07-2025',
    pickupTime: '09:00',
    returnDate: '09-07-2025',
    returnTime: '12:00',
    incidentDate: '09-07-2025',
    reportType: 'General Maintenance',
    vehicleReplaced: 'Yes',
    replacedVehicle: 'KAB 344',
    replacedDate: '09-07-2025',
  },
  {
    id: '#0032',
    vehicle: 'NAA 455',
    pickupDate: '07-07-2025',
    pickupTime: '10:30',
    returnDate: '08-07-2025',
    returnTime: '12:30',
    incidentDate: '08-07-2025',
    reportType: 'Damage',
    vehicleReplaced: 'Yes',
    replacedVehicle: 'ACD 300',
    replacedDate: '08-07-2025',
  },
  {
    id: '#0031',
    vehicle: 'PCR 455',
    pickupDate: '07-07-2025',
    pickupTime: '10:00',
    returnDate: '07-07-2025',
    returnTime: '16:00',
    incidentDate: '07-07-2025',
    reportType: 'Accident',
    vehicleReplaced: 'No',
    replacedVehicle: '-',
    replacedDate: '-',
  },
  {
    id: '#0030',
    vehicle: 'PCR 455',
    pickupDate: '06-07-2025',
    pickupTime: '10:30',
    returnDate: '07-07-2025',
    returnTime: '15:30',
    incidentDate: '07-07-2025',
    reportType: 'Breakdown',
    vehicleReplaced: 'Yes',
    replacedVehicle: 'ACD 300',
    replacedDate: '07-07-2025',
  },
  {
    id: '#0029',
    vehicle: 'QAB 567',
    pickupDate: '04-07-2025',
    pickupTime: '11:30',
    returnDate: '06-07-2025',
    returnTime: '10:30',
    incidentDate: '06-07-2025',
    reportType: 'Damage',
    vehicleReplaced: 'No',
    replacedVehicle: '-',
    replacedDate: '-',
  },
  {
    id: '#0028',
    vehicle: 'PCR 332',
    pickupDate: '04-07-2025',
    pickupTime: '11:00',
    returnDate: '05-07-2025',
    returnTime: '10:30',
    incidentDate: '05-07-2025',
    reportType: 'Accident',
    vehicleReplaced: 'Yes',
    replacedVehicle: 'AXZ 300',
    replacedDate: '05-07-2025',
  },
  {
    id: '#0027',
    vehicle: 'PCR 455',
    pickupDate: '03-07-2025',
    pickupTime: '10:30',
    returnDate: '04-07-2025',
    returnTime: '10:30',
    incidentDate: '04-07-2025',
    reportType: 'General Maintenance',
    vehicleReplaced: 'No',
    replacedVehicle: 'BCX 230',
    replacedDate: '04-07-2025',
  },
];

export const mockReservations = [
  { rego: 'SQA 758', rentalId: '#0145', pickupDate: new Date('2025-05-14T14:45:00'), returnDate: new Date('2025-05-20T14:45:00'), agreementNo: '072' },
  { rego: 'ABC 123', rentalId: '#0146', pickupDate: new Date('2025-06-01T09:00:00'), returnDate: new Date('2025-06-07T09:00:00'), agreementNo: '073' },
  { rego: 'XYZ 789', rentalId: '#0147', pickupDate: new Date('2025-07-01T12:00:00'), returnDate: new Date('2025-07-08T12:00:00'), agreementNo: '074' },
];

export const accidentMockData = {
  countries: ['Australia', 'USA', 'UK', 'Canada'],
  conditions: ['None', 'Glasses Required', 'Automatic Only', 'Daylight Only'],
};

export const policeMockData = {
  defaultCountry: 'Australia',
};

export const signatureDefaultData = {
  defaultCompletedBy: 'Ezel Nissan',
  defaultDate: '07/04/2025',
  defaultTime: '09:41:34 AM',
};

export const changeRequestMockData = [
  {
    id: '#0001',
    vehicle: 'PXT 983',
    vehicleClass: 'Economy Plus',
    pickupDate: '28-05-2025',
    pickupTime: '9:16',
    returnDate: '28-06-2025',
    returnTime: '9:16',
    requestType: 'Extension of Duration',
    status: 'In-Progress',
    note: 'Because the picnic we went to was extended for two more days.',
  },
  {
    id: '#0007',
    vehicle: 'Isuzu Nnr 45 150 - 2BY15U',
    vehicleClass: 'Economy',
    pickupDate: '10-04-2025',
    pickupTime: '10:20',
    returnDate: '22-04-2025',
    returnTime: '10:20',
    requestType: 'Extension of Duration',
    status: 'Approved',
  },
  {
    id: '#0014',
    vehicle: 'PVG 430',
    vehicleClass: 'Economy',
    pickupDate: '02-04-2025',
    pickupTime: '10:10',
    returnDate: '03-04-2025',
    returnTime: '10:10',
    requestType: 'Extension of Duration',
    status: 'Approved',
  },
];

export const mockRentalData = [
  {
    rego: 'PXT 983',
    rentalId: '#R001',
    vehicleClass: 'Economy Plus',
    pickupDate: '2025-05-28',
    pickupTime: '09:16',
    returnDate: '2025-06-28',
    returnTime: '09:16'
  },
  {
    rego: 'PVG 430',
    rentalId: '#R002',
    vehicleClass: 'Economy',
    pickupDate: '2025-04-02',
    pickupTime: '10:10',
    returnDate: '2025-04-03',
    returnTime: '10:10'
  },
  {
    rego: 'Isuzu Nnr 45 150 - 2BY15U',
    rentalId: '#R003',
    vehicleClass: 'Economy',
    pickupDate: '2025-04-10',
    pickupTime: '10:20',
    returnDate: '2025-04-22',
    returnTime: '10:20'
  }
];

export const mockChangeRequests: ChangeRequest[] = [
  {
    id: '#0001',
    vehicle: 'PXT 983',
    vehicleClass: 'Economy Plus',
    pickupDate: '28-05-2025',
    pickupTime: '9:16 AM',
    returnDate: '28-06-2025',
    returnTime: '9:16 AM',
    requestType: 'Extension of Duration',
    status: 'In-Progress',
    note: 'Because the picnic we went to was extended for two more days.',
  },
  {
    id: '#0007',
    vehicle: 'Isuzu Nnr 45 150 - 2BY15U',
    vehicleClass: 'Economy',
    pickupDate: '10-04-2025',
    pickupTime: '10:20 AM',
    returnDate: '22-04-2025',
    returnTime: '10:20 AM',
    requestType: 'Extension of Duration',
    status: 'Approved',
  },
];


export const invoicesData: Invoice[] = [
  {
    id: '1',
    rentalId: '#0026',
    type: 'Stranded long-term Rental Invoice',
    totalAmount: 592.00,
    receivedAmount: 592.00,
    dueAmount: 0,
    paymentType: 'Credit Card',
    status: 'Completed'
  },
  {
    id: '2',
    rentalId: '#0001',
    type: 'Stranded long-term Rental Invoice',
    totalAmount: 540.00,
    receivedAmount: 400.00,
    dueAmount: 140.00,
    paymentType: 'Walking - Credit Card',
    status: 'Pending Payment'
  },
  {
    id: '3',
    rentalId: '#0007',
    type: 'Short Term rental Invoice with Insurance Excess',
    totalAmount: 740.00,
    receivedAmount: 740.00,
    dueAmount: 0,
    paymentType: 'Walking- Cash',
    status: 'Completed'
  },
  {
    id: '4',
    rentalId: '#0014',
    type: 'Late Return Penalties or Fines',
    totalAmount: 470.00,
    receivedAmount: 470.00,
    dueAmount: 0,
    paymentType: 'Credit Card',
    status: 'Completed'
  },
  {
    id: '5',
    rentalId: '#0103',
    type: 'Early termination or Customer Requested Mid-Rental',
    totalAmount: 865.00,
    receivedAmount: 865.00,
    dueAmount: 0,
    paymentType: 'Phone Authorization',
    status: 'Completed'
  },
  {
    id: '6',
    rentalId: '#0105',
    type: 'Standard Rental Invoice',
    totalAmount: 320.00,
    receivedAmount: 320.00,
    dueAmount: 0,
    paymentType: 'Credit Card',
    status: 'Completed'
  },
  {
    id: '7',
    rentalId: '#0106',
    type: 'Insurance Claim Invoice',
    totalAmount: 1200.00,
    receivedAmount: 800.00,
    dueAmount: 400.00,
    paymentType: 'Bank Transfer',
    status: 'Pending Payment'
  },
  {
    id: '8',
    rentalId: '#0107',
    type: 'Extended Rental Invoice',
    totalAmount: 680.00,
    receivedAmount: 680.00,
    dueAmount: 0,
    paymentType: 'Cash',
    status: 'Completed'
  }
];

export const invoiceItems = [
  { description: 'Eco Plus Car', rate: 'AUD 38.18 Per Day', days: '1 Day', amount: '38.18' },
  { description: 'Bond Compulsory - 500', rate: '', days: '', amount: '500.00' },
  { description: 'Insurance', rate: '', days: '', amount: '13.64' },
  { description: 'Child Seat', rate: 'AUD 36.36 Per Day', days: '1 Day', amount: '36.36' },
];

export const notifications = [
  {
    id: 1,
    type: 'info',
    message: 'Your rental starts on 11-07-2025 (Tomorrow) at 9:00 AM',
    date: '2025-07-11'
  },
  {
    id: 2,
    type: 'info',
    message: 'Your vehicle is due in 2 hours on 10-07-2025 (Today) at 11:00 AM',
    date: '2025-07-10'
  },
  {
    id: 3,
    type: 'info',
    message: 'You Paid Complete Payment on 28-06-2025 at 1:20 AM',
    date: '2025-06-28'
  },
  {
    id: 4,
    type: 'info',
    message: 'You have received the cooperate customer status provided by Lion Car Rental. You have received a 10% discount . Discount code - 475ASU892. on 20-06-2025 at 11.22 AM.',
    date: '2025-06-20'
  }
];

export const activityEntries = [
  {
    id: 1,
    type: 'info',
    message: 'John Petter Logged in at 10-07-2025 at 10:30',
    date: '2025-07-10'
  },
  {
    id: 2,
    type: 'info',
    message: 'You Booked Economy Vehicle on 29-06-2025 to 06-06-2025 at 8:45',
    date: '2025-06-29'
  },
  {
    id: 3,
    type: 'info',
    message: 'You Paid Complete Payment on 15-06-2025 at 12:20',
    date: '2025-06-15'
  },
];

export const initialFormData = {
  currentPassword: '#566vdtg23',
  newPassword: '',
  confirmPassword: ''
};