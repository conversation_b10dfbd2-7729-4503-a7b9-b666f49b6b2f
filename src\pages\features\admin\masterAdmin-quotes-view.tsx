import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; 
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

interface FormData {
  id: string;
  reservationType: string;
  customerName: string;
  customerEmail: string;
  reservation: string;
  pickupDate: string;
  returnDate: string;
  vehicleClass: string;
  commissionPartner: string;
  clientPaysAtCommission: string;
  rackPrice: string;
  totalPrice: string;
  status: string;
  pickupLocation: string;
  returnLocation: string;
  branch: string;
  forcedSecurityDeposit: string;
  manualDiscount: string;
  phoneNumber: string;
  comments: string
}

export function MasterAdminQuotesView() {
  const navigate = useNavigate(); 

  const [formData, setFormData] = useState<FormData>({
      id: '178',
      reservationType: 'Reservations',
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      reservation: '',
      pickupDate: '2025-08-10',
      returnDate: '2025-08-15',
      vehicleClass: 'SUV',
      commissionPartner: 'Partner A',
      clientPaysAtCommission: '$50',
      rackPrice: '$500',
      totalPrice: '$550',
      status: 'Email Sent',
      pickupLocation: '',
      returnLocation: '',
      branch: '',
      forcedSecurityDeposit: '',
      manualDiscount: '',
      phoneNumber: '',
      comments: '',
  });

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return (
    <div className="min-h-screen p-4">
      <div className="flex items-center mb-4">
        <Button
          className='bg-[#330101] text-white text-sm px-3 py-2'
          size="sm"
          onClick={() => navigate('/admin/masterAdmin-quotes')}
        >
          <ArrowLeft className="w-4 h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="bg-white rounded-lg p-4">
        {/* General Information */}
        <h2 className="text-lg font-semibold mb-4">Quote Details</h2>

        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={formData.reservationType}
              onChange={(e) => handleInputChange('reservationType', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1"> Reservation Type </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1"> Customer Name </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.customerEmail}
              onChange={(e) => handleInputChange('customerEmail', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Customer Email</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Phone Number</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.reservation}
              onChange={(e) => handleInputChange('reservation', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Reservation</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.vehicleClass}
              onChange={(e) => handleInputChange('vehicleClass', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              required
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Vehicle Class</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.pickupDate}
              onChange={(e) => handleInputChange('pickupDate', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Pickup Date</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.returnDate}
              onChange={(e) => handleInputChange('returnDate', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Return Date</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.pickupLocation}
              onChange={(e) => handleInputChange('pickupLocation', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Pickup Location</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.returnLocation}
              onChange={(e) => handleInputChange('returnLocation', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Return Location</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.branch}
              onChange={(e) => handleInputChange('branch', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Branch</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.forcedSecurityDeposit}
              onChange={(e) => handleInputChange('forcedSecurityDeposit', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Forced Security Deposit</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.manualDiscount}
              onChange={(e) => handleInputChange('manualDiscount', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Manual Discount</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.rackPrice}
              onChange={(e) => handleInputChange('rackPrice', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Rack Price</Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.totalPrice}
              onChange={(e) => handleInputChange('totalPrice', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Total Price</Label>
          </div>
          <div className="relative col-span-2">
            <Input
              type="text"
              value={formData.comments}
              onChange={(e) => handleInputChange('comments', e.target.value)}
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              readOnly
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Comments</Label>
          </div>
        </div>

        <div className="flex justify-between items-center mt-2">
          <Button
            className="bg-gray-100 text-black px-4 py-1 text-sm hover:bg-gray-300 border "
            onClick={() => {}}
          >
            Cancel
          </Button>
          <div className="flex gap-2">
            <Button
              className="bg-red-600 text-white px-4 py-1 text-sm hover:bg-red-700"
              onClick={() => {}}
            >
              Delete
            </Button>
            <Button
              className="bg-blue-600 text-white px-4 py-1 text-sm hover:bg-blue-700"
              onClick={() => {}}
            >
              Copy Public Link
            </Button>
            <Button
              className="bg-green-600 text-white px-4 py-1 text-sm hover:bg-green-700"
              onClick={() => {}}
            >
              Create Reservation
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}