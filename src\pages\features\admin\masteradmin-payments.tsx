import React, { useState } from 'react';
import { Search, ChevronDown, Eye } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';

interface Payment {
  reservationID: string;
  customerName: string;
  confirmation: string;
  date: string;
  reference: string;
  totalPrice: string;
  vehicleClass: string;
  status: 'Rental' | 'Completed';
}

const paymentsData: Payment[] = [
  {
    reservationID: '27689',
    customerName: 'Ikram Rahman',
    confirmation: 'Rental Payment',
    date: '01-08-2025',
    reference: 'Rental',
    totalPrice: '$ 477.00',
    vehicleClass: 'Minibus 12 Seats - Premium',
    status: 'Rental'
  },
  {
    reservationID: '27815',
    customerName: '<PERSON>',
    confirmation: 'Bond Payment',
    date: '01-08-2025',
    reference: 'Bond',
    totalPrice: '$ 853.00',
    vehicleClass: 'Delivery Van Jumbo - 2 Tonne',
    status: 'Completed'
  },
  {
    reservationID: '56999',
    customerName: 'Alice Johnson',
    confirmation: 'Bond Payment',
    date: '01-08-2025',
    reference: 'Rental',
    totalPrice: '$853.00',
    vehicleClass: 'Moving Truck - Premium',
    status: 'Rental'
  },
];

export function MasterAdminPayments() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const navigate = useNavigate();

  const handleCustomerNameClick = (reservationID: string) => {
    const encodedID = encodeURIComponent(reservationID);
    navigate(`/admin/payments/payment-details/${encodedID}`);
  };

  const filteredPayments = paymentsData.filter(payment => {
    const matchesSearch = payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.reservationID.includes(searchTerm);
    const matchesFilter = filterStatus === 'All' || payment.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const totalRecords = filteredPayments.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentPayments = filteredPayments.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const getStatusBadge = (status: string): string => {
    switch (status) {
      case 'Completed':
        return 'bg-green-500 text-white px-2 xs:px-3 py-1 rounded text-xs font-medium';
      case 'Rental':
        return 'bg-blue-500 text-white px-2 xs:px-3 py-1 rounded text-xs font-medium';
      default:
        return '';
    }
  };

  return (
    <div className="p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8 bg-white min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3 xs:mb-4 sm:mb-6 gap-2 xs:gap-3 sm:gap-4">
        <div className="flex items-center">
          <h1 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-bold text-earth-dark">Payments</h1>
        </div>
          <Button
                  onClick={() => navigate('/admin/masterAdmin-requestPayment')}
                  className="px-2 xs:px-3 sm:px-4 py-1 xs:py-2 bg-[#330101] text-white rounded transition-colors text-xs xs:text-sm sm:text-base w-full sm:w-auto"
                >
                 Payment Requests
                </Button>
      </div>

      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-1 xs:gap-2 sm:gap-4 mb-3 xs:mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterStatus}
            onValueChange={(value) => setFilterStatus(value)}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none text-xs xs:text-sm sm:text-base">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
              <SelectItem value="Rental">Rental</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-2 xs:left-3 top-1/2 transform -translate-y-1/2 w-3 xs:w-4 h-3 xs:h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-8 xs:pl-10 pr-3 xs:pr-4 py-1 xs:py-2 rounded-md focus:border-transparent text-xs xs:text-sm sm:text-base"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Mobile Cards View */}
      <div className="block sm:hidden space-y-2 xs:space-y-3">
        {currentPayments.map((payment) => (
          <div key={payment.reservationID} className="bg-white border border-gray-200 rounded-lg p-3 xs:p-4 shadow-sm relative max-w-md mx-auto w-full">
            <div className="flex justify-end mb-3 xs:mb-4">
              <span className={getStatusBadge(payment.status)}>{payment.status}</span>
            </div>
            <div className="grid grid-cols-2 gap-x-4 xs:gap-x-6 gap-y-3 xs:gap-y-4 text-[10px] xs:text-xs">
              <div>
                <span className="text-gray-500 block">Reservation</span>
                <span 
                  className="text-blue-600 hover:underline cursor-pointer"
                  onClick={() => handleCustomerNameClick(payment.reservationID)}
                >
                  {payment.reservationID}
                </span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Customer Name</span>
                <span className="text-gray-900">{payment.customerName}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Confirmation</span>
                <span className="text-gray-900">{payment.confirmation}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Date</span>
                <span className="text-gray-900">{payment.date}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Reference</span>
                <span className="text-gray-900">{payment.reference}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Total Price</span>
                <span className="text-gray-900">{payment.totalPrice}</span>
              </div>
              <div>
                <span className="text-gray-500 font-medium block">Vehicle Class</span>
                <span className="text-gray-900">{payment.vehicleClass}</span>
              </div>
            </div>
            <div className="flex justify-end gap-1 xs:gap-2 mt-3 xs:mt-4">
              <Button
                onClick={() => navigate(`/admin/payments/payment-details/${payment.reservationID}`)}
                variant="ghost"
                className="text-gray-600 hover:text-gray-800 p-1 xs:p-2"
              >
                <Eye className="w-3 xs:w-4 h-3 xs:h-4" />
              </Button>
            </div>
          </div>
        ))}
      </div>

      {/* Desktop Table View */}
      <div className="hidden sm:block">
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className='bg-gray-50 uppercase'>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Reservation</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Customer Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Payment Confirmation</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Date</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Reference</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Total Price</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle Class</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Status</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentPayments.map((payment) => (
                <TableRow key={payment.reservationID}>
                  <TableCell 
                    className="text-xs lg:text-sm px-2 lg:px-4 cursor-pointer text-blue-600 hover:underline"
                    onClick={() => handleCustomerNameClick(payment.reservationID)}
                  >
                    {payment.reservationID}
                  </TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{payment.customerName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{payment.confirmation}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{payment.date}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{payment.reference}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{payment.totalPrice}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{payment.vehicleClass}</TableCell>
                  <TableCell className="px-2 lg:px-4">
                    <span className={getStatusBadge(payment.status)}>
                      {payment.status}
                    </span>
                  </TableCell>
                  <TableCell className="px-2 lg:px-4">
                    <div className="flex items-center">
                      <Button
                        onClick={() => navigate(`/admin/masterAdmin-viewPayment/${payment.reservationID}`)}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 lg:p-2"
                      >
                        <Eye className="w-3 lg:w-4 h-3 lg:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination - Only for desktop */}
        <div className="hidden sm:block mt-3 xs:mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="text-xs xs:text-sm sm:text-base"
          />
        </div>
      </div>
    </div>
  );
}