import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import { mockReservations } from '../common/mockdata';

export const useIncidentReportForm = () => {
  const navigate = useNavigate();
  const [rego, setRego] = useState<string>('');
  const [rentalId, setRentalId] = useState<string>('');
  const [pickupDate, setPickupDate] = useState<Date | undefined>(undefined);
  const [returnDate, setReturnDate] = useState<Date | undefined>(undefined);
  const [agreementNo, setAgreementNo] = useState<string>('');
  const [reportType, setReportType] = useState<string>('');
  const [showPickupCalendar, setShowPickupCalendar] = useState(false);
  const [showReturnCalendar, setShowReturnCalendar] = useState(false);
  const [showIncidentCalendar, setShowIncidentCalendar] = useState(false);
  const [showAdditionalAccidentForm, setShowAdditionalAccidentForm] = useState(false);

  const [accidentFields, setAccidentFields] = useState({
    payment: '',
    insuranceExcessCover: '',
    driverName: '',
    phoneNumber: '',
    email: '',
    birthday: undefined, 
    address: '',
    postcode: '',
    country: '',
    drugsAlcoholIncident: '',
    licenseNumber: '',
    issueDate: undefined,
    expiryDate: undefined,
    conditions: '',
    frontView: null,
    backView: null,
    nextDue: undefined,
    obtainDetails: '',
    isDrugsAlcoholConsumed: undefined,
  });

  const [additionalAccidentFields, setAdditionalAccidentFields] = useState({
    accidentLocation: '',
    damageDescription: '',
    policeReport: null,
    witnessDetails: '',
  });

  const [breakdownFields, setBreakdownFields] = useState({
    incidentDate: undefined,
    daysLeft: 0,
    odometerReading: '',
    fuelGauge: '',
    note: '',
    interiorImages: null,
    exteriorImages: null,
  });

  const [profilePhoto, setProfilePhoto] = useState<File | null>(null);
  const [idPhoto, setIdPhoto] = useState<File | null>(null);

  const handleRegoChange = (selectedRego: string) => {
    setRego(selectedRego);
    const reservation = mockReservations.find(r => r.rego === selectedRego);
    if (reservation) {
      setRentalId(reservation.rentalId);
      setPickupDate(reservation.pickupDate);
      setReturnDate(reservation.returnDate);
      setAgreementNo(reservation.agreementNo);
    }
  };

  const handleReportTypeChange = (value: string) => {
    setReportType(value);
    setShowAdditionalAccidentForm(false);
    setAccidentFields({
      payment: '',
      insuranceExcessCover: '',
      driverName: '',
      phoneNumber: '',
      email: '',
      birthday: undefined,
      address: '',
      postcode: '',
      country: '',
      drugsAlcoholIncident: '',
      licenseNumber: '',
      issueDate: undefined,
      expiryDate: undefined,
      conditions: '',
      frontView: null,
      backView: null,
      nextDue: undefined,
      obtainDetails: '',
      isDrugsAlcoholConsumed: undefined,
    });
    setAdditionalAccidentFields({
      accidentLocation: '',
      damageDescription: '',
      policeReport: null,
      witnessDetails: '',
    });
    setBreakdownFields({
      incidentDate: undefined,
      daysLeft: 0,
      odometerReading: '',
      fuelGauge: '',
      note: '',
      interiorImages: null,
      exteriorImages: null,
    });

    if (value === 'Accident Repair') {
      setAccidentFields(prev => ({
        ...prev,
        payment: 'No',
        insuranceExcessCover: 'Yes',
        driverName: 'John Doe',
        phoneNumber: '+**********',
        email: '<EMAIL>',
        birthday: new Date('1990-01-15'),
        address: '123 Main St',
        postcode: '12345',
        country: 'USA',
        licenseNumber: 'L123456',
        issueDate: new Date('2015-06-01'),
        expiryDate: new Date('2025-06-01'),
        conditions: 'None',
      }));
    }
  };

  useEffect(() => {
    if (returnDate && breakdownFields.incidentDate) {
      const diffTime = returnDate.getTime() - breakdownFields.incidentDate.getTime();
      const diffDays = Math.ceil(diffTime / (1000 * 60 * 60 * 24));
      setBreakdownFields(prev => ({
        ...prev,
        daysLeft: diffDays > 0 ? diffDays : 0,
      }));
    }
  }, [returnDate, breakdownFields.incidentDate]);

  const handleFrontViewChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setAccidentFields({ ...accidentFields, frontView: event.target.files[0] });
    }
  };

  const handleBackViewChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setAccidentFields({ ...accidentFields, backView: event.target.files[0] });
    }
  };

  const handleInteriorImagesChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setBreakdownFields({ ...breakdownFields, interiorImages: event.target.files[0] });
    }
  };

  const handleExteriorImagesChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setBreakdownFields({ ...breakdownFields, exteriorImages: event.target.files[0] });
    }
  };

  const handleDrugsAlcoholChange = (value: boolean) => {
    setAccidentFields(prev => ({
      ...prev,
      isDrugsAlcoholConsumed: value,
      payment: value ? 'yes' : '',
      insuranceExcessCover: value ? 'yes' : '',
    }));
  };

  const handleProfilePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setProfilePhoto(event.target.files[0]);
      setAccidentFields({ ...accidentFields, frontView: event.target.files[0] });
    }
  };

  const handleIdPhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setIdPhoto(event.target.files[0]);
      setAccidentFields({ ...accidentFields, backView: event.target.files[0] });
    }
  };

  const handleIncidentDateChange = (date: Date | undefined) => {
    setBreakdownFields(prev => ({
      ...prev,
      incidentDate: date,
    }));
  };

  const handleOdometerChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBreakdownFields(prev => ({
      ...prev,
      odometerReading: e.target.value,
    }));
  };

  const handleFuelGaugeChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBreakdownFields(prev => ({
      ...prev,
      fuelGauge: e.target.value,
    }));
  };

  const handleNoteChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setBreakdownFields(prev => ({
      ...prev,
      note: e.target.value,
    }));
  };

  const handleAccidentLocationChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAdditionalAccidentFields({ ...additionalAccidentFields, accidentLocation: e.target.value });
  };

  const handleDamageDescriptionChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAdditionalAccidentFields({ ...additionalAccidentFields, damageDescription: e.target.value });
  };

  const handleWitnessDetailsChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAdditionalAccidentFields({ ...additionalAccidentFields, witnessDetails: e.target.value });
  };

  const handlePoliceReportChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setAdditionalAccidentFields({ ...additionalAccidentFields, policeReport: event.target.files[0] });
    }
  };

  const handleSubmit = () => {
    console.log('Incident Report submitted:', { rego, rentalId, pickupDate, returnDate, agreementNo, reportType, accidentFields, additionalAccidentFields, breakdownFields });
    navigate(-1);
  };

  const handleBack = () => {
    navigate('/customer/incident-reporting');
  };

  const handleNext = () => {
    navigate('/customer/incidentReporting');
  };

  return {
    rego,
    setRego,
    rentalId,
    setRentalId,
    pickupDate,
    setPickupDate,
    returnDate,
    setReturnDate,
    agreementNo,
    setAgreementNo,
    reportType,
    setReportType,
    showPickupCalendar,
    setShowPickupCalendar,
    showReturnCalendar,
    setShowReturnCalendar,
    showIncidentCalendar,
    setShowIncidentCalendar,
    showAdditionalAccidentForm,
    setShowAdditionalAccidentForm,
    accidentFields,
    setAccidentFields,
    additionalAccidentFields,
    setAdditionalAccidentFields,
    breakdownFields,
    setBreakdownFields,
    profilePhoto,
    setProfilePhoto,
    idPhoto,
    setIdPhoto,
    handleRegoChange,
    handleReportTypeChange,
    handleFrontViewChange,
    handleBackViewChange,
    handleInteriorImagesChange,
    handleExteriorImagesChange,
    handleDrugsAlcoholChange,
    handleProfilePhotoChange,
    handleIdPhotoChange,
    handleIncidentDateChange,
    handleOdometerChange,
    handleFuelGaugeChange,
    handleNoteChange,
    handleAccidentLocationChange,
    handleDamageDescriptionChange,
    handleWitnessDetailsChange,
    handlePoliceReportChange,
    handleSubmit,
    handleBack,
    handleNext,
  };
};