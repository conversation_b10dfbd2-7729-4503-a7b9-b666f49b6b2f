import { useEffect, useState } from "react";
import { JobHistoryRow } from "../type/mechanictype";

const MOCK_DATA: JobHistoryRow[] = [
  {
    rego: "ADA 234",
    serviceType: "General Maintenance",
    serviceDate: "25/06/2025",
    status: "Completed",
  },
  {
    rego: "QWA 204",
    serviceType: "Accident Repair",
    serviceDate: "23/06/2025",
    status: "Completed",
  },
  {
    rego: "OQA 001",
    serviceType: "Breakdown Service",
    serviceDate: "20/06/2025",
    status: "Completed",
  },
  {
    rego: "YAC 012",
    serviceType: "Accident Repair",
    serviceDate: "19/06/2025",
    status: "Completed",
  },
];

export function useJobHistory() {
  const [search, setSearch] = useState("");
  const [filterType, setFilterType] = useState("All");
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [data, setData] = useState<JobHistoryRow[]>([]);

  useEffect(() => {
    setData(MOCK_DATA);
  }, []);

  const allServiceTypes = ["All", ...Array.from(new Set(MOCK_DATA.map(d => d.serviceType)))];

  const filtered = data.filter(row => {
    const matchesType = filterType === "All" || row.serviceType === filterType;
    const matchesSearch =
      row.rego.toLowerCase().includes(search.toLowerCase()) ||
      row.serviceType.toLowerCase().includes(search.toLowerCase()) ||
      row.serviceDate.toLowerCase().includes(search.toLowerCase()) ||
      row.status.toLowerCase().includes(search.toLowerCase());
    return matchesType && matchesSearch;
  });

  const totalRecords = filtered.length;
  const totalPages = Math.max(1, Math.ceil(totalRecords / pageSize));
  const paginated = filtered.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  return {
    search,
    setSearch,
    filterType,
    setFilterType,
    allServiceTypes,
    pageSize,
    setPageSize,
    currentPage,
    setCurrentPage,
    totalPages,
    totalRecords,
    paginated,
  };
}