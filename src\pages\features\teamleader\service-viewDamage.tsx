import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, Eye } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';

export function ServiceViewDamage() {
  const navigate = useNavigate();
  const { vehicleId } = useParams<{ vehicleId: string }>();

  // Dummy data auto-filled based on the selected vehicle (e.g., from ServiceAccident table)
  const [vehicleData] = useState({
    model: 'Isuzu',
    regNo: 'AFS 009',
    description: 'I was reversing out of the factory, and I accidentally reversed into our work van parked on the street.',
    insuranceStatus: 'Initial Review',
    insuranceClaimNumber: '',
    estimatedEndDate: '',
    actualEndDate: '',
    status: '',
  });

  // Dummy image data (simulating uploaded images)
  const [imageData] = useState({
    interiorImages: ['interior.jpg'],
    exteriorImages: ['exterior.jpg'],
    leftSideDoors: ['left.jpg'],
    rightSideDoors: ['right.jpg'],
    frontSideImages: ['front.jpg'],
    backSideImages: ['back.jpg'],
    sideMirrors: ['mirrors.jpg'],
    other: ['other.jpg'],
  });

  // Workshop data reflecting data from ServiceEditAccident
  const [workshopData] = useState({
    branch: 'Somerton',
    mechanicAssigned: 'John Doe, Mike Smith',
    comment: 'Type here...',
  });

  // State to manage the displayed image
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  return (
    <div className="min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <Button
          className="bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2"
          size="sm"
          onClick={() => navigate('/teamleader/service-damage')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="flex items-center mb-4 sm:mb-6">
        <h1 className="text-xl sm:text-2xl font-medium text-earth-dark">Vehicle Details</h1>
      </div>

      {/* Vehicle Details Section */}
      <div className="bg-white rounded-lg mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.model}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Model
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.regNo}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Reg No
            </Label>
          </div>
        </div>
        <div className="grid grid-cols-1 gap-3 sm:gap-6 md:gap-8">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.description}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base h-20 resize-none"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Description
            </Label>
          </div>
        </div>
      </div>

{/* Upload Images Section */}
    <div className="bg-white rounded-lg mb-6">
      <h2 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Upload Images</h2>
      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8">
        <div className="relative">
          <div className="flex items-center justify-between w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
            <span className="text-gray-600">interior.jpg</span>
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedImage(imageData.interiorImages[0])}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Interior Images
          </Label>
        </div>
        <div className="relative">
          <div className="flex items-center justify-between w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
            <span className="text-gray-600">exterior.jpg</span>
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedImage(imageData.exteriorImages[0])}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Exterior Images
          </Label>
        </div>
        <div className="relative">
          <div className="flex items-center justify-between w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
            <span className="text-gray-600">left.jpg</span>
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedImage(imageData.leftSideDoors[0])}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Left Side Doors
          </Label>
        </div>
        <div className="relative">
          <div className="flex items-center justify-between w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
            <span className="text-gray-600">right.jpg</span>
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedImage(imageData.rightSideDoors[0])}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Right Side Doors
          </Label>
        </div>
        <div className="relative">
          <div className="flex items-center justify-between w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
            <span className="text-gray-600">front.jpg</span>
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedImage(imageData.frontSideImages[0])}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Front Side Images
          </Label>
        </div>
        <div className="relative">
          <div className="flex items-center justify-between w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
            <span className="text-gray-600">back.jpg</span>
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedImage(imageData.backSideImages[0])}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Back Side Images
          </Label>
        </div>
        <div className="relative">
          <div className="flex items-center justify-between w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
            <span className="text-gray-600">mirrors.jpg</span>
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedImage(imageData.sideMirrors[0])}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Side Mirrors
          </Label>
        </div>
        <div className="relative">
          <div className="flex items-center justify-between w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base">
            <span className="text-gray-600">other.jpg</span>
            <Button
              variant="ghost"
              className="text-gray-600 hover:text-gray-800"
              onClick={() => setSelectedImage(imageData.other[0])}
            >
              <Eye className="w-4 h-4" />
            </Button>
          </div>
          <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Other
          </Label>
        </div>
      </div>
      </div>

      
      {/* Workshop Details Section */}
      <div className="bg-white rounded-lg mb-6">
        <h2 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Workshop Details</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={workshopData.branch}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Branch
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={workshopData.mechanicAssigned}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Mechanic Assigned
            </Label>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.insuranceStatus}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Insurance Status
                </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.insuranceClaimNumber}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Insurance Claim Number
            </Label>
          </div>
        </div>

        <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.estimatedEndDate}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Estimated End Date
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.actualEndDate}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Actual End Date
            </Label>
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.status}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Status
            </Label>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-3 sm:gap-6 md:gap-8 mt-4">
          <div className="relative">
            <Input
              type="text"
              value={workshopData.comment}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base h-20 resize-none"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Comment
            </Label>
          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg">
            <img src={selectedImage} alt="Preview" className="max-w-full max-h-96" />
            <Button
              variant="outline"
              className="mt-4 w-full"
              onClick={() => setSelectedImage(null)}
            >
              Close
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}