import { Plus, Trash2, Edit } from "lucide-react";
import { useRequestPartsAdd } from "./hook/userequest-parts-add";
import { Input } from "../../../components/ui/input";
import { Label } from "../../../components/ui/label";
import { Button } from "../../../components/ui/button";
import { useLocation } from 'react-router-dom';
import { useEffect, useState } from "react";

export const RequestPartsAdd = () => {
  const { form, handleInputChange, handleAdd, handleCancel } = useRequestPartsAdd();

  const [parts, setParts] = useState([]);
  const [nextPartNumber, setNextPartNumber] = useState(1);
  const [isEditing, setIsEditing] = useState<number | null>(null);
  const [editPart, setEditPart] = useState({ partName: "", quantity: 1 });
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const serviceType = params.get('service'); // 'accident', 'regular', etc.

  // Helper to get next part number as PN001, PN002, ...
  const getNextPartNumber = () => `PN${String(nextPartNumber).padStart(4, "0")}`;

  // Set initial part number on mount
  useEffect(() => {
    handleInputChange("partNumber", getNextPartNumber());
    // eslint-disable-next-line
  }, []);

  // Add part to table (always use current part number)
  const handleAddPart = () => {
    if (!form.partName || !form.quantity) return;
    setParts([
      ...parts,
      {
        partNumber: form.partNumber,
        partName: form.partName,
        quantity: form.quantity,
        comment: form.comment,
      },
    ]);
    // Do not increment part number here!
    handleInputChange("partName", "");
    handleInputChange("quantity", 1);
    handleInputChange("comment", "");
  };

  // Remove part from table
  const handleRemovePart = (idx: number) => {
    setParts(parts.filter((_, i) => i !== idx));
  };

  // Edit part in table
  const handleEditPart = (idx: number) => {
    setIsEditing(idx);
    setEditPart({
      partName: parts[idx].partName,
      quantity: parts[idx].quantity,
    });
  };

  // Save edited part
  const handleSaveEdit = (idx: number) => {
    const updatedParts = parts.map((part, i) =>
      i === idx
        ? { ...part, partName: editPart.partName, quantity: editPart.quantity }
        : part
    );
    setParts(updatedParts);
    setIsEditing(null);
    setEditPart({ partName: "", quantity: 1 });
  };

  // When user clicks "Add" at the bottom, increment part number for next batch
  const handleAddBatch = () => {
    // You may want to do something with the parts here (e.g., submit to backend)
    // For now, just increment part number for next batch and clear parts
    const newNext = nextPartNumber + 1;
    setNextPartNumber(newNext);
    handleInputChange("partNumber", `PN${String(newNext).padStart(4, "0")}`);
    setParts([]);
    handleInputChange("partName", "");
    handleInputChange("quantity", 1);
    handleInputChange("comment", "");
  };

  return (
    <div className="min-h-screen font-sans text-left flex">
      {/* Sidebar is assumed to be rendered outside this component */}
      <div className="flex-1 p-4 sm:p-6 md:p-8 max-w-lg">
        <h2 className="text-xl font-bold mb-6">Request Parts</h2>
        <div className="space-y-6">
          {/* Part Number, Part Name, Quantity horizontally aligned */}
          <div className="flex gap-6 items-end">
            <div className="flex-1 flex flex-col gap-2">
              <Label className="text-sm">Part Number</Label>
              <Input value={form.partNumber} readOnly className="bg-gray-50 text-sm w-full min-w-[150px]" />
            </div>
            <div className="flex-1 flex flex-col gap-2">
              <Label className="text-sm">Part Name</Label>
              <Input
                value={form.partName}
                onChange={e => handleInputChange("partName", e.target.value)}
                placeholder="Enter part name"
                className="text-sm w-full min-w-[150px]"
              />
            </div>
            <div className="flex-1 flex flex-col gap-2">
              <Label className="text-sm">Quantity</Label>
              <Input
                type="number"
                min={1}
                value={form.quantity}
                onChange={e => handleInputChange("quantity", Number(e.target.value))}
                placeholder="Enter quantity"
                className="text-sm w-full min-w-[100px]"
              />
            </div>
            {/* Plus icon to add new part */}
            <Button
              className="bg-[#330101] text-white px-2 py-2 h-9 flex items-center"
              onClick={handleAddPart}
              title="Add Part"
              type="button"
            >
              <Plus className="w-5 h-5" />
            </Button>
          </div>
          {/* Table of added parts */}
          {parts.length > 0 && (
            <div className="overflow-x-auto">
              <table className="min-w-full border mt-4 text-sm">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="px-3 py-2 border">Part Number</th>
                    <th className="px-3 py-2 border">Part Name</th>
                    <th className="px-3 py-2 border">Quantity</th>
                    <th className="px-3 py-2 border"></th>
                  </tr>
                </thead>
                <tbody>
                  {parts.map((part, idx) => (
                    <tr key={idx}>
                      <td className="px-3 py-2 border">{part.partNumber}</td>
                      <td className="px-3 py-2 border">
                        {isEditing === idx ? (
                          <Input
                            value={editPart.partName}
                            onChange={e => setEditPart({ ...editPart, partName: e.target.value })}
                            className="text-sm"
                          />
                        ) : (
                          part.partName
                        )}
                      </td>
                      <td className="px-3 py-2 border">
                        {isEditing === idx ? (
                          <Input
                            type="number"
                            min={1}
                            value={editPart.quantity}
                            onChange={e => setEditPart({ ...editPart, quantity: Number(e.target.value) })}
                            className="text-sm"
                          />
                        ) : (
                          part.quantity
                        )}
                      </td>
                      <td className="px-2 py-2 border text-center flex gap-2 justify-center">
                        {isEditing === idx ? (
                          <Button
                            size="sm"
                            className="px-2 py-1 bg-green-600 text-white"
                            onClick={() => handleSaveEdit(idx)}
                            type="button"
                          >
                            Save
                          </Button>
                        ) : (
                          <>
                            <button
                              type="button"
                              className="text-blue-500 hover:text-blue-700"
                              onClick={() => handleEditPart(idx)}
                              title="Edit"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              type="button"
                              className="text-red-500 hover:text-red-700"
                              onClick={() => handleRemovePart(idx)}
                              title="Remove"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          {/* Comment below */}
          <div className="flex flex-col gap-2">
            <Label className="text-sm">Comment</Label>
            <Input
              value={form.comment}
              onChange={e => handleInputChange("comment", e.target.value)}
              placeholder="Add comment"
              className="text-sm w-full min-w-[200px]"
            />
          </div>
          {/* Add and Cancel buttons right aligned */}
          <div className="flex justify-end gap-4 mt-8">
            <Button
              className="bg-[#330101] text-white px-6 text-sm flex items-center"
              onClick={handleAddBatch}
              type="button"
            >
              Add
            </Button>
            <Button variant="outline" className="px-6 text-sm" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};