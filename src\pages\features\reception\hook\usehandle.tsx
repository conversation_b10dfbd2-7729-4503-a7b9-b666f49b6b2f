import { useNavigate, useParams } from 'react-router-dom';
import { useEffect, useState } from 'react';
import {
  generalMaintenance,
  accident,
  breakdown,
} from '../common/mockData';
import { ChangeServiceType, FormData, ImageUpload } from '../type/reception-type';

export const useHandlers = (
  mode: 'accident' | 'maintenance' | 'breakdown' = 'maintenance'
) => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const [formData, setFormData] = useState<FormData>({
    vehicleClass: '',
    vehicleModel: '',
    rego: '',
    maintenanceType: '',
    serviceType: '',
    breakdownDate: '',
    location: '',
    insuranceClaimed: '',
    insuranceCompany: '',
    claimedNumber: '',
    comment: '',
  });

  const [images, setImages] = useState<ImageUpload>({
    interior: null,
    exterior: null,
    leftSide: null,
    rightSide: null,
    frontSide: null,
    backSide: null,
    sideMirrors: null,
    other: null,
  });

  const [changeServiceType, setChangeServiceType] = useState<ChangeServiceType>('no');

  useEffect(() => {
    let recordList;
    if (mode === 'accident') recordList = accident;
    else if (mode === 'breakdown') recordList = breakdown;
    else recordList = generalMaintenance;

    const record = recordList.find((item) => item.id === id);

    if (record) {
      if (mode === 'accident') {
        const [make, rego] = record.vehicle?.split(' - ') || ['', ''];
        setFormData((prev) => ({
          ...prev,
          vehicleModel: make,
          rego,
        }));
      }

      if (mode === 'breakdown') {
        const [make, rego] = record.vehicle?.split('-') || ['', ''];
        setFormData((prev) => ({
          ...prev,
          vehicleModel: make.trim(),
          rego: rego?.trim() || '',
          location: 'location' in record ? record.location || '' : '',
          insuranceClaimed: 'insuranceClaimed' in record ? record.insuranceClaimed || '' : '',
          insuranceCompany: 'insuranceCompany' in record ? record.insuranceCompany || '' : '',
          claimedNumber: 'claimedNumber' in record ? record.claimedNumber || '' : '',
        }));
      }

      if (mode === 'maintenance') {
        const [make, rego] = record.vehicle?.split(' - ') || ['', ''];
        setFormData((prev) => ({
          ...prev,
          vehicleClass: 'vehicleClass' in record ? record.vehicleClass || '' : '',
          vehicleModel: make,
          rego,
          maintenanceType: 'maintenanceType' in record ? record.maintenanceType || '' : '',
        }));
      }
    }
  }, [id, mode]);

  const handleInputChange = (field: string, value: string) => {
    setFormData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  const handleImageUpload = (field: keyof ImageUpload, file: File | null) => {
    setImages((prev) => ({
      ...prev,
      [field]: file,
    }));
  };

  const handleUpdate = () => {
    navigate('/reception/reception-workshops');
  };

  const handleCancel = () => {
    navigate('/reception/reception-workshops');
  };

  return {
    formData,
    changeServiceType,
    setChangeServiceType,
    handleInputChange,
    handleUpdate,
    handleCancel,
    images,
    handleImageUpload,
  };
};
