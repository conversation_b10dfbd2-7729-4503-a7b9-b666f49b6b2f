import React from 'react';
import { NavLink } from 'react-router-dom';
import { Home, UserCircle, CalendarPlus, History, FileWarning, FileEdit, FileText, DollarSign, Bell, ClipboardList, Settings } from 'lucide-react';
export function Sidebar() {
  const navItems = [{
    path: '/dashboard',
    label: 'Dashboard Overview',
    icon: <Home className="w-5 h-5 mr-3" />
  }, {
    path: '/profile',
    label: 'Profile',
    icon: <UserCircle className="w-5 h-5 mr-3" />
  }, {
    path: '/add-rental',
    label: 'Add a Rental',
    icon: <CalendarPlus className="w-5 h-5 mr-3" />
  }, {
    path: '/booking-history',
    label: 'Booking History',
    icon: <History className="w-5 h-5 mr-3" />
  }, {
    path: '/incident-reporting',
    label: 'Incident Reporting',
    icon: <FileWarning className="w-5 h-5 mr-3" />
  }, {
    path: '/change-requests',
    label: 'Change Requests',
    icon: <FileEdit className="w-5 h-5 mr-3" />
  }, {
    path: '/invoices',
    label: 'Invoices',
    icon: <FileText className="w-5 h-5 mr-3" />
  }, {
    path: '/fines',
    label: 'Fines',
    icon: <DollarSign className="w-5 h-5 mr-3" />
  }, {
    path: '/notifications',
    label: 'Notification Center',
    icon: <Bell className="w-5 h-5 mr-3" />
  }, {
    path: '/activity-log',
    label: 'Activity Log',
    icon: <ClipboardList className="w-5 h-5 mr-3" />
  }, {
    path: '/settings',
    label: 'Settings',
    icon: <Settings className="w-5 h-5 mr-3" />
  }];
  return <aside className="bg-earth-cream w-64 min-h-screen flex flex-col">
      <nav className="flex-1">
        <ul className="space-y-1">
          {navItems.map(item => <li key={item.path}>
              <NavLink to={item.path} className={({
            isActive
          }) => `flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors ${isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''}`}>
                {item.icon}
                <span>{item.label}</span>
              </NavLink>
            </li>)}
        </ul>
      </nav>
    </aside>;
}