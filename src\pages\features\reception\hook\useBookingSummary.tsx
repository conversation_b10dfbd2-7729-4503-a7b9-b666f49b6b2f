import { NavigateFunction } from 'react-router-dom';
import { reservationData } from '../common/mockData';
import { BookingSummary, ReservationManagement } from '../type/reception-type';

// Fetch booking by ID and transform to BookingSummary
export const getBookingById = (bookingId: string | undefined): BookingSummary | null => {
  if (!bookingId) return null;

  const reservation = reservationData.find((item: ReservationManagement) => item.rentalId === bookingId);
  if (!reservation) return null;

  const totalPrice = parseFloat(reservation.totalPrice.replace('$', ''));
  const days = 1; // Assuming 1 day for simplicity; adjust based on pickup/return dates if needed
  const dailyPrice = totalPrice / days;

  return {
    id: reservation.rentalId,
    outstandingBalance: `AUD ${parseFloat(reservation.outstandingBalance.replace('$', '')).toFixed(2)}`,
    pickupDate: reservation.pickupDate,
    pickupTime: reservation.pickupTime,
    returnDate: reservation.returnDate,
    returnTime: reservation.returnTime,
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    branch: 'Lion Car Rental',
    reservationType: reservation.reservationType,
    loanVehicle: reservation.loanVehicle,
    vehicle: {
      class: reservation.vehicle.class,
      category: reservation.vehicle.category,
      features: ['Automatic Transmission', 'Air-conditioning', 'Fuel: 92 Petrol', 'Power Steering', '5 Doors'],
      doors: '5 Doors',
      vehicleId: reservation.vehicle.vehicleId,
      price: `AUD ${dailyPrice.toFixed(2)}`,
      priceNote: `AUD ${dailyPrice.toFixed(2)} (Incl GST) Daily excluding GST 10%`
    },
    protections: [
      { name: 'Bond Compulsory - 500 (Mandatory)', price: 'AUD 500.00 One Time' },
      { name: 'Insurance-15', price: 'Yes AUD 15.00 One Time' }
    ],
    equipment: [
      { name: 'Child Seat', price: 'Yes AUD 40.00/Day' }
    ],
    customer: reservation.customer || {
      cooperate: 'Yes',
      walkIn: 'No',
      firstName: 'Unknown',
      lastName: 'Customer',
      email: '<EMAIL>',
      phone: '0411 111 111',
      address: '2185 Hume Hwy',
      postcode: '2091',
      country: 'Australia',
      birthday: '02/01/1960'
    },
    license: reservation.license || {
      type: 'Class C (Car License)',
      idNumber: '12345678',
      issueDate: '20/04/2024',
      expireDate: '20/04/2028',
      country: 'Australia'
    },
    emergency: reservation.emergency || {
      name: 'Unknown Contact',
      number: '0412 456 789'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: 'AUD 38.18', rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: 'AUD 500.00', days: '1 Day' },
      insurance: { description: 'Insurance Plus', amount: 'AUD 13.64', days: '1 Day' },
      childSeat: { description: 'Child Seat', amount: 'AUD 36.36', rate: 'AUD 36.36 Per Day', days: '1 Day' },
      subtotal: '588.18',
      gst: '8.82',
      discount: '3.00',
      total: '594.00',
      securityDeposit: '100.00',
      amountDue: '494.00'
    }
  };
};

// Handle navigation to reservation management
export const handleGoBack = (navigate: NavigateFunction) => {
  navigate('/reception/reception-reservantionmanagement');
};

// Handle navigation to assign vehicle
export const handleAssignVehicle = (navigate: NavigateFunction) => {
  navigate('/reception/reception-assignVehicle');
};

// Handle navigation to return vehicle
export const handleReturnVehicle = (navigate: NavigateFunction) => {
  navigate('/reception/reception-returnVehicle');
};

// Handle navigation to invoice
export const handleViewInvoice = (navigate: NavigateFunction) => {
  navigate('/search/receipt-invoice');
};

// Handle print agreement
export const handlePrintAgreement = (bookingId: string | undefined) => {
  if (bookingId) {
    window.open(`/customer/booking-summary/agreement/${bookingId}`, '_blank');
  }
};

// Handle email agreement
export const handleEmailAgreement = () => {
  alert('Agreement will be sent to the customer email address.');
};

// Handle print receipt
export const handlePrintReceipt = (bookingId: string | undefined) => {
  if (bookingId) {
    window.open(`/customer/booking-summary/receipt/${bookingId}`, '_blank');
  }
};

// Handle email receipt
export const handleEmailReceipt = () => {
  alert('Receipt will be sent to the customer email address.');
};

// Utility to filter data by tab and filter type
export const getFilteredDataByTabAndFilter = (
  data: ReservationManagement[],
  activeTab: string,
  filterType: string
): ReservationManagement[] => {
  let filteredData = [...data];

  // Apply tab-based filtering
  switch (activeTab) {
    case 'Website Bookings':
      filteredData = filteredData.filter(item => item.reservationType === 'Online');
      break;
    case "Today's Returns":
      filteredData = filteredData.filter(item => item.returnDate === new Date().toISOString().split('T')[0]);
      break;
    case "Tomorrow's Pickups":
      filteredData = filteredData.filter(
        item => item.pickupDate === new Date(Date.now() + 86400000).toISOString().split('T')[0]
      );
      break;
    case "Today's Pickups":
      filteredData = filteredData.filter(item => item.pickupDate === new Date().toISOString().split('T')[0]);
      break;
    case "Tomorrow's Returns":
      filteredData = filteredData.filter(
        item => item.returnDate === new Date(Date.now() + 86400000).toISOString().split('T')[0]
      );
      break;
    case 'On Rent':
      filteredData = filteredData.filter(item => item.status === 'Rental');
      break;
    case 'Completed':
      filteredData = filteredData.filter(item => item.status === 'Completed');
      break;
    case 'Cancelled':
      filteredData = filteredData.filter(item => item.status === 'Cancelled');
      break;
    case 'Outstanding Payment':
      filteredData = filteredData.filter(
        item => parseFloat(item.outstandingBalance.replace('$', '')) > 0
      );
      break;
    default:
      break;
  }

  // Apply filter type
  if (filterType !== 'All') {
    filteredData = filteredData.filter(item => item.reservationType === filterType);
  }

  return filteredData;
};

// Utility to get status color
export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'Open':
      return 'text-green-600';
    case 'Completed':
      return 'text-blue-600';
    case 'Rental':
      return 'text-yellow-600';
    case 'Cancelled':
      return 'text-red-600';
    default:
      return 'text-gray-600';
  }
};

// Utility to get Yes/No color
export const getYesNoColor = (value: string): string => {
  return value === 'Yes' ? 'text-green-600' : 'text-gray-900';
};

// Handle navigation to check availability
export const handleCheckAvailability = (navigate: NavigateFunction) => {
  navigate('/reception/availability-check');
};

// Handle navigation to add new reservation
export const handleAddNewReservation = (navigate: NavigateFunction) => {
  navigate('/reception/add-reservation');
};

// Handle navigation to view booking summary
export const handleViewBookingSummary = (rentalId: string, navigate: NavigateFunction) => {
  navigate(`/reception/booking-summary/${rentalId}`);
};

// Handle navigation to view outstanding payments
export const handleViewOutstanding = (rentalId: string, navigate: NavigateFunction) => {
  navigate(`/reception/outstanding/${rentalId}`);
};

// Handle search input change
export const handleSearchChange = (
  e: React.ChangeEvent<HTMLInputElement>,
  setSearchTerm: React.Dispatch<React.SetStateAction<string>>,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>
) => {
  setSearchTerm(e.target.value);
  setCurrentPage(1);
};

// Handle tab change
export const handleTabChange = (
  tab: string,
  setActiveTab: React.Dispatch<React.SetStateAction<string>>,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>
) => {
  setActiveTab(tab);
  setCurrentPage(1);
};

// Handle filter type change
export const handleFilterTypeChange = (
  value: string,
  setFilterType: React.Dispatch<React.SetStateAction<string>>,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>
) => {
  setFilterType(value);
  setCurrentPage(1);
};