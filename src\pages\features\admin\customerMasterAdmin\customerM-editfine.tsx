import React, { useState } from 'react';
import { useParams, useNavigate } from 'react-router-dom';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { AlertTriangle, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';

interface Booking {
  id: string;
  customerName: string;
  customerPhone: string;
  obligtionNo: string;
  penaltyAmount: number;
  dueDate: string;
  vehicle: string;
  offenseDate: string;
  offenseTime: string;
}

const bookingsData: Booking[] = [
  {
    id: '#0034',
    customerName: '<PERSON>',
    customerPhone: '0771234567',
    obligtionNo: '111237474',
    penaltyAmount: 500.00,
    dueDate: '2025-07-10',
    vehicle: 'PAC 200',
    offenseDate: '2025-06-20',
    offenseTime: '14:30'
  },
  {
    id: '#0025',
    customerName: '<PERSON>',
    customerPhone: '0777654321',
    obligtionNo: '122375521',
    penaltyAmount: 600.00,
    dueDate: '2025-05-12',
    vehicle: 'ABC 550',
    offenseDate: '2025-04-20',
    offenseTime: '10:30'
  },
  {
    id: '#0015',
    customerName: '<PERSON>',
    customerPhone: '0779876543',
    obligtionNo: '123456789',
    penaltyAmount: 400.00,
    dueDate: '2025-02-11',
    vehicle: 'QBV 233',
    offenseDate: '2025-01-20',
    offenseTime: '12:05'
  },
];

export function CustomerMEditfine() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  // Handle both #0034 and 0034 formats
  const cleanId = id?.startsWith('#') ? id : `#${id}`;

  console.log('URL id:', id, 'Cleaned id:', cleanId);

  const booking = bookingsData.find(b => b.id === cleanId);

  console.log('Booking found:', booking);

  const [formData, setFormData] = useState({
    obligtionNo: booking?.obligtionNo || '',
    penaltyAmount: booking?.penaltyAmount || 0,
    dueDate: booking?.dueDate || '',
    dueTime: booking?.offenseTime || '',
    offenseDate: booking?.offenseDate || '',
    offenseTime: booking?.offenseTime || '',
    uploadedFile: null as File | null
  });

  if (!booking) {
    return (
      <div className="p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8">
        <h2 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold text-red-600">Fine not found</h2>
        <p className="text-xs xs:text-sm sm:text-base">Could not find a fine with ID: {cleanId}</p>
        <Button
          className="mt-3 xs:mt-4 sm:mt-5 bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base"
          onClick={() => navigate('/admin/customerMasterAdmin/customerM-fines/')}
        >
          Go Back
        </Button>
      </div>
    );
  }

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: name === 'penaltyAmount' ? parseFloat(value) : value
    }));
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file && file.type === 'application/pdf') {
      setFormData(prev => ({
        ...prev,
        uploadedFile: file
      }));
    } else {
      alert('Please upload a valid PDF file');
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    console.log('Updated data:', {
      id: cleanId,
      customerName: booking.customerName,
      customerPhone: booking.customerPhone,
      vehicle: booking.vehicle,
      ...formData,
      dueDate: `${formData.dueDate} ${formData.dueTime}`,
      offenseDate: `${formData.offenseDate} ${formData.offenseTime}`
    });
    navigate('/admin/customerMasterAdmin/customerM-fines/');
  };

  const handleCancel = () => {
    navigate('/admin/customerMasterAdmin/customerM-fines/');
  };

  return (
    <div className="min-h-screen px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
      <div className="py-3 xs:py-4 sm:py-6">
        <div className="flex justify-between items-center mb-4 xs:mb-5 sm:mb-6">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base flex items-center"
            size="sm"
            onClick={() => navigate('/admin/customerMasterAdmin/customerM-fines/')}
          >
            <ArrowLeft className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
            <span className="hidden sm:inline">Go Back</span>
          </Button>
        </div>
        <div className="flex items-center gap-1 xs:gap-2 sm:gap-3 mb-4 xs:mb-5 sm:mb-6 mt-3 xs:mt-4 sm:mt-5">
          <AlertTriangle className="w-5 xs:w-6 sm:w-7 md:w-8 h-5 xs:h-6 sm:h-7 md:h-8" />
          <h1 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-semibold">Fines - Edit</h1>
        </div>

        <form onSubmit={handleSubmit}>
          <div className="space-y-3 xs:space-y-4 sm:space-y-5">
            <h3 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 mt-3 xs:mt-4 sm:mt-5 pb-1 xs:pb-2">Customer Information</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
              <div className="relative">
                <Label htmlFor="rentalId" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Rental ID</Label>
                <Input
                  value={booking.id}
                  readOnly
                  placeholder="Rental ID"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
                />
              </div>
              <div className="relative">
                <Label htmlFor="customerName" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Customer Name</Label>
                <Input
                  value={booking.customerName}
                  readOnly
                  placeholder="Customer Name"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
              <div className="relative">
                <Label htmlFor="phoneNumber" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Phone Number</Label>
                <Input
                  value={booking.customerPhone}
                  readOnly
                  placeholder="Phone Number"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
                />
              </div>
              <div className="relative">
                <Label htmlFor="vehicle" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Vehicle</Label>
                <Input
                  value={booking.vehicle}
                  readOnly
                  placeholder="Vehicle"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 bg-gray-100"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
              <div className="relative">
                <Label htmlFor="obligtionNo" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Obligation Number</Label>
                <Input
                  name="obligtionNo"
                  value={formData.obligtionNo}
                  onChange={handleInputChange}
                  placeholder="Obligation Number"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                />
              </div>
              <div className="relative">
                <Label htmlFor="penaltyAmount" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Penalty Amount</Label>
                <Input
                  type="number"
                  name="penaltyAmount"
                  value={formData.penaltyAmount}
                  onChange={handleInputChange}
                  step="0.01"
                  placeholder="Penalty Amount"
                  className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                />
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
              <div className="relative">
                <Label htmlFor="dueDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600 z-10">Due Date</Label>
                <div className="flex space-x-1 xs:space-x-2 sm:space-x-3">
                  <Input
                    id="dueDate"
                    type="date"
                    name="dueDate"
                    value={formData.dueDate}
                    onChange={handleInputChange}
                    className="flex-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                    placeholder="Date"
                  />
                  <Input
                    id="dueTime"
                    type="time"
                    name="dueTime"
                    value={formData.dueTime}
                    onChange={handleInputChange}
                    className="w-16 xs:w-20 sm:w-24 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                    placeholder="Time"
                  />
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
              <div className="relative">
                <h3 className="text-base xs:text-lg sm:text-xl md:text-xl font-semibold text-gray-900 mt-3 xs:mt-4 sm:mt-5 pb-1 xs:pb-2">Traffic Offense</h3>
                <div className="space-y-2 xs:space-y-3 sm:space-y-4">
                  <Label htmlFor="offenseDate" className="absolute left-2 xs:-top-1 xs:left-3s bg-white px-1 text-[10px] xs:text-xs sm:text-xs text-gray-600 z-10">Offense Date & Time</Label>
                  <div className="flex space-x-1 xs:space-x-2 sm:space-x-3">
                    <Input
                      id="offenseDate"
                      type="date"
                      name="offenseDate"
                      value={formData.offenseDate}
                      onChange={handleInputChange}
                      className="flex-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-xs sm:text-base p-1 xs:p-2 sm:p-3"
                      placeholder="Date"
                    />
                    <Input
                      id="offenseTime"
                      type="time"
                      name="offenseTime"
                      value={formData.offenseTime}
                      onChange={handleInputChange}
                      className="w-16 xs:w-20 sm:w-24 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                      placeholder="Time"
                    />
                  </div>
                </div>
              </div>
            </div>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
              <div className="relative">
                <Label className="block text-[10px] xs:text-xs sm:text-sm md:text-base font-medium text-gray-700 mt-3 xs:mt-4 sm:mt-5">Uploaded File</Label>
                <div className="flex items-center gap-1 xs:gap-2 sm:gap-3 mt-1 xs:mt-2 sm:mt-3">
                  <Button className="bg-gray-200 text-gray-700 px-2 xs:px-3 sm:px-4 py-1 xs:py-2 rounded text-xs xs:text-sm sm:text-base">View</Button>
                  <Input
                    type="file"
                    accept="application/pdf"
                    onChange={handleFileChange}
                    className="flex-1 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  />
                </div>
                {formData.uploadedFile && (
                  <p className="mt-1 xs:mt-2 sm:mt-3 text-[10px] xs:text-xs sm:text-sm text-gray-600">
                    Selected file: {formData.uploadedFile.name}
                  </p>
                )}
              </div>
            </div>

            <div className="flex justify-end space-x-2 xs:space-x-3 sm:space-x-4 mt-6 xs:mt-7 sm:mt-8">
              <Button
                variant="outline"
                onClick={handleCancel}
                className="px-4 xs:px-5 sm:px-6 py-1 xs:py-2 text-xs xs:text-sm sm:text-base"
              >
                Cancel
              </Button>
              <Button
                type="submit"
                className="px-4 xs:px-5 sm:px-6 py-1 xs:py-2 bg-[#330101] text-white hover:bg-[#660404] text-xs xs:text-sm sm:text-base"
              >
                Submit
              </Button>
            </div>
          </div>
        </form>
      </div>
    </div>
  );
}