import { useState, useEffect } from "react";

const AUDIT_TRAIL_KEY = "mock_audit_trail_entries";
const regoServiceTypeMap: Record<string, string> = {
  "1PX 1ZR": "General Maintenance",
  "1PX 5ZP": "Accident Repair",
  "2AB 3CD": "Tyre Replacement",
  "4EF 5GH": "Battery Replacement",
};
const regoOptions = Object.keys(regoServiceTypeMap);

function getAuditTrailEntries() {
  const data = localStorage.getItem(AUDIT_TRAIL_KEY);
  return data ? JSON.parse(data) : [];
}

function updateAuditTrailEntry(id: string, updated: any) {
  const entries = getAuditTrailEntries();
  const idx = entries.findIndex((e: any) => e.timestamp === id);
  if (idx !== -1) {
    entries[idx] = { ...entries[idx], ...updated };
    localStorage.setItem(AUDIT_TRAIL_KEY, JSON.stringify(entries));
  }
}

export function useAuditTrailEdit({ navigate, id }: { navigate: (path: string) => void; id?: string }) {
  const [rego, setRegoState] = useState("");
  const [serviceType, setServiceType] = useState("");
  const [timestamp, setTimestamp] = useState("");
  const [description, setDescription] = useState("");

  useEffect(() => {
    const entries = getAuditTrailEntries();
    const entry = entries.find((e: any) => e.timestamp === id);
    if (entry) {
      setRegoState(entry.rego);
      setServiceType(entry.serviceType);
      setTimestamp(entry.timestamp);
      setDescription(entry.description || "");
    }
  }, [id]);

  // When rego changes, update serviceType automatically
  const setRego = (value: string) => {
    setRegoState(value);
    setServiceType(regoServiceTypeMap[value] || "");
  };

  const handleUpdate = () => {
    if (id) {
      updateAuditTrailEntry(id, { rego, serviceType, description });
    }
    navigate("/mechanic/audit-trail");
  };

  const handleCancel = () => {
    navigate("/mechanic/audit-trail");
  };

  return {
    rego,
    setRego,
    serviceType,
    timestamp,
    description,
    setDescription,
    regoOptions,
    handleUpdate,
    handleCancel,
  };
}