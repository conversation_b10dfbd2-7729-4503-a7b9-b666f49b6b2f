import { NavLink, useLocation } from 'react-router-dom';
import { Home, UserCircle, CalendarPlus, FileWarning, ScrollText, Wrench, CarFront, Bell, ClipboardList, Settings, ChevronRight, Users, AlertTriangle, Car, CalendarCheck, Users2, Cog, ClipboardPenLine } from 'lucide-react';
import { useState } from 'react';

export function TeamLeaderSidebar() {
  const location = useLocation();
  const [expandedItems, setExpandedItems] = useState<{ [key: string]: boolean }>({
    'Team Management': false,
    'Fleet': false,
    'Service Management': false,
  });

  const navItems = [
    {
      path: '/teamleader/teamleader-dashboard',
      label: 'Dashboard Overview',
      icon: <Home className="w-5 h-5 mr-3" />
    },
    {
      path: '',
      label: 'Fleet',
      icon: <Car className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/teamleader/summary', label: 'Summary', icon: <CarFront className="w-4 h-4 mr-2" /> },
        { path: '/teamleader/quality', label: 'Quality', icon: <CalendarCheck className="w-4 h-4 mr-2" /> },
        { path: '/teamleader/taskoverview', label: 'Task Overview', icon: <FileWarning className="w-4 h-4 mr-2" /> }
      ]
    },
    {
      path: '/teamleader/mechanics',
      label: 'Mechanics',
      icon: <UserCircle className="w-5 h-5 mr-3" />
    },
    {
      path: '/teamleader/panelBeater',
      label: 'Panel Beaters',
      icon: <Users className="w-5 h-5 mr-3" />
    },
    {
      path: '/teamleader/vendors',
      label: 'Vendors',
      icon: <Users2 className="w-5 h-5 mr-3" />
    },
    {
      path: '',
      label: 'Parts & Services',
      icon: <Settings className="w-5 h-5 mr-3" />,
      subItems: [
        { path: '/teamleader/parts', label: 'Requested Parts', icon: <Wrench className="w-4 h-4 mr-2" />, notificationCount: 3 },
        { path: '/teamleader/services', label: 'Requested Services', icon: <Cog className="w-4 h-4 mr-2" />, notificationCount: 2 }
      ]
    },
    {
      path: '/teamleader/incidentreporting',
      label: 'Incident Reporting',
      icon: <ClipboardList className="w-5 h-5 mr-3" />
    },
    {
      path: '/teamleader/service-management',
      label: 'Service Management',
      icon: <AlertTriangle className="w-5 h-5 mr-3" />
    },
    {
      path: '/teamleader/preventive-maintenance',
      label: 'Preventive Maintenance',
      icon: <CalendarPlus className="w-5 h-5 mr-3" />
    },
    {
      path: '/teamleader/audittrail',
      label: 'Audit & Trail',
      icon: <ClipboardPenLine className="w-5 h-5 mr-3" />
    },
    {
      path: '/teamleader/teamleader-notification',
      label: 'Notifications Centre',
      icon: <Bell className="w-5 h-5 mr-3" />,
    },
    {
      path: '/teamleader/teamleader-activitylog',
      label: 'Activity Log',
      icon: <ScrollText className="w-5 h-5 mr-3" />
    },
  ];

  const toggleExpand = (label: string) => {
    setExpandedItems(prev => ({
      ...prev,
      [label]: !prev[label]
    }));
  };

  return (
    <aside className="bg-earth-cream w-64 min-h-screen flex flex-col">
      <nav className="flex-1">
        <div className="p-4 border-b border-gold-lighter">
          <h2 className="text-lg font-semibold text-earth-dark">Team Leader Portal</h2>
        </div>
        <ul className="space-y-1 pt-4">
          {navItems.map(item => (
            <li key={item.path || item.label}>
              {item.subItems ? (
                <div>
                  <div
                    className={`flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors cursor-pointer ${
                      location.pathname.startsWith(item.subItems[0].path.split('/')[1]) ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                    }`}
                    onClick={() => toggleExpand(item.label)}
                  >
                    {item.icon}
                    <span>{item.label}</span>
                    <ChevronRight
                      className={`w-4 h-4 ml-auto transition-transform ${
                        expandedItems[item.label] ? 'rotate-90' : ''
                      }`}
                    />
                  </div>
                  {expandedItems[item.label] && (
                    <ul className="ml-6 space-y-1">
                      {item.subItems.map(subItem => (
                        <li key={subItem.path}>
                          <NavLink
                            to={subItem.path}
                            className={({ isActive }) =>
                              `flex items-center px-4 py-2 text-earth-dark hover:bg-gold-lighter transition-colors ${
                                isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                              }`
                            }
                          >
                            {subItem.icon}
                            <span className="flex-1">{subItem.label}</span>
                            {subItem.notificationCount && (
                              <span className="bg-[#330101] text-white text-xs rounded-full px-2 py-1 ml-2">
                                {subItem.notificationCount}
                              </span>
                            )}
                          </NavLink>
                        </li>
                      ))}
                    </ul>
                  )}
                </div>
              ) : (
                <NavLink
                  to={item.path}
                  className={({ isActive }) =>
                    `flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors ${
                      isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                    }`
                  }
                >
                  {item.icon}
                  <span className="flex-1">{item.label}</span>
                </NavLink>
              )}
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}
