import React, { useState } from 'react';
import { Search, CalendarCheck } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { FleetQualityData } from '../../teamleader/type/teamleadertype';

export function FleetQualityPage() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data - replace with actual data source
  const sampleData: FleetQualityData[] = [
    {
      id: '1',
      vehicleId: 'VH001',
      rego: '1PX 12R',
      lastDetailClean: '',
      lastService: 'Breakdowns',
      lastInspection: '2025-07-05'
    },
    {
      id: '2',
      vehicleId: 'VH002',
      rego: 'PCR 455',
      lastDetailClean: '',
      lastService: 'Accident',
      lastInspection: '2025-07-03'
    },
    {
      id: '4',
      vehicleId: 'VH004',
      rego: 'ASR 321',
      lastDetailClean: '',
      lastService: 'General Maintenance',
      lastInspection: '2025-07-06'
    },
    {
      id: '5',
      vehicleId: 'VH005',
      rego: 'QBV 233',
      lastDetailClean: '',
      lastService: 'Damage',
      lastInspection: '2025-07-01'
    },
  ];

  // Filter data based on search term and filter status
  const filteredData = sampleData.filter((vehicle) =>
    (vehicle.rego.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.lastDetailClean.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.lastService.toLowerCase().includes(searchTerm.toLowerCase()) ||
      vehicle.lastInspection.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || vehicle.lastService === filterStatus)
  );

  // Pagination (only for md and up)
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Responsive: show cards for <md, table for md+
  return (
    <div className="p-2 sm:p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4 mb-3 sm:mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <CalendarCheck className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
          <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-earth-dark">Quality</h1>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 md:gap-4 mb-3 sm:mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-10 sm:h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Accident">Accident</SelectItem>
              <SelectItem value="General Maintenance">General Maintenance</SelectItem>
              <SelectItem value="Breakdowns">Breakdowns</SelectItem>
              <SelectItem value="Damage">Damage</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1 w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 h-10 sm:h-12 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap w-full sm:w-auto h-10 sm:h-12 text-sm">
          Save this search
        </Button>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="block md:hidden space-y-4 mb-4">
        {filteredData.length === 0 ? (
          <div className="text-center text-gray-500 py-8">No fleet quality data available</div>
        ) : (
          filteredData.map((vehicle) => (
            <Card
              key={vehicle.id}
              className="bg-white rounded-lg shadow-md border border-gray-200 p-4 mb-4 hover:shadow-lg transition-shadow relative"
            >
             
              {/* Card content */}
              <div className="mb-3 pr-[140px]">
                <span className="text-sm font-medium text-gray-700 break-words whitespace-pre-line">
                  {vehicle.rego}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Vehicle ID</div>
                  <div className="flex items-center text-sm">{vehicle.vehicleId}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Last Detail Clean</div>
                  <div className="flex items-center text-sm">{vehicle.lastDetailClean || '-'}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Last Service Type</div>
                  <div className="flex items-center text-sm">{vehicle.lastService}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Last Inspection Date</div>
                  <div className="flex items-center text-sm">{vehicle.lastInspection}</div>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block rounded-md border bg-white overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle ID</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Detail Clean</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Service</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Inspection</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.length > 0 ? (
              currentData.map((vehicle) => (
                <TableRow key={vehicle.id}>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.vehicleId}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.rego}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.lastDetailClean || '-'}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.lastService}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.lastInspection}</TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                  No fleet quality data available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination only for md and up */}
      <div className="mt-4 sm:mt-6 hidden md:block">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4"
        />
      </div>
    </div>
  );
}