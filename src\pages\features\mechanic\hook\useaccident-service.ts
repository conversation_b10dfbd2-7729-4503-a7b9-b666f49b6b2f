import { useState, useMemo } from "react";
import { AccidentService } from "../type/mechanictype";

const MOCK_DATA: AccidentService[] = [
  {
    vehicleId: "VID0001",
    vehicle: "Isuzu - AFS 009",
    insuranceClaimNumber: "452618",
    estimatedEndDate: "21/07/2025",
    actualEndDate: "22/07/2025",
    insuranceStatus: "Initial Review",
    status: "Pending",
    mechanicAssigned: "<PERSON>, <PERSON>",
  },
  {
    vehicleId: "VID0002",
    vehicle: "Isuzu - AAB 004",
    insuranceClaimNumber: "1234569",
    estimatedEndDate: "12/06/2025",
    actualEndDate: "10/06/2025",
    insuranceStatus: "Accepted",
    status: "InProgress",
    mechanicAssigned: "<PERSON>",
  },
  {
    vehicleId: "VID0003",
    vehicle: "Toyota - BCS 903",
    insuranceClaimNumber: "678954",
    estimatedEndDate: "08/06/2025",
    actualEndDate: "08/06/2025",
    insuranceStatus: "Declined",
    status: "Done",
    mechanicAssigned: "<PERSON>",
  },
];

export function useAccidentService() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const filteredData = useMemo(() => {
    let data = MOCK_DATA;

    if (filterStatus !== "All") {
      data = data.filter((item) => item.status === filterStatus);
    }

    if (searchTerm.trim()) {
      data = data.filter(
        (item) =>
          item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
          item.insuranceClaimNumber.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return data;
  }, [searchTerm, filterStatus]);

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);

  const currentData = useMemo(() => {
    const start = (currentPage - 1) * recordsPerPage;
    return filteredData.slice(start, start + recordsPerPage);
  }, [filteredData, currentPage, recordsPerPage]);

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    currentData,
    totalPages,
    totalEntries,
  };
}