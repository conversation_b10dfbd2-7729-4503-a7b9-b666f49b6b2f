import { Dispatch, SetStateAction } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { AddRequestFormData, Booking, Vehicle } from '../type/reception-type';

export const initializeFormData = (): AddRequestFormData => ({
  customerPhone: '',
  customerName: '',
  rentalId: '',
  vehicle: '',
  vehicleClass: '',
  pickupDate: '',
  pickupTime: '',
  returnDate: '',
  returnTime: '',
  requestType: '',
  dateRangeFrom: '',
  dateRangeTo: '',
  newReturnDate: '',
  newReturnTime: '',
  note: '',
  permitNumber: '12345678',
  issueCountry: 'Australia',
  issueDate: '2022-01-01',
  expiryDate: '2027-01-01',
  licenseType: 'Full License',
  frontView: null,
  backView: null
});

export const handlePhoneChange = (
  phone: string,
  setFormData: Dispatch<SetStateAction<AddRequestFormData>>,
  bookingsData: Booking[],
  mockVehicles: Vehicle[]
) => {
  setFormData(prev => ({ ...prev, customerPhone: phone }));

  const booking = bookingsData.find(b => b.customerPhone === phone);
  if (booking) {
    const vehicle = mockVehicles.find(v => v.rego === booking.vehicle);
    setFormData(prev => ({
      ...prev,
      customerName: booking.customerName,
      rentalId: booking.id,
      vehicle: booking.vehicle,
      vehicleClass: booking.vehicleClass,
      pickupDate: booking.offenseDate,
      pickupTime: booking.offenseTime,
      returnDate: booking.dueDate,
      returnTime: booking.offenseTime
    }));
  } else {
    setFormData(prev => ({
      ...prev,
      customerName: '',
      rentalId: '',
      vehicle: '',
      vehicleClass: '',
      pickupDate: '',
      pickupTime: '',
      returnDate: '',
      returnTime: ''
    }));
  }
};

export const handleRequestTypeChange = (
  type: string,
  setFormData: Dispatch<SetStateAction<AddRequestFormData>>,
  setShowValidationResult: Dispatch<SetStateAction<boolean>>,
  setExtensionGranted: Dispatch<SetStateAction<string>>
) => {
  setFormData(prev => ({
    ...prev,
    requestType: type,
    dateRangeFrom: '',
    dateRangeTo: '',
    newReturnDate: '',
    newReturnTime: ''
  }));
  setShowValidationResult(false);
  setExtensionGranted('');
};

export const handleInputChange = (
  field: keyof AddRequestFormData,
  value: string | File | null,
  setFormData: Dispatch<SetStateAction<AddRequestFormData>>
) => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};

export const handleValidate = (
  setShowValidationResult: Dispatch<SetStateAction<boolean>>
) => {
  setShowValidationResult(true);
};

export const handleGoBack = (navigate: NavigateFunction) => {
  navigate('/reception/reception-changerequest');
};

export const handleGenerateInvoice = (navigate: NavigateFunction) => {
  navigate('/reception/reception-changerequestinvoice');
};