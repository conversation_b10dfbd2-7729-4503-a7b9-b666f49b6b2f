import { useState, useEffect } from "react";
import { RequestPartMain } from "../type/mechanictype";

const PARTS_KEY = "mock_request_parts_main";

function getParts(): RequestPartMain[] {
  const data = localStorage.getItem(PARTS_KEY);
  return data ? JSON.parse(data) : [];
}

function updatePart(id: string, updated: Partial<RequestPartMain>) {
  const parts = getParts();
  const idx = parts.findIndex((p) => p.partNumber === id);
  if (idx !== -1) {
    parts[idx] = { ...parts[idx], ...updated };
    localStorage.setItem(PARTS_KEY, JSON.stringify(parts));
  }
}

export function useRequestPartsMainEdit({
  navigate,
  id,
}: {
  navigate: (path: string) => void;
  id?: string;
}) {
  const [partNumber, setPartNumber] = useState("");
  const [partName, setPartName] = useState("");
  const [quantity, setQuantity] = useState("");
  const [description, setDescription] = useState("");

  useEffect(() => {
    const parts = getParts();
    const part = parts.find((p) => p.partNumber === id);
    if (part) {
      setPartNumber(part.partNumber);
      setPartName(part.partName);
      setQuantity(part.quantity);
      setDescription(part.description);
    }
  }, [id]);

  const handleUpdate = () => {
    if (id) {
      updatePart(id, { partName, quantity, description });
    }
    navigate("/mechanic/request-part");
  };

  const handleCancel = () => {
    navigate("/mechanic/request-part");
  };

  return {
    partNumber,
    partName,
    setPartName,
    quantity,
    setQuantity,
    description,
    setDescription,
    handleUpdate,
    handleCancel,
  };
}