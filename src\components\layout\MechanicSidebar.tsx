import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  LayoutDashboard, 
  Car, 
  FileSearch, 
  Settings,
  BarChart3, 
  Calendar,
  Shield
} from 'lucide-react';

export function MechanicSidebar({ isOpen, onClose }) {
  const navItems = [
    {
      path: '/mechanic/mechanic-dashboard',
      label: 'Dashboard Overview',
      icon: <LayoutDashboard className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/mechanic/service-management-all',
      label: 'Service Management',
      icon: <Car className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/mechanic/request-part',
      label: 'Request Part',
      icon: <FileSearch className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/mechanic/request-services',
      label: 'Request Services',
      icon: <Settings className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    /* {
      path: '/mechanic/audit-trail',
      label: 'Audit & Trail',
      icon: <FileSearch className="w-5 h-5 mr-3 flex-shrink-0" />
    }, */
    {
      path: '/mechanic/job-history',
      label: 'Job History',
      icon: <Calendar className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/mechanic/notification-center',
      label: 'Notification Centre',
      icon: <BarChart3 className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/mechanic/activity-log',
      label: 'Activity Log',
      icon: <Calendar className="w-5 h-5 mr-3 flex-shrink-0" />
    }
  ];

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <aside className={`
        bg-earth-cream flex flex-col
        fixed lg:static inset-y-0 left-0 z-50
        w-64 lg:w-64
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <nav className="flex-1 overflow-y-auto">
          <div className="p-4 border-b border-gold-lighter">
            <div className="flex items-center">
              <Shield className="w-6 h-6 mr-2 text-earth-dark" />
              <h2 className="text-lg font-semibold text-earth-dark">Mechanic Portal</h2>
            </div>
          </div>
          <ul className="space-y-1 pt-4 pb-4">
            {navItems.map(item => (
              <li key={item.path}>
                <NavLink
                  to={item.path}
                  onClick={() => {
                    // Close sidebar on mobile when nav item is clicked
                    if (window.innerWidth < 1024) {
                      onClose();
                    }
                  }}
                  className={({ isActive }) =>
                    `flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors ${
                      isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                    }`
                  }
                >
                  {item.icon}
                  <span className="text-sm lg:text-base truncate">{item.label}</span>
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
      </aside>
    </>
  );
}