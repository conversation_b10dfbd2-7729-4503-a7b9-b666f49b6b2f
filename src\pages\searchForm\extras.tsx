import React from 'react';
import { useNavigate } from 'react-router-dom'; 
import SearchHeader from '@/components/layout/SearchHeader';
import { ChevronDown, Pencil } from 'lucide-react';
import { Card, CardHeader, CardFooter, CardTitle, CardDescription, CardContent } from '@/components/ui/card';
import { cn } from '../../lib/utils';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Textarea } from '@/components/ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

// Define TypeScript interface for rentalDetails
interface RentalDetails {
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  pickupLocation: string;
  returnLocation: string;
  rateType: string;
}

export function Extras() {
  const navigate = useNavigate(); 
  const handleNextStep = (): void => {
    navigate('/search/customer-information');
  };

  const quoteNavigate = useNavigate(); 
  const handleQuote = (): void => {
    quoteNavigate('/search/quote');
  };

  // Simulate user role (default to master admin until backend login is implemented)
  const userRole = 'customer'; // Change to 'customer' to test customer view

  // Define steps based on user role
  const steps = userRole === 'masterAdmin' ? [
    { number: 1, label: 'Dates', active: false },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: true },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: false },
    { number: 6, label: 'Payment', active: false },
    { number: 7, label: 'Receipt', active: false },
    { number: 8, label: 'Pickup', active: false },
    { number: 9, label: 'Agreement', active: false },
    { number: 10, label: 'Return', active: false },
  ] : [
    { number: 1, label: 'Dates', active: false },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: true },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: false },
    { number: 6, label: 'Payment', active: false },
    { number: 7, label: 'Receipt', active: false },
  ];

  const [pickupLocation, setPickupLocation] = React.useState<string>('Somerton');
  const [returnLocation, setReturnLocation] = React.useState<string>('Somerton');
  const [rateType, setRateType] = React.useState<string>('');
  const [coinfee, setCoinfee] = React.useState<boolean>(false);
  const [bondDebit, setBondDebit] = React.useState<boolean>(true);
  const [insurance, setInsurance] = React.useState<boolean>(true);
  const [additionalDriverQty, setAdditionalDriverQty] = React.useState<number>(0);
  const [childSeatQty, setChildSeatQty] = React.useState<number>(1);
  const [gpsQty, setGpsQty] = React.useState<number>(0);
  const [rentalDetails, setRentalDetails] = React.useState<RentalDetails>({
    pickupDate: '13/06/2025',
    pickupTime: '11:30',
    returnDate: '14/06/2025',
    returnTime: '10:30',
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    rateType: 'New Year 2021',
  });
  const [isPopupOpen, setIsPopupOpen] = React.useState<boolean>(false);
  const locationOptions: string[] = ['', 'Somerton'];
  const rateOptions: string[] = ['', 'New Year 2021', 'Long Term Rental'];

  const [unlimitedMileage, setUnlimitedMileage] = React.useState(false);
  const [earlyPickupLateDropOff, setEarlyPickupLateDropOff] = React.useState(true);
  const [additionalDriver, setAdditionalDriver] = React.useState(false);
  const [babySeat, setBabySeat] = React.useState(false);
  const [otherCharges, setOtherCharges] = React.useState(true);

  const [bondRefund250, setBondRefund250] = React.useState(false);
  const [bondRefund500, setBondRefund500] = React.useState(false);
  const [bondRefund1000, setBondRefund1000] = React.useState(false);
  const [excessMileage, setExcessMileage] = React.useState(true);
  const [fuelCharges, setFuelCharges] = React.useState(true);
  const [insuranceExcessDamages, setInsuranceExcessDamages] = React.useState(true);
  const [couponCode, setCouponCode] = React.useState('');
  const [manualDiscount, setManualDiscount] = React.useState('');
  const [isPercentage, setIsPercentage] = React.useState(false);

  const Select = ({ value, onChange, className = "", children }) => (
    <select 
      value={value}
      onChange={onChange}
      className={`px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-blue-500 bg-white ${className}`}
    >
      {children}
    </select>
  );

  const handleEditClick = (type: 'pickup' | 'return') => {
    setIsPopupOpen(true);
  };

  const handleSave = (newPickupDate: string, newPickupTime: string, newReturnDate: string, newReturnTime: string) => {
    setRentalDetails({
      ...rentalDetails,
      pickupDate: newPickupDate,
      pickupTime: newPickupTime,
      returnDate: newReturnDate,
      returnTime: newReturnTime
    });
    setIsPopupOpen(false);
  };

  // Find the index of the active step
  const activeIndex = steps.findIndex(step => step.active);

  // Determine visible steps: active step, up to 2 before, up to 2 after
  const getVisibleSteps = () => {
    const visible = [activeIndex];
    if (activeIndex - 1 >= 0) visible.unshift(activeIndex - 1);
    if (activeIndex - 2 >= 0) visible.unshift(activeIndex - 2);
    if (activeIndex + 1 < steps.length) visible.push(activeIndex + 1);
    if (activeIndex + 2 < steps.length) visible.push(activeIndex + 2);
    return visible.sort((a, b) => a - b);
  };

  const visibleStepIndices = getVisibleSteps();

  return (
    <div className="min-h-screen">
      <SearchHeader />
      <div className="w-full px-16 sm:px-32 py-6">
        <div className="p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
          <div className="flex items-center justify-center mb-2 sm:mb-4 md:mb-6 lg:mb-8 gap-1 sm:gap-2 md:gap-3 lg:gap-4">
            {steps.map((step, index) => {
              const isVisibleOnXs = visibleStepIndices.includes(index);
              const isVisibleOnSm = visibleStepIndices.includes(index);
              const isVisibleOnMd = visibleStepIndices.includes(index) || index <= activeIndex + 3;
              const isVisibleOnLg = true;

              return (
                <div
                  key={step.number}
                  className={`flex items-center ${!isVisibleOnXs ? 'hidden' : ''} ${isVisibleOnSm ? 'sm:flex' : 'sm:hidden'} ${isVisibleOnMd ? 'md:flex' : 'md:hidden'} ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-full flex items-center justify-center text-xs sm:text-sm md:text-base lg:text-base font-semibold ${
                        step.active ? 'bg-amber-500 text-white' : 'bg-gray-300 text-gray-600'
                      }`}
                    >
                      {step.number}
                    </div>
                    <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm mt-0.5 sm:mt-1">{step.label}</span>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`w-3 sm:w-4 md:w-6 lg:w-8 h-0.5 bg-gray-300 mx-0.5 sm:mx-1 md:mx-2 lg:mx-3 mt-[-12px] sm:mt-[-16px] md:mt-[-18px] lg:mt-[-20px] ${
                        !isVisibleOnXs || !visibleStepIndices.includes(index + 1) ? 'hidden' : ''
                      } ${isVisibleOnSm && visibleStepIndices.includes(index + 1) ? 'sm:flex' : 'sm:hidden'} ${
                        isVisibleOnMd && (isVisibleOnMd || index + 1 <= activeIndex + 3) ? 'md:flex' : 'md:hidden'
                      } ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                    ></div>
                  )}
                </div>
              );
            })}
          </div>

          <div className="flex flex-col md:flex-row gap-2 sm:gap-4 md:gap-6 lg:gap-8">
            <div className="w-full md:w-2/3 lg:w-3/4">
              {userRole === 'masterAdmin' && (
                <>
                  <div className="mb-2 sm:mb-4 md:mb-6">
                    <p className="bg-yellow-100 text-black p-1 sm:p-2 md:p-3 rounded text-[10px] sm:text-xs md:text-sm lg:text-sm">
                      Now your Insurance Excess is AUD 1000 (if you are under 25 years it is AUD 2000).
                    </p>
                    {coinfee && (
                      <p className="bg-yellow-100 text-black p-1 sm:p-2 md:p-3 rounded mt-1 sm:mt-2 text-[10px] sm:text-xs md:text-sm lg:text-sm">
                        15% transaction fee is added.
                      </p>
                    )}
                    <p className="bg-blue-100 text-black p-1 sm:p-2 md:p-3 rounded mt-1 sm:mt-2 text-[10px] sm:text-xs md:text-sm lg:text-sm">
                      Kilometers Limit per day (if you exceed below limit you will be charged 0.20c per kilometer) Passenger Vehicles: 250km Commercial Vehicles: 100km
                    </p>
                  </div>

                  <div className="mb-2 sm:mb-4 md:mb-6">
  <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2">Pickup Details</h2>
  <div className="flex flex-col sm:flex-row gap-1 sm:gap-2 md:gap-4">
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full justify-between border-b text-base"
          disabled
        >
          {pickupLocation || 'Pickup Location'}
          <ChevronDown className="w-4 h-4 text-gray-500" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-full">
        {locationOptions.map((loc) => (
          <DropdownMenuItem
            key={loc}
            onSelect={() => setPickupLocation(loc)}
            className={pickupLocation === loc ? 'bg-amber-100 font-semibold' : ''}
          >
            {loc || 'Pickup Location'}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full justify-between border-b text-base"
          disabled
        >
          {returnLocation || 'Return Location'}
          <ChevronDown className="w-4 h-4 text-gray-500" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-full">
        {locationOptions.map((loc) => (
          <DropdownMenuItem
            key={loc}
            onSelect={() => setReturnLocation(loc)}
            className={returnLocation === loc ? 'bg-amber-100 font-semibold' : ''}
          >
            {loc || 'Return Location'}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</div>

                  <div className="mb-2 sm:mb-4 md:mb-6">
                    <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2">Protection & Coverages</h2>
                    <h3 className="text-[10px] sm:text-sm md:text-base lg:text-lg font-medium mb-1">Bond Compulsory - 500</h3>
                    <p className="mb-1 text-[10px] sm:text-xs md:text-sm lg:text-sm">Provide your Credit Card when you pick up the vehicle and this will be reduced to AUD 250.</p>
                    <p className="text-red-600 font-semibold mb-1 text-[10px] sm:text-xs md:text-sm lg:text-sm">Please note if you want to pay the bond by Cash, it is AUD 1000.</p>
                    <div className="mb-2 sm:mb-4 md:mb-6">
                      <div className="border border-gray-300 rounded p-1 sm:p-2 bg-white w-[120px] sm:w-[150px] md:w-[180px] lg:w-[200px]">
                        <Label className="flex items-center">
                          <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">AUD 500.00 - Debit</span>
                          <Input
                            type="checkbox"
                            checked={bondDebit}
                            onChange={(e) => setBondDebit(e.target.checked)}
                            className="ml-1 sm:ml-2 w-[25px] h-[16px]"
                          />
                        </Label>
                      </div>
                    </div>

                    <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2 flex items-center">
                      <span>Insurance - 15</span>
                      <button className="ml-1 sm:ml-2 bg-green-500 text-white px-1 sm:px-2 py-0.5 sm:py-1 rounded text-[10px] sm:text-xs md:text-sm">Recommended</button>
                    </h2>
                    <p className="mb-1 text-[10px] sm:text-xs md:text-sm lg:text-sm">
                      Take the insurance and bring down the Insurance Excess from AUD 5000 to AUD 1000. (If you are 21 - 25 years old this is compulsory and the Insurance Excess in AUD 2000.)
                    </p>
                    <div className="mb-2 sm:mb-4 md:mb-6">
                      <div className="border border-gray-300 rounded p-1 sm:p-2 bg-white w-[120px] sm:w-[150px] md:w-[180px] lg:w-[200px]">
                        <Label className="flex items-center">
                          <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">AUD 15.00/Day</span>
                          <Input
                            type="checkbox"
                            checked={insurance}
                            onChange={(e) => setInsurance(e.target.checked)}
                            className="ml-1 sm:ml-2 w-[25px] h-[16px]"
                          />
                        </Label>
                      </div>
                    </div>
                  </div>

                  <div className="mb-2 sm:mb-4 md:mb-6">
                    <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2">Equipment & Services</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-1 sm:gap-2 md:gap-4">
                      <Card className={cn('text-center', { 'bg-gray-100 cursor-not-allowed': true })}>
                        <CardHeader>
                          <CardTitle className="text-[10px] sm:text-sm md:text-base lg:text-base">Additional Driver</CardTitle>
                          <CardDescription className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">
                            Share the driver, Add an extra driver for hassle-free journeys.
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">AUD 40.00/Day</p>
                        </CardContent>
                        <CardFooter className="justify-center">
                          <div className="flex">
                            <button disabled className="border rounded-l px-1 sm:px-2">-</button>
                            <span className="border-t border-b px-2 sm:px-3 md:px-4 text-[9px] sm:text-[10px] md:text-xs lg:text-sm">{additionalDriverQty}</span>
                            <button disabled className="border rounded-r px-1 sm:px-2">+</button>
                          </div>
                        </CardFooter>
                      </Card>
                      <Card className="text-center">
                        <CardHeader>
                          <CardTitle className="text-[10px] sm:text-sm md:text-base lg:text-base">Child Seat</CardTitle>
                          <CardDescription className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">
                            Ensure your little one's safety with our comfortable and secure Child Seat option.
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">AUD 40.00/Day</p>
                        </CardContent>
                        <CardFooter className="justify-center">
                          <div className="flex">
                            <button onClick={() => setChildSeatQty(Math.max(0, childSeatQty - 1))} className="border rounded-l px-1 sm:px-2">-</button>
                            <span className="border-t border-b px-2 sm:px-3 md:px-4 text-[9px] sm:text-[10px] md:text-xs lg:text-sm">{childSeatQty}</span>
                            <button onClick={() => setChildSeatQty(childSeatQty + 1)} className="border rounded-r px-1 sm:px-2">+</button>
                          </div>
                        </CardFooter>
                      </Card>
                      <Card className="text-center">
                        <CardHeader>
                          <CardTitle className="text-[10px] sm:text-sm md:text-base lg:text-base">GPS</CardTitle>
                          <CardDescription className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">
                            Stay on track with our easy-to-use GPS rental services.
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">AUD 80.00/Day</p>
                        </CardContent>
                        <CardFooter className="justify-center">
                          <div className="flex">
                            <button onClick={() => setGpsQty(Math.max(0, gpsQty - 1))} className="border rounded-l px-1 sm:px-2">-</button>
                            <span className="border-t border-b px-2 sm:px-3 md:px-4 text-[9px] sm:text-[10px] md:text-xs lg:text-sm">{gpsQty}</span>
                            <button onClick={() => setGpsQty(gpsQty + 1)} className="border rounded-r px-1 sm:px-2">+</button>
                          </div>
                        </CardFooter>
                      </Card>
                    </div>
                  </div>

                  <div className="p-2 sm:p-3 md:p-4">
                    <div className="mb-6">
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-1 gap-3 sm:gap-4">
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">⏳</span>
                              <span className="text-sm sm:text-base">Unlimited Mileage - 40</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <input
                                type="checkbox"
                                checked={unlimitedMileage}
                                onChange={(e) => setUnlimitedMileage(e.target.checked)}
                                className="w-4 h-4 sm:w-5 sm:h-5"
                              />
                              <span className="text-sm sm:text-base">AUD40.00/Day</span>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">⏰</span>
                              <span className="text-sm sm:text-base">Early PickUp/Late Drop Off</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <input
                                type="checkbox"
                                checked={earlyPickupLateDropOff}
                                onChange={(e) => setEarlyPickupLateDropOff(e.target.checked)}
                                className="w-4 h-4 sm:w-5 sm:h-5"
                              />
                              <span className="text-sm sm:text-base">AUD20.00 One Time</span>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">👶</span>
                              <span className="text-sm sm:text-base">Other Charges</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <input
                                type="checkbox"
                                checked={otherCharges}
                                onChange={(e) => setOtherCharges(e.target.checked)}
                                className="w-4 h-4 sm:w-5 sm:h-5"
                              />
                              <span className="text-sm sm:text-base">AUD100 One Time</span>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>

                  <div className="p-2 sm:p-3 md:p-4">
                    <div className="mb-6">
                      <h2 className="text-base sm:text-lg font-semibold mb-4">Returns</h2>
                      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-1 lg:grid-cols-1 gap-3 sm:gap-4">
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">💰</span>
                              <span className="text-sm sm:text-base">Bond Refund (250)</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <input
                                type="checkbox"
                                checked={bondRefund250}
                                onChange={(e) => setBondRefund250(e.target.checked)}
                                className="w-4 h-4"
                              />
                              <span className="text-sm sm:text-base">-AUD250.00 One Time</span>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">💰</span>
                              <span className="text-sm sm:text-base">Bond Refund (500)</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <input
                                type="checkbox"
                                checked={bondRefund500}
                                onChange={(e) => setBondRefund500(e.target.checked)}
                                className="w-4 h-4"
                              />
                              <span className="text-sm sm:text-base">-AUD500.00 One Time</span>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">💰</span>
                              <span className="text-sm sm:text-base">Bond Refund (1000)</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <input
                                type="checkbox"
                                checked={bondRefund1000}
                                onChange={(e) => setBondRefund1000(e.target.checked)}
                                className="w-4 h-4"
                              />
                              <span className="text-sm sm:text-base">-AUD1,000.00 One Time</span>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">⏳</span>
                              <span className="text-sm sm:text-base">Excess Mileage</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <Select
                                value={excessMileage}
                                onChange={(e) => setExcessMileage(e.target.value)}
                                className="w-16 sm:w-20 text-sm sm:text-base"
                              >
                                <option value="">0</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                              </Select>
                              <span className="text-sm sm:text-base">AUD100 One Time</span>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">⛽</span>
                              <span className="text-sm sm:text-base">Fuel Charges</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <Select
                                value={fuelCharges}
                                onChange={(e) => setFuelCharges(e.target.value)}
                                className="w-16 sm:w-20 text-sm sm:text-base"
                              >
                                <option value="">0</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                              </Select>
                              <span className="text-sm sm:text-base">AUD100 One Time</span>
                            </div>
                          </CardContent>
                        </Card>
                        <Card>
                          <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-lg sm:text-xl">🚗</span>
                              <span className="text-sm sm:text-base">Insurance Excess / Damages</span>
                            </div>
                            <div className="flex items-center gap-2 justify-between sm:justify-end">
                              <Select
                                value={insuranceExcessDamages}
                                onChange={(e) => setInsuranceExcessDamages(e.target.value)}
                                className="w-16 sm:w-20 text-sm sm:text-base"
                              >
                                <option value="">0</option>
                                <option value="1">1</option>
                                <option value="2">2</option>
                                <option value="3">3</option>
                                <option value="4">4</option>
                                <option value="5">5</option>
                              </Select>
                              <span className="text-sm sm:text-base">AUD100 One Time</span>
                            </div>
                          </CardContent>
                        </Card>
                      </div>
                    </div>
                  </div>

                  <div>
                    <h2 className="text-base sm:text-lg font-semibold mb-4">Discounts</h2>
                    <div className="grid grid-cols-1 gap-3 sm:gap-4">
                      <Card>
                        <CardContent className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between p-3 sm:p-4">
                          <div className="flex items-center gap-2 mb-2 sm:mb-0">
                            <span className="text-sm sm:text-base">Coupon Code</span>
                          </div>
                          <div className="flex items-center gap-2">
                            <Input
                              type="text"
                              value={couponCode}
                              onChange={(e) => setCouponCode(e.target.value)}
                              className="w-full sm:w-32 md:w-40"
                            />
                            <span className="text-lg">×</span>
                          </div>
                        </CardContent>
                      </Card>
                      <Card>
                        <CardContent className="p-3 sm:p-4">
                          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between lg:flex-row lg:items-center lg:justify-between mb-3 sm:mb-4">
                            <div className="flex items-center gap-2 mb-2 sm:mb-0">
                              <span className="text-sm sm:text-base">Manual Discount (AU$)</span>
                            </div>
                            <Input
                              type="text"
                              value={manualDiscount}
                              onChange={(e) => setManualDiscount(e.target.value)}
                              className="w-full sm:w-32 md:w-40"
                            />
                          </div>
                          <div className="flex flex-col sm:flex-row sm:items-center lg:flex-row lg:items-center gap-2">
                            <Label className="text-sm">Manual Discount is a Percentage?</Label>
                            <div className="flex items-center gap-2 sm:gap-4">
                              <label className="flex items-center gap-1 text-sm">
                                <input
                                  type="radio"
                                  checked={!isPercentage}
                                  onChange={() => setIsPercentage(false)}
                                  className="w-4 h-4"
                                />
                                Yes
                              </label>
                              <label className="flex items-center gap-1 text-sm">
                                <input
                                  type="radio"
                                  checked={isPercentage}
                                  onChange={() => setIsPercentage(true)}
                                  className="w-4 h-4"
                                />
                                No
                              </label>
                            </div>
                          </div>
                        </CardContent>
                      </Card>
                    </div>
                  </div>

                  <div className="mt-4 sm:mt-6">
                    <div className="flex flex-col sm:flex-row gap-2 sm:gap-3">
                      <Button variant="default" className="bg-blue-700 text-white text-sm sm:text-base px-3 sm:px-4 py-2 sm:py-2.5">
                        Update Order
                      </Button>
                      <Button variant="outline" className="text-sm sm:text-base px-3 sm:px-4 py-2 sm:py-2.5">
                        Next Step
                      </Button>
                      <Button variant="outline" className="text-sm sm:text-base px-3 sm:px-4 py-2 sm:py-2.5">
                        Loan Vehicle
                      </Button>
                      <Button variant="outline" className="text-sm sm:text-base px-3 sm:px-4 py-2 sm:py-2.5">
                        Quote
                      </Button>
                      <Button variant="outline" className="text-sm sm:text-base px-3 sm:px-4 py-2 sm:py-2.5">
                        Manual Charge
                      </Button>
                    </div>
                  </div>

                  <div className="mb-2 sm:mb-4 md:mb-6">
                    <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2">Note</h2>
                    <Textarea className="w-full border border-gray-300 rounded p-1 sm:p-2 h-10 sm:h-12 md:h-16 text-[10px] sm:text-xs md:text-sm lg:text-base"></Textarea>
                  </div>

                  <div className="flex gap-1 sm:gap-2 md:gap-4 flex justify-end">
                    <Button
                      onClick={handleQuote}
                      style={{ backgroundColor: '#330101' }}
                      className="text-white px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 md:py-2 rounded text-[10px] sm:text-xs md:text-sm lg:text-base"
                    >
                      Download Quote
                    </Button>
                    <Button
                      onClick={handleNextStep}
                      style={{ backgroundColor: '#EBBB4E' }}
                      className="text-white px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 md:py-2 rounded text-[10px] sm:text-xs md:text-sm lg:text-base"
                    >
                      Proceed to Booking
                    </Button>
                  </div>
                </>
              )}

              {userRole === 'customer' && (
                <>
                  <div className="mb-2 sm:mb-4 md:mb-6">
                    <p className="bg-yellow-100 text-black p-1 sm:p-2 md:p-3 rounded text-[10px] sm:text-xs md:text-sm lg:text-sm">
                      Now your Insurance Excess is AUD 1000 (if you are under 25 years it is AUD 2000).
                    </p>
                    {coinfee && (
                      <p className="bg-yellow-100 text-black p-1 sm:p-2 md:p-3 rounded mt-1 sm:mt-2 text-[10px] sm:text-xs md:text-sm lg:text-sm">
                        15% transaction fee is added.
                      </p>
                    )}
                    <p className="bg-blue-100 text-black p-1 sm:p-2 md:p-3 rounded mt-1 sm:mt-2 text-[10px] sm:text-xs md:text-sm lg:text-sm">
                      Kilometers Limit per day (if you exceed below limit you will be charged 0.20c per kilometer) Passenger Vehicles: 250km Commercial Vehicles: 100km
                    </p>
                  </div>

              <div className="mb-2 sm:mb-4 md:mb-6">
  <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2">Pickup Details</h2>
  <div className="flex flex-col sm:flex-row gap-1 sm:gap-2 md:gap-4">
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full justify-between border-b text-base"
          disabled
        >
          {pickupLocation || 'Pickup Location'}
          <ChevronDown className="w-4 h-4 text-gray-500" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-full">
        {locationOptions.map((loc) => (
          <DropdownMenuItem
            key={loc}
            onSelect={() => setPickupLocation(loc)}
            className={pickupLocation === loc ? 'bg-amber-100 font-semibold' : ''}
          >
            {loc || 'Pickup Location'}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button 
          variant="outline" 
          className="w-full justify-between border-b text-base"
          disabled
        >
          {returnLocation || 'Return Location'}
          <ChevronDown className="w-4 h-4 text-gray-500" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent className="w-full">
        {locationOptions.map((loc) => (
          <DropdownMenuItem
            key={loc}
            onSelect={() => setReturnLocation(loc)}
            className={returnLocation === loc ? 'bg-amber-100 font-semibold' : ''}
          >
            {loc || 'Return Location'}
          </DropdownMenuItem>
        ))}
      </DropdownMenuContent>
    </DropdownMenu>
  </div>
</div>

                  <div className="mb-2 sm:mb-4 md:mb-6">
                    <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2">Protection & Coverages</h2>
                    <h3 className="text-[10px] sm:text-sm md:text-base lg:text-lg font-medium mb-1">Bond Compulsory - 500</h3>
                    <p className="mb-1 text-[10px] sm:text-xs md:text-sm lg:text-sm">Provide your Credit Card when you pick up the vehicle and this will be reduced to AUD 250.</p>
                    <p className="text-red-600 font-semibold mb-1 text-[10px] sm:text-xs md:text-sm lg:text-sm">Please note if you want to pay the bond by Cash, it is AUD 1000.</p>
                    <div className="mb-2 sm:mb-4 md:mb-6">
                      <div className="border border-gray-300 rounded p-1 sm:p-2 bg-white w-[120px] sm:w-[150px] md:w-[180px] lg:w-[200px]">
                        <Label className="flex items-center">
                          <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">AUD 500.00 - Debit</span>
                          <Input
                            type="checkbox"
                            checked={bondDebit}
                            onChange={(e) => setBondDebit(e.target.checked)}
                            className="ml-1 sm:ml-2 w-[25px] h-[16px]"
                          />
                        </Label>
                      </div>
                    </div>

                    <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2 flex items-center">
                      <span>Insurance - 15</span>
                      <button className="ml-1 sm:ml-2 bg-green-500 text-white px-1 sm:px-2 py-0.5 sm:py-1 rounded text-[10px] sm:text-xs md:text-sm">Recommended</button>
                    </h2>
                    <p className="mb-1 text-[10px] sm:text-xs md:text-sm lg:text-sm">
                      Take the insurance and bring down the Insurance Excess from AUD 5000 to AUD 1000. (If you are 21 - 25 years old this is compulsory and the Insurance Excess in AUD 2000.)
                    </p>
                    <div className="mb-2 sm:mb-4 md:mb-6">
                      <div className="border border-gray-300 rounded p-1 sm:p-2 bg-white w-[120px] sm:w-[150px] md:w-[180px] lg:w-[200px]">
                        <Label className="flex items-center">
                          <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">AUD 15.00/Day</span>
                          <Input
                            type="checkbox"
                            checked={insurance}
                            onChange={(e) => setInsurance(e.target.checked)}
                            className="ml-1 sm:ml-2 w-[25px] h-[16px]"
                          />
                        </Label>
                      </div>
                    </div>
                  </div>

                  <div className="mb-2 sm:mb-4 md:mb-6">
                    <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2">Equipment & Services</h2>
                    <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-1 sm:gap-2 md:gap-4">
                      <Card className={cn('text-center', { 'bg-gray-100 cursor-not-allowed': true })}>
                        <CardHeader>
                          <CardTitle className="text-[10px] sm:text-sm md:text-base lg:text-base">Additional Driver</CardTitle>
                          <CardDescription className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">
                            Share the driver, Add an extra driver for hassle-free journeys.
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">AUD 40.00/Day</p>
                        </CardContent>
                        <CardFooter className="justify-center">
                          <div className="flex">
                            <button disabled className="border rounded-l px-1 sm:px-2">-</button>
                            <span className="border-t border-b px-2 sm:px-3 md:px-4 text-[9px] sm:text-[10px] md:text-xs lg:text-sm">{additionalDriverQty}</span>
                            <button disabled className="border rounded-r px-1 sm:px-2">+</button>
                          </div>
                        </CardFooter>
                      </Card>
                      <Card className="text-center">
                        <CardHeader>
                          <CardTitle className="text-[10px] sm:text-sm md:text-base lg:text-base">Child Seat</CardTitle>
                          <CardDescription className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">
                            Ensure your little one's safety with our comfortable and secure Child Seat option.
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">AUD 40.00/Day</p>
                        </CardContent>
                        <CardFooter className="justify-center">
                          <div className="flex">
                            <button onClick={() => setChildSeatQty(Math.max(0, childSeatQty - 1))} className="border rounded-l px-1 sm:px-2">-</button>
                            <span className="border-t border-b px-2 sm:px-3 md:px-4 text-[9px] sm:text-[10px] md:text-xs lg:text-sm">{childSeatQty}</span>
                            <button onClick={() => setChildSeatQty(childSeatQty + 1)} className="border rounded-r px-1 sm:px-2">+</button>
                          </div>
                        </CardFooter>
                      </Card>
                      <Card className="text-center">
                        <CardHeader>
                          <CardTitle className="text-[10px] sm:text-sm md:text-base lg:text-base">GPS</CardTitle>
                          <CardDescription className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">
                            Stay on track with our easy-to-use GPS rental services.
                          </CardDescription>
                        </CardHeader>
                        <CardContent>
                          <p className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm">AUD 80.00/Day</p>
                        </CardContent>
                        <CardFooter className="justify-center">
                          <div className="flex">
                            <button onClick={() => setGpsQty(Math.max(0, gpsQty - 1))} className="border rounded-l px-1 sm:px-2">-</button>
                            <span className="border-t border-b px-2 sm:px-3 md:px-4 text-[9px] sm:text-[10px] md:text-xs lg:text-sm">{gpsQty}</span>
                            <button onClick={() => setGpsQty(gpsQty + 1)} className="border rounded-r px-1 sm:px-2">+</button>
                          </div>
                        </CardFooter>
                      </Card>
                    </div>
                  </div>

                  <div className="mb-2 sm:mb-4 md:mb-6">
                    <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2">Note</h2>
                    <Textarea className="w-full border border-gray-300 rounded p-1 sm:p-2 h-10 sm:h-12 md:h-16 text-[10px] sm:text-xs md:text-sm lg:text-base"></Textarea>
                  </div>

                  <div className="flex gap-1 sm:gap-2 md:gap-4 flex justify-end">
                    <Button
                      onClick={handleQuote}
                      style={{ backgroundColor: '#330101' }}
                      className="text-white px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 md:py-2 rounded text-[10px] sm:text-xs md:text-sm lg:text-base"
                    >
                      Download Quote
                    </Button>
                    <Button
                      onClick={handleNextStep}
                      style={{ backgroundColor: '#EBBB4E' }}
                      className="text-white px-2 sm:px-3 md:px-4 py-1 sm:py-1.5 md:py-2 rounded text-[10px] sm:text-xs md:text-sm lg:text-base"
                    >
                      Proceed to Booking
                    </Button>
                  </div>
                </>
              )}
            </div>

            <div className="lg:col-span-1">
              {userRole === 'masterAdmin' && (
                <div className="p-4 sm:p-6 md:p-6 lg:p-6 rounded-lg shadow-sm top-6">
                   <h2 className="text-base sm:text-lg md:text-xl lg:text-xl font-semibold mb-2 sm:mb-4">Rate Type</h2>
                    <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="w-full justify-between border-b text-base">
                            {rateType || 'Rate Type'}
                            <ChevronDown className="w-4 h-4 text-gray-500" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-full">
                          {rateOptions.map((loc) => (
                            <DropdownMenuItem
                              key={loc}
                              onSelect={() => setRateType(loc)}
                              className={rateType === loc ? 'bg-amber-100 font-semibold' : ''}
                            >
                              {loc || 'Rate Type'}
                            </DropdownMenuItem>
                          ))}
                          </DropdownMenuContent>
                          </DropdownMenu>
                           <hr className="my-2 sm:my-4" />
                        
                  <h2 className="text-base sm:text-lg md:text-xl lg:text-xl font-semibold mb-2 sm:mb-4">Summary</h2>
                  <span className="bg-gray-500 text-white text-[10px] sm:text-xs md:text-sm font-semibold px-2 sm:px-3 py-1 sm:py-2 rounded-md">Quote </span>
                  <div className="space-y-2 sm:space-y-3 text-[10px] sm:text-xs md:text-sm">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1 sm:gap-2 mt-2 sm:mt-4">
                          <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Pickup</h2>
                          <button className="text-gray-500 hover:text-blue-600" onClick={() => handleEditClick('pickup')}>
                            <Pencil size={14} sm:size={16} md:size={18} />
                          </button>
                        </div>
                        <p>{`Friday, ${rentalDetails.pickupDate} at ${rentalDetails.pickupTime}`}</p>
                        <p>{rentalDetails.pickupLocation}</p>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1 sm:gap-2">
                          <h3 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Return</h3>
                          <button className="text-gray-500 hover:text-blue-600" onClick={() => handleEditClick('return')}>
                            <Pencil size={14} sm:size={16} md:size={18} />
                          </button>
                        </div>
                        <p>{`Saturday, ${rentalDetails.returnDate} at ${rentalDetails.returnTime}`}</p>
                        <p>{rentalDetails.returnLocation}</p>
                      </div>
                    </div>

                    <hr className="my-2 sm:my-4" />

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Eco Plus Car</p>
                      <div className="flex justify-between">
                        <span>1 Day</span>
                        <span>AUD 38.18</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Protection & Coverages</p>
                      <div className="flex justify-between">
                        <span>Bond Compulsory - 500</span>
                        <span>AUD 500.00</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Equipment & Services</p>
                      <div className="flex justify-between">
                        <span>Child Seat</span>
                        <span>AUD 36.36</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Miscellaneous</p>
                      <div className="flex justify-between">
                        <span>Insurance</span>
                        <span>AUD 13.64</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Included Distance</p>
                      <div className="flex justify-between">
                        <span>200 km</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Discount</p>
                      <div className="flex justify-between">
                        <span>10%</span>
                        <span>AUD 3.00</span>
                      </div>
                    </div>

                    <hr className="my-2 sm:my-4" />

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Total</p>
                      <div className="flex justify-between font-bold text-sm sm:text-base md:text-lg lg:text-lg">
                        <span></span>
                        <span className="text-xl sm:text-2xl md:text-3xl lg:text-3xl font-semibold">AUD 594.00</span>
                      </div>
                    </div>

                    <div>
                      <p className="font-medium text-gray-700 text-[10px] sm:text-xs md:text-sm lg:text-sm">Amount Required</p>
                      <div className="flex justify-between text-xl sm:text-2xl md:text-3xl lg:text-3xl">
                        <span></span>
                        <span>AUD 100.00</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}

              {userRole === 'customer' && (
                <div className="p-4 sm:p-6 md:p-6 lg:p-6 rounded-lg shadow-sm top-6">
                 
                  <h2 className="text-base sm:text-lg md:text-xl lg:text-xl font-semibold mb-2 sm:mb-4">Summary</h2>
                  <span className="bg-gray-500 text-white text-[10px] sm:text-xs md:text-sm font-semibold px-2 sm:px-3 py-1 sm:py-2 rounded-md">Quote </span>
                  <div className="space-y-2 sm:space-y-3 text-[10px] sm:text-xs md:text-sm">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1 sm:gap-2 mt-2 sm:mt-4">
                          <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Pickup</h2>
                          <button className="text-gray-500 hover:text-blue-600" onClick={() => handleEditClick('pickup')}>
                            <Pencil size={14} sm:size={16} md:size={18} />
                          </button>
                        </div>
                        <p>{`Friday, ${rentalDetails.pickupDate} at ${rentalDetails.pickupTime}`}</p>
                        <p>{rentalDetails.pickupLocation}</p>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1 sm:gap-2">
                          <h3 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Return</h3>
                          <button className="text-gray-500 hover:text-blue-600" onClick={() => handleEditClick('return')}>
                            <Pencil size={14} sm:size={16} md:size={18} />
                          </button>
                        </div>
                        <p>{`Saturday, ${rentalDetails.returnDate} at ${rentalDetails.returnTime}`}</p>
                        <p>{rentalDetails.returnLocation}</p>
                      </div>
                    </div>

                    <hr className="my-2 sm:my-4" />

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Eco Plus Car</p>
                      <div className="flex justify-between">
                        <span>1 Day</span>
                        <span>AUD 38.18</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Protection & Coverages</p>
                      <div className="flex justify-between">
                        <span>Bond Compulsory - 500</span>
                        <span>AUD 500.00</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Equipment & Services</p>
                      <div className="flex justify-between">
                        <span>Child Seat</span>
                        <span>AUD 36.36</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Miscellaneous</p>
                      <div className="flex justify-between">
                        <span>Insurance</span>
                        <span>AUD 13.64</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Included Distance</p>
                      <div className="flex justify-between">
                        <span>200 km</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Discount</p>
                      <div className="flex justify-between">
                        <span>10%</span>
                        <span>AUD 3.00</span>
                      </div>
                    </div>

                    <hr className="my-2 sm:my-4" />

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Total</p>
                      <div className="flex justify-between font-bold text-sm sm:text-base md:text-lg lg:text-lg">
                        <span></span>
                        <span className="text-xl sm:text-2xl md:text-3xl lg:text-3xl font-semibold">AUD 594.00</span>
                      </div>
                    </div>

                    <div>
                      <p className="font-medium text-gray-700 text-[10px] sm:text-xs md:text-sm lg:text-sm">Amount Required</p>
                      <div className="flex justify-between text-xl sm:text-2xl md:text-3xl lg:text-3xl">
                        <span></span>
                        <span>AUD 100.00</span>
                      </div>
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {isPopupOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl">
              <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold mb-2 sm:mb-4">Update Date and Times</h2>

              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Pickup Date</Label>
                  <Input
                    type="text"
                    value={rentalDetails.pickupDate}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, pickupDate: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Pickup Time</Label>
                  <Input
                    type="text"
                    value={rentalDetails.pickupTime}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, pickupTime: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Return Date</Label>
                  <Input
                    type="text"
                    value={rentalDetails.returnDate}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, returnDate: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Return Time</Label>
                  <Input
                    type="text"
                    value={rentalDetails.returnTime}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, returnTime: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
              </div>

              <div className="mt-2 sm:mt-4 flex justify-end gap-2 sm:gap-4">
                <Button
                  className="px-2 sm:px-4 py-1 sm:py-2 bg-gray-200 rounded hover:bg-gray-300 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                  onClick={() => setIsPopupOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  className="px-2 sm:px-4 py-1 sm:py-2 bg-[#330101] text-white rounded hover:bg-amber-800 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                  onClick={() => handleSave(
                    rentalDetails.pickupDate,
                    rentalDetails.pickupTime,
                    rentalDetails.returnDate,
                    rentalDetails.returnTime
                  )}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}