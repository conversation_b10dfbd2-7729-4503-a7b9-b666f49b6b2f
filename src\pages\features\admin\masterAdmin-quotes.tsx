import React, { useState } from 'react';
import { Search, CalendarPlus } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';

export function MasterAdminQuotes() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data for the table
  const quotesData = [
    {
      id: '178',
      reservationType: 'Reservations',
      customerName: '<PERSON>',
      customerEmail: '<EMAIL>',
      reservation: '',
      pickupDate: '2025-08-10',
      returnDate: '2025-08-15',
      vehicleClass: 'SUV',
      commissionPartner: 'Partner A',
      clientPaysAtCommission: '$50',
      rackPrice: '$500',
      totalPrice: '$550',
      status: 'Email Sent',
    },
    {
      id: '177',
      reservationType: 'Reservations',
      customerName: 'Jane Smith',
      customerEmail: '<EMAIL>',
      reservation: '',
      pickupDate: '2025-08-12',
      returnDate: '2025-08-18',
      vehicleClass: 'Sedan',
      commissionPartner: 'Partner B',
      clientPaysAtCommission: '$40',
      rackPrice: '$400',
      totalPrice: '$440',
      status: 'Email Sent',
    },
    {
      id: '176',
      reservationType: 'Reservations',
      customerName: 'Alice Brown',
      customerEmail: '<EMAIL>',
      reservation: '27778',
      pickupDate: '2025-08-15',
      returnDate: '2025-08-20',
      vehicleClass: 'Van',
      commissionPartner: 'Partner C',
      clientPaysAtCommission: '$60',
      rackPrice: '$600',
      totalPrice: '$660',
      status: 'Email Sent',
    },
    {
      id: '175',
      reservationType: 'Reservations',
      customerName: 'Bob Wilson',
      customerEmail: '<EMAIL>',
      reservation: '',
      pickupDate: '2025-08-18',
      returnDate: '2025-08-22',
      vehicleClass: 'Truck',
      commissionPartner: 'Partner A',
      clientPaysAtCommission: '$70',
      rackPrice: '$700',
      totalPrice: '$770',
      status: 'Completed',
    },
    {
      id: '174',
      reservationType: 'Reservations',
      customerName: 'Emma Davis',
      customerEmail: '<EMAIL>',
      reservation: '',
      pickupDate: '2025-08-20',
      returnDate: '2025-08-25',
      vehicleClass: 'Convertible',
      commissionPartner: 'Partner D',
      clientPaysAtCommission: '$45',
      rackPrice: '$450',
      totalPrice: '$495',
      status: 'Email Sent',
    },
    {
      id: '173',
      reservationType: 'Reservations',
      customerName: 'Michael Chen',
      customerEmail: '<EMAIL>',
      reservation: '',
      pickupDate: '2025-08-22',
      returnDate: '2025-08-28',
      vehicleClass: 'Hatchback',
      commissionPartner: 'Partner B',
      clientPaysAtCommission: '$30',
      rackPrice: '$300',
      totalPrice: '$330',
      status: 'Completed',
    },
    {
      id: '172',
      reservationType: 'Reservations',
      customerName: 'Sarah Lee',
      customerEmail: '<EMAIL>',
      reservation: '26178',
      pickupDate: '2025-08-25',
      returnDate: '2025-08-30',
      vehicleClass: 'Luxury',
      commissionPartner: 'Partner E',
      clientPaysAtCommission: '$80',
      rackPrice: '$800',
      totalPrice: '$880',
      status: 'Email Sent',
    },
  ];

  // Filter data based on search term and filter status
  const filteredData = quotesData.filter((item) =>
    (searchTerm.trim() === '' ||
      item.id.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.customerName.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.pickupDate.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.returnDate.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.customerEmail.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.vehicleClass.toLowerCase().includes(searchTerm.trim().toLowerCase())) &&
    (filterStatus === 'All' || item.status === filterStatus)
  );

  const handleIdClick = (id: string) => {
    navigate(`/admin/masterAdmin-quotes-view/${id}`);
  };

  // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'email sent':
        return 'bg-gray-500 text-white';
      case 'completed':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-200 text-gray-700';
    }
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <CalendarPlus className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Quotes</h1>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Email Sent">Email Sent</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Table View */}
      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>#</TableHead>
              {/* <TableHead>Reservation Type</TableHead> */}
              <TableHead>Customer Name</TableHead>
              <TableHead>Customer Email</TableHead>
              {/* <TableHead>Reservation</TableHead> */}
              <TableHead>Pickup Date</TableHead>
              <TableHead>Return Date</TableHead>
              <TableHead>Vehicle Class</TableHead>
              <TableHead>Rack Price</TableHead>
              <TableHead>Total Price</TableHead>
              <TableHead>Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={11} className="text-center py-4">
                  No quotes found.
                </TableCell>
              </TableRow>
            ) : (
              currentData.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50 transition-colors">
                  <TableCell>
                    <span
                      className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                      onClick={() => handleIdClick(item.id)}
                    >
                      {item.id}
                    </span>
                  </TableCell>
                  {/* <TableCell>{item.reservationType}</TableCell> */}
                  <TableCell>
                    <span
                      className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                      onClick={() => handleIdClick(item.id)}
                    >
                      {item.customerName}
                    </span>
                  </TableCell>
                  {/* <TableCell>{item.reservation}</TableCell> */}
                  <TableCell>{item.customerEmail}</TableCell>
                  <TableCell>{item.pickupDate}</TableCell>
                  <TableCell>{item.returnDate}</TableCell>
                  <TableCell>{item.vehicleClass}</TableCell>
                  <TableCell>{item.rackPrice}</TableCell>
                  <TableCell>{item.totalPrice}</TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(
                        item.status
                      )}`}
                    >
                      {item.status}
                    </span>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-6"
      />
    </div>
  );
}