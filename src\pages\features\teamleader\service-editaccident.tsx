import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, ChevronDown } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { VehicleData, WorkshopData, ImageData, ServiceTypeOption } from './type/teamleadertype';
import { handleVehicleDataChange, handleWorkshopDataChange, handleMechanicChange, handleImageUpload, handleSubmit, handleCancel } from './hook/useservice-editaccident';
import { initialVehicleData, initialImageData, initialWorkshopData, serviceTypeOptions, branchOptions, mechanicOptions, statusOptions, insuranceStatusOptions, statusOfVehicleOptions } from './common/mockData';

export function ServiceEditAccident() {
  const navigate = useNavigate();
  const { vehicleId } = useParams<{ vehicleId: string }>();
  const { toast } = useToast();

  const [vehicleData, setVehicleData] = useState<VehicleData>(initialVehicleData);
  const [imageData, setImageData] = useState<ImageData>(initialImageData);
  const [workshopData, setWorkshopData] = useState<WorkshopData>(initialWorkshopData);
  const [changeServiceType, setChangeServiceType] = useState(false);
  const [serviceType, setServiceType] = useState('');

  return (
    <div className="min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <Button
          className="bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2"
          size="sm"
          onClick={() => navigate('/teamleader/service-accident')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="flex items-center mb-4 sm:mb-6">
        <h3 className="text-xl sm:text-2xl font-medium text-earth-dark">Vehicle Details</h3>
      </div>

      <div className="bg-white rounded-lg mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.vehicleId}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
              aria-disabled="true"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Vehicle ID
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.model}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
              aria-disabled="true"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Model
            </Label>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.regNo}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
              aria-disabled="true"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Reg No
            </Label>
          </div>
        </div>
        
        <div className="grid grid-cols-1 gap-3 sm:gap-6 md:gap-8">
          <div className="relative">
            <textarea
              value={vehicleData.description}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base h-20 resize-none"
              required
              disabled
              aria-disabled="true"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Description
            </Label>
          </div>
        </div>
      </div>

      {(!changeServiceType || (changeServiceType && serviceType !== 'General Maintenance' && serviceType !== 'Breakdowns')) && (
        <div className="bg-white rounded-lg mb-6">
          <h2 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Upload Images</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8">
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleImageUpload('interiorImages', e.target.files, setImageData, toast)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Interior Images
              </Label>
            </div>
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleImageUpload('exteriorImages', e.target.files, setImageData, toast)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Exterior Images
              </Label>
            </div>
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleImageUpload('leftSideDoors', e.target.files, setImageData, toast)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Left Side Doors
              </Label>
            </div>
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleImageUpload('rightSideDoors', e.target.files, setImageData, toast)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Right Side Doors
              </Label>
            </div>
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleImageUpload('frontSideImages', e.target.files, setImageData, toast)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Front Side Images
              </Label>
            </div>
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleImageUpload('backSideImages', e.target.files, setImageData, toast)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Back Side Images
              </Label>
            </div>
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleImageUpload('sideMirrors', e.target.files, setImageData, toast)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Side Mirrors
              </Label>
            </div>
            <div className="relative">
              <Input
                type="file"
                accept="image/*"
                multiple
                onChange={(e) => handleImageUpload('other', e.target.files, setImageData, toast)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Other
              </Label>
            </div>
          </div>
        </div>
      )}

      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <label className="text-sm sm:text-base">Do You Want to Change Service Type?</label>
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="changeServiceType"
                checked={changeServiceType}
                onChange={() => setChangeServiceType(true)}
              /> Yes
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="changeServiceType"
                checked={!changeServiceType}
                onChange={() => setChangeServiceType(false)}
              /> No
            </label>
          </div>
        </div>
        {changeServiceType && (
          <div className="mb-8">
            <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
              <div className="relative">
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Service Types</Label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                      {serviceType || 'Select Service Type'}
                      <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-full">
                    {serviceTypeOptions.length > 0 ? (
                      serviceTypeOptions.map((type) => (
                        <DropdownMenuItem
                          key={type}
                          onSelect={() => setServiceType(type)}
                          className={serviceType === type ? 'bg-amber-100 font-regular' : ''}
                        >
                          {type || 'Select Service Type'}
                        </DropdownMenuItem>
                      ))
                    ) : (
                      <DropdownMenuItem disabled>No options available</DropdownMenuItem>
                    )}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>

            {serviceType === 'General Maintenance' && (
              <div className="bg-white rounded-lg mb-6 p-4">
                <h2 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Maintenance Details</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                  <div className="relative">
                    <Input
                      type="text"
                      value={vehicleData.maintenanceType || ''}
                      onChange={(e) => handleVehicleDataChange('maintenanceType', e.target.value, setVehicleData)}
                      className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Maintenance Type
                    </Label>
                  </div>
                  <div className="relative">
                    <Input
                      type="text"
                      value={vehicleData.typeInterval || ''}
                      onChange={(e) => handleVehicleDataChange('typeInterval', e.target.value, setVehicleData)}
                      className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Maintenance Type Interval
                    </Label>
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                  <div className="relative">
                    <Input
                      type="text"
                      value={vehicleData.odometerAtDueDate || ''}
                      onChange={(e) => handleVehicleDataChange('odometerAtDueDate', e.target.value, setVehicleData)}
                      className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Odometer at Maintenance Due Date
                    </Label>
                  </div>
                  <div className="relative">
                    <Input
                      type="text"
                      value={vehicleData.currentOdometer || ''}
                      onChange={(e) => handleVehicleDataChange('currentOdometer', e.target.value, setVehicleData)}
                      className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Current Odometer
                    </Label>
                  </div>
                </div>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                  <div className="relative">
                    <Input
                      type="text"
                      value={vehicleData.vehicleCurrentRenter || ''}
                      onChange={(e) => handleVehicleDataChange('vehicleCurrentRenter', e.target.value, setVehicleData)}
                      className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Vehicle Current Renter
                    </Label>
                  </div>
                  <div className="relative">
                    <Input
                      type="text"
                      value={vehicleData.vehicleCurrentLocation || ''}
                      onChange={(e) => handleVehicleDataChange('vehicleCurrentLocation', e.target.value, setVehicleData)}
                      className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Vehicle Current Location
                    </Label>
                  </div>
                </div>
                <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                  <div className="relative">
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Vehicle Status</Label>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                          {vehicleData.statusOfVehicle || 'Select Vehicle Status'}
                          <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-full">
                        {statusOfVehicleOptions.map((statusOfVehicle) => (
                          <DropdownMenuItem
                            key={statusOfVehicle}
                            onSelect={() => handleVehicleDataChange('statusOfVehicle', statusOfVehicle, setVehicleData)}
                            className={vehicleData.statusOfVehicle === statusOfVehicle ? 'bg-amber-100 font-regular' : ''}
                          >
                            {statusOfVehicle || 'Select Vehicle Status'}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            )}
            {serviceType === 'Breakdowns' && (
              <div className="bg-white rounded-lg mb-6 p-4">
                <h2 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Breakdown Details</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                  <div className="relative">
                    <Input
                      type="text"
                      value={vehicleData.incidentDate || ''}
                      onChange={(e) => handleVehicleDataChange('incidentDate', e.target.value, setVehicleData)}
                      className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Incident Date
                    </Label>
                  </div>
                </div>
              </div>
            )}
            {serviceType === 'Damage' && (
              <div className="bg-white rounded-lg mb-6 p-4">
                <h3 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Damage Details</h3>
              </div>
            )}
          </div>
        )}
      </div>

      <div className="bg-white rounded-lg mb-6">
        <h3 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Workshop Details</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Branch</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {workshopData.branch || 'Select Branch'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {branchOptions.length > 0 ? (
                  branchOptions.map((branch) => (
                    <DropdownMenuItem
                      key={branch}
                      onSelect={() => handleWorkshopDataChange('branch', branch, setWorkshopData)}
                      className={workshopData.branch === branch ? 'bg-amber-100 font-regular' : ''}
                    >
                      {branch || 'Select Branch'}
                    </DropdownMenuItem>
                  ))
                ) : (
                  <DropdownMenuItem disabled>No options available</DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Mechanic Assigned</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {workshopData.mechanicAssigned.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {workshopData.mechanicAssigned.map((mechanic) => (
                        <Badge key={mechanic} variant="secondary">
                          {mechanic}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    'Select Mechanic'
                  )}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {mechanicOptions.length > 0 ? (
                  mechanicOptions.map((mechanic) => (
                    <DropdownMenuItem
                      key={mechanic}
                      onSelect={() => handleMechanicChange(mechanic, workshopData, setWorkshopData)}
                      className={workshopData.mechanicAssigned.includes(mechanic) ? 'bg-amber-100 font-regular' : ''}
                    >
                      {mechanic || 'Select Mechanic'}
                    </DropdownMenuItem>
                  ))
                ) : (
                  <DropdownMenuItem disabled>No options available</DropdownMenuItem>
                )}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {(!changeServiceType || (changeServiceType && serviceType !== 'General Maintenance' && serviceType !== 'Breakdowns')) && (
          <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
            <div className="relative">
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Insurance Status</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                    {vehicleData.insuranceStatus || 'Select Status'}
                    <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  {insuranceStatusOptions.map((insuranceStatus) => (
                    <DropdownMenuItem
                      key={insuranceStatus}
                      onSelect={() => handleVehicleDataChange('insuranceStatus', insuranceStatus, setVehicleData)}
                      className={vehicleData.insuranceStatus === insuranceStatus ? 'bg-amber-100 font-regular' : ''}
                    >
                      {insuranceStatus || 'Select Status'}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
            <div className="relative">
              <Input
                type="text"
                value={vehicleData.insuranceClaimNumber || ''}
                onChange={(e) => handleVehicleDataChange('insuranceClaimNumber', e.target.value, setVehicleData)}
                className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
              />
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Insurance Claim Number
              </Label>
            </div>
          </div>
        )}

        <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="date"
              value={vehicleData.estimatedEndDate}
              onChange={(e) => handleVehicleDataChange('estimatedEndDate', e.target.value, setVehicleData)}
              className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Estimated End Date
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={vehicleData.actualEndDate}
              onChange={(e) => handleVehicleDataChange('actualEndDate', e.target.value, setVehicleData)}
              className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Actual End Date
            </Label>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Status</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {vehicleData.status || 'Select Status'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {statusOptions.map((status) => (
                  <DropdownMenuItem
                    key={status}
                    onSelect={() => handleVehicleDataChange('status', status, setVehicleData)}
                    className={vehicleData.status === status ? 'bg-amber-100 font-regular' : ''}
                  >
                    {status || 'Select Status'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="grid grid-cols-1 gap-3 sm:gap-6 md:gap-8 mt-4">
          <div className="relative">
            <textarea
              value={workshopData.comment}
              onChange={(e) => handleWorkshopDataChange('comment', e.target.value, setWorkshopData)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base h-20 resize-none"
              placeholder="Type here..."
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Comment
            </Label>
          </div>
        </div>
      </div>

      <div className="flex justify-end gap-4">
        <Button
          className="bg-[#330101] text-white px-4 py-2 text-sm sm:text-base"
          onClick={() => handleSubmit(vehicleData, workshopData, serviceType, imageData, navigate, toast)}
        >
          Update
        </Button>
        <Button
          variant="outline"
          className="border-gray-500 text-gray-700 px-4 py-2 text-sm sm:text-base"
          onClick={() => handleCancel(initialVehicleData, initialImageData, initialWorkshopData, setVehicleData, setImageData, setWorkshopData, setChangeServiceType, setServiceType, navigate)}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
}