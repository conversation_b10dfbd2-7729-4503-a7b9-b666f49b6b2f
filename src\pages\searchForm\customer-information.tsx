import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom'; 
import { ChevronDown, Pencil, LifeBuoy, Snowflake, Music, Power, Box } from 'lucide-react';
import SearchHeader from '@/components/layout/SearchHeader';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Textarea } from '@/components/ui/textarea';

const useAuth = () => {
  const [role] = useState<'admin' | 'customer'>('admin');
  return { role };
};

interface CustomerFormData {
  cooperateCustomer: string;
  customerName: string;
  firstName: string;
  lastName: string;
  address: string;
  postCode: string;
  country: string;
  email: string;
  phone: string;
  birthday: {
    day: string;
    month: string;
    year: string;
  };
  companyName: string;
  emergencyContactName: string;
  phoneType: string;
  emergencyContactNumber: string;
  dlNumber: string;
  issueCountry: string;
  issueDate: string;
  expiryDate: string;
  conditions: string;
  comments: string;
}

interface Step {
  number: number;
  label: string;
  active: boolean;
}

interface RentalDetails {
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  pickupLocation: string;
  returnLocation: string;
  rateType?: string;
}

interface VehicleFeature {
  name: string;
  icon: React.ComponentType<any>;
}

interface VehicleClass {
  name: string;
  features: VehicleFeature[];
  pricePerDay: number;
  totalPrice: number;
  days: number;
}

interface PricingBreakdown {
  vehiclePrice: number;
  bondCompulsory: number;
  childSeat: number;
  insurance: number;
  discount: number;
  total: number;
  amountRequired: number;
}

export function CustomerInformationPage() {
  const { role } = useAuth();
  const navigate = useNavigate(); 
  const handleNextStep = (): void => {
    navigate('/search/confirm');
  };

  const [formData, setFormData] = useState<CustomerFormData>({
    cooperateCustomer: 'Yes',
    customerName: '',
    firstName: '',
    lastName: '',
    address: '',
    postCode: '',
    country: '',
    email: '',
    phone: '',
    birthday: { day: '', month: '', year: '' },
    companyName: '',
    emergencyContactName: '',
    phoneType: '',
    emergencyContactNumber: '',
    dlNumber: '',
    issueCountry: '',
    issueDate: '',
    expiryDate: '',
    conditions: '',
    comments: ''
  });

  const [rentalDetails, setRentalDetails] = useState<RentalDetails>({
    pickupDate: '13/06/2025',
    pickupTime: '11:30',
    returnDate: '14/06/2025',
    returnTime: '10:30',
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    rateType: 'New Year - 2021',
  });

  const [isPopupOpen, setIsPopupOpen] = useState<boolean>(false);
  const [showNewCustomer, setShowNewCustomer] = useState<boolean>(false);
  const [frontViewFile, setFrontViewFile] = useState<File | null>(null);
  const [backViewFile, setBackViewFile] = useState<File | null>(null);
  const [outstandingBalance, setOutstandingBalance] = useState<number>(0);

  // Simulated customer data for dropdown and auto-fill
  const customerDatabase: Record<string, CustomerFormData & { balance: number }> = {
    Pradeep: {
      cooperateCustomer: 'Yes',
      customerName: 'Pradeep',
      firstName: 'Pradeep',
      lastName: 'Testing',
      address: '2/85 Hume Hwy, Somerton VIC',
      postCode: '3062',
      country: 'Australia',
      email: '<EMAIL>',
      phone: '0421 234 567',
      birthday: { day: '02', month: '01', year: '1960' },
      companyName: 'Lion Car Rentals',
      emergencyContactName: 'Smith Peter',
      phoneType: 'Mobile',
      emergencyContactNumber: '0421 234 567',
      dlNumber: '12345678',
      issueCountry: 'Australia',
      issueDate: '02/02/2021',
      expiryDate: '02/02/2026',
      conditions: 'Light Vehicles',
      comments: '',
      balance: 0,
    },
    John: {
      cooperateCustomer: 'No',
      customerName: 'John',
      firstName: 'John',
      lastName: 'Doe',
      address: '123 Main St, Melbourne VIC',
      postCode: '3000',
      country: 'Australia',
      email: '<EMAIL>',
      phone: '0423 456 789',
      birthday: { day: '15', month: '06', year: '1985' },
      companyName: '',
      emergencyContactName: 'Jane Doe',
      phoneType: 'Mobile',
      emergencyContactNumber: '0423 456 790',
      dlNumber: '87654321',
      issueCountry: 'Australia',
      issueDate: '01/01/2019',
      expiryDate: '01/01/2024',
      conditions: 'Heavy Vehicles',
      comments: '',
      balance: 150.75,
    },
    Sarah: {
      cooperateCustomer: 'Yes',
      customerName: 'Sarah',
      firstName: 'Sarah',
      lastName: 'Smith',
      address: '45 Queen St, Sydney NSW',
      postCode: '2000',
      country: 'Australia',
      email: '<EMAIL>',
      phone: '0425 678 901',
      birthday: { day: '10', month: '03', year: '1990' },
      companyName: 'Smith Rentals',
      emergencyContactName: 'Tom Smith',
      phoneType: 'Mobile',
      emergencyContactNumber: '0425 678 902',
      dlNumber: '98765432',
      issueCountry: 'Australia',
      issueDate: '03/03/2020',
      expiryDate: '03/03/2025',
      conditions: 'Light Vehicles',
      comments: '',
      balance: 320.50,
    },
  };

  // Pre-fill form for customers or auto-fill for admins when selecting a customer
  useEffect(() => {
    if (role === 'customer') {
      // Simulate fetching logged-in customer data (e.g., Pradeep)
      const customerData = customerDatabase['Pradeep'];
      if (customerData) {
        setFormData({ ...customerData, comments: '' });
        setOutstandingBalance(customerData.balance);
      }
    } else if (role === 'admin' && formData.customerName && !showNewCustomer) {
      const selectedCustomer = customerDatabase[formData.customerName];
      if (selectedCustomer) {
        setFormData({ ...selectedCustomer, comments: formData.comments });
        setOutstandingBalance(selectedCustomer.balance);
      }
    }
  }, [formData.customerName, role, showNewCustomer]);

  const handleFrontViewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFrontViewFile(file);
  };

  const handleBackViewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setBackViewFile(file);
  };

  const steps: Step[] = [
    { number: 1, label: 'Dates', active: false },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: false },
    { number: 4, label: 'Customer', active: true },
    { number: 5, label: 'Confirm', active: false },
    { number: 6, label: 'Payment', active: false },
    { number: 7, label: 'Receipt', active: false },
  ];

  const vehicleClass: VehicleClass = {
    name: 'Eco Plus Car',
    features: [
      { name: 'Automatic Transmission', icon: LifeBuoy },
      { name: 'Air Conditioning', icon: Snowflake },
      { name: 'Petrol CD Player', icon: Music },
      { name: 'Power Steering', icon: Power },
      { name: '5+ Doors', icon: Box }
    ],
    pricePerDay: 42.00,
    totalPrice: 42.00,
    days: 1
  };

  const pricing: PricingBreakdown = {
    vehiclePrice: 38.18,
    bondCompulsory: 500.00,
    childSeat: 36.36,
    insurance: 13.64,
    discount: 3.00,
    total: 594.00,
    amountRequired: 100.00
  };

  const handleInputChange = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleBirthdayChange = (field: keyof CustomerFormData['birthday'], value: string) => {
    setFormData(prev => ({
      ...prev,
      birthday: { ...prev.birthday, [field]: value }
    }));
  };

  const handleEditClick = (type: 'pickup' | 'return') => {
    setIsPopupOpen(true);
  };

  const handleSave = (newPickupDate: string, newPickupTime: string, newReturnDate: string, newReturnTime: string) => {
    setRentalDetails({
      ...rentalDetails,
      pickupDate: newPickupDate,
      pickupTime: newPickupTime,
      returnDate: newReturnDate,
      returnTime: newReturnTime
    });
    setIsPopupOpen(false);
  };

  const handleCreateNewCustomer = () => {
    setShowNewCustomer(true);
    setFormData({
      cooperateCustomer: '',
      customerName: '',
      firstName: '',
      lastName: '',
      address: '',
      postCode: '',
      country: '',
      email: '',
      phone: '',
      birthday: { day: '', month: '', year: '' },
      companyName: '',
      emergencyContactName: '',
      phoneType: '',
      emergencyContactNumber: '',
      dlNumber: '',
      issueCountry: '',
      issueDate: '',
      expiryDate: '',
      conditions: '',
      comments: ''
    });
    setOutstandingBalance(0);
  };

  const handleSaveNewCustomer = () => {
    console.log('Saving new customer:', formData);
    setShowNewCustomer(false);
    customerDatabase[formData.customerName] = { ...formData, balance: 0 };
  };

  const handleCancelNewCustomer = () => {
    setShowNewCustomer(false);
    setFormData({
      cooperateCustomer: 'Yes',
      customerName: '',
      firstName: '',
      lastName: '',
      address: '',
      postCode: '',
      country: '',
      email: '',
      phone: '',
      birthday: { day: '', month: '', year: '' },
      companyName: '',
      emergencyContactName: '',
      phoneType: '',
      emergencyContactNumber: '',
      dlNumber: '',
      issueCountry: '',
      issueDate: '',
      expiryDate: '',
      conditions: '',
      comments: ''
    });
    setOutstandingBalance(0);
  };

  const activeIndex = steps.findIndex(step => step.active);

  const getVisibleSteps = () => {
    const visible = [activeIndex];
    if (activeIndex - 1 >= 0) visible.unshift(activeIndex - 1);
    if (activeIndex - 2 >= 0) visible.unshift(activeIndex - 2);
    if (activeIndex + 1 < steps.length) visible.push(activeIndex + 1);
    return visible.sort((a, b) => a - b);
  };

  const visibleStepIndices = getVisibleSteps();

  const conditionsOptions: string[] = ['', 'Light Vehicles', 'Heavy Vehicles', 'Motorcycles'];
  const customerOptions: string[] = ['', ...Object.keys(customerDatabase)];
  const countryOptions: string[] = ['', 'Australia', 'New Zealand', 'United Kingdom', 'United States'];
  const phoneOptions: string[] = ['', 'Mobile', 'Land'];

  return (
    <div className="min-h-screen">
      <SearchHeader />
      <div className="flex flex-col md:flex-row px-4 sm:px-8 md:px-16 lg:px-24 xl:px-32">
        
        <div className="w-full md:w-3/4 p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">

          <div style={{ marginLeft: '32px', marginRight: '32px' }}>
          <div className="p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
            <div className="flex items-center justify-center mb-2 sm:mb-4 md:mb-6 lg:mb-8 gap-1 sm:gap-2 md:gap-3 lg:gap-4">
              {steps.map((step, index) => {
                const isVisibleOnXs = visibleStepIndices.includes(index);
                const isVisibleOnSm = visibleStepIndices.includes(index);
                const isVisibleOnMd = visibleStepIndices.includes(index) || index <= activeIndex + 3;
                const isVisibleOnLg = true;

                return (
                  <div
                    key={step.number}
                    className={`flex items-center ${!isVisibleOnXs ? 'hidden' : ''} ${isVisibleOnSm ? 'sm:flex' : 'sm:hidden'} ${isVisibleOnMd ? 'md:flex' : 'md:hidden'} ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                  >
                    <div className="flex flex-col items-center">
                      <div
                        className={`w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-full flex items-center justify-center text-xs sm:text-sm md:text-base lg:text-base font-semibold ${
                          step.active ? 'bg-amber-500 text-white' : 'bg-gray-300 text-gray-600'
                        }`}
                      >
                        {step.number}
                      </div>
                      <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm mt-0.5 sm:mt-1">{step.label}</span>
                    </div>
                    {index < steps.length - 1 && (
                      <div
                        className={`w-3 sm:w-4 md:w-6 lg:w-8 h-0.5 bg-gray-300 mx-0.5 sm:mx-1 md:mx-2 lg:mx-3 mt-[-12px] sm:mt-[-16px] md:mt-[-18px] lg:mt-[-20px] ${
                          !isVisibleOnXs || !visibleStepIndices.includes(index + 1) ? 'hidden' : ''
                        } ${isVisibleOnSm && visibleStepIndices.includes(index + 1) ? 'sm:flex' : 'sm:hidden'} ${
                          isVisibleOnMd && (isVisibleOnMd || index + 1 <= activeIndex + 3) ? 'md:flex' : 'md:hidden'
                        } ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                      ></div>
                    )}
                  </div>
                );
              })}
            </div>
          </div>
      </div>

          <div className="bg-blue-100 p-4 mb-4 text-blue-800">
            If you want to add additional drivers please inform the reception at the pick up time
            (Additional driver should be present to pick up the vehicle bearing a valid driver licence).
          </div>

          <div className="bg-white rounded-lg shadow-sm p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
            <div className="flex justify-between mb-2">
              <h2 className="text-base sm:text-lg md:text-xl lg:text-2xl font-semibold mb-2 sm:mb-4 md:mb-6">Customer Information</h2>
              {role === 'admin' && formData.customerName && (
                <div>Outstanding Balance: AU${outstandingBalance.toFixed(2)}</div>
              )}
            </div>

            {/* Customer Dropdown for Admin */}
            {role === 'admin' && (
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
                <div className="relative">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button 
                        variant="outline" 
                        className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base justify-between flex items-center"
                        disabled={showNewCustomer}
                      >
                        {formData.customerName || 'Select Customer'}
                        <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full">
                      {customerOptions.map((customerName) => (
                        <DropdownMenuItem
                          key={customerName}
                          onSelect={() => handleInputChange('customerName', customerName)}
                          className={formData.customerName === customerName ? 'bg-amber-100 font-semibold' : ''}
                        >
                          {customerName || 'Select Customer'}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Customer</Label>
                </div>

                <>
                  {showNewCustomer ? (
                    <Button
                      onClick={handleCancelNewCustomer}
                      className="w-full bg-gray-300 hover:bg-gray-400 text-[10px] sm:text-xs md:text-sm lg:text-base"
                    >
                      Cancel
                    </Button>
                  ) : (
                    <Button
                      onClick={handleCreateNewCustomer}
                      className="w-full bg-green-600 text-white hover:bg-green-700 text-[10px] sm:text-xs md:text-sm lg:text-base"
                    >
                      or create a new one
                    </Button>
                  )}
                </>
              </div>
            )}

            {/* Customer Form - Shown for customers or when admin selects a customer or creates a new one */}
            {(role === 'customer' || (role === 'admin' && (formData.customerName || showNewCustomer))) && (
              <>
                {/* Cooperate Customer */}
                <div className="mb-4 sm:mb-6 md:mb-8">
                  <Label className="block sm:text-xs md:text-sm lg:text-base text-gray-700 mb-1 sm:mb-2">
                    Cooperate Customer
                  </Label>
                  <div className="flex gap-2 sm:gap-4">
                    <Label className="flex items-center">
                      <Input
                        type="radio"
                        name="cooperateCustomer"
                        value="Yes"
                        checked={formData.cooperateCustomer === 'Yes'}
                        onChange={(e) => handleInputChange('cooperateCustomer', e.target.value)}
                        className="mr-1 sm:mr-2"
                        disabled={role === 'customer'}
                      />
                      Yes
                    </Label>
                    <Label className="flex items-center">
                      <Input
                        type="radio"
                        name="cooperateCustomer"
                        value="No"
                        checked={formData.cooperateCustomer === 'No'}
                        onChange={(e) => handleInputChange('cooperateCustomer', e.target.value)}
                        className="mr-1 sm:mr-2"
                        disabled={role === 'customer'}
                      />
                      No
                    </Label>
                  </div>
                </div>

                {/* Name Fields */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
                  <div className="relative">
                    <Input
                      type="text"
                      value={formData.firstName}
                      onChange={(e) => handleInputChange('firstName', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      required
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      First Name
                    </Label>
                  </div>
                  <div className="relative">
                    <Input
                      type="text"
                      value={formData.lastName}
                      onChange={(e) => handleInputChange('lastName', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      required
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Last Name
                    </Label>
                  </div>
                </div>

                {/* Address */}
                <div className="mb-4 sm:mb-6 md:mb-8 relative">
                  <Input
                    type="text"
                    value={formData.address}
                    onChange={(e) => handleInputChange('address', e.target.value)}
                    className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                    required
                    disabled={role === 'customer'}
                  />
                  <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                    Address
                  </Label>
                </div>

                {/* Post Code and Country */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
                  <div className="relative">
                    <Input
                      type="text"
                      value={formData.postCode}
                      onChange={(e) => handleInputChange('postCode', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Post Code
                    </Label>
                  </div>
                  <div className="relative">
                    <select
                      value={formData.country}
                      onChange={(e) => handleInputChange('country', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md appearance-none bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      disabled={role === 'customer'}
                    >
                      {countryOptions.map((country) => (
                        <option key={country} value={country}>{country || 'Select Country'}</option>
                      ))}
                    </select>
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Country
                    </Label>
                  </div>
                </div>

                {/* Email */}
                <div className="mb-4 sm:mb-6 md:mb-8 relative">
                  <Input
                    type="email"
                    value={formData.email}
                    onChange={(e) => handleInputChange('email', e.target.value)}
                    className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                    required
                    disabled={role === 'customer'}
                  />
                  <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                    Email
                  </Label>
                </div>

                {/* Phone and Birthday */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
                  <div className="relative">
                    <Input
                      type="tel"
                      value={formData.phone}
                      onChange={(e) => handleInputChange('phone', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      required
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Phone Number
                    </Label>
                  </div>
                  <div className="relative">
                    <Input
                      type="date"
                      value={formData.birthday.year && formData.birthday.month && formData.birthday.day 
                        ? `${formData.birthday.year}-${formData.birthday.month.padStart(2, '0')}-${formData.birthday.day.padStart(2, '0')}`
                        : ''
                      }
                      onChange={(e) => {
                        const [year, month, day] = e.target.value.split('-');
                        setFormData(prev => ({
                          ...prev,
                          birthday: { day, month, year }
                        }));
                      }}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      required
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Birthday
                    </Label>
                  </div>
                </div>

                {/* Company Name */}
                <div className="mb-4 sm:mb-6 md:mb-8 relative">
                  <Input
                    type="text"
                    value={formData.companyName}
                    onChange={(e) => handleInputChange('companyName', e.target.value)}
                    className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                    disabled={role === 'customer'}
                  />
                  <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                    Company Name
                  </Label>
                </div>

                {/* Emergency Contact Details */}
                <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Emergency Contact Details</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
                  <div className="relative">
                    <Input
                      type="text"
                      value={formData.emergencyContactName}
                      onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Emergency Contact Person's Name
                    </Label>
                  </div>
                  <div className="relative">
                    <div className="flex">
                      <div className="relative">
                        <select
                          value={formData.phoneType}
                          onChange={(e) => handleInputChange('phoneType', e.target.value)}
                          className="p-1 sm:p-2 md:p-3 border border-gray-300 rounded-l-md appearance-none bg-white pr-6 sm:pr-8 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                          disabled={role === 'customer'}
                        >
                          {phoneOptions.map((option) => (
                            <option key={option} value={option}>{option || 'Select Type'}</option>
                          ))}
                        </select>
                      </div>
                      <div className="flex items-center px-2 sm:px-3 border-t border-b border-gray-300 bg-gray-50">
                        <span className="text-[10px] sm:text-xs md:text-sm lg:text-base font-medium">🇦🇺</span>
                      </div>
                      <Input
                        type="tel"
                        value={formData.emergencyContactNumber}
                        onChange={(e) => handleInputChange('emergencyContactNumber', e.target.value)}
                        className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-r-md border-l-0 focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                        disabled={role === 'customer'}
                      />
                    </div>
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Emergency Contact Number
                    </Label>
                  </div>
                </div>

                {/* Driver's License */}
                <h3 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Driver's License</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
                  
                  <div className="relative">
                    <Input
                      type="text"
                      value={formData.dlNumber}
                      onChange={(e) => handleInputChange('dlNumber', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      DL Number
                    </Label>
                  </div>

                  <div className="relative">
                    <select
                      value={formData.issueCountry}
                      onChange={(e) => handleInputChange('issueCountry', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md appearance-none bg-white focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      disabled={role === 'customer'}
                    >
                      {countryOptions.map((country) => (
                        <option key={country} value={country}>{country || 'Select Country'}</option>
                      ))}
                    </select>
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Issue Country
                    </Label>
                  </div>
                </div>

                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">

                  <div className="relative">
                    <Input
                      type="date"
                      value={formData.issueDate}
                      onChange={(e) => handleInputChange('issueDate', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Issue Date
                    </Label>
                  </div>
                  
                  <div className="relative">
                    <Input
                      type="date"
                      value={formData.expiryDate}
                      onChange={(e) => handleInputChange('expiryDate', e.target.value)}
                      className="w-full p-1 sm:p-2 md:p-3 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      disabled={role === 'customer'}
                    />
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Expiry Date
                    </Label>
                  </div>
                </div>

                <div className="mb-4 sm:mb-6 md:mb-8 relative">
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">Conditions</Label>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between border-b text-[10px] sm:text-xs md:text-sm lg:text-base">
                        {formData.conditions || 'Select Conditions'}
                        <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full">
                      {conditionsOptions.map((condition) => (
                        <DropdownMenuItem
                          key={condition}
                          onSelect={() => handleInputChange('conditions', condition)}
                          className={formData.conditions === condition ? 'bg-amber-100 font-semibold' : ''}
                        >
                          {condition || 'Select Conditions'}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>

                {/* Upload Files */}
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
                  <div className="relative">
                    <div className="flex">
                      <Input
                        type="text"
                        value={frontViewFile?.name || 'No file selected'}
                        readOnly
                        className="flex-1 p-2 sm:p-3 border border-gray-300 rounded-l-md bg-gray-50 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="flex items-center px-3 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                        <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Choose File</span>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleFrontViewChange}
                          className="hidden"
                        />
                      </Label>
                    </div>
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Upload Front View
                    </Label>
                  </div>

                  <div className="relative">
                    <div className="flex">
                      <Input
                        type="text"
                        value={backViewFile?.name || 'No file selected'}
                        readOnly
                        className="flex-1 p-2 sm:p-3 border border-gray-300 rounded-l-md bg-gray-50 text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="flex items-center px-3 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                        <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Choose File</span>
                        <Input
                          type="file"
                          accept="image/*"
                          onChange={handleBackViewChange}
                          className="hidden"
                        />
                      </Label>
                    </div>
                    <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                      Upload Back View
                    </Label>
                  </div>
                </div>

                {/* Comments */}
                <div className="mb-4 sm:mb-6 md:mb-8 relative">
                  <Textarea
                    value={formData.comments}
                    onChange={(e) => handleInputChange('comments', e.target.value)}
                    rows={4}
                    className="p-1 h-28 sm:p-2 md:p-3 resize-none focus:outline-none focus:ring-0 focus:border-none  sm:text-xs md:text-sm lg:text-base"
                    placeholder="Add any additional comments here..."
                  />
                  <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
                    Comments
                  </Label>
                </div>

                <div className="flex justify-between gap-2 sm:gap-4">
                  {role === 'admin' && showNewCustomer ? (
                    <Button 
                      onClick={handleSaveNewCustomer}
                      className="px-2 sm:px-3 md:px-4 py-1 sm:py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors text-[10px] sm:text-xs md:text-sm lg:text-base"
                    >
                      Save New Customer
                    </Button>
                  ) : (
                    <Button 
                      className="bg-yellow-600 text-white px-4 rounded w-auto xs:w-auto sm:w-auto md:w-auto lg:w-auto xl:w-auto"
                    >
                      Save Comment
                    </Button>
                  )}
                  <Button 
                    onClick={handleNextStep}
                    className="bg-[#330101] text-white hover:bg-amber-800 px-2 sm:px-3 md:px-5 py-1 sm:py-2 sm:text-xs md:text-sm lg:text-base"
                  >
                    Next Step
                  </Button>
                </div>
              </>
            )}
          </div>
        </div>

        <div className="w-full md:w-1/4 p-4 sm:p-6 my-40">
          <div className="rounded-lg shadow-sm">
            {/* Rate Type - Visible Only for Admin */}
            {role === 'admin' && (
              <div className="mb-4">
                <label className="block text-sm sm:text-base md:text-lg lg:text-lg font-semibold mb-2">Rate Type</label>
                <select
                  value={rentalDetails.rateType || ''}
                  onChange={(e) => setRentalDetails({ ...rentalDetails, rateType: e.target.value })}
                  className="w-auto p-2 border rounded text-sm sm:text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="New Year - 2021">New Year - 2021</option>
                  <option value="Long Term Rental">Long Term Rental</option>
                </select>
              </div>
            )}

            {/* Summary Sidebar */}
            <h2 className="text-base sm:text-lg md:text-xl lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Summary</h2>
            <span className="bg-gray-500 text-white text-[10px] sm:text-xs md:text-sm font-semibold px-2 sm:px-3 py-1 sm:py-2 rounded-md">Quote </span>
            <div className="space-y-2 sm:space-y-3 text-[10px] sm:text-xs md:text-sm">
              <div className="flex justify-between items-center">
                <div>
                  <div className="flex items-center gap-1 sm:gap-2 mt-2 sm:mt-4">
                    <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Pickup</h2>
                    <Button
                      variant="ghost"
                      onClick={() => handleEditClick('pickup')}
                      className="p-1 hover:bg-transparent"
                    >
                      <Pencil 
                        size={14}
                        className="text-gray-500 hover:text-blue-600" 
                      />
                    </Button>
                  </div>
                  <p>{`Friday, ${rentalDetails.pickupDate} at ${rentalDetails.pickupTime}`}</p>
                  <p>{rentalDetails.pickupLocation}</p>
                </div>
              </div>

              <div className="flex justify-between items-center">
                <div>
                  <div className="flex items-center gap-1 sm:gap-2">
                    <h3 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Return</h3>
                    <Button
                      variant="ghost"
                      onClick={() => handleEditClick('return')}
                      className='p-1 sm:p-0 hover:bg-transparent'
                    >
                      <Pencil 
                        size={14} 
                        className="text-gray-500 sm:hover:text-blue-600"
                      />
                    </Button>
                  </div>
                  <p>{`Saturday, ${rentalDetails.returnDate} at ${rentalDetails.returnTime}`}</p>
                  <p>{rentalDetails.returnLocation}</p>
                </div>
              </div>

              <hr className="my-2 sm:my-4" />

              <div>
                <p className="text-sm sm:text-base md:text-lg lg:text-sm font-semibold">{vehicleClass.name}</p>
                <div className="flex justify-between">
                  <span>{vehicleClass.days} Day</span>
                  <span>AUD {pricing.vehiclePrice.toFixed(2)}</span>
                </div>
              </div>

              <div>
                <p className="text-sm sm:text-base md:text-lg lg:text-sm font-semibold">Protection & Coverages</p>
                <div className="flex justify-between">
                  <span>Bond Compulsory - 500</span>
                  <span>AUD {pricing.bondCompulsory.toFixed(2)}</span>
                </div>
              </div>

              <div>
                <p className="text-sm sm:text-base md:text-lg lg:text-sm font-semibold">Equipment & Services</p>
                <div className="flex justify-between">
                  <span>Child Seat</span>
                  <span>AUD {pricing.childSeat.toFixed(2)}</span>
                </div>
              </div>

              <div>
                <p className="text-sm sm:text-base md:text-lg lg:text-sm font-semibold">Miscellaneous</p>
                <div className="flex justify-between">
                  <span>Insurance</span>
                  <span>AUD {pricing.insurance.toFixed(2)}</span>
                </div>
              </div>

              <div>
                <p className="text-sm sm:text-base md:text-lg lg:text-sm font-semibold">Included Distance</p>
                <div className="flex justify-between">
                  <span>200 km</span>
                </div>
              </div>

              <div>
                <p className="text-sm sm:text-base md:text-lg lg:text-sm font-semibold">Discount</p>
                <div className="flex justify-between">
                  <span>10%</span>
                  <span>AUD {pricing.discount.toFixed(2)}</span>
                </div>
              </div>

              <hr className="my-2 sm:my-4" />

              <div>
                <p className="text-sm sm:text-base md:text-lg lg:text-sm font-semibold">Total</p>
                <div className="flex justify-between font-bold text-sm sm:text-base md:text-lg lg:text-lg">
                  <span></span>
                  <span className="text-xl sm:text-2xl md:text-3xl lg:text-3xl font-semibold">
                    AUD {pricing.total.toFixed(2)}
                  </span>
                </div>
              </div>

              <div>
                <p className="font-medium text-gray-700 text-[10px] sm:text-xs md:text-sm lg:text-sm">Amount Required</p>
                <div className="flex justify-between text-xl sm:text-2xl md:text-3xl lg:text-3xl">
                  <span></span>
                  <span>AUD {pricing.amountRequired.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {isPopupOpen && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-sm sm:max-w-md md:max-w-lg">
            <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-2 sm:mb-4 md:mb-6">Update Dates and Times</h2>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6">
              <div>
                <Label className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Pickup Date</Label>
                <Input
                  type="date"
                  value={rentalDetails.pickupDate}
                  onChange={(e) => setRentalDetails({ ...rentalDetails, pickupDate: e.target.value })}
                  className="mt-1 w-full p-1 sm:p-2 md:p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                />
              </div>
              <div>
                <Label className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Pickup Time</Label>
                <Input
                  type="time"
                  value={rentalDetails.pickupTime}
                  onChange={(e) => setRentalDetails({ ...rentalDetails, pickupTime: e.target.value })}
                  className="mt-1 w-full p-1 sm:p-2 md:p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                />
              </div>
              <div>
                <Label className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Return Date</Label>
                <Input
                  type="date"
                  value={rentalDetails.returnDate}
                  onChange={(e) => setRentalDetails({ ...rentalDetails, returnDate: e.target.value })}
                  className="mt-1 w-full p-1 sm:p-2 md:p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                />
              </div>
              <div>
                <Label className="text-[10px] sm:text-xs md:text-sm lg:text-sm">Return Time</Label>
                <Input
                  type="time"
                  value={rentalDetails.returnTime}
                  onChange={(e) => setRentalDetails({ ...rentalDetails, returnTime: e.target.value })}
                  className="mt-1 w-full p-1 sm:p-2 md:p-3 border rounded-md focus:ring-2 focus:ring-blue-500 focus:border-blue-500 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                />
              </div>
            </div>

            <div className="flex justify-end mt-2 sm:mt-4 md:mt-6 gap-2 sm:gap-4">
              <Button
                className="px-2 sm:px-3 md:px-4 py-1 sm:py-2 bg-gray-200 hover:bg-gray-300 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                onClick={() => setIsPopupOpen(false)}
              >
                Cancel
              </Button>
              <Button
                className="px-2 sm:px-3 md:px-4 py-1 sm:py-2 bg-[#330101] text-white hover:bg-amber-800 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                onClick={() => handleSave(rentalDetails.pickupDate, rentalDetails.pickupTime, rentalDetails.returnDate, rentalDetails.returnTime)}
              >
                Save
              </Button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}