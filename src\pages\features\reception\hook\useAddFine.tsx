import { NavigateFunction } from 'react-router-dom';
import { AddFineFormData, RentalData } from '../type/reception-type';
import { rentalsData, bookingsData } from '../common/mockData';

export const handleRentalIdChange = (
  value: string,
  formData: AddFineFormData,
  setFormData: React.Dispatch<React.SetStateAction<AddFineFormData>>,
  setSuggestions: React.Dispatch<React.SetStateAction<RentalData[]>>,
  setShowSuggestions: React.Dispatch<React.SetStateAction<boolean>>,
  setSelectedRental: React.Dispatch<React.SetStateAction<RentalData | null>>
) => {
  setFormData(prev => ({ ...prev, rentalId: value }));
  
  if (value.length > 0) {
    const filtered = rentalsData.filter(rental => 
      rental.id.toLowerCase().includes(value.toLowerCase())
    );
    setSuggestions(filtered);
    setShowSuggestions(true);
  } else {
    setSuggestions([]);
    setShowSuggestions(false);
    setFormData(prev => ({
      ...prev,
      customerName: '',
      phoneNumber: '',
      vehicle: ''
    }));
    setSelectedRental(null);
  }
};

export const handleSuggestionSelect = (
  rental: RentalData,
  formData: AddFineFormData,
  setFormData: React.Dispatch<React.SetStateAction<AddFineFormData>>,
  setShowSuggestions: React.Dispatch<React.SetStateAction<boolean>>,
  setSelectedRental: React.Dispatch<React.SetStateAction<RentalData | null>>
) => {
  setFormData(prev => ({
    ...prev,
    rentalId: rental.id,
    customerName: rental.customerName,
    phoneNumber: rental.phoneNumber,
    vehicle: rental.vehicle
  }));
  setSelectedRental(rental);
  setShowSuggestions(false);
};

export const handleFileUpload = (
  event: React.ChangeEvent<HTMLInputElement>,
  formData: AddFineFormData,
  setFormData: React.Dispatch<React.SetStateAction<AddFineFormData>>
) => {
  const file = event.target.files?.[0];
  if (file && ['application/pdf', 'image/jpeg', 'image/png'].includes(file.type)) {
    setFormData(prev => ({ ...prev, uploadedFile: file }));
  } else {
    alert('Please upload a valid PDF, JPG, or PNG file');
  }
};

export const handleSubmit = (
  formData: AddFineFormData,
  setFormData: React.Dispatch<React.SetStateAction<AddFineFormData>>,
  setSuggestions: React.Dispatch<React.SetStateAction<RentalData[]>>,
  setShowSuggestions: React.Dispatch<React.SetStateAction<boolean>>,
  setSelectedRental: React.Dispatch<React.SetStateAction<RentalData | null>>,
  navigate: NavigateFunction
) => {
  // Generate a new fine ID (e.g., #000N)
  const newId = `#${(bookingsData.length + 1).toString().padStart(4, '0')}`;
  
  // Add to bookingsData
  bookingsData.push({
    id: newId,
    customerName: formData.customerName,
    customerPhone: formData.phoneNumber,
    obligationNo: formData.obligationNo,
    penaltyAmount: parseFloat(formData.penaltyAmount) || 0,
    dueDate: formData.dueDate.split('-').reverse().join('-'), // Convert YYYY-MM-DD to DD-MM-YYYY
    vehicle: formData.vehicle,
    offenseDate: formData.offenseDate.split('-').reverse().join('-'), // Convert YYYY-MM-DD to DD-MM-YYYY
    offenseTime: formData.offenseTime,
    type: 'Normal', // or 'Cooperate' if you have that info
    license: '', // or formData.license if available
    expireDate: '', // or formData.expireDate if available
    reservationCount: '0',
    status: 'Active'
  });

  console.log('Form submitted with data:', {
    ...formData,
    id: newId
  });

  // Reset form
  setFormData({
    rentalId: '',
    customerName: '',
    phoneNumber: '',
    vehicle: '',
    obligationNo: '',
    penaltyAmount: '',
    dueDate: '',
    offenseDate: '',
    offenseTime: '00:00',
    uploadedFile: null
  });
  setSuggestions([]);
  setShowSuggestions(false);
  setSelectedRental(null);

  navigate('/reception/reception-fines/');
};

export const handleCancel = (
  formData: AddFineFormData,
  setFormData: React.Dispatch<React.SetStateAction<AddFineFormData>>,
  setSuggestions: React.Dispatch<React.SetStateAction<RentalData[]>>,
  setShowSuggestions: React.Dispatch<React.SetStateAction<boolean>>,
  setSelectedRental: React.Dispatch<React.SetStateAction<RentalData | null>>,
  navigate: NavigateFunction
) => {
  // Reset form
  setFormData({
    rentalId: '',
    customerName: '',
    phoneNumber: '',
    vehicle: '',
    obligationNo: '',
    penaltyAmount: '',
    dueDate: '',
    offenseDate: '',
    offenseTime: '00:00',
    uploadedFile: null
  });
  setSuggestions([]);
  setShowSuggestions(false);
  setSelectedRental(null);

  navigate('/reception/reception-fines/');
};