import React from 'react';
import { Search, Eye, FileText, ChevronDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Pagination } from '@/components/layout/Pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useInvoicesHook } from './hook/useInvoicesHook';


export function InvoicesPage() {
  const navigate = useNavigate();
  const {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    currentPage,
    setCurrentPage,
    recordsPerPage,
    setRecordsPerPage,
    filteredInvoices,
    totalRecords,
    totalPages,
    currentInvoices,
    mobileRequests,
    handleViewInvoice,
    getStatusBadgeStyle,
  } = useInvoicesHook();

  return (
    <div className="p-4 sm:p-6 min-h-screen">
      {/* Header */}
      <div className="flex items-center mb-6">
        <FileText className="w-6 h-6 mr-3 text-gray-800" />
        <h1 className="text-2xl font-bold text-gray-800">Invoices</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4 mb-6">
        <div className="relative">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending Payment">Payment Pending</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
           </div>
              
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
              
        <button className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-4 py-2 rounded-md transition-colors">
          Save this search
        </button>
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rental ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Received Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentInvoices.map((invoice) => (
                <tr key={invoice.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.rentalId}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {invoice.type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${invoice.totalAmount.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${invoice.receivedAmount.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.dueAmount === 0 ? '-' : `$${invoice.dueAmount.toFixed(2)}`}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.paymentType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getStatusBadgeStyle(invoice.status)}>
                      {invoice.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => handleViewInvoice(invoice.rentalId)}
                      className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-100"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile/Tablet Card View */}
      <div className="lg:hidden space-y-4">
        {mobileRequests.map((invoice) => (
          <div key={invoice.id} className="bg-white shadow p-4 relative">
            {/* Status Badge - Top Right */}
            <div className="absolute top-4 right-4">
              <span className={getStatusBadgeStyle(invoice.status)}>
                {invoice.status}
              </span>
            </div>
            
            {/* Invoice ID */}
            <div className="mb-3">
              <span className="text-sm font-medium text-gray-500">ID: </span>
              <span className="text-base font-semibold text-gray-900">{invoice.rentalId}</span>
            </div>
            
            {/* Invoice Type */}
            <div className="mb-3">
              <span className="text-sm font-medium text-gray-500">Invoice Type: </span>
              <span className="text-sm text-gray-900">{invoice.type}</span>
            </div>
            
            {/* Amount Details */}
            <div className="grid grid-cols-2 gap-4 mb-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Total Amount: </span>
                <span className="text-sm font-semibold text-gray-900">${invoice.totalAmount.toFixed(2)}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Received Amount: </span>
                <span className="text-sm font-semibold text-gray-900">${invoice.receivedAmount.toFixed(2)}</span>
              </div>
            </div>
            
            {/* Due Amount */}
            <div className="mb-3">
              <span className="text-sm font-medium text-gray-500">Due Amount: </span>
              <span className="text-sm font-semibold text-gray-900">
                {invoice.dueAmount === 0 ? '-' : `$${invoice.dueAmount.toFixed(2)}`}
              </span>
            </div>
            
            {/* Payment Type */}
            <div className="mb-4">
              <span className="text-sm font-medium text-gray-500">Payment Type: </span>
              <span className="text-sm text-gray-900">{invoice.paymentType}</span>
            </div>
            
            {/* Action Button */}
            <div className="flex justify-end">
              <button
                onClick={() => handleViewInvoice(invoice.rentalId)}
                className="text-black p-2 rounded-full"
              >
                <Eye className="w-5 h-5" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="hidden lg:block">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-6"
        />
      </div>
    </div>
  );
}