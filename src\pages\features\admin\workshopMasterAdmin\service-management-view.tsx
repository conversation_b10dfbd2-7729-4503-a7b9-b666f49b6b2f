import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; 
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Card } from '@/components/ui/card';

interface VehicleFormData {
  vehicleId: string;
  vehicleModel: string;
  regoNumber: string;
  serviceType: string;
  totalParts: string;
  totalLabor: string;
  damages: string;
  notes: string;
  dateIn: string;
  dateOut: string;
  timeOut: string;
  timeIn: string;
  comments: string;
  reservation: string;
  fuelIn: string;
  fuelOut: string;
  odometerIn: string;
  odometerOut: string;
  status: string;
}

export function ServiceManagementView() {
  const navigate = useNavigate(); 

  const [formData, setFormData] = useState<VehicleFormData>({
    vehicleId: 'VID0001',
    vehicleModel: 'Nuzzi N200',
    regoNumber: '7Q9 4H9',
    serviceType: 'General Maintenance',
    totalParts: '00:00',
    totalLabor: '00:00',
    status: 'Pending',
    damages: '',
    notes: '',
    dateIn: '2025-07-28',
    dateOut: '2025-08-02',
    timeOut: '13:43',
    timeIn: '13:43',
    comments: '',
    reservation: '',
    fuelIn: '',
    fuelOut: '',
    odometerIn: '',
    odometerOut: ''
  });

  const handleInputChange = (field: keyof VehicleFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  // --- MOBILE/TABLET VIEW (xs/sm/md) ---
  const MobileView = () => (
    <div className="block md:hidden min-h-screen p-2 bg-white">
      <div className="mb-4">
        <Button
          className="bg-[#330101] text-white w-full text-sm px-3 py-2 flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/admin/workshopMasterAdmin/service-management')}
        >
          <ArrowLeft className="w-3 h-3 mr-1" />
          Go Back
        </Button>
      </div>
      <Card className="bg-white rounded-lg p-3 mb-4">
        <h2 className="text-lg font-semibold mb-4">General Information</h2>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.vehicleId}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Vehicle ID</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.vehicleModel}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Vehicle Model</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.regoNumber}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Rego #</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.serviceType}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Service Type</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.reservation}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Reservation</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.totalLabor}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Total in Labour</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.totalParts}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Total in Parts</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.status}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Status</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.damages}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Damages</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.notes}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Notes</Label>
        </div>
        <h2 className="text-lg font-semibold mb-4 mt-6">Vehicle Out Information</h2>
        <div className="mb-3 relative">
          <Input
            type="date"
            value={formData.dateIn}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Date In</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="date"
            value={formData.dateOut}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Date Out</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="time"
            value={formData.timeIn}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Time In</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="time"
            value={formData.timeOut}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Time Out</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.fuelIn}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Fuel In</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.fuelOut}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Fuel Out</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.odometerIn}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Odometer In</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.odometerOut}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Odometer Out</Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.comments}
            className="w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            readOnly
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1">Comments</Label>
        </div>
      </Card>
    </div>
  );

  // --- DESKTOP VIEW (md and up) ---
  return (
    <>
      <div className="block md:hidden">
        <MobileView />
      </div>
      <div className="hidden md:block">
        {/* ...existing desktop view code remains unchanged... */}
        <div className="min-h-screen p-4">
          <div className="flex items-center mb-4">
            <Button
              className='bg-[#330101] text-white text-sm px-3 py-2'
              size="sm"
              onClick={() => navigate('/admin/workshopMasterAdmin/service-management')}
            >
              <ArrowLeft className="w-4 h-4 mr-1" />
              Go Back
            </Button>
          </div>
          <div className="bg-white rounded-lg p-4">
            {/* General Information */}
            <h2 className="text-lg font-semibold mb-4">General Information</h2>
            <div className="grid grid-cols-2 gap-4 mb-4">
              {/* ...all your existing desktop fields... */}
              <div className="relative">
                <Input
                  type="text"
                  value={formData.vehicleId}
                  onChange={(e) => handleInputChange('vehicleId', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1  focus:border-transparent text-sm"
                  required
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Vehicle ID</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.vehicleModel}
                  onChange={(e) => handleInputChange('vehicleModel', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Vehicle Model</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.regoNumber}
                  onChange={(e) => handleInputChange('regoNumber', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Rego #</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.serviceType}
                  onChange={(e) => handleInputChange('serviceType', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Service Type</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.reservation}
                  onChange={(e) => handleInputChange('reservation', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Reservation</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.totalLabor}
                  onChange={(e) => handleInputChange('totalLabor', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Total in Labour</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.totalParts}
                  onChange={(e) => handleInputChange('totalParts', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Total in Parts</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.status}
                  onChange={(e) => handleInputChange('status', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Status</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.damages}
                  onChange={(e) => handleInputChange('damages', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Damages</Label>
              </div>
              <div className="relative col-span-2">
                <Input
                  type="text"
                  value={formData.notes}
                  onChange={(e) => handleInputChange('notes', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Notes</Label>
              </div>
            </div>
            {/* Vehicle Out Information */}
            <h2 className="text-lg font-semibold mb-4 mt-6">Vehicle Out Information</h2>
            <div className="grid grid-cols-2 gap-4 mb-4">
              <div className="relative">
                <Input
                  type="date"
                  value={formData.dateIn}
                  onChange={(e) => handleInputChange('dateIn', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Date In</Label>
              </div>
              <div className="relative">
                <Input
                  type="date"
                  value={formData.dateOut}
                  onChange={(e) => handleInputChange('dateOut', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Date Out</Label>
              </div>
              <div className="relative">
                <Input
                  type="time"
                  value={formData.timeIn}
                  onChange={(e) => handleInputChange('timeIn', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Time In</Label>
              </div>
              <div className="relative">
                <Input
                  type="time"
                  value={formData.timeOut}
                  onChange={(e) => handleInputChange('timeOut', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Time Out</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.fuelIn}
                  onChange={(e) => handleInputChange('fuelIn', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Fuel In</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.fuelOut}
                  onChange={(e) => handleInputChange('fuelOut', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  required
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Fuel Out</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.odometerIn}
                  onChange={(e) => handleInputChange('odometerIn', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Odometer In</Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={formData.odometerOut}
                  onChange={(e) => handleInputChange('odometerOut', e.target.value)}
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                  readOnly
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Odometer Out</Label>
              </div>
            </div>
            <div className="mb-4 relative">
              <Input
                type="text"
                value={formData.comments}
                onChange={(e) => handleInputChange('comments', e.target.value)}
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                readOnly
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">Comments</Label>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}