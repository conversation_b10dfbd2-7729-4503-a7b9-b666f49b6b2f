import { useState, useMemo } from 'react'
import { FleetSummaryData } from '../type/teamleadertype'

export function useFleetSummary() {
  const [currentPage, setCurrentPage] = useState(1)
  const [recordsPerPage, setRecordsPerPage] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('All')

  // Sample data - replace with actual data source
  const sampleData: FleetSummaryData[] = [
    {
      id: '1',
      category: 'Passenger',
      vehicle: 'Civic Hybrid Sedan - 1PX 12R',
      lastServiceType: 'General Maintenance',
      lastOdometer: 170333,
      status: 'In Rental',
      mechanicAssigned: '-'
    },
    {
      id: '2',
      category: 'Passenger',
      vehicle: 'Atom - PCR 455',
      lastServiceType: 'Breakdown',
      lastOdometer: 176394,
      status: 'In Progress',
      mechanicAssigned: '<PERSON>'
    },
    {
      id: '3',
      category: 'Commercial',
      vehicle: 'Ford Mustang Match - 1PY 2TR',
      lastServiceType: 'Accident Repair',
      lastOdometer: 172200,
      status: 'Awaiting Parts',
      mechanicAssigned: 'Jane Peter'
    },
    {
      id: '4',
      category: 'Passenger',
      vehicle: 'Atom - ASR 321',
      lastServiceType: 'Accident Repair',
      lastOdometer: 176204,
      status: 'Rentable',
      mechanicAssigned: '-'
    },
  ]

  // Filter and search data using useMemo for performance
  const filteredData = useMemo(() => {
    return sampleData.filter((fleet) => {
      const matchesSearch = searchTerm === '' || 
        fleet.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fleet.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fleet.lastServiceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fleet.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fleet.mechanicAssigned.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesFilter = filterStatus === 'All' || fleet.status === filterStatus
      
      return matchesSearch && matchesFilter
    })
  }, [sampleData, searchTerm, filterStatus])
  
  const filteredTotalRecords = filteredData.length
  const filteredTotalPages = Math.ceil(filteredTotalRecords / recordsPerPage)
  
  // Calculate current page data from filtered results
  const currentData = useMemo(() => {
    const startIndex = (currentPage - 1) * recordsPerPage
    const endIndex = startIndex + recordsPerPage
    return filteredData.slice(startIndex, endIndex)
  }, [filteredData, currentPage, recordsPerPage])

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in rental':
        return 'text-xs font-medium rounded bg-gray-300 text-gray-800 px-2 py-1.5'
      case 'in progress':
        return 'text-xs font-medium rounded bg-blue-500 text-white px-2 py-1.5'
      case 'awaiting parts':
        return 'text-xs font-medium rounded bg-orange-400 text-white px-2 py-1.5'
      case 'rentable':
        return 'text-xs font-medium rounded bg-green-500 text-white px-2 py-1.5'
      default:
        return 'text-xs font-medium rounded bg-gray-200 text-gray-700 px-2 py-1.5'
    }
  }

  const handleRecordsPerPageChange = (records: number) => {
    setRecordsPerPage(records)
    setCurrentPage(1) // Reset to first page when changing records per page
  }

  return {
    // State
    currentPage,
    recordsPerPage,
    searchTerm,
    filterStatus,
    
    // Computed values
    currentData,
    filteredTotalRecords,
    filteredTotalPages,
    
    // Functions
    setCurrentPage,
    setSearchTerm,
    setFilterStatus,
    getStatusColor,
    handleRecordsPerPageChange,
  }
}