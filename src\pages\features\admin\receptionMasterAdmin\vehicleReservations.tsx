import React from 'react';
import { <PERSON><PERSON><PERSON><PERSON>, TrendingUp, TrendingDown, DollarSign, Wrench, Package } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent } from '@/components/ui/card';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface ReservationRow {
  reservation: string;
  pickupDate: string;
  returnDate: string;
  pickupLocation: string;
  returnLocation: string;
  fuelAtPickup: string;
  fuelAtReturn: string;
  chargeFuelToClient: string;
  odometerAtPickup: number;
  odometerAtReturn: number;
  totalRevenue: string;
  daysRented: number;
  status: string;
}

export function VehicleReservationsPage() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  const currentTab = 'Reservations';

  const reservationData: ReservationRow[] = [
    {
      reservation: "#26884 <PERSON>den Rice",
      pickupDate: "02/06/2025 16:45",
      returnDate: "26/07/2025 12:00",
      pickupLocation: "Somerton",
      returnLocation: "Somerton",
      fuelAtPickup: "8",
      fuelAtReturn: "8",
      chargeFuelToClient: "No",
      odometerAtPickup: 258877,
      odometerAtReturn: null,
      totalRevenue: "AUD2,169.09",
      daysRented: 54,
      status: "Rental"
    },
    {
      reservation: "#26332 Wael Obeid",
      pickupDate: "24/04/2025 09:56",
      returnDate: "26/04/2025 09:17",
      pickupLocation: "Somerton",
      returnLocation: "Somerton",
      fuelAtPickup: "8",
      fuelAtReturn: "8",
      chargeFuelToClient: "No",
      odometerAtPickup: 256939,
      odometerAtReturn: 257135,
      totalRevenue: "AUD76.36",
      daysRented: 2,
      status: "Completed"
    },
    {
      reservation: "#26299 Shenthuran Mathipalan",
      pickupDate: "19/04/2025 10:16",
      returnDate: "22/04/2025 08:19",
      pickupLocation: "Somerton",
      returnLocation: "Somerton",
      fuelAtPickup: "8",
      fuelAtReturn: "8",
      chargeFuelToClient: "No",
      odometerAtPickup: 256435,
      odometerAtReturn: 256989,
      totalRevenue: "AUD114.55",
      daysRented: 3,
      status: "Completed"
    },
    {
      reservation: "#26218 Wael Obeid",
      pickupDate: "13/04/2025 11:46",
      returnDate: "17/04/2025 17:30",
      pickupLocation: "Somerton",
      returnLocation: "Somerton",
      fuelAtPickup: "8",
      fuelAtReturn: "8",
      chargeFuelToClient: "No",
      odometerAtPickup: 255694,
      odometerAtReturn: 256435,
      totalRevenue: "AUD172.73",
      daysRented: 5,
      status: "Completed"
    },
    {
      reservation: "#25746 Liang Chen",
      pickupDate: "15/03/2025 08:31",
      returnDate: "17/03/2025 07:15",
      pickupLocation: "Somerton",
      returnLocation: "Somerton",
      fuelAtPickup: "8",
      fuelAtReturn: "8",
      chargeFuelToClient: "No",
      odometerAtPickup: 255320,
      odometerAtReturn: 255694,
      totalRevenue: "AUD68.73",
      daysRented: 2,
      status: "Completed"
    },
    {
      reservation: "#25758 Lama Shankar",
      pickupDate: "12/03/2025 17:33",
      returnDate: "14/03/2025 17:50",
      pickupLocation: "Somerton",
      returnLocation: "Somerton",
      fuelAtPickup: "8",
      fuelAtReturn: "8",
      chargeFuelToClient: "No",
      odometerAtPickup: 255226,
      odometerAtReturn: 255320,
      totalRevenue: "AUD103.64",
      daysRented: 2,
      status: "Completed"
    }
  ];

  const handleTabClick = (tab: string) => {
    if (!id) {
      console.error('Vehicle ID is undefined for tab navigation');
      navigate('/receptionMasterAdmin/fleet/vehicles'); 
      return;
    }

    const tabRoutes: { [key: string]: string } = {
      'Edit': `/admin/receptionMasterAdmin/fleet/vehicles/edit-vehicle/${id}`,
      'Reservations': `/admin/receptionMasterAdmin/fleet/vehicles/reservations/${id}`,
      'Damages': `/admin/receptionMasterAdmin/fleet/vehicles/damages/${id}`,
      'Blocked Periods': `/admin/receptionMasterAdmin/fleet/vehicles/blocked-periods/${id}`,
      'Expenses': `/admin/receptionMasterAdmin/fleet/vehicles/expenses/${id}`,
      'Relocations': `/admin/receptionMasterAdmin/fleet/vehicles/relocations/${id}`,
      'Repair Orders': `/admin/receptionMasterAdmin/fleet/vehicles/repair-orders/${id}`,
      'Files': `/admin/receptionMasterAdmin/fleet/vehicles/files/${id}`,
      'Check List': `/admin/receptionMasterAdmin/fleet/vehicles/check-list/${id}`
    };
  
    navigate(tabRoutes[tab]);
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Rental':
        return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Completed':
        return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Available':
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };
  
  const handleReservation = () => {
    navigate(`/admin/receptionMasterAdmin/reservations`);
    };

    const handleRepairs = () => {
      navigate(`/admin/receptionMasterAdmin/fleet/vehicles/repair-orders/${id}`);
    };
    const handleExpenses = () => {
      navigate(`/admin/receptionMasterAdmin/fleet/vehicles/expenses/${id}`);
    };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-semibold text-gray-900">
                Mercedes-Benz E 280 CDI Classic - {id || 'Unknown'}
              </h1>
              <span className="bg-[#330101] text-white px-3 py-1 rounded text-sm font-medium">
                Rental
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button className="bg-[#330101] hover:bg-[#660404] text-white">
                Vehicle Depreciation
              </Button>
            </div>
          </div>
          
          {/* Tab Navigation */}
          <div className="flex space-x-1">
            {['Edit', 'Reservations', 'Damages', 'Blocked Periods', 'Expenses', 'Relocations', 'Repair Orders', 'Files', 'Check List'].map((tab) => (
              <Button
                key={tab}
                onClick={() => handleTabClick(tab)}
                className={`px-4 py-2 text-sm font-medium border border-gray-300 transition-colors hover:bg-gray-100 ${
                  tab === currentTab 
                    ? 'bg-white text-gray-900' 
                    : 'bg-gray-100 text-gray-600'
                }`}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        {/* Finances Section */}
        <div className="mb-8">
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Finances</h2>
          
          <div className="grid grid-cols-3 gap-4 mb-8">
            {/* Revenue */}
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" 
              onClick={handleReservation}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-blue-600 rounded-full flex items-center justify-center">
                    <TrendingUp className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-3xl font-bold text-gray-900">AUD 592.00</p>
                    <p className="text-sm text-gray-600">Revenue</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Total Expenses */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-red-600 rounded-full flex items-center justify-center">
                    <TrendingDown className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-3xl font-bold text-gray-900">AUD 00.00</p>
                    <p className="text-sm text-gray-600">Total Expenses</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Earnings */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-green-600 rounded-full flex items-center justify-center">
                    <DollarSign className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-3xl font-bold text-gray-900">AUD 592.00</p>
                    <p className="text-sm text-gray-600">Earnings</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Total Repairs */}
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={handleRepairs}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-orange-600 rounded-full flex items-center justify-center">
                    <Wrench className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-3xl font-bold text-gray-900">AUD 00.00</p>
                    <p className="text-sm text-gray-600">Total Repairs</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Other Expenses */}
            <Card className="cursor-pointer hover:shadow-lg transition-shadow" onClick={handleExpenses}>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-amber-600 rounded-full flex items-center justify-center">
                    <Package className="w-6 h-6 text-white" />
                  </div>
                  <div>
                    <p className="text-3xl font-bold text-gray-900">AUD 00.00</p>
                    <p className="text-sm text-gray-600">Other Expenses</p>
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* ROI */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center space-x-4">
                  <div className="w-12 h-12 bg-emerald-600 rounded-full flex items-center justify-center">
                    <span className="text-white font-bold text-lg">%</span>
                  </div>
                  <div>
                    <p className="text-3xl font-bold text-gray-900">0%</p>
                    <p className="text-sm text-gray-600">ROI to Date</p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>

        {/* Reservations History */}
        <div>
          <h2 className="text-xl font-semibold text-gray-900 mb-6">Reservations History</h2>
          
          <div className="hidden md:block rounded-md border overflow-hidden">
            <div className="overflow-x-auto">
              <Table>
                <TableHeader>
                  <TableRow className='bg-gray-50 uppercase'>
                    <TableHead>Reservation</TableHead>
                    <TableHead>Pickup</TableHead>
                    <TableHead>Return</TableHead>
                  {/*  <TableHead>Pickup Location</TableHead>
                    <TableHead>Return Location</TableHead>*/} 
                    <TableHead>Fuel at Pickup</TableHead>
                    <TableHead>Fuel at Return</TableHead>
                    <TableHead>Charge fuel to client?</TableHead>
                    <TableHead>Odometer at Pickup</TableHead>
                    <TableHead>Odometer at Return</TableHead>
                    <TableHead>Total Revenue</TableHead>
                    <TableHead>Days Rented</TableHead>
                    <TableHead>Status</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody>
                  {reservationData.map((reservation, index) => (
                    <TableRow key={index} className="hover:bg-gray-50">
                      <TableCell>{reservation.reservation}</TableCell>
                      <TableCell>{reservation.pickupDate}</TableCell>
                      <TableCell>{reservation.returnDate}</TableCell>
                      {/*<TableCell className="text-sm text-gray-900">{reservation.pickupLocation}</TableCell>
                      <TableCell className="text-sm text-gray-900">{reservation.returnLocation}</TableCell> */}
                      <TableCell>{reservation.fuelAtPickup}</TableCell>
                      <TableCell>{reservation.fuelAtReturn}</TableCell>
                      <TableCell>{reservation.chargeFuelToClient}</TableCell>
                      <TableCell>{reservation.odometerAtPickup.toLocaleString()}</TableCell>
                      <TableCell>{reservation.odometerAtReturn ? reservation.odometerAtReturn.toLocaleString() : 'N/A'}</TableCell>
                      <TableCell>{reservation.totalRevenue}</TableCell>
                      <TableCell>{reservation.daysRented}</TableCell>
                      <TableCell>
                        <span className={getStatusBadge(reservation.status)}>
                          {reservation.status}
                        </span>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}