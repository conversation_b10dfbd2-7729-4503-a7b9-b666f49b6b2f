import React, { useState } from 'react';
import { <PERSON>, Eye } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';

export function MasterAdminRefund() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Mock data for refunds
  const refundsData = [
    {
      id: '19744',
      reservation: '27828',
      customer: '<PERSON><PERSON>',
      date: '2025-08-01',
      reference: 'Bond Refund',
      vehicleClass: 'Moving Truck',
      totalPrice: 'AU$617.00',
      totalRefunded: 'AU$500.00',
      status: 'Completed',
    },
    {
      id: '19743',
      reservation: '27838',
      customer: 'Haady Yu-An Hisham',
      date: '2025-08-01',
      reference: 'Refund',
      vehicleClass: 'Standard Car',
      totalPrice: 'AU$577.00',
      totalRefunded: 'AU$500.00',
      status: 'Completed',
    },
    {
      id: '19742',
      reservation: '27816',
      customer: 'Nisho Kasho',
      date: '2025-08-01',
      reference: 'Refund',
      vehicleClass: 'Moving Truck',
      totalPrice: 'AU$617.00',
      totalRefunded: 'AU$455.00',
      status: 'Completed',
    },
    {
      id: '19741',
      reservation: '27756',
      customer: 'Emanuel Day',
      date: '2025-08-01',
      reference: 'refund',
      vehicleClass: 'Moving Truck',
      totalPrice: 'AU$761.00',
      totalRefunded: 'AU$500.00',
      status: 'Completed',
    },
    {
      id: '19740',
      reservation: '27831',
      customer: 'Dalbir S Singh',
      date: '2025-08-01',
      reference: 'refund',
      vehicleClass: 'Moving Truck - Premium',
      totalPrice: 'AU$631.00',
      totalRefunded: 'AU$487.00',
      status: 'Completed',
    },
    {
      id: '19739',
      reservation: '27807',
      customer: 'Bimlesh .',
      date: '2025-07-31',
      reference: 'Bond Refund',
      vehicleClass: 'Delivery Van Jumbo - 2 Tonne',
      totalPrice: 'AU$612.00',
      totalRefunded: 'AU$494.00',
      status: 'Completed',
    },
    {
      id: '19738',
      reservation: '27814',
      customer: 'Melagia Alaseu',
      date: '2025-07-31',
      reference: 'refund',
      vehicleClass: 'Moving Truck',
      totalPrice: 'AU$617.00',
      totalRefunded: 'AU$500.00',
      status: 'Completed',
    },
    {
      id: '19737',
      reservation: '27810',
      customer: 'Dilshan Arachchinge',
      date: '2025-07-31',
      reference: 'Bond Refund',
      vehicleClass: 'Moving Truck - Premium',
      totalPrice: 'AU$612.00',
      totalRefunded: 'AU$500.00',
      status: 'Completed',
    },
  ];

  // Filter data based on search term and filter status
  const filteredData = refundsData.filter((item) =>
    (searchTerm.trim() === '' ||
      item.reservation.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.customer.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.date.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.reference.toLowerCase().includes(searchTerm.trim().toLowerCase()) ||
      item.vehicleClass.toLowerCase().includes(searchTerm.trim().toLowerCase())) &&
    (filterStatus === 'All' || item.status === filterStatus)
  );

  // Pagination logic
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'completed':
        return 'bg-green-500 text-white';
      default:
        return 'bg-gray-200 text-gray-700';
    }
  };

  // Handle view icon click
  const handleViewClick = (id: string) => {
    navigate(`/admin/masterAdmin-refund-view/${id}`);
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <Eye className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Refunds</h1>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Table View */}
      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>Reservation</TableHead>
              <TableHead>Customer</TableHead>
              <TableHead>Date</TableHead>
              <TableHead>Reference</TableHead>
              <TableHead>Vehicle Class</TableHead>
              <TableHead>Total Price</TableHead>
              <TableHead>Total Refunded</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.length === 0 ? (
              <TableRow>
                <TableCell colSpan={9} className="text-center py-4">
                  No refunds found.
                </TableCell>
              </TableRow>
            ) : (
              currentData.map((item) => (
                <TableRow key={item.id} className="hover:bg-gray-50 transition-colors">
                  <TableCell>
                    <span
                      className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                      onClick={() => handleViewClick(item.id)}
                    >
                      {item.reservation}
                    </span>
                  </TableCell>
                  <TableCell>{item.customer}</TableCell>
                  <TableCell>{item.date}</TableCell>
                  <TableCell>{item.reference}</TableCell>
                  <TableCell>{item.vehicleClass}</TableCell>
                  <TableCell>{item.totalPrice}</TableCell>
                  <TableCell>{item.totalRefunded}</TableCell>
                  <TableCell>
                    <span
                      className={`inline-flex items-center gap-2 px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(
                        item.status
                      )}`}
                    >
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <Button
                      onClick={() => handleViewClick(item.id)}
                      variant="ghost"
                      className="text-gray-600 hover:text-gray-800 p-1 md:p-2"
                    >
                      <Eye className="w-3 md:w-4 h-3 md:h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-6"
      />
    </div>
  );
}
