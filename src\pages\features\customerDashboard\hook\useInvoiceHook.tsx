import { useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { invoiceItems } from '../common/mockdata';


export const useInvoiceHook = () => {
  const navigate = useNavigate();
  const invoiceRef = useRef(null);

  const handleDownloadInvoice = async () => {
    if (!invoiceRef.current) return;

    try {
      const hiddenContainer = document.createElement('div');
      hiddenContainer.style.position = 'absolute';
      hiddenContainer.style.left = '-9999px';
      hiddenContainer.style.width = '794px';
      hiddenContainer.style.minHeight = '1122px';
      hiddenContainer.innerHTML = invoiceRef.current.innerHTML;

      document.body.appendChild(hiddenContainer);

      const canvas = await html2canvas(hiddenContainer, {
        scale: 2,
        useCORS: true,
      });

      const imgData = canvas.toDataURL('image/png');

      const a4Width = 210;
      const a4Height = 297;

      const pdf = new jsPDF({
        orientation: 'portrait',
        unit: 'mm',
        format: 'a4',
      });

      const imgWidth = canvas.width / 3.7795;
      const imgHeight = canvas.height / 3.7795;
      const ratio = Math.min(a4Width / imgWidth, a4Height / imgHeight);
      const scaledWidth = imgWidth * ratio;
      const scaledHeight = imgHeight * ratio;
      const xOffset = (a4Width - scaledWidth) / 2;
      const yOffset = (a4Height - scaledHeight) / 2;

      pdf.addImage(imgData, 'PNG', xOffset, yOffset, scaledWidth, scaledHeight);

      pdf.save('Lion_invoice.pdf');

      document.body.removeChild(hiddenContainer);
    } catch (error) {
      console.error('Error generating PDF:', error);
    }
  };

  const handlePrintInvoice = () => {
    window.print();
  };

  const handleSendInvoice = () => {
    console.log('Send invoice clicked');
  };

  return {
    invoiceRef,
    invoiceItems,
    handleDownloadInvoice,
    handlePrintInvoice,
    handleSendInvoice,
    navigate,
  };
};