import React, { useState } from 'react';
import { Share2 } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { Label } from '@/components/ui/label';

interface Rental {
  rentalId: string;
  rego: string;
  customerName: string;
  phone: string;
  payment: string;
  vehicleClass: string;
  pickupDateTime: string;
  returnDateTime: string;
}

const dummyRentals: Rental[] = [
  {
    rentalId: '#304',
    rego: 'ABC 107',
    customerName: 'John',
    phone: '0423 451 222',
    payment: 'Completed',
    vehicleClass: 'Economy car',
    pickupDateTime: '01/08/2025 10:33',
    returnDateTime: '03/08/2025 10:30',
  },
  {
    rentalId: '#305',
    rego: 'XYZ 789',
    customerName: 'Jane',
    phone: '0451 123 456',
    payment: 'Pending',
    vehicleClass: 'SUV',
    pickupDateTime: '02/08/2025 09:00',
    returnDateTime: '04/08/2025 11:00',
  },
];

export function CustomerMCheckVehicle() {
  const [rego, setRego] = useState('');
  const [filteredRentals, setFilteredRentals] = useState<Rental[]>([]);
  const navigate = useNavigate();

  const handleOkClick = () => {
    const latestRentals = dummyRentals
      .filter(r => r.rego.toLowerCase() === rego.toLowerCase())
      .sort((a, b) => new Date(b.pickupDateTime).getTime() - new Date(a.pickupDateTime).getTime());
    setFilteredRentals(latestRentals);
  };

  const handleShareClick = (rental: Rental) => {
    navigate('/admin/customerMasterAdmin/customerM-addFine', {
      state: {
        rentalId: rental.rentalId,
        rego: rental.rego,
        customerName: rental.customerName,
        phoneNumber: rental.phone,
        pickupDateTime: rental.pickupDateTime,
        returnDateTime: rental.returnDateTime,
      },
    });
  };

  return (
    <div className="p-4 bg-white min-h-screen">
      <div className="flex items-center gap-2 mb-4">
        <div className="w-[350px] relative">
          <Label htmlFor="rego" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Enter Vehicle Rego</Label>
          <Input
            id="rego"
            value={rego}
            onChange={(e) => setRego(e.target.value)}
            className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
            placeholder="eg: ABC 107"
          />
        </div>
        <Button
          onClick={handleOkClick}
          className="bg-[#330101] text-white px-4 py-2 text-sm"
        >
          OK
        </Button>
      </div>
      {filteredRentals.length > 0 && (
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="text-sm px-4">Rental ID</TableHead>
                <TableHead className="text-sm px-4">Rego</TableHead>
                <TableHead className="text-sm px-4">Customer Name</TableHead>
                <TableHead className="text-sm px-4">Phone</TableHead>
                <TableHead className="text-sm px-4">Payment</TableHead>
                <TableHead className="text-sm px-4">Vehicle Class</TableHead>
                <TableHead className="text-sm px-4">Pickup Date & Time</TableHead>
                <TableHead className="text-sm px-4">Return Date & Time</TableHead>
                <TableHead className="text-sm px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredRentals.map((rental) => (
                <TableRow key={rental.rentalId}>
                  <TableCell className="text-sm px-4">{rental.rentalId}</TableCell>
                  <TableCell className="text-sm px-4">{rental.rego}</TableCell>
                  <TableCell className="text-sm px-4">{rental.customerName}</TableCell>
                  <TableCell className="text-sm px-4">{rental.phone}</TableCell>
                  <TableCell className="text-sm px-4">{rental.payment}</TableCell>
                  <TableCell className="text-sm px-4">{rental.vehicleClass}</TableCell>
                  <TableCell className="text-sm px-4">{rental.pickupDateTime}</TableCell>
                  <TableCell className="text-sm px-4">{rental.returnDateTime}</TableCell>
                  <TableCell className="text-sm px-4">
                    <Button
                      onClick={() => handleShareClick(rental)}
                      variant="ghost"
                      className="text-gray-600 hover:text-gray-800 p-1"
                    >
                      <Share2 className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      )}
    </div>
  );
}