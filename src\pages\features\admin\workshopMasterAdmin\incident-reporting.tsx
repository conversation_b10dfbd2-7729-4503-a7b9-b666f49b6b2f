import React, { useState } from 'react';
import { Search, Edit, FileText, ClipboardList } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';

interface Booking {
  id: string;
  vehicle: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  incidentDate: string;
  reportType: 'Accident' | 'Breakdown' | 'General Maintenance' | 'Damage';
}

// Example data
const bookingsData: Booking[] = [
  {
    id: 'IR0036',
    vehicle: 'PCR 455',
    pickupDate: '09-07-2025',
    pickupTime: '10:30',
    returnDate: '10-07-2025',
    returnTime: '11:30',
    incidentDate: '10-07-2025',
    reportType: 'Breakdown',
  },
  {
    id: 'IR0034',
    vehicle: 'XBB 211',
    pickupDate: '08-07-2025',
    pickupTime: '12:30',
    returnDate: '10-07-2025',
    returnTime: '10:00',
    incidentDate: '10-07-2025',
    reportType: 'Accident',
  },
  {
    id: 'IR0033',
    vehicle: 'PPR 255',
    pickupDate: '07-07-2025',
    pickupTime: '09:00',
    returnDate: '09-07-2025',
    returnTime: '12:00',
    incidentDate: '09-07-2025',
    reportType: 'General Maintenance',
  },
  {
    id: 'IR0032',
    vehicle: 'NAA 455',
    pickupDate: '07-07-2025',
    pickupTime: '10:30',
    returnDate: '08-07-2025',
    returnTime: '12:30',
    incidentDate: '08-07-2025',
    reportType: 'Damage',
  },
  {
    id: 'IR0031',
    vehicle: 'PCR 455',
    pickupDate: '07-07-2025',
    pickupTime: '10:00',
    returnDate: '07-07-2025',
    returnTime: '16:00',
    incidentDate: '07-07-2025',
    reportType: 'Accident',
  },
];

export function IncidentReporting() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterType, setFilterType] = useState<string>('All');
  const navigate = useNavigate();

  const handleEditIdClick = (id: string) => {
    navigate(`/admin/workshopMasterAdmin/incident-reporting-edit/${id}`);
  };

  const handleIdClick = (id: string) => {
    navigate(`/admin/workshopMasterAdmin/incident-reporting/${id}`);
  };

  // Filter bookings based on search term and report type
  const filteredBookings = bookingsData.filter((booking) => {
    const matchesType = filterType === 'All' || booking.reportType === filterType;
    const matchesSearch =
      booking.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.reportType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.pickupDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.returnDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.incidentDate.toLowerCase().includes(searchTerm.toLowerCase());
    return matchesType && matchesSearch;
  });

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <div className="flex items-center">
          <ClipboardList className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
          <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Incident Reporting</h1>
        </div>
      </div>

      {/* Filter, Search, Save (in this order) */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterType} onValueChange={setFilterType}>
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
              <SelectValue placeholder="All" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Accident">Accident</SelectItem>
              <SelectItem value="Breakdown">Breakdown</SelectItem>
              <SelectItem value="Damage">Damage</SelectItem>
              <SelectItem value="General Maintenance">General Maintenance</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Desktop Table View */}
      <div className="hidden sm:block">
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs sm:text-sm">Incident ID</TableHead>
                <TableHead className="text-xs sm:text-sm">Vehicle</TableHead>
                <TableHead className="text-xs sm:text-sm">Pickup Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Pickup Time</TableHead>
                <TableHead className="text-xs sm:text-sm">Return Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Return Time</TableHead>
                <TableHead className="text-xs sm:text-sm">Incident Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Report Type</TableHead>
                <TableHead className="text-xs sm:text-sm">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredBookings.map((booking) => (
                <TableRow key={booking.id}>
                  <TableCell className="text-xs sm:text-sm">{booking.id}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.vehicle}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.pickupDate}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.pickupTime}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.returnDate}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.returnTime}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.incidentDate}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.reportType}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Button
                        onClick={() => handleEditIdClick(booking.id)}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </Button>
                      <Button
                        onClick={() => handleIdClick(booking.id)}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                      >
                        <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Mobile/Tablet Card View */}
      <div className="sm:hidden space-y-4 mb-4">
        {filteredBookings.length > 0 ? (
          filteredBookings.map((booking) => (
            <div key={booking.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
              <div className="mb-2 flex items-center">
                <span className="text-base font-semibold text-gray-700">{booking.vehicle}</span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Incident ID</div>
                  <div className="text-sm">{booking.id}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Incident Date</div>
                  <div className="text-sm">{booking.incidentDate}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Pickup Date</div>
                  <div className="text-sm">{booking.pickupDate}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Pickup Time</div>
                  <div className="text-sm">{booking.pickupTime}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Return Date</div>
                  <div className="text-sm">{booking.returnDate}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Return Time</div>
                  <div className="text-sm">{booking.returnTime}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Report Type</div>
                  <div className="text-sm">{booking.reportType}</div>
                </div>
              </div>
              {/* Action buttons at the bottom */}
              <div className="flex justify-end gap-2 mt-4">
                <Button
                  onClick={() => handleEditIdClick(booking.id)}
                  variant="ghost"
                  className="text-gray-600 hover:text-gray-800 p-1"
                >
                  <Edit className="w-4 h-4" />
                  <span className="ml-2 text-xs">Edit</span>
                </Button>
                <Button
                  onClick={() => handleIdClick(booking.id)}
                  variant="ghost"
                  className="text-gray-600 hover:text-gray-800 p-1"
                >
                  <FileText className="w-4 h-4" />
                  <span className="ml-2 text-xs">Report</span>
                </Button>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center text-sm text-gray-500 py-4">No incident reports found.</div>
        )}
      </div>
    </div>
  );
}