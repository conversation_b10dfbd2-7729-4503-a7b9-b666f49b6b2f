import React, { useState } from 'react';
import { ArrowLeft, Car, DollarSign } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Label } from '@/components/ui/label';
import { useNavigate } from 'react-router-dom';

interface VehicleClass {
  id: string;
  name: string;
  fleet: string;
}

interface RateData {
  vehicleClass: string;
  fleet: string;
  rate1_3: string;
  rate4_6: string;
  rate6_7: string;
  longTerm: string;
  perDayKm: string;
  additionalKm: string;
}

export function ReceptionAddRatePage() {
  const [rateData, setRateData] = useState<RateData>({
    vehicleClass: '',
    fleet: '',
    rate1_3: '',
    rate4_6: '',
    rate6_7: '',
    longTerm: '',
    perDayKm: '',
    additionalKm: ''
  });

  // Mock data for vehicle classes and their corresponding fleets
  const vehicleClasses: VehicleClass[] = [
    { id: 'economy', name: 'Economy', fleet: 'Fleet A - Economy Vehicles' },
    { id: 'compact', name: 'Compact', fleet: 'Fleet B - Compact Cars' },
    { id: 'midsize', name: 'Midsize', fleet: 'Fleet C - Midsize Sedans' },
    { id: 'fullsize', name: 'Full Size', fleet: 'Fleet D - Full Size Cars' },
    { id: 'luxury', name: 'Luxury', fleet: 'Fleet E - Luxury Vehicles' },
    { id: 'suv', name: 'SUV', fleet: 'Fleet F - SUV Collection' }
  ];

  const handleVehicleClassChange = (value: string) => {
    const selectedClass = vehicleClasses.find(vc => vc.id === value);
    setRateData(prev => ({
      ...prev,
      vehicleClass: value,
      fleet: selectedClass ? selectedClass.fleet : ''
    }));
  };

  const handleInputChange = (field: keyof RateData, value: string) => {
    setRateData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = () => {
    console.log('Rate data submitted:', rateData);
    // Handle form submission here
  };

  const handleCancel = () => {
    setRateData({
      vehicleClass: '',
      fleet: '',
      rate1_3: '',
      rate4_6: '',
      rate6_7: '',
      longTerm: '',
      perDayKm: '',
      additionalKm: ''
    });
  };

  const navigate = useNavigate();

  return (
    <div className="p-6 min-h-screen">
      <div className="mb-6">
        {/* Go Back Button */}
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={() => navigate('/admin/receptionMasterAdmin/fleet/rates')}
        >
          <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
          <span className="hidden md:inline">Go Back</span>
        </Button>

        {/* Title */}
        <div className="flex items-center space-x-2 mt-4">
          <Car className="w-6 h-6 text-[#330101]" />
          <h1 className="text-2xl font-bold text-gray-900">Rates - Add</h1>
        </div>
      </div>

      {/* Main Content */}
      <div className="p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Vehicle Class */}
          <div className="relative space-y-2">
            <Label htmlFor="vehicle-class" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Vehicle Class
            </Label>
            <Select value={rateData.vehicleClass} onValueChange={handleVehicleClassChange}>
              <SelectTrigger id="vehicle-class" className="h-12">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {vehicleClasses.map((vc) => (
                  <SelectItem key={vc.id} value={vc.id}>
                    {vc.name}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Fleet - Auto-filled and disabled */}
          <div className="relative space-y-2">
            <Label htmlFor="fleet" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Fleet
            </Label>
            <Select value={rateData.fleet} disabled>
              <SelectTrigger id="fleet" className="h-12 bg-gray-50">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {vehicleClasses.map((vc) => (
                  <SelectItem key={vc.fleet} value={vc.fleet}>
                    {vc.fleet}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Rate 1 - 3 */}
          <div className="relative space-y-2">
            <Label htmlFor="rate1-3" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Rate 1 - 3
            </Label>
            <Input
              id="rate1-3"
              placeholder="eg. $ 27.00"
              className="h-12"
              value={rateData.rate1_3}
              onChange={(e) => handleInputChange('rate1_3', e.target.value)}
            />
          </div>

          {/* Rate 4 - 6 */}
          <div className="relative space-y-2">
            <Label htmlFor="rate4-6" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Rate 4 - 6
            </Label>
            <Input
              id="rate4-6"
              placeholder="eg. $ 34.00"
              className="h-12"
              value={rateData.rate4_6}
              onChange={(e) => handleInputChange('rate4_6', e.target.value)}
            />
          </div>

          {/* Rate 6 - 7 */}
          <div className="relative space-y-2">
            <Label htmlFor="rate6-7" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Rate 6 - 7
            </Label>
            <Input
              id="rate6-7"
              placeholder="eg. $ 30.00"
              className="h-12"
              value={rateData.rate6_7}
              onChange={(e) => handleInputChange('rate6_7', e.target.value)}
            />
          </div>

          {/* Long Term */}
          <div className="relative space-y-2">
            <Label htmlFor="long-term" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Long Term
            </Label>
            <Input
              id="long-term"
              placeholder="eg. $ 182.00"
              className="h-12"
              value={rateData.longTerm}
              onChange={(e) => handleInputChange('longTerm', e.target.value)}
            />
          </div>

          {/* Per Day km */}
          <div className="relative space-y-2">
            <Label htmlFor="per-day-km" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Per Day km
            </Label>
            <Input
              id="per-day-km"
              placeholder="eg. 250/km"
              className="h-12"
              value={rateData.perDayKm}
              onChange={(e) => handleInputChange('perDayKm', e.target.value)}
            />
          </div>

          {/* Additional km (per km) */}
          <div className="relative space-y-2">
            <Label htmlFor="additional-km" className="absolute left-3 bg-white px-2 text-xs text-gray-600 z-10">
              Additional km (per km)
            </Label>
            <Input
              id="additional-km"
              placeholder="eg. $0.20"
              className="h-12"
              value={rateData.additionalKm}
              onChange={(e) => handleInputChange('additionalKm', e.target.value)}
            />
          </div>
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end space-x-4 mt-8">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="px-6 py-2"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="px-6 py-2 bg-[#330101] text-white hover:bg-[#660404]"
          >
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
}