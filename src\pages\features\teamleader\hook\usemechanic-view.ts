import { useState, useEffect } from 'react'
import { useParams, useNavigate } from 'react-router-dom'
import { MechanicsData, MechanicViewData } from '../type/teamleadertype'

const STORAGE_KEY = 'mechanicsData'

export const useMechanicView = () => {
  const { id } = useParams<{ id: string }>()
  const navigate = useNavigate()
  
  const [mechanic, setMechanic] = useState<MechanicViewData | null>(null)
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const loadMechanic = () => {
      try {
        const savedData = localStorage.getItem(STORAGE_KEY)
        const mechanicsData: MechanicsData[] = savedData ? JSON.parse(savedData) : []
        
        const foundMechanic = mechanicsData.find(m => m.id === id)
        
        if (foundMechanic) {
          // Split fullName into firstName and lastName
          const nameParts = foundMechanic.fullName.split(' ')
          const firstName = nameParts[0] || ''
          const lastName = nameParts.slice(1).join(' ') || ''
          
          // Create view data with mock emergency contact and verification (since it's not in the original data)
          // Use different verification types for different mechanics for demo purposes
          const verificationType = foundMechanic.id === '2' || foundMechanic.id === '4' ? 'passport' : 'driverLicense'
          
          const viewData: MechanicViewData = {
            id: foundMechanic.id,
            firstName,
            lastName,
            phoneNumber: foundMechanic.phoneNumber,
            address: foundMechanic.address,
            enabled: foundMechanic.enabled,
            emergencyContact: {
              name: 'John Doe', // Mock data - in real app this would come from database
              relationship: 'Spouse',
              phoneNumber: '******-0199'
            },
            verification: {
              type: verificationType,
              images: {
                frontView: '/DL-front.png', // Using existing images from public folder
                backView: '/DL-back.png'
              },
              ...(verificationType === 'driverLicense' 
                ? {
                    driverLicense: {
                      number: 'DL123456789',
                      issueState: 'Victoria',
                      issueDate: '2020-03-15',
                      expiryDate: '2025-03-15'
                    }
                  }
                : {
                    passport: {
                      number: 'P1234567890'
                    }
                  }
              )
            }
          }
          
          setMechanic(viewData)
        } else {
          setError('Mechanic not found')
        }
      } catch (err) {
        setError('Failed to load mechanic data')
        console.error('Error loading mechanic:', err)
      } finally {
        setLoading(false)
      }
    }

    if (id) {
      loadMechanic()
    } else {
      setError('No mechanic ID provided')
      setLoading(false)
    }
  }, [id])

  const handleBack = () => {
    navigate('/teamleader/mechanics')
  }

  return {
    mechanic,
    loading,
    error,
    handleBack
  }
}
