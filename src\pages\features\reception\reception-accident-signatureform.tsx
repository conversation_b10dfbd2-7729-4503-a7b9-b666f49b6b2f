import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft, X } from 'lucide-react';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface AccidentSignatureFormData {
  completedBy: string;
  submissionDate: string;
  submissionTime: string;
}

export const ReceptionAccidentSignatureForm: React.FC = () => {
  const [formData, setFormData] = useState<AccidentSignatureFormData>({
    completedBy: 'Ezel Nissan',
    submissionDate: '07/04/2025',
    submissionTime: '09:41:34 AM'
  });

  const [showPopup, setShowPopup] = useState(false);
  const [isTermsAccepted, setIsTermsAccepted] = useState(false);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const navigate = useNavigate(); 
      const handleBack = (): void => {
        navigate('');
      };

  const handleSubmit = () => {
    setShowPopup(true);
  };

  const handleClosePopup = () => {
    setShowPopup(false);
    setIsTermsAccepted(false);
  };

  const handleAcceptTerms = () => {
    if (isTermsAccepted) {
      console.log('Form submitted:', formData);
      alert('Accident report submitted successfully!');
      setShowPopup(false);
      window.location.href = '/reception/reception-incidentReporting';
    }
  };

  return (
    <div className="min-h-screen">
      {/* Go Back Button */}
      <div className="flex items-center mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2'
          size="sm"
          onClick={() => navigate('/reception/reception-accident-policedetails')}
          >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
            Go Back
        </Button>
      </div>

      {/* Main Content */}
      <div className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8 p-2 xs:p-3 sm:p-4 md:p-6">
        <h1 className="text-base xs:text-lg sm:text-xl md:text-2xl lg:text-2xl font-bold text-gray-800 mb-4 xs:mb-5 sm:mb-6 md:mb-8">Additional Details</h1>

        <div className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8">
          {/* Declaration Section */}
          <div>
            <p className="text-[10px] xs:text-xs sm:text-sm md:text-base font-semibold text-gray-800 mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              By submitting this motor claim request for review you hereby declare that:
            </p>
            
            <div className="space-y-2 xs:space-y-3 sm:space-y-4 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-700 ml-2 xs:ml-3 sm:ml-4 md:ml-6">
              <p>You agree to be bound by the terms and conditions of the rental agreement that is currently available to you;</p>
              <p>The disclosure particulars are true and correct;</p>
              <p>You have not withheld or suppressed any information concerning the above particulars.</p>
            </div>
          </div>

          {/* Consent Section */}
          <div>
            <p className="text-[10px] xs:text-xs sm:text-sm md:text-base font-semibold text-gray-800 mb-3 xs:mb-4 sm:mb-5 md:mb-6">You Also Consent to :</p>
            
            <div className="space-y-2 xs:space-y-3 sm:space-y-4 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-700 ml-2 xs:ml-3 sm:ml-4 md:ml-6">
              <p>The use of your personal information for the purpose shown in our Privacy Policy;</p>
              <p>The disclosure of your personal information to, and obtaining information from, other parties as shown in the Privacy Policy.</p>
            </div>
          </div>

          {/* Additional Consent Section */}
          <div>
            <p className="text-[10px] xs:text-xs sm:text-sm md:text-base font-semibold text-gray-800 mb-3 xs:mb-4 sm:mb-5 md:mb-6">
              You also confirm that if you have disclosed personal information about any other person, you are authorised to:
            </p>
            
            <div className="space-y-2 xs:space-y-3 sm:space-y-4 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-700 ml-2 xs:ml-3 sm:ml-4 md:ml-6">
              <p>Disclose to us the personal information about the person and give us consent to use it for the purpose shown in the Privacy Policy;</p>
              <p>Consent to disclose to and obtain any other information about that person from other parties including those shown in the Privacy</p>
            </div>
          </div>

          {/* Information Note */}
          <div className="bg-gray-100 p-2 xs:p-3 sm:p-4 md:p-6 rounded-md">
            <p className="text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
              This report provides a snapshot of the information captured digitally. Complete details including 
              full size images are available by viewing the incident in IDC.
            </p>
            <p className="text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600 mt-1 xs:mt-2 sm:mt-3">
              If a response is N/A, the question was either not answered or it was not asked based on the type 
              of accident.
            </p>
          </div>

          {/* Signature Details Section */}
          <div className="border-t pt-4 xs:pt-5 sm:pt-6 md:pt-8">
            <h2 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold text-gray-800 mb-4 xs:mb-5 sm:mb-6 md:mb-8">Signature Details</h2>
            
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
              {/* Completed By */}
              <div className="relative">
                <Input
                  id="completedBy"
                  name="completedBy"
                  value={formData.completedBy}
                  onChange={handleInputChange}
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-400 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="completedBy" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600">
                  Completed By:
                </Label>
              </div>

              {/* Submitted Date and Time */}
              <div className="relative">
                <Input
                  id="submissionDateTime"
                  name="submissionDateTime"
                  value={`${formData.submissionDate}, ${formData.submissionTime}`}
                  readOnly
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-400 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm bg-gray-50 cursor-not-allowed"
                />
                <Label htmlFor="submissionDateTime" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Submitted:
                </Label>
              </div>
            </div>
          </div>

          {/* Submit Button */}
          <div className="flex justify-end mt-4 xs:mt-5 sm:mt-6 md:mt-8 pt-4 xs:pt-5 sm:pt-6 md:pt-8">
            <Button
              type="button"
              onClick={handleSubmit}
              className="bg-[#330101] text-white px-4 xs:px-6 sm:px-8 md:px-10 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-lg rounded transition-colors"
            >
              Submit
            </Button>
          </div>
        </div>
      </div>

      {/* Terms and Conditions Popup */}
      {showPopup && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white rounded-lg border-2 border-[#330101] max-w-[90%] xs:max-w-[85%] sm:max-w-[600px] md:max-w-md w-full mx-2 xs:mx-3 sm:mx-4 max-h-[90vh] overflow-y-auto">
            {/* Header */}
            <div className="bg-[#330101] text-white px-2 xs:px-3 sm:px-4 md:px-6 py-2 xs:py-2.5 sm:py-3 rounded-t-lg flex justify-between items-center">
              <h3 className="font-semibold text-[10px] xs:text-xs sm:text-sm md:text-base">Terms and Conditions</h3>
              <button 
                onClick={handleClosePopup}
                className="text-white hover:text-gray-200"
              >
                <X size={12} xs:size={14} sm:size={16} md:size={18} />
              </button>
            </div>

            {/* Content */}
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <p className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-700 mb-3 xs:mb-4 sm:mb-5">
                Please review and agree before proceeding.
              </p>

              <div className="space-y-3 xs:space-y-4 sm:space-y-5 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                <div>
                  <p className="font-semibold mb-1 xs:mb-2 sm:mb-3">1. Lion Rentals Pty Ltd is not responsible for any personal items left in the vehicle. Customers must ensure all personal belongings are removed before returning the vehicle.</p>
                </div>

                <div>
                  <p className="font-semibold mb-1 xs:mb-2 sm:mb-3">2. If the customer's payment is pending, the vehicle will not be released for rental under any circumstances. Full payment must be confirmed prior to vehicle pickup.</p>
                </div>

                <div>
                  <p className="font-semibold mb-1 xs:mb-2 sm:mb-3">3. Customers are solely responsible for paying any traffic fines, tolls, or other charges incurred during the rental period. Lion Rentals Pty Ltd will assign and invoice such fines via the customer dashboard.</p>
                </div>

                <div className="bg-orange-50 p-2 xs:p-3 sm:p-4 rounded border-l-4 border-orange-400">
                  <p className="font-semibold text-orange-800 mb-1 xs:mb-2 sm:mb-3">Declaration</p>
                  <p className="text-orange-700">I, the undersigned, confirm that the information provided in this vehicle rental agreement is accurate and complete to the best of my knowledge. I acknowledge and agree to the following:</p>
                </div>

                <div className="space-y-2 xs:space-y-3 sm:space-y-4 ml-2 xs:ml-3 sm:ml-4 md:ml-6">
                  <p>• <span className="font-semibold">Accuracy of Information:</span> I am responsible for ensuring that all details regarding the rental, including customer information, rental dates, vehicle selection, and extras, are accurate.</p>
                  
                  <p>• <span className="font-semibold">Compliance with Regulations:</span> This rental complies with all applicable traffic, rental, and legal regulations, including restrictions on prohibited activities (e.g., unauthorized drivers, commercial use without approval).</p>
                  
                  <p>• <span className="font-semibold">Liability and Insurance:</span>  I understand that Lion Rentals Pty Ltd is not liable for damages to the vehicle beyond the coverage provided under the selected rental agreement. Additional insurance is recommended for high-value or long-term rentals. Customers must upload vehicle condition and odometer photos via the Customer Mobile App or Web Portal to document the vehicle's state at pickup and return.</p>
                  
                  <p>• <span className="font-semibold">Fees and Charges:</span>  I agree to pay all applicable rental fees, fines, tolls, and other related charges as per the terms outlined by Lion Rentals Pty Ltd, including those synced via the Xero Accounting API.</p>
                  
                  <p>• <span className="font-semibold">Electronic Signature Agreement:</span> I acknowledge that by electronically signing this document, I am entering into a legally binding agreement with Lion Rentals Pty Ltd, and my electronic signature carries the same legal effect as a handwritten signature.</p>
                </div>

                <div className="bg-gray-50 p-2 xs:p-3 sm:p-4 rounded">
                  <p className="font-semibold text-gray-800 mb-1 xs:mb-2 sm:mb-3">Electronic Signature Details</p>
                  <div className="space-y-1 xs:space-y-2 text-[10px] xs:text-xs sm:text-sm md:text-base">
                    <p><span className="font-semibold">Signer's Name:</span> testing</p>
                    <p><span className="font-semibold">Date:</span> 10/07/2025, 12:35:48 PM</p>
                    <p><span className="font-semibold">IP Address:</span> ***************</p>
                  </div>
                </div>

                <div className="flex items-start space-x-1 xs:space-x-2 sm:space-x-3 mt-3 xs:mt-4 sm:mt-5">
                  <input
                    type="checkbox"
                    id="termsCheckbox"
                    checked={isTermsAccepted}
                    onChange={(e) => setIsTermsAccepted(e.target.checked)}
                    className="mt-0.5 xs:mt-1 w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5"
                  />
                  <label htmlFor="termsCheckbox" className="text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-700">
                    I hereby confirm that I have read, clearly understood, and agree to the above Terms and Conditions provided by Lion Car Rentals Pty Ltd.
                  </label>
                </div>

                <p className="text-[9px] xs:text-[10px] sm:text-xs md:text-sm text-gray-500 mt-1 xs:mt-2 sm:mt-3">
                  This electronic signature or by clicking it legally binding and has the same affect as a handwritten signature.
                </p>
              </div>

              <div className="flex justify-center mt-3 xs:mt-4 sm:mt-5 md:mt-6">
                <Button
                  onClick={handleAcceptTerms}
                  disabled={!isTermsAccepted}
                  className={`px-4 xs:px-5 sm:px-6 py-1 xs:py-1.5 sm:py-2 rounded text-white font-semibold text-xs xs:text-sm sm:text-sm md:text-sm ${
                    isTermsAccepted 
                      ? 'bg-[#330101] hover:bg-[#441111]' 
                      : 'bg-gray-400 cursor-not-allowed'
                  }`}
                >
                  OK
                </Button>
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};