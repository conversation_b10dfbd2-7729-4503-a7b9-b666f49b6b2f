import { NavigateFunction } from 'react-router-dom';
import { Management } from '../type/teamleadertype';


export const handleVehicleIdClick = (vehicleId: string, navigate: NavigateFunction): void => {
  navigate(`/teamleader/service-management-view/${vehicleId}`);
};

export const handleEditVehicleIdClick = (vehicleId: string, navigate: NavigateFunction): void => {
  navigate(`/teamleader/service-management-edit/${vehicleId}`);
};


export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'Done':
      return 'bg-green-500 text-white';
    case 'Pending':
      return 'bg-gray-400 text-white';
    case 'InProgress':
      return 'bg-blue-500 text-white';
    default:
      return '';
  }
};