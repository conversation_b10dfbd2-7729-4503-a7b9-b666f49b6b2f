import { ReceptionLayout } from "@/components/layout/ReceptionLayout"
import { ReceptionActivitylogs } from "@/pages/features/reception/reception-activitylogs"
import { ReceptionBookingSummary } from "@/pages/features/reception/reception-bookingsummery"
import { ReceptionAddrequest } from "@/pages/features/reception/reception-addrequest"
import { ReceptionChangerequest } from "@/pages/features/reception/changerequest"
import { ReceptionChangerequestInvoice } from "@/pages/features/reception/reception-changerequestInvoice"
import { ReceptionFinesPage } from "@/pages/features/reception/fines"
import { ReceptionFleetrates } from "@/pages/features/reception/reception-fleetrates"
import { ReceptionNotificationCentre } from "@/pages/features/reception/reception-notificationcentre"
import { ReceptionReservationManagement } from "@/pages/features/reception/reservationManagement"
import { ReceptionVehicle } from "@/pages/features/reception/vehicles"
import { ReceptionViewrequest } from "@/pages/features/reception/reception-viewrequest"
import { Route, Routes } from "react-router-dom"
import { ReceptionAddFinePage } from "@/pages/features/reception/addFine"
import { ReceptionEditFinePage } from "@/pages/features/reception/editFine"
import { ReceptionWorkshop } from "@/pages/features/reception/workshop"
import { EditMaintenancePage } from "@/pages/features/reception/editMaintanance"
import { EditAccidentPage } from "@/pages/features/reception/editAccident"
import { EditBreakdownPage } from "@/pages/features/reception/editBreakdown"


import ReceptionCustomerPage  from '../../pages/features/reception/customers';
import { ReceptionCustomerAddPage } from "../../pages/features/reception/addCustomer";
import { ReceptionCustomerEditPage } from "../../pages/features/reception/editCustomer";
import ReceptionCustomerDocumentsPage from "../../pages/features/reception/customer-documents";
import { ReceptionIncidentReportingPage } from "../../pages/features/reception/incidentReporting";
import ReceptionIncidentReportingAddPage from "../../pages/features/reception/reception-incidentReporting-add";
import { ReceptionAccidentForm } from '@/pages/features/reception/reception-accident-form';
import { ReceptionAccidentFormsecond } from '@/pages/features/reception/reception-accident-formsecond';
import { ReceptionAccidentPoliceDetails } from '@/pages/features/reception/reception-accident-policedetails';
import { ReceptionAccidentSignatureForm } from '@/pages/features/reception/reception-accident-signatureform';
import { ReceptionCheckAvailability } from "@/pages/features/reception/reception-checkavailability"
import { ReceptionOutstanding } from "@/pages/features/reception/reception-outstanding"
import { ReceptionEditRequest } from "@/pages/features/reception/reception-editrequest"
import { AssignVehiclePage } from "@/pages/features/reception/assignVehicle"
import { ReturnVehiclePage } from "@/pages/features/reception/returnVehicle"
import { InteractionManagementPage } from "@/pages/features/reception/interactionManagement"
import { AddInteractionPage } from "@/pages/features/reception/addInteractions"
import ReceptionOverviewPage from "@/pages/features/reception/overview"
import { WorkshopAllViewPage } from "@/pages/features/reception/workshopAllView"
import ReceptionNewReservationPage from "@/pages/features/reception/newReservation"
import { InteractionDetailsPage } from "@/pages/features/reception/viewInteraction"

export const ReceptionRoutes = () => {
  return (

    <Routes>
        <Route element={<ReceptionLayout />}>
       <Route path="reception-dashboard/:receptionId" element={<ReceptionOverviewPage />} />
        <Route path="reception-fines" element={<ReceptionFinesPage />} />
        <Route path="reception-fines/reception-addFine" element={<ReceptionAddFinePage />} />
        <Route path="reception-fines/reception-editFine/:id" element={<ReceptionEditFinePage />} />
        <Route path="reception-workshops" element={<ReceptionWorkshop/>} />
        <Route path="reception-workshops/all" element={<WorkshopAllViewPage />} />
        <Route path="reception-workshops/edit-maintenance/:id" element={<EditMaintenancePage />} />
        <Route path="reception-workshops/edit-accident/:id" element={<EditAccidentPage />} />
        <Route path="reception-workshops/edit-breakdown/:id" element={<EditBreakdownPage />} />
        
        
       <Route path="reception-reservantionmanagement" element={<ReceptionReservationManagement/>}/>
       <Route path="reception-bookingsummary/:rentalId" element={<ReceptionBookingSummary/>}/>
       <Route path="reception-checkavailability" element={<ReceptionCheckAvailability/>}/>
       <Route path="newReservation" element={<ReceptionNewReservationPage />} />
       <Route path="reception-outstanding/:rentalId" element={<ReceptionOutstanding />} />
        <Route path="interaction-management" element={<InteractionManagementPage />} />
        <Route path="interaction-management/view-interaction/:id" element={<InteractionDetailsPage />} />
        <Route path="interaction-management/add-interaction" element={<AddInteractionPage />} />
       <Route path="reception-changerequest" element={<ReceptionChangerequest/>}/>
       <Route path="reception-addrequest" element={<ReceptionAddrequest/>}/>
       <Route path="reception-changerequestinvoice" element={<ReceptionChangerequestInvoice/>}/>
       <Route path="reception-changerequest/reception-viewrequest/:id" element={<ReceptionViewrequest/>}/>
       <Route path="reception-fleetrates" element={<ReceptionFleetrates/>}/>
       <Route path="reception-vehicle" element={<ReceptionVehicle/>}/>
       <Route path="reception-notificationcentre" element={<ReceptionNotificationCentre/>}/>
       <Route path="reception-activitylogs" element={<ReceptionActivitylogs/>}/>
       <Route path="reception-changerequest/reception-editrequest/:id" element={<ReceptionEditRequest/>}/>

       <Route path="reception-customer" element={<ReceptionCustomerPage />} />
       <Route path="reception-customer-add" element={<ReceptionCustomerAddPage />} />
       <Route path="reception-customer-edit/:id" element={<ReceptionCustomerEditPage />} />
       <Route path="reception-customer-documents/:id" element={<ReceptionCustomerDocumentsPage />} />
       <Route path="reception-incidentReporting" element={<ReceptionIncidentReportingPage />} />
       <Route path="reception-incidentReporting-add" element={<ReceptionIncidentReportingAddPage />} />
       <Route path="reception-accident-form" element={<ReceptionAccidentForm/>} /> 
       <Route path="reception-accident-formsecond" element={<ReceptionAccidentFormsecond/>}/>
       <Route path="reception-accident-policedetails" element={<ReceptionAccidentPoliceDetails/>} />
       <Route path="reception-accident-signatureform" element={< ReceptionAccidentSignatureForm/>}/>
       <Route path="reception-assignVehicle" element={<AssignVehiclePage />} /> 
       <Route path="reception-returnVehicle" element={<ReturnVehiclePage />} /> 

        </Route>
    </Routes>
  )
}