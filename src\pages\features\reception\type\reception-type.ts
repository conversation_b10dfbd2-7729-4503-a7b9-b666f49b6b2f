export interface FormData {
  serviceType: string;
  breakdownDate: string;
  location: string;
  insuranceClaimed: string;
  insuranceCompany: string;
  claimedNumber: string;
  comment: string;
  [key: string]: string;
}

export type ChangeServiceType = 'yes' | 'no';

export interface SectionProps {
  formData: FormData;
  handleInputChange: (field: string, value: string) => void;
}

export interface ServiceTypeSectionProps extends SectionProps {
  changeServiceType: ChangeServiceType;
  setChangeServiceType: (value: ChangeServiceType) => void;
}

export interface AccidentFormData {
  vehicleClass: string;
  regno: string;
  damageDescription: string;
  incidentDate: string;
  location: string;
  insuranceClaimed: string;
  insuranceCompanyName: string;
  claimedNumber: string;
  changeServiceType: 'yes' | 'no';
  comment: string;
}

export interface ImageUpload {
  interior: File | null;
  exterior: File | null;
  leftSide: File | null;
  rightSide: File | null;
  frontSide: File | null;
  backSide: File | null;
  sideMirrors: File | null;
  other: File | null;
}

export interface Booking {
  id: string;
  customerName: string;
  customerPhone: string;
  obligationNo: string;
  penaltyAmount: number;
  dueDate: string;
  vehicle: string;
  vehicleClass: string;
  offenseDate: string;
  offenseTime: string;
  type: string;
  license: string;
  expireDate: string;
  reservationCount: string;
  status: 'Active' | 'Blacklist';
  frontLicenseImage?: string;
  backLicenseImage?: string;
}


export interface Interaction {
  id: string;
  customerName: string;
  phoneNumber: string;
  mode: string;
  dateTime: string;
  purpose: string;
  status: 'In Progress' | 'Resolved' | 'Escalated';
  email?: string;
  callType?: 'Incoming' | 'Outgoing' | 'Missed';
  callDuration?: number;
}

export interface InteractionData {
  interactionId: string;
  customerName: string;
  phoneNumber: string;
  email: string;
  interactionMode: string;
  purpose: string;
  typeOfReporting?: string;
  typeOfNewInquiry?: string;
  typeOfExistingInquiry?: string;
  typeOfServiceDue?: string;
  typeOfIncidentReport?: string;
  status: string;
  staffMember?: string;
  staffRole: string; // Added staffRole field
  serviceType?: string;
  breakdownDate?: string;
  location?: string;
  insuranceClaimed?: boolean;
  additionalNotes?: string;
}

export interface CustomerFormData {
  customerId: string;
  firstName: string;
  lastName: string;
  address: string;
  postCode: string;
  country: string;
  email: string;
  phone: string;
  birthday: {
    day: string;
    month: string;
    year: string;
  };
  companyName: string;
  emergencyContactName: string;
  emergencyContactNumber: string;
  code: string;
  dlNumber: string;
  issueCountry: string;
  issueDate: string;
  expiryDate: string;
  conditions: string;
  password: string;
  isCorporate: string;
  discount: string;
  discountCode: string;
}

export interface DocumentData {
  id: string;
  customerName: string;
  frontLicenseImage?: string;
  backLicenseImage?: string;
}
export interface FineFormData {
  obligationNo: string;
  penaltyAmount: number;
  dueDate: string;
  offenseDate: string;
  offenseTime: string;
  uploadedFile: File | null;
}
export interface RentalData {
  id: string;
  customerName: string;
  phoneNumber: string;
  vehicle: string;
}

export interface AddFineFormData {
  rentalId: string;
  customerName: string;
  phoneNumber: string;
  vehicle: string;
  obligationNo: string;
  penaltyAmount: string;
  dueDate: string;
  offenseDate: string;
  offenseTime: string;
  uploadedFile: File | null;
}
export interface IncidentBooking {
  id: string;
  customerName: string;
  vehicle: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  incidentDate: string;
  reportType: 'Accident' | 'Breakdown' | 'General Maintenance' | 'Damage';
  insuranceEx?: 'Yes' | 'No';
  paymentDue: string;
  vehicleReplaced?: 'Yes' | 'No';
  replacedVehicle: string;
  replacedDate: string;
  status: 'Accept' | 'Decline' | 'InProgress' | '-';
}
export interface Reservation {
  rego: string;
  rentalId: string;
  pickupDate: Date;
  returnDate: Date;
}

export interface RentalDetails {
  phoneNumber: string;
  email: string;
  customerName: string;
  agreementNo: string;
}
export interface AccidentReportFields {
  payment: string;
  insuranceExcessCover: string;
  driverName: string;
  phoneNumber: string;
  email: string;
  birthday: Date | undefined;
  address: string;
  postcode: string;
  country: string;
  drugsAlcoholIncident: string;
  licenseNumber: string;
  issueDate: Date | undefined;
  expiryDate: Date | undefined;
  conditions: string;
  frontView: File | null;
  backView: File | null;
  nextDue: Date | undefined;
  obtainDetails: string;
  isDrugsAlcoholConsumed: boolean | undefined;
}

export interface AdditionalAccidentFields {
  accidentLocation: string;
  damageDescription: string;
  policeReport: File | null;
  witnessDetails: string;
}

export interface BreakdownFields {
  interiorImages: File | null;
  exteriorImages: File | null;
}
export interface AccidentFormData {
  vehicleClass: string;
  regno: string;
  damageDescription: string;
  incidentDate: string;
  location: string;
  insuranceClaimed: string;
  insuranceCompanyName: string;
  claimedNumber: string;
  changeServiceType: 'yes' | 'no';
  comment: string;
}
export interface IncidentFormData {
  rego: string;
  rentalId: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  customerName: string;
  email: string;
  phoneNumber: string;
  agreementNo: string;
  reportType: string;
  replacementRequested: string;
  vehicleDamaged: string;
  payingExcess: string;
  estimatedAmount: string;
  actualPaid: string;
  totalAmount: string;
  escalateToManager: string;
}
export interface Vehicle {
  id: string;
  rego: string;
  vin: string;
  class: string;
  type: string;
  currentRenter: string;
  lastMileage: number;
  fuel: string;
  status: string;
  availability: string;
}
export interface ChangeRequest {
  id: any;
  rentalId: string;
  customerName: string;
  customerPhone: string;
  vehicle: string;
  vehicleClass: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  requestType: string;
  status: string;
  note?: string;
}
export interface AddRequestFormData {
  customerPhone: string;
  customerName: string;
  rentalId: string;
  vehicle: string;
  vehicleClass: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  requestType: string;
  dateRangeFrom: string;
  dateRangeTo: string;
  newReturnDate: string;
  newReturnTime: string;
  note: string;
  permitNumber: string;
  issueCountry: string;
  issueDate: string;
  expiryDate: string;
  licenseType: string;
  frontView: File | null;
  backView: File | null;
}
export interface InvoiceItem {
  description: string;
  rate: string;
  days: string;
  amount: string;
}

export interface BookingSummary {
  id: string;
  outstandingBalance: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  pickupLocation: string;
  returnLocation: string;
  branch: string;
  reservationType: string;
  loanVehicle: string;
  vehicle: {
    class: string;
    category: string;
    features: string[];
    doors: string;
    vehicleId: string;
    price: string;
    priceNote: string;
  };
  protections: { name: string; price: string }[];
  equipment: { name: string; price: string }[];
  customer: {
    cooperate: string;
    walkIn: string;
    firstName: string;
    lastName: string;
    email: string;
    phone: string;
    address: string;
    postcode: string;
    country: string;
    birthday: string;
  };
  license: {
    type: string;
    idNumber: string;
    issueDate: string;
    expireDate: string;
    country: string;
  };
  emergency: {
    name: string;
    number: string;
  };
  summary: {
    ecoPlus: { description: string; amount: string; rate: string, days: string };
    bond: { description: string; amount: string; days: string };
    insurance: { description: string; amount: string; days: string };
    childSeat: { description: string; amount: string; rate: string, days: string };
    subtotal: string;
    gst: string;
    discount: string;
    total: string;
    securityDeposit: string;
    amountDue: string;
  };
}

export interface ReservationManagement extends BookingSummary {
  rentalId: string;
  totalPrice: string;
  totalRevenue: string;
  totalRefunded: string;
  status: 'Open' | 'Rental' | 'Completed' | 'Cancelled';
  loanVehicle: string;
}
export interface AssignVehicleFormData extends Pick<ReservationManagement, 
    'rentalId' | 'pickupDate' | 'pickupTime' | 'returnDate' | 'returnTime' | 'pickupLocation' | 'returnLocation' | 'vehicle' | 'reservationType'> 
    {
  odometerAtPickup: string;
  includedDistance: string;
  fuelLevelAtPickup: string;
  fuelSameAsPickup: string;
  fuelLevelAtReturn: string;
  comment: string;
  paymentDue: string;
  excessCoverObtained: string;
}

export interface ImageCategory {
  title: string;
  items: string[];
}

export interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageTitle: string;
}