import React from 'react';
import { <PERSON><PERSON><PERSON>, Line, XAxis, YAxis, CartesianGrid, ResponsiveContainer, <PERSON><PERSON>hart, <PERSON>, Cell } from 'recharts';
import { Car, DollarSign, Users, Phone, MessageCircle, MessageSquare, AlertCircle, Mail } from 'lucide-react';

export const CustomerMOverview: React.FC = () => {
  // Monthly revenue data
  const monthlyRevenueData = [
    { month: 'Jan', revenue: 200 },
    { month: 'Feb', revenue: 180 },
    { month: 'Mar', revenue: 300 },
    { month: 'Apr', revenue: 500 },
    { month: 'May', revenue: 380 },
    { month: 'Jun', revenue: 400 },
    { month: 'Jul', revenue: 460 },
    { month: 'Aug', revenue: 300 },
    { month: 'Sep', revenue: 400 },
    { month: 'Oct', revenue: 300 },
    { month: 'Nov', revenue: 460 },
    { month: 'Dec', revenue: 500 }
  ];

  // Complaint types data
  const complaintTypes = [
    { type: 'Phone', percentage: 14, color: '#8B5CF6', icon: Phone },
    { type: 'WhatsApp', percentage: 1, color: '#10B981', icon: MessageCircle },
    { type: 'Chat', percentage: 4, color: '#06B6D4', icon: MessageSquare },
    { type: 'Info', percentage: 0.3, color: '#F59E0B', icon: AlertCircle },
    { type: 'Email', percentage: 0.6, color: '#06B6D4', icon: Mail }
  ];

  // Pie chart data for pending change requests
  const pieData = [
    { name: 'Completed', value: 92, color: '#E5E7EB' },
    { name: 'Pending', value: 8, color: '#EF4444' }
  ];

  return (
    <div className="min-h-screen">
      {/* Key Metrics Cards */}
      <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
        {/* Total Rental Card */}
        <div className="bg-white rounded-xl shadow-sm border-l-4 border-blue-500 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-2xl sm:text-3xl font-bold text-blue-600 truncate">670</h3>
              <p className="text-gray-600 font-medium text-sm sm:text-base">Total Rental</p>
              <p className="text-xs sm:text-sm text-gray-500">Monthly</p>
            </div>
            <div className="bg-blue-100 p-2 sm:p-3 rounded-full ml-4 flex-shrink-0">
              <Car className="w-6 h-6 sm:w-8 sm:h-8 text-blue-600" />
            </div>
          </div>
        </div>

        {/* Total Revenue Card */}
        <div className="bg-white rounded-xl shadow-sm border-l-4 border-green-500 p-4 sm:p-6">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-2xl sm:text-3xl font-bold text-green-600 truncate">500 852</h3>
              <p className="text-gray-600 font-medium text-sm sm:text-base">Total Revenue</p>
              <p className="text-xs sm:text-sm text-gray-500">Monthly</p>
            </div>
            <div className="bg-green-100 p-2 sm:p-3 rounded-full ml-4 flex-shrink-0">
              <DollarSign className="w-6 h-6 sm:w-8 sm:h-8 text-green-600" />
            </div>
          </div>
        </div>

        {/* Total Customer Card */}
        <div className="bg-white rounded-xl shadow-sm border-l-4 border-yellow-500 p-4 sm:p-6 sm:col-span-2 lg:col-span-1">
          <div className="flex items-center justify-between">
            <div className="flex-1 min-w-0">
              <h3 className="text-2xl sm:text-3xl font-bold text-yellow-600 truncate">47,690</h3>
              <p className="text-gray-600 font-medium text-sm sm:text-base">Total Customer</p>
              <p className="text-xs sm:text-sm text-gray-500">All</p>
            </div>
            <div className="bg-yellow-100 p-2 sm:p-3 rounded-full ml-4 flex-shrink-0">
              <Users className="w-6 h-6 sm:w-8 sm:h-8 text-yellow-600" />
            </div>
          </div>
        </div>
      </div>

      {/* Charts Section */}
      <div className="grid grid-cols-1 xl:grid-cols-3 gap-4 sm:gap-6">
        {/* Monthly Revenue Chart */}
        <div className="xl:col-span-2 bg-white rounded-xl shadow-sm p-4 sm:p-6 order-2 xl:order-1">
          <h3 className="text-lg sm:text-xl font-semibold text-gray-800 mb-4">Monthly Revenue Earned</h3>
          <div className="h-64 sm:h-80">
            <ResponsiveContainer width="100%" height="100%">
              <LineChart data={monthlyRevenueData} margin={{ top: 5, right: 10, left: 10, bottom: 5 }}>
                <CartesianGrid strokeDasharray="3 3" stroke="#f0f0f0" />
                <XAxis 
                  dataKey="month" 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 10, fill: '#666' }}
                  interval={0}
                />
                <YAxis 
                  axisLine={false}
                  tickLine={false}
                  tick={{ fontSize: 10, fill: '#666' }}
                  tickFormatter={(value) => `${value}`}
                  width={40}
                />
                <Line 
                  type="monotone" 
                  dataKey="revenue" 
                  stroke="#8B5CF6" 
                  strokeWidth={2}
                  dot={{ fill: '#8B5CF6', strokeWidth: 1, r: 3 }}
                  activeDot={{ r: 4, fill: '#8B5CF6' }}
                />
              </LineChart>
            </ResponsiveContainer>
          </div>
          <div className="flex items-center justify-center mt-4">
            <span className="text-xs sm:text-sm text-gray-500">← 2024 →</span>
          </div>
        </div>

        {/* Right Side Panel */}
        <div className="space-y-4 sm:space-y-6 order-1 xl:order-2">
          {/* Customer Complaint Types */}
          <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6">
            <h3 className="text-base sm:text-lg font-semibold text-gray-800 mb-4">Customer Complaint / Inquiry Type</h3>
            <div className="space-y-3 sm:space-y-4">
              {complaintTypes.map((item, index) => {
                const IconComponent = item.icon;
                return (
                  <div key={index} className="flex items-center justify-between">
                    <div className="flex items-center space-x-2 sm:space-x-3 flex-1 min-w-0">
                      <div 
                        className="p-1.5 sm:p-2 rounded-full flex-shrink-0"
                        style={{ backgroundColor: `${item.color}20` }}
                      >
                        <IconComponent 
                          className="w-3 h-3 sm:w-4 sm:h-4" 
                          style={{ color: item.color }}
                        />
                      </div>
                      <span className="text-xs sm:text-sm font-medium text-gray-700 truncate">{item.type}</span>
                    </div>
                    <span 
                      className="text-xs sm:text-sm font-bold ml-2 flex-shrink-0"
                      style={{ color: item.color }}
                    >
                      {item.percentage}%
                    </span>
                  </div>
                );
              })}
            </div>
          </div>

          {/* Pending Change Requests */}
          <div className="bg-white rounded-xl shadow-sm p-4 sm:p-6">
            <div className="flex items-center justify-between mb-4">
              <h3 className="text-base sm:text-lg font-semibold text-gray-800">Pending Change Requests</h3>
              <span className="text-red-500 text-xs sm:text-sm font-medium">!</span>
            </div>
            
            <div className="relative h-24 sm:h-32 mb-4">
              <ResponsiveContainer width="100%" height="100%">
                <PieChart>
                  <Pie
                    data={pieData}
                    cx="50%"
                    cy="50%"
                    innerRadius={25}
                    outerRadius={45}
                    startAngle={90}
                    endAngle={450}
                    dataKey="value"
                  >
                    {pieData.map((entry, index) => (
                      <Cell key={`cell-${index}`} fill={entry.color} />
                    ))}
                  </Pie>
                </PieChart>
              </ResponsiveContainer>
              <div className="absolute inset-0 flex items-center justify-center">
                <span className="text-lg sm:text-2xl font-bold text-gray-800">8%</span>
              </div>
            </div>

            <div className="text-center">
              <button className="bg-red-500 text-white px-3 py-1.5 sm:px-4 sm:py-2 rounded-lg text-xs sm:text-sm font-medium hover:bg-red-600 transition-colors w-full sm:w-auto">
                Resolutions
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

