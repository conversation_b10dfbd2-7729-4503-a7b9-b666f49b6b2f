import { useState } from "react";
import { MechanicAccidentServiceData } from "../type/mechanictype";

const MOCK_DATA: MechanicAccidentServiceData = {
  model: "Toyota",
  regNo: "DMS 202",
  description: "Scratched rear bumper while parking.",
  insuranceStatus: "Accepted",
  insuranceClaimNumber: "D-654321",
  estimatedEndDate: "2025-06-15",
  actualEndDate: "2025-06-16",
  status: "InProgress",
  images: {
    interiorImages: ["interior-damage.jpg"],
    exteriorImages: ["exterior-damage.jpg"],
    leftSideDoors: ["left-damage.jpg"],
    rightSideDoors: ["right-damage.jpg"],
    frontSideImages: ["front-damage.jpg"],
    backSideImages: ["back-damage.jpg"],
    sideMirrors: ["mirrors-damage.jpg"],
    other: ["other-damage.jpg"],
  },
  branch: "Somerton",
  mechanicAssigned: ["<PERSON> <PERSON>"],
  comment: "Awaiting parts.",
  mechanicNoteStatus: "Pending",
  mechanicNoteComment: "Parts ordered.",
};

export function useDamageServiceView() {
  const [formData] = useState<MechanicAccidentServiceData>(MOCK_DATA);
  return { formData };
}