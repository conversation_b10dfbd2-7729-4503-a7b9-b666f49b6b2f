import React, { useState } from 'react';
import { Search, ChevronDown, RotateCcw, ClipboardList } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
interface notifications {
  id: number;
  type: string;
  message: string;
  date: string;
}

const notifications = [
  {
    id: 1,
    type: 'info',
    message: '<PERSON> logged into the system on 14 May 2025 at 08:15 am',
    date: '2025-07-10'
  },
  {
    id: 2,
    type: 'info',
    message: 'Receptionist <PERSON><PERSON> registered a complaint CM4789 for a delayed return on 13 May 2025 at 05:20 pm',
    date: '2025-06-29'
  },
  {
    id: 3,
    type: 'info',
    message: '<PERSON><PERSON><PERSON><PERSON> processed a new booking BL#60012 for customer <PERSON> on 13 May 2025 at 03:45 pm',
    date: '2025-06-15'
  },
   {
    id: 3,
    type: 'info',
    message: 'Vehicle #KL-3456 was marked as ready for pickup on 13 May 2025 at 01:10 pm.',
    date: '2025-06-15'
  },
    {
    id: 3,
    type: 'info',
    message: 'Payment of LKR 12,500 was received for Booking BL#59998 on 12 May 2025 at 04:30 pm.',
    date: '2025-06-15'
  },
   {
    id: 3,
    type: 'info',
    message: 'Admin shared new rental policy update to all users on 12 May 2025 at 10:00 am.',
    date: '2025-06-15'
  },
 
];

export function ReceptionMAActivitylogPage() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);


  const isDateInRange = (notificationDate: string, filter: string): boolean => {
    const today = new Date();
    const notifDate = new Date(notificationDate);
    
    switch (filter) {
      case 'Today':
        return notifDate.toDateString() === today.toDateString();
      
      case 'This Week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        weekStart.setHours(0, 0, 0, 0);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        
        return notifDate >= weekStart && notifDate <= weekEnd;
      
      case 'This Month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);
        
        return notifDate >= monthStart && notifDate <= monthEnd;
      
      case 'Last 30 Days':
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        thirtyDaysAgo.setHours(0, 0, 0, 0);
        
        return notifDate >= thirtyDaysAgo && notifDate <= today;
      
      default:
        return true;
    }
  };

  // Filter notifications based on search and date filter
  const filteredNotifications = notifications.filter(notification => {
    const matchesSearch = notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         notification.id.toString().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || isDateInRange(notification.date, filterStatus);
    return matchesSearch && matchesFilter;
  });

  // Pagination
  const totalRecords = filteredNotifications.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentNotifications = filteredNotifications.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    }
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title*/}
      <div className="flex items-center mb-4 sm:mb-6">
        <ClipboardList className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-gray-800" />
        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Activity Log</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">

          <Select
                value={filterStatus} 
                onValueChange={(value) => setFilterStatus(value)}>
                <SelectTrigger className=" h-12 focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
                <SelectValue placeholder="Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Today">Today</SelectItem>
                  <SelectItem value="This Week">This Week</SelectItem>
                  <SelectItem value="This Month">This Month</SelectItem>
                <SelectItem value="Last 30 Days">Last 30 Days</SelectItem>
                </SelectContent>
             </Select>
           </div>
        
        <div className="relative flex-1 w-full sm:w-auto">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 text-sm sm:text-base rounded-md focus:border-transparent"
          />
        </div>
        
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 w-full sm:w-auto text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Activity Log List */}
      <div className="space-y-3 sm:space-y-4">
        {currentNotifications.length === 0 ? (
          <div className="text-center py-6 sm:py-8">
            <p className="text-gray-500 text-sm sm:text-base">No notifications found matching your criteria.</p>
          </div>
        ) : (
          currentNotifications.map((notification) => (
            <div
              key={notification.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-2 sm:space-x-3">
                <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#4A0000] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 sm:mt-0">
                  <span className="text-white text-xs sm:text-sm font-medium">i</span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-gray-800 text-sm sm:text-base leading-relaxed break-words">
                    {notification.message}
                  </p>
                </div>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Pagination */}
      {totalRecords > 0 && (
        <div className="mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="overflow-x-auto"
          />
        </div>
      )}
    </div>
  );
}