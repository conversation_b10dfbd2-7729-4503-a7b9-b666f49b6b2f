import React from "react";
import { <PERSON><PERSON> } from "../../../components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "../../../components/ui/table";
import { Pagination } from "../../../components/layout/Pagination";
import { Input } from "../../../components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select";
import { Search, Package, Edit } from "lucide-react";
import { useRequestPartsMain } from "./hook/userequest-parts-main";
import { useNavigate } from "react-router-dom";
import { Card } from "../../../components/ui/card";

export function RequestPartsMain() {
  const {
    currentPage,
    pageSize,
    search,
    setSearch,
    filterType,
    setFilterType,
    paginated,
    totalPages,
    totalRecords,
    setCurrentPage,
    setPageSize,
    allPartNames,
  } = useRequestPartsMain();

  const navigate = useNavigate();

  // Card for mobile/tablet view
  const PartCard = ({ row }: { row: any }) => (
    <Card className="relative mb-4 p-4 shadow-md border border-gray-200 rounded-lg">
      
      <div className="flex justify-between items-start mb-2">
        <span className="text-lg font-semibold text-[#330101]">{row.partNumber}</span>
      </div>
      {/* Details */}
      <div className="mb-2">
        <div className="text-xs text-gray-500 uppercase font-medium">Part Name</div>
        <div className="text-sm">{row.partName}</div>
      </div>
      <div className="mb-2">
        <div className="text-xs text-gray-500 uppercase font-medium">Quantity</div>
        <div className="text-sm">{row.quantity}</div>
      </div>
      <div className="mb-2">
        <div className="text-xs text-gray-500 uppercase font-medium">Description</div>
        <div className="text-sm">{row.description}</div>
      </div>
      {/* Actions at the bottom */}
      <div className="flex justify-end gap-2 pt-3 border-t border-gray-100">
        <Button
          variant="ghost"
          size="sm"
          onClick={() => navigate(`/mechanic/request-parts-main-edit/${row.partNumber}`)}
          className="text-gray-600 hover:text-gray-800"
          title="Edit"
        >
          <Edit className="w-4 h-4 mr-1" />
          Edit
        </Button>
      </div>
    </Card>
  );

  return (
    <div className="pt-0 px-4 pb-8 w-full bg-white min-h-screen">
      {/* Title */}
      <div className="flex items-center mb-4 sm:mb-6">
        <Package className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3" style={{ color: "#330101" }} />
        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
          Request parts
        </h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterType}
            onValueChange={(value) => {
              setFilterType(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none text-sm sm:text-base">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {allPartNames
                .filter((type) => type !== "All")
                .map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
        <div className="relative w-full sm:flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing a name...."
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
              setCurrentPage(1);
            }}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap w-full sm:w-auto">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="block md:hidden">
        {paginated.length > 0 ? (
          paginated.map((row) => (
            <PartCard key={row.partNumber} row={row} />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            No parts data available
          </div>
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block">
        <div className="w-full rounded-md border overflow-x-auto shadow-sm">
          <Table className="min-w-[900px] w-full">
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                  Part Number
                </TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                  Part Name
                </TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                  Quantity
                </TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                  Description
                </TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginated.length > 0 ? (
                paginated.slice(0, 3).map((row) => (
                  <TableRow key={row.partNumber}>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                      {row.partNumber}
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                      {row.partName}
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                      {row.quantity}
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                      {row.description}
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() =>
                          navigate(
                            `/mechanic/request-parts-main-edit/${row.partNumber}`
                          )
                        }
                        className="text-gray-600 hover:text-gray-800 transition-colors"
                        title="Edit"
                      >
                        <Edit className="w-4 h-4 mr-1" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={5}
                    className="text-center py-8 text-gray-500"
                  >
                    No parts data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>

        {/* Pagination for desktop only */}
        <div className="mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={pageSize}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setPageSize(records);
              setCurrentPage(1);
            }}
            className="text-sm sm:text-base"
          />
        </div>
      </div>
    </div>
  );
}