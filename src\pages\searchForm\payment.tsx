import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; 
import SearchHeader from '@/components/layout/SearchHeader';
import { ChevronDown, Pencil } from 'lucide-react';
import Visa<PERSON>ogo from '../../assets/Visa-Symbol.png';
import MastercardLogo from '../../assets/master_card.png';
import Amex<PERSON>ogo from '../../assets/american_express.png';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

// Define TypeScript interface for rentalDetails
interface RentalDetails {
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  pickupLocation: string;
  returnLocation: string;
}

export function Payment() {
  const navigate = useNavigate(); 
  const handlePayNow = (): void => {
    navigate('/search/receipt-invoice');
  };

  // Simulate user role (default to masterAdmin until backend login is implemented)
  const userRole = 'masterAdmin'; // Change to 'customer' to test customer view

  // Define steps based on user role
  const steps = userRole === 'masterAdmin' ? [
    { number: 1, label: 'Dates', active: false },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: false },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: false },
    { number: 6, label: 'Payment', active: true },
    { number: 7, label: 'Receipt', active: false },
    { number: 8, label: 'Pickup', active: false },
    { number: 9, label: 'Agreement', active: false },
    { number: 10, label: 'Return', active: false },
  ] : [
    { number: 1, label: 'Dates', active: false },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: false },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: false },
    { number: 6, label: 'Payment', active: true },
    { number: 7, label: 'Receipt', active: false },
  ];

  const [selectedCard, setSelectedCard] = useState('visa');
  const [selectedCountry, setSelectedCountry] = useState<string>('Australia');
  const [rateType, setRateType] = useState<string>('');
  const [rentalDetails, setRentalDetails] = useState<RentalDetails>({
    pickupDate: '13/06/2025',
    pickupTime: '11:30',
    returnDate: '14/06/2025',
    returnTime: '10:30',
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton'
  });
  const [isPopupOpen, setIsPopupOpen] = useState<boolean>(false);
  const countryOptions: string[] = ['', 'Australia'];
  const rateOptions: string[] = ['', 'New Year 2021', 'Long Term Rental'];

  const handleEditClick = (type: 'pickup' | 'return') => {
    setIsPopupOpen(true);
  };

  const handleSave = (newPickupDate: string, newPickupTime: string, newReturnDate: string, newReturnTime: string) => {
    setRentalDetails({
      ...rentalDetails,
      pickupDate: newPickupDate,
      pickupTime: newPickupTime,
      returnDate: newReturnDate,
      returnTime: newReturnTime
    });
    setIsPopupOpen(false);
  };

  // Find the index of the active step
  const activeIndex = steps.findIndex(step => step.active);

  // Determine visible steps: active step, up to 2 before, up to 1 after
  const getVisibleSteps = () => {
    const visible = [activeIndex];
    if (activeIndex - 1 >= 0) visible.unshift(activeIndex - 1);
    if (activeIndex - 2 >= 0) visible.unshift(activeIndex - 2);
    if (activeIndex + 1 < steps.length) visible.push(activeIndex + 1);
    return visible.sort((a, b) => a - b);
  };

  const visibleStepIndices = getVisibleSteps();

  return (
    <div className="min-h-screen">
      <SearchHeader />
      <div className="w-full px-24 py-6">
        <div className="p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
          <div className="flex items-center justify-center mb-2 sm:mb-4 md:mb-6 lg:mb-8 gap-1 sm:gap-2 md:gap-3 lg:gap-4">
            {steps.map((step, index) => {
              const isVisibleOnXs = visibleStepIndices.includes(index);
              const isVisibleOnSm = visibleStepIndices.includes(index);
              const isVisibleOnMd = visibleStepIndices.includes(index) || index >= activeIndex - 3;
              const isVisibleOnLg = true;

              return (
                <div
                  key={step.number}
                  className={`flex items-center ${!isVisibleOnXs ? 'hidden' : ''} ${isVisibleOnSm ? 'sm:flex' : 'sm:hidden'} ${isVisibleOnMd ? 'md:flex' : 'md:hidden'} ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-full flex items-center justify-center text-xs sm:text-sm md:text-base lg:text-base font-semibold ${
                        step.active ? 'bg-amber-500 text-white' : 'bg-gray-300 text-gray-600'
                      }`}
                    >
                      {step.number}
                    </div>
                    <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm mt-0.5 sm:mt-1">{step.label}</span>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`w-3 sm:w-4 md:w-6 lg:w-8 h-0.5 bg-gray-300 mx-0.5 sm:mx-1 md:mx-2 lg:mx-3 mt-[-12px] sm:mt-[-16px] md:mt-[-18px] lg:mt-[-20px] ${
                        !isVisibleOnXs || !visibleStepIndices.includes(index + 1) ? 'hidden' : ''
                      } ${isVisibleOnSm && visibleStepIndices.includes(index + 1) ? 'sm:flex' : 'sm:hidden'} ${
                        isVisibleOnMd && (isVisibleOnMd || index + 1 >= activeIndex - 3) ? 'md:flex' : 'md:hidden'
                      } ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                    ></div>
                  )}
                </div>
              );
            })}
          </div>

          <div className="flex flex-col md:flex-row gap-2 sm:gap-4 md:gap-6 lg:gap-8">
            {/* 3/4 Payment Details Section */}
            <div className="w-full md:w-2/3 lg:w-3/4">
              <p className="text-red-600 font-semibold mb-2 sm:mb-4 md:mb-6 text-[10px] sm:text-xs md:text-sm lg:text-base">
                Please select a payment method to pay for your Reservation
              </p>

              <div className="mb-2 sm:mb-4 md:mb-6 flex items-center gap-2 sm:gap-4 md:gap-6">
                <Label className="flex items-center">
                  <Input
                    type="radio"
                    name="cardType"
                    value="visa"
                    checked={selectedCard === 'visa'}
                    onChange={(e) => setSelectedCard(e.target.value)}
                    className="mr-1 sm:mr-2"
                  />
                  <img src={VisaLogo} alt="Visa" className="h-5 sm:h-6 md:h-7 lg:h-8" />
                </Label>
                <Label className="flex items-center">
                  <Input
                    type="radio"
                    name="cardType"
                    value="mastercard"
                    checked={selectedCard === 'mastercard'}
                    onChange={(e) => setSelectedCard(e.target.value)}
                    className="mr-1 sm:mr-2"
                  />
                  <img src={MastercardLogo} alt="Mastercard" className="h-5 sm:h-6 md:h-7 lg:h-8" />
                </Label>
                <Label className="flex items-center">
                  <Input
                    type="radio"
                    name="cardType"
                    value="amex"
                    checked={selectedCard === 'amex'}
                    onChange={(e) => setSelectedCard(e.target.value)}
                    className="mr-1 sm:mr-2"
                  />
                  <img src={AmexLogo} alt="American Express" className="h-5 sm:h-6 md:h-7 lg:h-8" />
                </Label>
              </div>

              <div className="border border-gray-300 rounded-lg p-2 sm:p-4 md:p-6 bg-white">
                <h2 className="text-sm sm:text-base md:text-lg lg:text-xl font-semibold mb-1 sm:mb-2 md:mb-4">Payment Details - AUD</h2>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6">
                  <div>
                    <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">Card number</Label>
                    <Input
                      type="text"
                      placeholder="1234 1234 1234 1234"
                      className="w-full border border-gray-300 rounded focus:outline-none focus:ring-0 focus:border-none  p-1 sm:p-2 text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                  </div>
                  <div>
                    <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">Expiration date</Label>
                    <Input
                      type="text"
                      placeholder="MM / YY"
                      className="w-full border border-gray-300 rounded focus:outline-none focus:ring-0 focus:border-none  p-1 sm:p-2 text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                  </div>
                  <div>
                    <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">Security code</Label>
                    <Input
                      type="text"
                      placeholder="CVC"
                      className="w-full border border-gray-300 rounded p-1 focus:outline-none focus:ring-0 focus:border-none  sm:p-2 text-[10px] sm:text-xs md:text-sm lg:text-base"
                    />
                  </div>
                  <div>
                    <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">Country</Label>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="outline" className="w-full justify-between border-b text-[10px] sm:text-xs md:text-sm lg:text-base">
                          {selectedCountry || 'Select Country'}
                          <ChevronDown className="w-4 h-4 text-gray-500" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent className="w-full">
                        {countryOptions.map((country) => (
                          <DropdownMenuItem
                            key={country}
                            onSelect={() => setSelectedCountry(country)}
                            className={selectedCountry === country ? 'bg-amber-100 font-semibold' : ''}
                          >
                            {country || 'Select Country'}
                          </DropdownMenuItem>
                        ))}
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
                
                <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mt-1 sm:mt-2 md:mt-4">
                  By providing your card information, you allow Lion Rentals Pty Ltd to charge your card for future payments in
                  accordance with their terms.
                </p>
                <span className="w-full bg-blue-900 text-white px-2 sm:px-4 py-1 sm:py-2 rounded mt-2 sm:mt-4 md:mt-6 flex justify-between items-center text-[10px] sm:text-xs md:text-sm lg:text-base">
                  <span>Payment</span>
                  <span className="bg-white text-blue-900 px-1 sm:px-2 py-0.5 sm:py-1 rounded text-[9px] sm:text-[10px] md:text-xs lg:text-sm">
                    AUD 100.00
                  </span>
                </span>

                <Button
                  variant="ghost"
                  onClick={handlePayNow}
                  className="w-full border border-gray-900 px-2 sm:px-4 py-1 sm:py-2 mt-2 sm:mt-4 md:mt-6 sm:text-xs md:text-sm lg:text-base"
                >
                  <span>Pay Now</span>
                </Button>
              </div>
            </div>

            {/* 1/4 Summary Section */}
            <div className="lg:col-span-1">
              {userRole === 'masterAdmin' && (
                <div className="p-4 sm:p-6 md:p-6 lg:p-6 rounded-lg shadow-sm top-6">
                  <h2 className="text-base sm:text-lg md:text-xl lg:text-xl font-semibold mb-2 sm:mb-4">Rate Type</h2>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between border-b text-base">
                        {rateType || 'Rate Type'}
                        <ChevronDown className="w-4 h-4 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full">
                      {rateOptions.map((loc) => (
                        <DropdownMenuItem
                          key={loc}
                          onSelect={() => setRateType(loc)}
                          className={rateType === loc ? 'bg-amber-100 font-semibold' : ''}
                        >
                          {loc || 'Rate Type'}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                  <hr className="my-2 sm:my-4" />
                  
                  <h2 className="text-base sm:text-lg md:text-xl lg:text-xl font-semibold mb-2 sm:mb-4">Summary</h2>
                  <span className="bg-[#EBBB4E] text-white text-[10px] sm:text-xs md:text-sm font-semibold px-2 sm:px-3 py-1 sm:py-2 rounded-md">Open</span>
                  <div className="space-y-2 sm:space-y-3 text-[10px] sm:text-xs md:text-sm">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1 sm:gap-2 mt-2 sm:mt-4">
                          <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Pickup</h2>
                          <button className="text-gray-500 hover:text-blue-600" onClick={() => handleEditClick('pickup')}>
                            <Pencil size={14} sm:size={16} md:size={18} />
                          </button>
                        </div>
                        <p>{`Friday, ${rentalDetails.pickupDate} at ${rentalDetails.pickupTime}`}</p>
                        <p>{rentalDetails.pickupLocation}</p>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1 sm:gap-2">
                          <h3 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Return</h3>
                          <button className="text-gray-500 hover:text-blue-600" onClick={() => handleEditClick('return')}>
                            <Pencil size={14} sm:size={16} md:size={18} />
                          </button>
                        </div>
                        <p>{`Saturday, ${rentalDetails.returnDate} at ${rentalDetails.returnTime}`}</p>
                        <p>{rentalDetails.returnLocation}</p>
                      </div>
                    </div>

                    <hr className="my-2 sm:my-4" />

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Eco Plus Car</p>
                      <div className="flex justify-between">
                        <span>1 Day</span>
                        <span>AUD 38.18</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Protection & Coverages</p>
                      <div className="flex justify-between">
                        <span>Bond Compulsory - 500</span>
                        <span>AUD 500.00</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Equipment & Services</p>
                      <div className="flex justify-between">
                        <span>Child Seat</span>
                        <span>AUD 36.36</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Miscellaneous</p>
                      <div className="flex justify-between">
                        <span>Insurance</span>
                        <span>AUD 13.64</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Included Distance</p>
                      <div className="flex justify-between">
                        <span>200 km</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Discount</p>
                      <div className="flex justify-between">
                        <span>10%</span>
                        <span>AUD 3.00</span>
                      </div>
                    </div>

                    <hr className="my-2 sm:my-4" />

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Total</p>
                      <div className="flex justify-between font-bold text-sm sm:text-base md:text-lg lg:text-lg">
                        <span></span>
                        <span className="text-xl sm:text-2xl md:text-3xl lg:text-3xl font-semibold">AUD 594.00</span>
                      </div>
                    </div>

                    <div>
                      <p className="font-medium text-gray-700 text-[10px] sm:text-xs md:text-sm lg:text-sm">Amount Required</p>
                      <div className="flex justify-between text-xl sm:text-2xl md:text-3xl lg:text-3xl">
                        <span></span>
                        <span>AUD 100.00</span>
                      </div>
                    </div>

                    {/* <button 
                      className="bg-[#330101] text-white px-4 sm:px-6 md:px-8 py-2 sm:py-3 rounded-lg font-semibold hover:bg-[#220101] transition-colors text-[10px] sm:text-xs md:text-sm lg:text-base"
                      type="button"
                    >
                      Next Step
                    </button> */}
                  </div>
                </div>
              )}

              {userRole === 'customer' && (
                <div className="p-4 sm:p-6 md:p-6 lg:p-6 rounded-lg shadow-sm top-6">
                  <h2 className="text-base sm:text-lg md:text-xl lg:text-xl font-semibold mb-2 sm:mb-4">Summary</h2>
                  <span className="bg-[#EBBB4E] text-white text-[10px] sm:text-xs md:text-sm font-semibold px-2 sm:px-3 py-1 sm:py-2 rounded-md">Open</span>
                  <div className="space-y-2 sm:space-y-3 text-[10px] sm:text-xs md:text-sm">
                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1 sm:gap-2 mt-2 sm:mt-4">
                          <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Pickup</h2>
                          <button className="text-gray-500 hover:text-blue-600" onClick={() => handleEditClick('pickup')}>
                            <Pencil size={14} sm:size={16} md:size={18} />
                          </button>
                        </div>
                        <p>{`Friday, ${rentalDetails.pickupDate} at ${rentalDetails.pickupTime}`}</p>
                        <p>{rentalDetails.pickupLocation}</p>
                      </div>
                    </div>

                    <div className="flex justify-between items-center">
                      <div>
                        <div className="flex items-center gap-1 sm:gap-2">
                          <h3 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Return</h3>
                          <button className="text-gray-500 hover:text-blue-600" onClick={() => handleEditClick('return')}>
                            <Pencil size={14} sm:size={16} md:size={18} />
                          </button>
                        </div>
                        <p>{`Saturday, ${rentalDetails.returnDate} at ${rentalDetails.returnTime}`}</p>
                        <p>{rentalDetails.returnLocation}</p>
                      </div>
                    </div>

                    <hr className="my-2 sm:my-4" />

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Eco Plus Car</p>
                      <div className="flex justify-between">
                        <span>1 Day</span>
                        <span>AUD 38.18</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Protection & Coverages</p>
                      <div className="flex justify-between">
                        <span>Bond Compulsory - 500</span>
                        <span>AUD 500.00</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Equipment & Services</p>
                      <div className="flex justify-between">
                        <span>Child Seat</span>
                        <span>AUD 36.36</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Miscellaneous</p>
                      <div className="flex justify-between">
                        <span>Insurance</span>
                        <span>AUD 13.64</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Included Distance</p>
                      <div className="flex justify-between">
                        <span>200 km</span>
                      </div>
                    </div>

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Discount</p>
                      <div className="flex justify-between">
                        <span>10%</span>
                        <span>AUD 3.00</span>
                      </div>
                    </div>

                    <hr className="my-2 sm:my-4" />

                    <div>
                      <p className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Total</p>
                      <div className="flex justify-between font-bold text-sm sm:text-base md:text-lg lg:text-lg">
                        <span></span>
                        <span className="text-xl sm:text-2xl md:text-3xl lg:text-3xl font-semibold">AUD 594.00</span>
                      </div>
                    </div>

                    <div>
                      <p className="font-medium text-gray-700 text-[10px] sm:text-xs md:text-sm lg:text-sm">Amount Required</p>
                      <div className="flex justify-between text-xl sm:text-2xl md:text-3xl lg:text-3xl">
                        <span></span>
                        <span>AUD 100.00</span>
                      </div>
                    </div>

                    {/* <button 
                      className="bg-[#330101] text-white px-4 sm:px-6 md:px-8 py-2 sm:py-3 rounded-lg font-semibold hover:bg-[#220101] transition-colors text-[10px] sm:text-xs md:text-sm lg:text-base"
                      type="button"
                    >
                      Next Step
                    </button> */}
                  </div>
                </div>
              )}
            </div>
          </div>
        </div>

        {isPopupOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl">
              <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold mb-2 sm:mb-4">Update Date and Times</h2>

              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Pickup Date</Label>
                  <Input
                    type="text"
                    value={rentalDetails.pickupDate}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, pickupDate: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Pickup Time</Label>
                  <Input
                    type="text"
                    value={rentalDetails.pickupTime}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, pickupTime: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Return Date</Label>
                  <Input
                    type="text"
                    value={rentalDetails.returnDate}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, returnDate: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Return Time</Label>
                  <Input
                    type="text"
                    value={rentalDetails.returnTime}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, returnTime: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
              </div>

              <div className="mt-2 sm:mt-4 flex justify-end gap-2 sm:gap-4">
                <Button
                  className="px-2 sm:px-4 py-1 sm:py-2 bg-gray-200 rounded hover:bg-gray-300 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                  onClick={() => setIsPopupOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  className="px-2 sm:px-4 py-1 sm:py-2 bg-[#330101] text-white rounded hover:bg-amber-800 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                  onClick={() => handleSave(
                    rentalDetails.pickupDate,
                    rentalDetails.pickupTime,
                    rentalDetails.returnDate,
                    rentalDetails.returnTime
                  )}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}