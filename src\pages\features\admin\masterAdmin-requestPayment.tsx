import React, { useState } from 'react';
import { Search, ChevronDown } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';

interface Payment {
  reservation: string;
  pickupDate: string;
  returnDate: string;
  customerName: string;
  email: string;
  amount: string;
  requestType: string;
  status: 'Opened' | 'Expired';
  dueDate: string;
  paidAt: string;
  paymentLink: string;
}

const paymentsData: Payment[] = [
  { reservation: '#7303', pickupDate: '24-03-2021 07:06', returnDate: '26-03-2021 18:08', customerName: '<PERSON><PERSON>', email: '<EMAIL>', amount: 'AUS$45.00', requestType: 'Payment', status: 'Expired', dueDate: '26-03-2021', paidAt: '', paymentLink: 'Click here to copy' },
  { reservation: '#6943', pickupDate: '26-02-2021 13:00', returnDate: '18-03-2021 14:26', customerName: 'Scott Robert Ramsay', email: '<EMAIL>', amount: 'AUS$500.00', requestType: 'Payment', status: 'Expired', dueDate: '13-03-2021', paidAt: '', paymentLink: 'Click here to copy' },
  { reservation: '#6582', pickupDate: '11-03-2021 13:07', returnDate: '15-03-2021 13:27', customerName: 'Caitlyn E Haffenden', email: '<EMAIL>', amount: 'AUS$600.00', requestType: 'Payment', status: 'Expired', dueDate: '15-03-2021', paidAt: '', paymentLink: 'Click here to copy' },
  { reservation: '#1630', pickupDate: '11-11-2019 09:10', returnDate: '11-11-2019 12:43', customerName: 'Gavy Samra Inam', email: '<EMAIL>', amount: 'AUS$10.00', requestType: 'Payment', status: 'Opened', dueDate: '11-10-2019', paidAt: '', paymentLink: 'Click here to copy' },
  { reservation: '#1630', pickupDate: '23-09-2019 17:15', returnDate: '28-10-2019 15:30', customerName: 'Mohammed El Sayegh', email: '<EMAIL>', amount: 'AUS$343.33', requestType: 'Payment', status: 'Expired', dueDate: '13-10-2019', paidAt: '', paymentLink: 'Click here to copy' },
];

export function MasterAdminRequestPayment() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(10);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const navigate = useNavigate();

  const filteredPayments = paymentsData.filter(payment => {
    const matchesSearch = payment.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         payment.reservation.includes(searchTerm);
    const matchesFilter = filterStatus === 'All' || payment.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const totalRecords = filteredPayments.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentPayments = filteredPayments.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const getStatusBadge = (status: string): string => {
    switch (status) {
      case 'Opened':
        return 'bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium';
      case 'Expired':
        return 'bg-orange-500 text-white px-2 py-1 rounded text-xs font-medium';
      default:
        return '';
    }
  };

  return (
    <div className="p-4 bg-white min-h-screen">
   
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4 mb-4">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterStatus}
            onValueChange={(value) => setFilterStatus(value)}
          >
            <SelectTrigger className="w-full sm:w-auto focus:outline-none focus:ring-1 focus:ring-gray-300 text-sm">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Opened">Opened</SelectItem>
              <SelectItem value="Expired">Expired</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-4 py-2 text-sm">
          Save this search
        </Button>
      </div>

      <div className="overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50">
              <TableHead className="text-sm px-4">Reservation</TableHead>
              <TableHead className="text-sm px-4">Pickup Date</TableHead>
              <TableHead className="text-sm px-4">Return Date</TableHead>
              <TableHead className="text-sm px-4">Customer Name</TableHead>
              <TableHead className="text-sm px-4">Email</TableHead>
              <TableHead className="text-sm px-4">Amount</TableHead>
              <TableHead className="text-sm px-4">Request Type</TableHead>
              <TableHead className="text-sm px-4">Status</TableHead>
              <TableHead className="text-sm px-4">Due Date</TableHead>
              <TableHead className="text-sm px-4">Paid At</TableHead>
              <TableHead className="text-sm px-4">Payment Link</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentPayments.map((payment) => (
              <TableRow key={payment.reservation}>
                <TableCell className="text-sm px-4">{payment.reservation}</TableCell>
                <TableCell className="text-sm px-4">{payment.pickupDate}</TableCell>
                <TableCell className="text-sm px-4">{payment.returnDate}</TableCell>
                <TableCell className="text-sm px-4">{payment.customerName}</TableCell>
                <TableCell className="text-sm px-4">{payment.email}</TableCell>
                <TableCell className="text-sm px-4">{payment.amount}</TableCell>
                <TableCell className="text-sm px-4">{payment.requestType}</TableCell>
                <TableCell className="px-4">
                  <span className={getStatusBadge(payment.status)}>
                    {payment.status}
                  </span>
                </TableCell>
                <TableCell className="text-sm px-4">{payment.dueDate}</TableCell>
                <TableCell className="text-sm px-4">{payment.paidAt}</TableCell>
                <TableCell className="text-sm px-4 text-blue-600 underline cursor-pointer">
                  {payment.paymentLink}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      <div className="mt-4">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={setRecordsPerPage}
          className="text-sm"
        />
      </div>
    </div>
  );
}