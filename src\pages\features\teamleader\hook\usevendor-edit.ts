import { NavigateFunction } from 'react-router-dom';
import { VendorEditFormData } from '../type/teamleadertype';

export const handleInputChange = (
  field: keyof VendorEditFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<VendorEditFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};

export const handleSave = (navigate: NavigateFunction): void => {
  navigate('/teamleader/vendors');
};