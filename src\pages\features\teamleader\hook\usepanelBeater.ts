import { PanelBeater } from '../type/teamleadertype';
import { NavigateFunction } from 'react-router-dom';

export const handleIdClick = (id: string, navigate: NavigateFunction) => {
  navigate(`/teamleader/panelBeater-edit/${id}`);
};

export const filterPanelBeaters = (panelBeaters: PanelBeater[], searchTerm: string): PanelBeater[] => {
  return panelBeaters.filter(panelBeater =>
    panelBeater.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
    panelBeater.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
    panelBeater.address.toLowerCase().includes(searchTerm.toLowerCase())
  );
};