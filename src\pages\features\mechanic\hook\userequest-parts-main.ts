import { useEffect, useState } from "react";
import { RequestPartMain } from "../type/mechanictype";

const PARTS_KEY = "mock_request_parts_main";
const MOCK_DATA: RequestPartMain[] = [
  {
    partNumber: "PN0001",
    partName: "Door",
    quantity: "04",
    description: "There were no doors matching the model code 12345678.",
  },
  {
    partNumber: "PN0002",
    partName: "Side Mirrors",
    quantity: "02",
    description: "There were no side mirrors in the stock",
  },
  {
    partNumber: "PN0003",
    partName: "Tyres",
    quantity: "03",
    description: "There were no tyres matching the model code 12345678.",
  },
];

function getParts(): RequestPartMain[] {
  const data = localStorage.getItem(PARTS_KEY);
  return data ? JSON.parse(data) : [];
}

function setParts(parts: RequestPartMain[]) {
  localStorage.setItem(PARTS_KEY, JSON.stringify(parts));
}

export function useRequestPartsMain() {
  const [search, setSearch] = useState("");
  const [filterType, setFilterType] = useState("All");
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const [parts, setPartsState] = useState<RequestPartMain[]>([]);

  useEffect(() => {
    let data = getParts();
    if (data.length === 0) {
      setParts(MOCK_DATA);
      data = MOCK_DATA;
    }
    setPartsState(data);
  }, []);

  // Get all unique part names for filter dropdown
  const allPartNames = ["All", ...Array.from(new Set(parts.map(d => d.partName)))];

  // Filter and search
  const filtered = parts.filter(row => {
    const matchesType = filterType === "All" || row.partName === filterType;
    const matchesSearch =
      row.partNumber.toLowerCase().includes(search.toLowerCase()) ||
      row.partName.toLowerCase().includes(search.toLowerCase()) ||
      row.description.toLowerCase().includes(search.toLowerCase());
    return matchesType && matchesSearch;
  });

  const totalRecords = filtered.length;
  const totalPages = Math.max(1, Math.ceil(totalRecords / pageSize));
  const paginated = filtered.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  return {
    search,
    setSearch,
    filterType,
    setFilterType,
    allPartNames,
    pageSize,
    setPageSize,
    currentPage,
    setCurrentPage,
    totalPages,
    totalRecords,
    paginated,
  };
}