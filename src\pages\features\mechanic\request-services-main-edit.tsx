import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useRequestServicesMainEdit } from "./hook/userequest-services-main-edit";
import { Button } from "../../../components/ui/button";
import { Input } from "../../../components/ui/input";
import { ArrowLeft } from "lucide-react";

export default function RequestServicesMainEdit() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const {
    serviceCode,
    serviceName,
    setServiceName,
    description,
    setDescription,
    handleUpdate,
    handleCancel,
  } = useRequestServicesMainEdit({ navigate, id });

  return (
    <div className="w-full min-h-screen bg-white flex flex-col items-start pt-10 px-4">
      {/* Back Button - Desktop only */}
      <div className="hidden md:flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <Button
          className="w-full sm:w-auto px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center"
          size="sm"
          onClick={() => navigate('/mechanic/request-services-main')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>

      {/* Mobile/Tablet Back Button */}
      <div className="block md:hidden w-full">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/mechanic/request-services-main')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>

      <form
        className="w-full max-w-5xl"
        onSubmit={e => {
          e.preventDefault();
          handleUpdate();
        }}
      >
        {/* Service Code & Service Name */}
        <div className="flex flex-col md:flex-row gap-4 md:gap-8 mb-8 w-full">
          <div className="relative flex-1 mb-6 md:mb-0">
            <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Service Code
            </label>
            <Input
              className="w-full h-12 border border-gray-300 rounded px-4 text-base bg-gray-100 focus:border-[#ffde5c] focus:ring-2 focus:ring-[#ffde5c] transition-colors"
              value={serviceCode}
              placeholder="Service Code"
              readOnly
            />
          </div>
          <div className="relative flex-1">
            <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Service Name
            </label>
            <Input
              className="w-full h-12 border border-gray-300 rounded px-4 text-base focus:border-[#ffde5c] focus:ring-2 focus:ring-[#ffde5c] transition-colors"
              value={serviceName}
              onChange={e => setServiceName(e.target.value)}
              placeholder="Service Name"
            />
          </div>
        </div>

        {/* Description */}
        <div className="relative mb-12 w-full">
          <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
            Description
          </label>
          <Input
            as="textarea"
            className="w-full min-h-[70px] h-24 border border-gray-300 rounded px-4 py-3 text-base bg-gray-100 focus:border-[#ffde5c] focus:ring-2 focus:ring-[#ffde5c] transition-colors resize-none"
            placeholder="Description"
            value={description}
            onChange={e => setDescription(e.target.value)}
          />
        </div>

        {/* Buttons - same line, full width on mobile, fixed width on desktop */}
        <div className="flex flex-row gap-3 w-full">
          <Button
            type="submit"
            className="bg-[#330101]  text-white px-8 py-2 w-full md:w-32"
          >
            Update
          </Button>
          <Button
            type="button"
            className="bg-gray-400 hover:bg-gray-500 text-white px-8 py-2 w-full md:w-32"
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
    
  );
}
