import React, { useState } from 'react';
import { useLocation, useNavigate } from 'react-router-dom';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { ArrowLeft, Plus, X } from 'lucide-react';
import InfNotice from "../../../assets/InfNotice.png";

export function EditFinesPage() {
  const navigate = useNavigate();

  const [notes, setNotes] = useState([{ id: 1, value: '' }]);

  const handleAddNote = () => {
    const newNote = {
      id: Date.now(), // Use timestamp as unique ID
      value: ''
    };
    setNotes([...notes, newNote]);
  };

  const handleRemoveNote = (id) => {
    setNotes(notes.filter(note => note.id !== id));
  };

  const handleNoteChange = (id, value) => {
    setNotes(notes.map(note => 
      note.id === id ? { ...note, value } : note
    ));
  };

  const handleSave = () => {
    // Filter out empty notes and save to backend or state management
    const validNotes = notes.filter(note => note.value.trim() !== '');
    console.log('Saving notes:', validNotes);
    navigate('');
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Back Navigation */}
      <div className="flex items-center mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2'
          size="sm"
          onClick={() => navigate('/fines')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>

      {/*Infringement Notice */}
      <div className="mb-4 sm:mb-6 flex justify-center">
        <img
          src={InfNotice}
          alt="InfNotice"
          className="w-full max-w-xs sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl"
        />
      </div>

      {/* Customer Notes */}
      <div className="mb-4 sm:mb-6">
        <label className="text-xs sm:text-sm font-medium text-gray-500 block mb-2">Customer Note</label>
        
        {notes.map((note, index) => (
          <div key={note.id} className="mb-3">
            <div className="flex gap-2 items-start">
              <Textarea
                value={note.value}
                onChange={(e) => handleNoteChange(note.id, e.target.value)}
                placeholder="Add a note..."
                className="flex-1 h-24 sm:h-28 md:h-32 p-3 sm:p-4 rounded-md border focus:ring-2 focus:ring-yellow-500 focus:border-transparent text-sm sm:text-base"
              />
              
              {/* Plus icon for first field, Cancel icon for additional fields */}
              {index === 0 ? (
                <Button
                  onClick={handleAddNote}
                  className="p-2 bg-gray-100 hover:bg-gray-200 text-gray-600 rounded-full w-10 h-10 flex items-center justify-center mt-1"
                  size="sm"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              ) : (
                <Button
                  onClick={() => handleRemoveNote(note.id)}
                  className="p-2 bg-red-100 hover:bg-red-200 text-red-600 rounded-full w-10 h-10 flex items-center justify-center mt-1"
                  size="sm"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        ))}
      </div>

      {/* Action Buttons */}
      <div className="flex gap-2 sm:gap-4">
        <Button
          onClick={handleSave}
          className="bg-yellow-600 text-white px-3 sm:px-4 py-2 rounded text-sm sm:text-base w-full sm:w-auto"
        >
          Save Note{notes.length > 1 ? 's' : ''}
        </Button>
      </div>
    </div>
  );
}