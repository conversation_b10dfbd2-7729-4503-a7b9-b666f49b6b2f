import { useState } from "react";
import { mechanicAuditTrailType } from "../type/mechanictype";

// Mock data for demo
const MOCK_DATA: mechanicAuditTrailType[] = [
  {
    rego: "1PX 1ZR",
    serviceType: "General Maintenance",
    timestamp: "21-02-2025 : 10.00 am",
  },
  {
    rego: "1PX 5ZP",
    serviceType: "Accident Repair",
    timestamp: "28-02-2025 : 11.30 am",
  },
  // ...add more rows as needed
];

export function useAuditTrail() {
  const [search, setSearch] = useState("");
  const [filterType, setFilterType] = useState("All");
  const [pageSize, setPageSize] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Get all unique service types for filter dropdown
  const allServiceTypes = ["All", ...Array.from(new Set(MOCK_DATA.map(d => d.serviceType)))];

  // Filter and search
  const filtered = MOCK_DATA.filter(row => {
    const matchesType = filterType === "All" || row.serviceType === filterType;
    const matchesSearch =
      row.rego.toLowerCase().includes(search.toLowerCase()) ||
      row.serviceType.toLowerCase().includes(search.toLowerCase());
    return matchesType && matchesSearch;
  });

  const totalRecords = filtered.length;
  const totalPages = Math.max(1, Math.ceil(totalRecords / pageSize));
  const paginated = filtered.slice((currentPage - 1) * pageSize, currentPage * pageSize);

  return {
    search,
    setSearch,
    filterType,
    setFilterType,
    allServiceTypes,
    pageSize,
    setPageSize,
    currentPage,
    setCurrentPage,
    totalPages,
    totalRecords,
    paginated,
  };
}