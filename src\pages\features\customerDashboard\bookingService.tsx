{/*export interface Booking {
  id: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  vehicle: string;
  totalPrice: number;
  outstandingBalance: number;
  status: 'Rental' | 'Open' | 'Completed';
  loanVehicle: 'Yes' | 'No';
}

// Examples
const bookingsData: Booking[] = [
 {
    id: '#0026',
    pickupDate: '10-07-2025',
    pickupTime: '10:30',
    returnDate: '11-07-2025',
    returnTime: '10:30',
    vehicle: 'PCR 455',
    totalPrice: 592.00,
    outstandingBalance: 492.00,
    status: 'Rental',
    loanVehicle: 'Yes'
  },
  {
    id: '#0025',
    pickupDate: '13-07-2025',
    pickupTime: '08:16',
    returnDate: '14-07-2025',
    returnTime: '08:16',
    vehicle: 'Isuzu Nmr 45 150 - 2A05QR',
    totalPrice: 550.00,
    outstandingBalance: 450.00,
    status: 'Open',
    loanVehicle: 'No'
  },
  {
    id: '#0024',
    pickupDate: '11-01-2025',
    pickupTime: '21:16',
    returnDate: '24-01-2025',
    returnTime: '21:16',
    vehicle: 'Toyota Hiace Commuter - 2BH88D',
    totalPrice: 592.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: '#0023',
    pickupDate: '20-12-2024',
    pickupTime: '21:16',
    returnDate: '24-12-2024',
    returnTime: '21:16',
    vehicle: 'DEF 456',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: '#0022',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: '#0021',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: '#0020',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: '#0019',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: '#0018',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: '#0017',
    pickupDate: '11-10-2024',
    pickupTime: '21:16',
    returnDate: '14-11-2024',
    returnTime: '21:16',
    vehicle: 'GHI 987',
    totalPrice: 548.00,
    outstandingBalance: 0.00,
    status: 'Completed',
    loanVehicle: 'No'
  }
];

export const getBookings = (): Booking[] => {
  return bookingsData;
};

export const getBookingById = (id: string): Booking | undefined => {
  return bookingsData.find(booking => booking.id === id || booking.id === `#${id}`);
};
*/}