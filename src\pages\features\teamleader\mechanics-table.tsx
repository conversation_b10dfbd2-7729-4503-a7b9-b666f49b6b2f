import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table'
import { Pagination } from '../../../components/layout/Pagination'
import { Input } from '../../../components/ui/input'
import { Button } from '../../../components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import { Search, UserCircle, Eye } from 'lucide-react'
import { useMechanicsTable } from './hook/usemechanics-table'
import { useNavigate } from 'react-router-dom'

export function MechanicsTable() {
  const navigate = useNavigate()
  const {
    currentPage,
    recordsPerPage,
    searchTerm,
    filterStatus,
    currentData,
    filteredTotalRecords,
    filteredTotalPages,
    setCurrentPage,
    setSearchTerm,
    setFilterStatus,
    getStatusColor,
    handleRecordsPerPageChange,
  } = useMechanicsTable()

  const handleViewMechanic = (mechanicId: string) => {
    navigate(`/teamleader/mechanics/view/${mechanicId}`)
  }

  return (
    <div className="pt-0 px-4 pb-8 max-w-7xl mx-auto">
      {/* Title*/}
      <div className="flex items-center mb-6 sm:mb-8">
        <UserCircle className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Mechanics</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-6 mb-8 sm:mb-10">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterStatus} 
            onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
            <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
            <SelectItem value="All">All</SelectItem>
            <SelectItem value="Yes">Yes</SelectItem>
            <SelectItem value="No">No</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Desktop Table View */}
      <div className="hidden md:block rounded-md border overflow-hidden shadow-sm">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className='bg-gray-50 uppercase'>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Full Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Phone Number</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Address</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Enabled</TableHead>
                {/*<TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-center">Action</TableHead>*/}
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length > 0 ? (
                currentData.map((mechanic) => (
                  <TableRow key={mechanic.id}>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 font-normal">{mechanic.fullName}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{mechanic.phoneNumber}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{mechanic.address}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                      <span className={getStatusColor(mechanic.enabled)}>
                        {mechanic.enabled ? 'Yes' : 'No'}
                      </span>
                    </TableCell>
                    {/*<TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => handleViewMechanic(mechanic.id)}
                      >
                        <Eye className="h-4 w-4" />
                      </Button>
                    </TableCell>*/}
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                    No mechanics data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <div className="mt-4 sm:mt-6">
        <Pagination
          currentPage={currentPage}
          totalPages={filteredTotalPages}
          totalRecords={filteredTotalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={handleRecordsPerPageChange}
          className="text-sm sm:text-base"
        />
      </div>
    </div>
  )
}