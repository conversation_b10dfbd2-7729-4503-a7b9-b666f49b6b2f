import React, { useState } from 'react';
import { Search, Edit, Eye, Wrench, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Pagination } from '@/components/layout/Pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';

interface MaintenanceRecord {
  vehicleId: string;
  vehicle: string;
  maintenanceType: string;
  estimatedEndDate: string;
  actualEndDate: string;
  status: string;
  statusOfVehicle: string;
  vehicleCurrentRenter: string;
  mechanic: string;
}

export const ServiceMaintenance = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [activeTab, setActiveTab] = useState('General Maintenance');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const maintenanceData: MaintenanceRecord[] = [
    {
      vehicleId: 'VID0003',
      vehicle: 'Civic Hybrid Sedan - 1PX 1ZR',
      maintenanceType: 'General Maintenance Every 5000km',
      estimatedEndDate: '26-05-2025',
      actualEndDate: '-',
      status: 'Pending',
      statusOfVehicle: 'Dirty',
      vehicleCurrentRenter: 'Not Assigned',
      mechanic: '-'
    },
    {
      vehicleId: 'VID0004',
      vehicle: 'BMW 330 d Coupe - 191',
      maintenanceType: 'General Maintenance Every 10000km',
      estimatedEndDate: '26-05-2025',
      actualEndDate: '-',
      status: 'InProgress',
      statusOfVehicle: 'Dirty',
      vehicleCurrentRenter: 'Not Assigned',
      mechanic: 'Peter Smith'
    },
    {
      vehicleId: 'VID0005',
      vehicle: 'Holden CTS H2 Automatic - 181',
      maintenanceType: 'General Maintenance Every 5000km',
      estimatedEndDate: '26-05-2025',
      actualEndDate: '-',
      status: 'InProgress',
      statusOfVehicle: 'Dirty',
      vehicleCurrentRenter: 'John Doe',
      mechanic: 'Anne Johnson'
    },
    {
      vehicleId: 'VID0006',
      vehicle: 'Atom - 1PX 5ZP',
      maintenanceType: 'General Maintenance Every 7000km',
      estimatedEndDate: '26-05-2025',
      actualEndDate: '31-05-2025',
      status: 'Completed',
      statusOfVehicle: 'Cleaned',
      vehicleCurrentRenter: 'Not Assigned',
      mechanic: '-'
    }
  ];

  const navigate = useNavigate(); 

  const filteredData = maintenanceData.filter(
    (record) =>
      record.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.maintenanceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.actualEndDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.estimatedEndDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.mechanic.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const handleVehicleIdClick = (vehicleId: string) => {
    navigate(`/admin/workshopMasterAdmin/service-maintenance-edit/${vehicleId}`);
  };

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  );

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-green-500 text-white';
      case 'Pending':
        return 'bg-gray-400 text-white';
      case 'InProgress':
        return 'bg-blue-500 text-white';
      case 'Dirty':
        return 'bg-red-500 text-white';
      case 'Cleaned':
        return 'bg-yellow-500 text-white';
      default:
        return 'bg-gray-300 text-gray-700';
    }
  };

  // --- Responsive Card View for xs, sm, md ---
  const isMobileOrTablet = typeof window !== "undefined" && window.innerWidth < 1024;

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center mb-4 sm:mb-0">
            <Wrench className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-gray-700" />
            <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">General Maintenance</h1>
          </div>
        </div>

        {/* Tab Bar */}
        <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
          {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
            <Button
              key={tab}
              variant={activeTab === tab ? 'default' : 'outline'}
              onClick={() => {
                if (tab === 'All') {
                  navigate('/admin/workshopMasterAdmin/service-management');
                } else if (tab === 'Accident') {
                  navigate('/admin/workshopMasterAdmin/service-accident');
                } else if (tab === 'Breakdowns') {
                  navigate('/admin/workshopMasterAdmin/service-breakdown');
                } else if (tab === 'Damage') {
                  navigate('/admin/workshopMasterAdmin/service-damage');
                } else {
                  setActiveTab(tab);
                }
              }}
              className="text-sm md:text-base"
            >
              {tab}
            </Button>
          ))}
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          {/* Filter dropdown - always first */}
          <div className="relative w-full sm:w-auto order-1">
            <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
              <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="InProgress">InProgress</SelectItem>
                <SelectItem value="Completed">Completed</SelectItem>
              </SelectContent>
            </Select>
          </div>

          {/* Search input - always second */}
          <div className="relative flex-1 order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing a name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>

          {/* Save button - always third */}
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
      </div>

      {/* Responsive Card View for xs, sm, md (below lg) */}
      <div className="lg:hidden space-y-4 mb-4">
        {filteredData.length === 0 && (
          <div className="text-center text-gray-500 py-8">No records found.</div>
        )}
        {filteredData.map((record) => (
          <Card key={record.vehicleId} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
            {/* Status badges - top right, one per line */}
            <div className="absolute top-4 right-4 flex flex-col items-end gap-1 min-w-[120px]">
              <span
                className={`inline-flex items-center justify-center rounded px-3 py-1.5 text-xs font-semibold w-[120px] text-center ${getStatusColor(
                  record.status
                )} whitespace-nowrap`}
              >
                {record.status}
              </span>
              <span
                className={`inline-flex items-center justify-center rounded px-3 py-1.5 text-xs font-semibold w-[120px] text-center ${getStatusColor(
                  record.statusOfVehicle
                )} whitespace-nowrap`}
              >
                Vehicle: {record.statusOfVehicle}
              </span>
            </div>
            {/* Card content */}
            <div className="mb-3 pr-[110px]">
              <span
                className="text-blue-600 hover:text-blue-800 font-semibold cursor-pointer text-base"
                onClick={() => handleVehicleIdClick(record.vehicleId)}
              >
                {record.vehicleId}
              </span>
            </div>
            {/* Vehicle name under status badges, left aligned, then Maintenance Type */}
            <div className="mb-2 mt-8 pl-0">
              <span className="text-gray-900 font-medium text-left block">{record.vehicle}</span>
            </div>
            <div className="mb-2 pl-0">
              <div className="text-xs text-gray-500 uppercase font-medium mb-1">Maintenance Type</div>
              <div className="text-sm">{record.maintenanceType}</div>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-2">
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Estimated End Date</div>
                <div className="text-sm">{record.estimatedEndDate}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Actual End Date</div>
                <div className="text-sm">{record.actualEndDate}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Current Renter</div>
                <div className="text-sm">{record.vehicleCurrentRenter}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Mechanic</div>
                <div className="text-sm">{record.mechanic}</div>
              </div>
            </div>
            {/* Actions at the bottom */}
            <div className="flex justify-end gap-2 mt-4">
              <Button
                onClick={() => handleVehicleIdClick(record.vehicleId)}
                variant="ghost"
                size="sm"
                className="text-gray-600 hover:text-gray-800 p-1"
              >
                <Edit className="w-5 h-5" />
                <span className="ml-2 text-xs">Edit</span>
              </Button>
              {/* <Button
                onClick={() => navigate('/admin/workshopMasterAdmin/service-maintenance-view')}
                variant="ghost"
                size="sm"
                className="text-gray-600 hover:text-gray-800 p-1"
              >
                <Eye className="w-5 h-5" />
                <span className="ml-2 text-xs">View</span>
              </Button> */}
            </div>
          </Card>
        ))}
      </div>

      {/* Table View for lg and up */}
      <div className="hidden lg:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle ID</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Maintenance Type</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Estimated End Date</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Actual End Date</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Status</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Status of the Vehicle</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle Current Renter</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Mechanic Assigned</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.map((record) => (
                <TableRow key={record.vehicleId}>
                  <TableCell className="text-sm px-3 py-4">{record.vehicleId}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.vehicle}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.maintenanceType}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.estimatedEndDate}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.actualEndDate}</TableCell>
                  <TableCell className="px-3 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(record.status)}`}>
                      {record.status}
                    </span>
                  </TableCell>
                  <TableCell className="px-3 py-4">
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(record.statusOfVehicle)}`}>
                      {record.statusOfVehicle}
                    </span>
                  </TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.vehicleCurrentRenter}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.mechanic}</TableCell>
                  <TableCell className="px-3 py-4">
                    <Button
                      onClick={() => handleVehicleIdClick(record.vehicleId)}
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                    {/* <Button
                      onClick={() => navigate('/admin/workshopMasterAdmin/service-maintenance-view')}
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                    >
                      <Eye className="w-4 h-4" />
                    </Button> */}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination only for lg and up */}
      <div className="mt-4 sm:mt-6 hidden lg:block">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={entriesPerPage}
          onPageChange={handlePageChange}
          onRecordsPerPageChange={(records) => {
            setEntriesPerPage(records);
            setCurrentPage(1);
          }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4"
        />
      </div>
    </div>
  );
};