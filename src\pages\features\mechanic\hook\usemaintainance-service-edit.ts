import { useState } from "react";
import { MaintainanceServiceEditData } from "../type/mechanictype";

const MOCK_DATA: MaintainanceServiceEditData = {
  vehicleId: "V12345",
  model: "Civic Hybrid Sedan",
  regNo: "1PX 1ZR",
  maintenanceType: "General Maintenance Every 5000km",
  maintenanceTypeInterval: "Every 5000km",
  odometerAtDue: "",
  currentOdometer: "4500",
  vehicleCurrentRenter: "Not Assigned",
  vehicleCurrentLocation: "Office",
  description: "",
  branch: "Somerton",
  mechanicAssigned: ["<PERSON>", "<PERSON>"],
  estimatedEndDate: "05/26/2025",
  actualEndDate: "05/31/2025",
  status: "Pending",
  vehicleStatus: "Dirty",
  comment: "",
  mechanicNoteStatus: "Pending",
  mechanicNoteComment: "",
};

export function useMaintainanceServiceEdit() {
  const [formData, setFormData] = useState<MaintainanceServiceEditData>(MOCK_DATA);

  const handleInputChange = (field: keyof MaintainanceServiceEditData, value: any) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  return { formData, setFormData, handleInputChange };
}