import { InteractionData } from '../type/reception-type';
import {
  reportingTypes,
  newInquiryTypes,
  existingInquiryTypes,
  serviceDueTypes,
  incidentReportTypes
} from '../common/mockData';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';

export const handleInputChange = (
  field: keyof InteractionData,
  value: string | boolean,
  setFormData: React.Dispatch<React.SetStateAction<InteractionData>>
) => {
  setFormData(prev => {
    const updated = { ...prev, [field]: value };
    
    // Reset conditional fields when purpose changes
    if (field === 'purpose') {
      delete updated.typeOfReporting;
      delete updated.typeOfNewInquiry;
      delete updated.typeOfExistingInquiry;
      delete updated.typeOfServiceDue;
      delete updated.typeOfIncidentReport;
    }
    
    // Reset staff member when status is not Escalate
    if (field === 'status' && value !== 'Escalate') {
      delete updated.staffMember;
    }

    return updated;
  });
};

export const handleSubmit = (formData: InteractionData) => {
  console.log('Form submitted:', formData);
  // Handle form submission logic here
};

export const handleCancel = () => {
  console.log('Form cancelled');
  // Handle cancel logic here
};

export const renderConditionalDropdown = (
  formData: InteractionData,
  handleInputChange: (field: keyof InteractionData, value: string) => void
) => {
  switch (formData.purpose) {
    case 'Service Reporting':
      return (
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Type of Reporting
          </label>
          <Select 
            value={formData.typeOfReporting || ''} 
            onValueChange={(value) => handleInputChange('typeOfReporting', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {reportingTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );

    case 'New Inquiry':
      return (
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Type of New Inquiries
          </label>
          <Select 
            value={formData.typeOfNewInquiry || ''} 
            onValueChange={(value) => handleInputChange('typeOfNewInquiry', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {newInquiryTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );

    case 'Existing Inquiry':
      return (
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Type of Existing Inquiries
          </label>
          <Select 
            value={formData.typeOfExistingInquiry || ''} 
            onValueChange={(value) => handleInputChange('typeOfExistingInquiry', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {existingInquiryTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );

    case 'Service Due':
      return (
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Type of Service Due
          </label>
          <Select 
            value={formData.typeOfServiceDue || ''} 
            onValueChange={(value) => handleInputChange('typeOfServiceDue', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {serviceDueTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );

    case 'Incident Reporting':
      return (
        <div className="w-full">
          <label className="block text-sm font-medium text-gray-700 mb-2">
            Type of Incident Reports
          </label>
          <Select 
            value={formData.typeOfIncidentReport || ''} 
            onValueChange={(value) => handleInputChange('typeOfIncidentReport', value)}
          >
            <SelectTrigger className="w-full">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {incidentReportTypes.map((type) => (
                <SelectItem key={type} value={type}>
                  {type}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
      );

    default:
      return null;
  }
};