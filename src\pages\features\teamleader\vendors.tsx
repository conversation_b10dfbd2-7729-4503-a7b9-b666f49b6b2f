import React, { useState } from 'react';
import { Search, Edit, Users2, ChevronDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { vendorData } from './common/mockData';
import { filterVendors, handleIdClick } from './hook/usevendors';

export const VendorsPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);
  const navigate = useNavigate();

  const filteredVendors = filterVendors(vendorData, searchTerm);
  const totalEntries = filteredVendors.length;

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center mb-4 sm:mb-6">
            <Users2 className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
            <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Vendors</h1>
          </div>
          <Button
            onClick={() => navigate('/teamleader/vendor-add')}
            className="px-4 py-2 bg-[#330101] text-white rounded transition-colors w-full sm:w-auto"
          >
            Add New Vendor
          </Button>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          <div className="relative">
            <button className="flex items-center gap-2 px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 text-sm">
              Filter
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
          <div className="relative flex-1 order-3 sm:order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing a name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="hidden md:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vendor Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Phone Number</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Email</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Address</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {filteredVendors.map((vendor) => (
                <TableRow key={vendor.id} className="hover:bg-gray-50">
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vendor.vendorName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vendor.phone}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vendor.email}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vendor.address}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    <div className="flex items-center">
                      <Button
                        onClick={() => handleIdClick(vendor.id, navigate)}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={Math.ceil(totalEntries / entriesPerPage)}
        totalRecords={totalEntries}
        recordsPerPage={entriesPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setEntriesPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-4 sm:mt-6"
      />
    </div>
  );
};