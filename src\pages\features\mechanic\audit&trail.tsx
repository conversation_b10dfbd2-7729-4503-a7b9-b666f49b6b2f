import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "../../../components/ui/table";
import { Pagination } from "../../../components/layout/Pagination";
import { Input } from "../../../components/ui/input";
import { Button } from "../../../components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../../../components/ui/select";
import { Search, FilePenLine, ClipboardPenLine, Edit } from "lucide-react";
import { useAuditTrail } from "./hook/useaudit&trail";
import { useNavigate } from "react-router-dom";

export function AuditTrail() {
  const {
    currentPage,
    pageSize,
    search,
    setSearch,
    paginated,
    totalPages,
    totalRecords,
    setCurrentPage,
    setPageSize,
    allServiceTypes,
    filterType,
    setFilterType,
  } = useAuditTrail();

  const navigate = useNavigate();

  return (
    <div className="pt-0 px-4 pb-8 w-full bg-white min-h-screen">
      {/* Title */}
      <div className="flex items-center mb-6 sm:mb-8 justify-between">
        <div className="flex items-center">
          {/* Removed the outer round border and kept only the icon */}
          <ClipboardPenLine className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3" style={{ color: "#330101" }} />
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
            Audit & Trail
          </h1>
        </div>
        <Button
          className="bg-[#330101] hover:bg-[#ffde5c] text-white px-8 py-2 w-32 flex items-center justify-center"
          onClick={() => navigate("/mechanic/audit-trail-add")}
        >
          Add
        </Button>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterType}
            onValueChange={(value) => {
              setFilterType(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none text-sm sm:text-base">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {allServiceTypes
                .filter((type) => type !== "All")
                .map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing a name...."
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
              setCurrentPage(1);
            }}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Table */}
      <div className="w-full rounded-md border overflow-x-auto shadow-sm">
        <Table className="min-w-[900px] w-full">
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                Rego
              </TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                Service Type
              </TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                Timestamp
              </TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                Action
              </TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {paginated.length > 0 ? (
              paginated.slice(0, 3).map((row) => (
                <TableRow key={row.rego + row.timestamp}>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                    {row.rego}
                  </TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                    {row.serviceType}
                  </TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                    {row.timestamp}
                  </TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left">
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => navigate(`/mechanic/audit-trail-edit/${row.timestamp}`)}
                      className="text-gray-600 hover:text-gray-800 transition-colors"
                      title="Edit"
                    >
                      <Edit className="w-4 h-4 mr-1" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell
                  colSpan={4}
                  className="text-center py-8 text-gray-500"
                >
                  No audit trail data available
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="mt-4 sm:mt-6">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={pageSize}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setPageSize(records);
            setCurrentPage(1);
          }}
          className="text-sm sm:text-base"
        />
      </div>
    </div>
  );
}
