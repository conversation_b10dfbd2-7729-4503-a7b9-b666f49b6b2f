import React from 'react';
import { ArrowLeft, FileText } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate, useParams } from 'react-router-dom';
import { bookingsData } from './common/mockData';
import { handleBack } from './hook/useEditCustomer';
import { DocumentData } from './type/reception-type';

const DocumentsInterface: React.FC = () => {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();

  const document = bookingsData.find((booking) => booking.id === id || booking.id === `#${id}`);

  // Handle case where document is not found
  if (!document) {
    return (
      <div className="min-h-screen p-6">
        <div className="max-w-4xl mx-auto">
          <Button
            className="bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2"
            size="sm"
            onClick={() => handleBack(navigate)}
          >
            <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
            Go Back
          </Button>
          <div className="mt-6 text-center text-gray-900 text-lg">Customer documents not found</div>
        </div>
      </div>
    );
  }

  const documentData: DocumentData = {
    id: document.id,
    customerName: document.customerName,
    frontLicenseImage: document.frontLicenseImage,
    backLicenseImage: document.backLicenseImage,
  };

  return (
    <div className="min-h-screen">
      <div className="flex items-center mb-4 sm:mb-6">
        <Button
          className="bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2"
          size="sm"
          onClick={() => handleBack(navigate)}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>

      {/* Main Content */}
      <div className="p-5">
        {/* Documents Title */}
        <div className="flex items-center gap-2 mb-8">
          <FileText size={20} className="text-gray-700" />
          <h1 className="text-xl font-semibold text-gray-800">Documents</h1>
        </div>

        {/* Documents Table */}
        <div className="bg-white rounded-lg border border-gray-200 overflow-hidden shadow-sm">
          <table className="w-full">
            <thead className="bg-gray-50 border-b border-gray-200">
              <tr>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700">
                  Customer Name
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700">
                  Driving License<br />Front View
                </th>
                <th className="px-4 py-4 text-center text-sm font-semibold text-gray-700">
                  Driving License<br />Back View
                </th>
              </tr>
            </thead>
            <tbody>
              <tr className="border-b border-gray-100 last:border-b-0">
                <td className="px-4 py-5 text-center">
                  <div className="text-sm text-gray-600 font-medium">{documentData.customerName}</div>
                </td>
                <td className="px-4 py-5 text-center">
                  {documentData.frontLicenseImage ? (
                    <img 
                      src={documentData.frontLicenseImage} 
                      alt="Driving License Front"
                      className="w-30 h-auto rounded border shadow-sm mx-auto"
                    />
                  ) : (
                    <div className="w-30 h-20 bg-gradient-to-br from-green-500 to-green-600 rounded flex items-center justify-center text-white text-xs text-center shadow-sm mx-auto">
                      DRIVING LICENSE<br />FRONT VIEW
                    </div>
                  )}
                </td>
                <td className="px-4 py-5 text-center">
                  {documentData.backLicenseImage ? (
                    <img 
                      src={documentData.backLicenseImage} 
                      alt="Driving License Back"
                      className="w-30 h-auto rounded border shadow-sm mx-auto"
                    />
                  ) : (
                    <div className="w-30 h-20 bg-gradient-to-br from-gray-600 to-gray-700 rounded flex items-center justify-center text-white text-xs text-center shadow-sm mx-auto">
                      DRIVING LICENSE<br />BACK VIEW
                    </div>
                  )}
                </td>
              </tr>
            </tbody>
          </table>
        </div>
      </div>
    </div>
  );
};

export default DocumentsInterface;