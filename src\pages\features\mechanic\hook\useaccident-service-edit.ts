import { useState } from "react";
import { MechanicAccidentServiceData } from "../type/mechanictype";

const MOCK_DATA: MechanicAccidentServiceData = {
  vehicleID: "V123456",
  model: "Isuzu",
  regNo: "AFS 009",
  description: "I was reversing out of the factory, and I accidentally reversed into our work van parked on the street.",
  insuranceStatus: "Initial Review",
  insuranceClaimNumber: "",
  estimatedEndDate: "2025-05-26",
  actualEndDate: "2025-05-31",
  status: "Pending",
  images: {
    interiorImages: ["interior.jpg"],
    exteriorImages: ["exterior.jpg"],
    leftSideDoors: ["left.jpg"],
    rightSideDoors: ["right.jpg"],
    frontSideImages: ["front.jpg"],
    backSideImages: ["back.jpg"],
    sideMirrors: ["mirrors.jpg"],
    other: ["other.jpg"],
  },
  branch: "Somerton",
  mechanicAssigned: ["<PERSON>", "<PERSON>"],
  comment: "Type here...",
  mechanicNoteStatus: "Pending",
  mechanicNoteComment: "Needs further inspection.",
};

export function useAccidentServiceEdit() {
  const [formData, setFormData] = useState<MechanicAccidentServiceData>(MOCK_DATA);

  const handleInputChange = (field: keyof MechanicAccidentServiceData, value: any) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
  };

  const handleImageChange = (category: keyof MechanicAccidentServiceData["images"], files: FileList | null) => {
    if (files) {
      setFormData((prev) => ({
        ...prev,
        images: { ...prev.images, [category]: Array.from(files).map((f) => f.name) },
      }));
    }
  };

  return { formData, setFormData, handleInputChange, handleImageChange };
}