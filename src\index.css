@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 47.4% 11.2%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 47.4% 11.2%;
    --primary: 46 100% 50%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --accent: 210 40% 96.1%;
    --accent-foreground: 222.2 47.4% 11.2%;
    --destructive: 0 100% 50%;
    --destructive-foreground: 210 40% 98%;
    --ring: 46 100% 50%;
    --radius: 0.5rem;
  }
  :root[class~="dark"] {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --muted: 223 47% 11%;
    --muted-foreground: 215.4 16.3% 56.9%;
    --accent: 216 34% 17%;
    --accent-foreground: 210 40% 98%;
    --popover: 224 71% 4%;
    --popover-foreground: 215 20.2% 65.1%;
    --border: 216 34% 17%;
    --input: 216 34% 17%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --primary: 46 100% 50%;
    --primary-foreground: 222.2 47.4% 1.2%;
    --secondary: 222.2 47.4% 11.2%;
    --secondary-foreground: 210 40% 98%;
    --destructive: 0 63% 31%;
    --destructive-foreground: 210 40% 98%;
    --ring: 216 34% 17%;
    --radius: 0.5rem;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
    font-feature-settings: "rlig" 1, "calt" 1;
  }
}

.auth-container {
  @apply flex min-h-screen w-full flex-col items-center justify-center bg-white p-4;
}

.auth-form {
  @apply w-full max-w-md space-y-6;
}

.auth-logo {
  @apply mx-auto mb-8 h-24 w-auto;
}

.auth-heading {
  @apply text-center text-3xl font-bold;
}

.auth-subheading {
  @apply text-center text-xl mb-8;
}

.auth-button {
  @apply w-full bg-gold-light hover:bg-gold text-earth-dark font-medium py-3 rounded-md transition-colors;
}

.auth-link {
  @apply text-gold-dark hover:text-gold font-medium;
}

.auth-back-button {
  @apply absolute top-8 left-8 flex items-center justify-center w-10 h-10 rounded-full bg-gold-light text-earth-dark hover:bg-gold transition-colors;
}

.otp-input-container {
  @apply flex gap-2 justify-center my-8;
}

.otp-input {
  @apply w-14 h-14 text-2xl text-center border-2 border-gold-light rounded-md focus:border-gold focus:outline-none;
}