import React from 'react';
import { ChevronLeft, ChevronRight, MoreHorizontal } from 'lucide-react';
import { Button } from '../ui/button';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '../ui/select';

interface PaginationProps {
  currentPage: number;
  totalPages: number;
  totalRecords: number;
  recordsPerPage: number;
  onPageChange: (page: number) => void;
  onRecordsPerPageChange: (records: number) => void;
  recordsPerPageOptions?: number[];
  showRecordsSelector?: boolean;
  showInfo?: boolean;
  maxVisiblePages?: number;
  className?: string;
}

export function Pagination({
  currentPage,
  totalPages,
  totalRecords,
  recordsPerPage,
  onPageChange,
  onRecordsPerPageChange,
  recordsPerPageOptions = [5, 10, 20, 50],
  showRecordsSelector = true,
  showInfo = true,
  maxVisiblePages = 6,
  className = ''
}: PaginationProps) {
  const startRecord = totalRecords === 0 ? 0 : (currentPage - 1) * recordsPerPage + 1;
  const endRecord = Math.min(currentPage * recordsPerPage, totalRecords);

  // Generate page numbers to display
  const generatePageNumbers = () => {
    const pages: (number | string)[] = [];
    
    if (totalPages <= maxVisiblePages) {
      // If total pages is less than or equal to max visible, show all pages
      for (let i = 1; i <= totalPages; i++) {
        pages.push(i);
      }
    } else {
      // Complex pagination logic
      const halfVisible = Math.floor(maxVisiblePages / 2);
      let startPage = Math.max(1, currentPage - halfVisible);
      const endPage = Math.min(totalPages, startPage + maxVisiblePages - 1);
      
      // Adjust if we're near the end
      if (endPage - startPage + 1 < maxVisiblePages) {
        startPage = Math.max(1, endPage - maxVisiblePages + 1);
      }
      
      // Add first page and ellipsis if needed
      if (startPage > 1) {
        pages.push(1);
        if (startPage > 2) {
          pages.push('...');
        }
      }
      
      // Add visible pages
      for (let i = startPage; i <= endPage; i++) {
        pages.push(i);
      }
      
      // Add ellipsis and last page if needed
      if (endPage < totalPages) {
        if (endPage < totalPages - 1) {
          pages.push('...');
        }
        pages.push(totalPages);
      }
    }
    
    return pages;
  };

  const pageNumbers = generatePageNumbers();

  const handlePrevious = () => {
    if (currentPage > 1) {
      onPageChange(currentPage - 1);
    }
  };

  const handleNext = () => {
    if (currentPage < totalPages) {
      onPageChange(currentPage + 1);
    }
  };

  const handlePageClick = (page: number | string) => {
    if (typeof page === 'number') {
      onPageChange(page);
    }
  };

  if (totalPages <= 1 && !showRecordsSelector) {
    return null;
  }

  return (
    <div className={`flex items-center justify-between flex-wrap gap-4 ${className}`}>
      {/* Records per page selector */}
      {showRecordsSelector && (
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-700">Show</span>
          <Select
            value={String(recordsPerPage)}
            onValueChange={(value) => onRecordsPerPageChange(Number(value))}
          >
            <SelectTrigger className="w-[60px] border border-gray-300 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {recordsPerPageOptions.map((option) => (
                <SelectItem key={option} value={String(option)}>
                  {option}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
          <span className="text-sm text-gray-700">Records</span>
        </div>
      )}

      {/* Info and pagination controls */}
      <div className="flex items-center space-x-4 flex-wrap">
        {/* Info text */}
        {showInfo && (
          <span className="text-sm text-gray-700 whitespace-nowrap">
            Showing {startRecord} to {endRecord} of {totalRecords} entries
          </span>
        )}
        
        {/* Pagination controls - Always show if totalPages > 1 */}
        <div className="flex items-center space-x-1">
          {/* Previous button */}
          <Button
            onClick={handlePrevious}
            disabled={currentPage === 1}
            className={`
              flex items-center px-3 py-2 transition-colors
              ${currentPage === 1
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gray-900'
              }
            `}
          >
            <ChevronLeft className="w-4 h-4 mr-1" />
            Previous
          </Button>
          
          {/* Page numbers - Only show if more than 1 page */}
          {totalPages > 1 && (
            <div className="flex items-center space-x-1">
              {pageNumbers.map((page, index) => (
                <React.Fragment key={index}>
                  {page === '...' ? (
                    <span className="flex items-center justify-center w-10 h-10 text-gray-500">
                      <MoreHorizontal className="w-4 h-4" />
                    </span>
                  ) : (
                    <Button
                      onClick={() => handlePageClick(page)}
                      className={`
                        w-10 h-10 transition-colors
                        ${currentPage === page
                          ? 'bg-[#330101] text-white '
                          : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gray-900'
                        }
                      `}
                    >
                      {page}
                    </Button>
                  )}
                </React.Fragment>
              ))}
            </div>
          )}
          
          {/* Next button */}
          <Button
            onClick={handleNext}
            disabled={currentPage === totalPages}
            className={`
              flex items-center px-3 py-2 transition-colors
              ${currentPage === totalPages
                ? 'bg-gray-100 text-gray-400 cursor-not-allowed'
                : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50 hover:text-gray-900'
              }
            `}
          >
            Next
            <ChevronRight className="w-4 h-4 ml-1" />
          </Button>
        </div>
      </div>
    </div>
  );
}