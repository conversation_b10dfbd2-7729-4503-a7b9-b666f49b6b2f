import { NavigateFunction } from 'react-router-dom';
import { PartsFormData, Part } from '../type/teamleadertype';

export const handleInputChange = (
  field: keyof PartsFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<PartsFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};

export const handlePartChange = (
  id: number,
  field: keyof Part,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<PartsFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    parts: prev.parts.map(part =>
      part.id === id ? { ...part, [field]: value } : part
    )
  }));
};

export const addPart = (
  setFormData: React.Dispatch<React.SetStateAction<PartsFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    parts: [...prev.parts, { id: Date.now(), partName: '', quantity: '', comment: '' }]
  }));
};

export const removePart = (
  id: number,
  setFormData: React.Dispatch<React.SetStateAction<PartsFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    parts: prev.parts.filter(part => part.id !== id)
  }));
};

export const handleSave = (navigate: NavigateFunction): void => {
  navigate('/teamleader/parts');
};