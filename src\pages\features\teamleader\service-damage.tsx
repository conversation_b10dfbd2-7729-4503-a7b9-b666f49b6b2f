import React, { useState } from 'react';
import { Search, Eye, Edit } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';

export function ServiceDamage() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('Damage');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data for the table
  const accidentData = [
    {
      vehicleId: 'VID0010',
      vehicle: 'Atom - 1PX 5ZP',
      insuranceClaimNumber: '1234569',
      actualDate:'22/07/2025',
      estimatedDate:'21/07/2025',
      insuranceStatus: 'Accepted',
      mechanicAssigned: 'Anne Katrina',
      status: 'Inprogress'
    },
    {
      vehicleId: 'VID0011',
      vehicle: 'BMW 330 d Coupe - 191',
      insuranceClaimNumber: '-',
      actualDate:'08/06/2025',
      estimatedDate:'08/06/2025',
      insuranceStatus: 'Declined',
      mechanicAssigned: '-',
      status: 'Done'
    },
    {
      vehicleId: 'VID0012',
      vehicle: 'Isuzu - AAB 004',
      insuranceClaimNumber: '-',
      actualDate:'10/06/2025',
      estimatedDate:'12/06/2025',
      insuranceStatus: 'Initial Review',
      mechanicAssigned: 'Mike Smith',
      status: 'Pending'
    },
    
  ];

  // Filter data based on search term
  const filteredData = accidentData.filter((item) =>
    (item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.insuranceClaimNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.actualDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.estimatedDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.status.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.mechanicAssigned.toLowerCase().includes(searchTerm.toLowerCase()))
  );

  const handleVehicleIdClick = (vehicleId: string) => {
    navigate(`/teamleader/service-editDamage/${vehicleId}`);
  };

  // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Get status badge color
  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'initial review':
        return 'bg-yellow-500 text-white';
      case 'accepted':
        return 'bg-green-500 text-white';
      case 'declined':
        return 'bg-red-500 text-white';
      case 'done':
        return 'bg-green-500 text-white';
      case 'pending':
        return 'bg-gray-500 text-white';
      case 'inprogress':
        return 'bg-blue-500 text-white';
    }
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Damage</h1>
      </div>

      {/* Tab Bar */}
      <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
        {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
          <Button
            key={tab}
            variant={activeTab === tab ? 'default' : 'outline'}
            onClick={() => {
                    if (tab === 'All') {
                      navigate('/teamleader/service-management');
                    } else {
                      setActiveTab(tab);
                    }
                    if (tab === 'General Maintenance') {
                      navigate('/teamleader/maintenance');
                    } else {
                      setActiveTab(tab);
                    }
                    if (tab === 'Breakdowns') {
                      navigate('/teamleader/service-breakdown');
                    } else {
                      setActiveTab(tab);
                    }
                    if (tab === 'Accident') {
                      navigate('/teamleader/service-accident');
                    } else {
                      setActiveTab(tab);
                    }
                  }}
            className="text-sm md:text-base"
          >
            {tab}
          </Button>
        ))}
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Table View */}
      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">

              <TableHead>Vehicle ID</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Insurance Claim Number</TableHead>
              <TableHead>Estimated End Date</TableHead>
              <TableHead>Actual End Date</TableHead>
              <TableHead>Insurance Status</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Mechanic Assigned</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item, index) => (
              <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                <TableCell>{item.vehicleId}</TableCell>
                <TableCell>{item.vehicle}</TableCell>
                <TableCell>{item.insuranceClaimNumber}</TableCell>
                <TableCell>{item.estimatedDate}</TableCell>
                <TableCell>{item.actualDate}</TableCell>
                <TableCell>
                  <span className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.insuranceStatus)}`}>
                    {item.insuranceStatus}
                  </span>
                </TableCell>
                <TableCell>
                  <span className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </TableCell>
                <TableCell>{item.mechanicAssigned}</TableCell>
                <TableCell>


                <Button variant="ghost" className="text-gray-600 hover:text-gray-800"
                   onClick={() => handleVehicleIdClick(item.vehicleId)}>

                    <Edit className="w-4 h-4" />

                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-6"
      />
    </div>
  );
}