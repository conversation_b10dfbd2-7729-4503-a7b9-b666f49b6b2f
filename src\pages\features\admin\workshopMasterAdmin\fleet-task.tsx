import React, { useState, useMemo, useEffect } from 'react';
import { Search, FileWarning, Eye } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { FleetTaskData } from '../../teamleader/type/teamleadertype';

export function FleetTaskPage() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(10);
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [taskData, setTaskData] = useState<FleetTaskData[]>([]);

  // Initial data (used only if localStorage is empty)
  const initialData: FleetTaskData[] = [
    {
      id: '1',
      taskId: 'TO001',
      rego: '1PX 12R',
      lastService: 'Oil Change',
      lastServiceDate: '2025-07-10',
      status: 'Rentable'
    },
    {
      id: '2',
      taskId: 'TO002',
      rego: 'PCR 455',
      lastService: 'Brake Service',
      lastServiceDate: '2025-07-08',
      status: 'Pending'
    },
    {
      id: '3',
      taskId: 'TO003',
      rego: '1PY 2TR',
      lastService: 'Full Service',
      lastServiceDate: '2025-07-06',
      status: 'Pending'
    },
    {
      id: '4',
      taskId: 'TO004',
      rego: 'ASR 321',
      lastService: 'Tire Rotation',
      lastServiceDate: '2025-07-09',
      status: 'Rentable'
    },
    {
      id: '5',
      taskId: 'TO005',
      rego: 'QBV 233',
      lastService: 'Engine Check',
      lastServiceDate: '2025-07-05',
      status: 'Pending'
    },
    {
      id: '6',
      taskId: 'TO006',
      rego: 'MNO 789',
      lastService: 'Battery Check',
      lastServiceDate: '2025-07-07',
      status: 'Pending'
    },
    {
      id: '7',
      taskId: 'TO007',
      rego: 'XYZ 456',
      lastService: 'Air Filter',
      lastServiceDate: '2025-07-04',
      status: 'Pending'
    }
  ];

  // Load data from localStorage on component mount
  useEffect(() => {
    try {
      const savedData = localStorage.getItem('fleetTaskData');
      if (savedData) {
        setTaskData(JSON.parse(savedData));
      } else {
        // Initialize localStorage with initialData if not present
        localStorage.setItem('fleetTaskData', JSON.stringify(initialData));
        setTaskData(initialData);
      }
    } catch (error) {
      console.error('Error loading data from localStorage:', error);
      setTaskData(initialData); // Fallback to initialData on error
    }
  }, []);

  // Memoized filtered data to prevent unnecessary recalculations
  const filteredData = useMemo(() => {
    const lowerSearchTerm = searchTerm.toLowerCase();
    return taskData.filter((task) =>
      (task.rego.toLowerCase().includes(lowerSearchTerm) ||
        task.lastService.toLowerCase().includes(lowerSearchTerm) ||
        task.lastServiceDate.toLowerCase().includes(lowerSearchTerm)) &&
      (filterStatus === 'All' || task.status === filterStatus)
    );
  }, [searchTerm, filterStatus, taskData]);

  // Pagination calculations
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  const getStatusColor = (status: string): string => {
    switch (status.toLowerCase()) {
      case 'rentable':
        return 'bg-green-500 text-white';
      case 'pending':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-200 text-gray-700';
    }
  };

  const handleViewClick = (taskId: string) => {
    try {
      navigate(`/admin/workshopMasterAdmin/fleet-task-view/${taskId}`);
    } catch (error) {
      console.error('Navigation error:', error);
    }
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <FileWarning className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Task Overview</h1>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="h-12 border border-gray-200">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Rentable">Rentable</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Search by rego, service, or date..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="block md:hidden space-y-4 mb-4">
        {filteredData.length === 0 ? (
          <div className="text-center text-gray-500 py-8">No tasks found matching the current filters</div>
        ) : (
          filteredData.map((task) => (
            <Card
              key={task.id}
              className="bg-white rounded-lg shadow-md border border-gray-200 p-4 mb-4 hover:shadow-lg transition-shadow relative"
            >
              {/* Status badge - top right, fixed width */}
              <div className="absolute top-4 right-4 flex flex-col items-end gap-2">
                <span
                  className={`inline-flex items-center justify-center rounded px-3 py-1 text-xs font-semibold w-[110px] text-center ${getStatusColor(
                    task.status
                  )} whitespace-nowrap`}
                >
                  {task.status}
                </span>
              </div>
              {/* Card content */}
              <div className="mb-3 pr-[120px]">
                <span className="text-sm font-medium text-gray-700 break-words whitespace-pre-line">
                  {task.rego}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Last Service</div>
                  <div className="flex items-center text-sm">{task.lastService}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Last Service Date</div>
                  <div className="flex items-center text-sm">
                    {new Date(task.lastServiceDate).toLocaleDateString()}
                  </div>
                </div>
              </div>
              {/* Action button at the bottom */}
              <div className="flex justify-end mt-2">
                <Button
                  onClick={() => handleViewClick(task.taskId)}
                  variant="ghost"
                  className="text-gray-600 hover:text-gray-800 p-1"
                >
                  <Eye className="w-4 h-4" />
                  <span className="ml-2 text-xs">View</span>
                </Button>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Rego</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Service</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Service Date</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Status</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Action</TableHead>
            </TableRow>
          </TableHeader>

          <TableBody>
            {currentData.length > 0 ? (
              currentData.map((task) => (
                <TableRow key={task.id}>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{task.rego}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{task.lastService}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    {new Date(task.lastServiceDate).toLocaleDateString()}
                  </TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    <span className={`inline-flex items-center justify-center rounded px-3 py-1 text-xs font-semibold w-[110px] text-center ${getStatusColor(task.status)} whitespace-nowrap`}>
                      {task.status}
                    </span>
                  </TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    <Button
                      onClick={() => handleViewClick(task.taskId)}
                      variant="ghost"
                      className="text-gray-600 hover:text-gray-800 p-1 lg:p-2"
                    >
                      <Eye className="w-3 h-3 lg:w-4 lg:h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))
            ) : (
              <TableRow>
                <TableCell colSpan={5} className="text-center py-4">
                  No tasks found matching the current filters
                </TableCell>
              </TableRow>
            )}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <div className="mt-6 hidden md:block">
        {totalEntries > 0 && (
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalEntries}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4"
          />
        )}
      </div>
    </div>
  );
}