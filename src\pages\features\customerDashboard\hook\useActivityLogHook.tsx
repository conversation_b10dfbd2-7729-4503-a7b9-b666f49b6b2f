import { useState } from 'react';
import { activityEntries } from '../common/mockdata';


export const useActivityLogHook = () => {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const isDateInRange = (activityDate: string, filter: string): boolean => {
    const today = new Date();
    const activityEntryDate = new Date(activityDate);
    
    switch (filter) {
      case 'Today':
        return activityEntryDate.toDateString() === today.toDateString();
      
      case 'This Week':
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        weekStart.setHours(0, 0, 0, 0);
        
        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);
        
        return activityEntryDate >= weekStart && activityEntryDate <= weekEnd;
      
      case 'This Month':
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);
        
        return activityEntryDate >= monthStart && activityEntryDate <= monthEnd;
      
      case 'Last 30 Days':
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        thirtyDaysAgo.setHours(0, 0, 0, 0);
        
        return activityEntryDate >= thirtyDaysAgo && activityEntryDate <= today;
      
      default:
        return true;
    }
  };

  const filteredActivityEntries = activityEntries.filter(activityEntry => {
    const matchesSearch = activityEntry.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
                         activityEntry.id.toString().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || isDateInRange(activityEntry.date, filterStatus);
    return matchesSearch && matchesFilter;
  });

  const totalRecords = filteredActivityEntries.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentActivityEntries = filteredActivityEntries.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const formatDate = (dateString: string): string => {
    const date = new Date(dateString);
    const today = new Date();
    const yesterday = new Date(today);
    yesterday.setDate(today.getDate() - 1);
    const tomorrow = new Date(today);
    tomorrow.setDate(today.getDate() + 1);

    if (date.toDateString() === today.toDateString()) {
      return 'Today';
    } else if (date.toDateString() === yesterday.toDateString()) {
      return 'Yesterday';
    } else if (date.toDateString() === tomorrow.toDateString()) {
      return 'Tomorrow';
    } else {
      return date.toLocaleDateString('en-GB', {
        day: '2-digit',
        month: '2-digit',
        year: 'numeric'
      });
    }
  };

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    filteredActivityEntries,
    totalRecords,
    totalPages,
    currentActivityEntries,
    formatDate,
  };
};