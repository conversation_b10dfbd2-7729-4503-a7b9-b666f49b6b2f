import { useState, useMemo } from 'react'
import { useNavigate, useLocation, useParams } from 'react-router-dom'
import { FleetTaskData } from '../type/teamleadertype'

export function useFleetTaskOverviewView() {
  const navigate = useNavigate()
  const location = useLocation()
  const params = useParams()
  const [isConfirmModalOpen, setIsConfirmModalOpen] = useState(false)
  
  // Get task ID from URL params, fallback to navigation state, then to '1'
  const formattedTaskId = params.taskId || location.state?.formattedTaskId || 'TO001'
  
  // If we have a formatted task ID from URL, find the corresponding internal ID
  const getInternalTaskId = (): string => {
    if (location.state?.taskId) {
      return location.state.taskId
    }
    
    // If no state, find internal ID from formatted task ID
    const allTasks = getAllTasks()
    const task = allTasks.find(t => t.taskId === formattedTaskId)
    return task?.id || '1'
  }
  
  const internalTaskId = getInternalTaskId()
  
  // Function to get all tasks from localStorage or return default data
  const getAllTasks = (): FleetTaskData[] => {
    // Try to get data from localStorage first, fallback to default data
    const savedData = localStorage.getItem('fleetTaskData')
    if (savedData) {
      return JSON.parse(savedData)
    }
    
    // Default fallback data
    return [
      {
        id: '1',
        taskId: 'TO001',
        rego: '1PX 12R',
        lastService: 'Oil Change',
        lastServiceDate: '2025-07-10',
        status: 'Pending'
      },
      {
        id: '2',
        taskId: 'TO002',
        rego: 'PCR 455',
        lastService: 'Brake Service',
        lastServiceDate: '2025-07-08',
        status: 'Pending'
      },
      {
        id: '3',
        taskId: 'TO003',
        rego: '1PY 2TR',
        lastService: 'Full Service',
        lastServiceDate: '2025-07-06',
        status: 'Pending'
      },
      {
        id: '4',
        taskId: 'TO004',
        rego: 'ASR 321',
        lastService: 'Tire Rotation',
        lastServiceDate: '2025-07-09',
        status: 'Completed'
      },
      {
        id: '5',
        taskId: 'TO005',
        rego: 'QBV 233',
        lastService: 'Engine Check',
        lastServiceDate: '2025-07-05',
        status: 'Pending'
      },
      {
        id: '6',
        taskId: 'TO006',
        rego: 'MNO 789',
        lastService: 'Battery Check',
        lastServiceDate: '2025-07-07',
        status: 'Pending'
      },
      {
        id: '7',
        taskId: 'TO007',
        rego: 'XYZ 456',
        lastService: 'Air Filter',
        lastServiceDate: '2025-07-04',
        status: 'Pending'
      }
    ]
  }
  
  // Get all tasks and find the specific task using useMemo for performance
  const taskDetails = useMemo(() => {
    const allTasks = getAllTasks()
    // Try to find by formattedTaskId first, then by internal ID
    return allTasks.find((task: FleetTaskData) => 
      task.taskId === formattedTaskId || task.id === internalTaskId
    ) || allTasks[0]
  }, [formattedTaskId, internalTaskId])

  const handleApprove = () => {
    // Open confirmation modal instead of navigating to separate page
    setIsConfirmModalOpen(true)
  }

  const handleConfirmYes = () => {
    // Handle approval logic here
    console.log('Task approved for ID:', formattedTaskId)
    // Navigate back to task overview after approval with status update
    navigate(`/teamleader/taskoverview?taskId=${formattedTaskId}`, { 
      state: { 
        taskId: internalTaskId,
        formattedTaskId: formattedTaskId,
        newStatus: 'Completed' 
      } 
    })
  }

  const handleConfirmNo = () => {
    // Handle rejection logic here - set status to Pending when user clicks No
    console.log('Task rejected for ID:', formattedTaskId)
    // Navigate back to task overview after rejection with status update to Pending
    navigate(`/teamleader/taskoverview?taskId=${formattedTaskId}`, { 
      state: { 
        taskId: internalTaskId,
        formattedTaskId: formattedTaskId,
        newStatus: 'Pending' 
      } 
    })
  }

  const handleCloseModal = () => {
    // Close the confirmation modal
    setIsConfirmModalOpen(false)
  }

  const handleBack = () => {
    // Navigate back to task overview page with task ID in URL
    navigate(`/teamleader/taskoverview?taskId=${formattedTaskId}`)
  }

  return {
    // Data
    formattedTaskId,
    internalTaskId,
    taskDetails,
    isConfirmModalOpen,
    
    // Functions
    handleApprove,
    handleConfirmYes,
    handleConfirmNo,
    handleCloseModal,
    handleBack,
  }
}
