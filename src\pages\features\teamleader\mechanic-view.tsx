import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { ArrowLeft, User, Phone, MapPin, Users, FileText, CreditCard, Calendar, Image } from 'lucide-react'
import { useMechanicView } from './hook/usemechanic-view'

export function MechanicViewPage() {
  const { mechanic, loading, error, handleBack } = useMechanicView()

  if (loading) {
    return (
      <div className="pt-0 px-4 pb-8 max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-8">
          <div className="text-lg">Loading mechanic details...</div>
        </div>
      </div>
    )
  }

  if (error || !mechanic) {
    return (
      <div className="pt-0 px-4 pb-8 max-w-4xl mx-auto">
        <div className="flex items-center justify-center py-8">
          <div className="text-center">
            <div className="text-lg text-red-600 mb-4">{error || 'Mechanic not found'}</div>
            <Button 
              className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
              size="sm"
              onClick={handleBack}
            >
              <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
              <span className="hidden md:inline">Go Back</span>
            </Button>
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="pt-0 px-4 pb-8 max-w-4xl mx-auto">
      {/* Header */}
      <div className="flex items-center justify-between mb-6 sm:mb-8">
        <div className="flex items-center">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
          <User className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark ml-4" />
          <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">
            View Mechanic Details
          </h1>
        </div>
      </div>

      {/* Basic Information Card */}
      <Card className="shadow-sm mb-6">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900">
            Basic Information
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* First Name and Last Name Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="firstName" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  First Name
                </Label>
                <Input
                  id="firstName"
                  type="text"
                  value={mechanic.firstName}
                  readOnly
                  className="mt-1 pr-10 bg-gray-50"
                />
                <User className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>

              <div className="relative">
                <Label htmlFor="lastName" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  Last Name
                </Label>
                <Input
                  id="lastName"
                  type="text"
                  value={mechanic.lastName}
                  readOnly
                  className="mt-1 pr-10 bg-gray-50"
                />
                <User className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>
            </div>

            {/* Phone Number and Address Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="phoneNumber" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  Phone Number
                </Label>
                <Input
                  id="phoneNumber"
                  type="tel"
                  value={mechanic.phoneNumber}
                  readOnly
                  className="mt-1 pr-10 bg-gray-50"
                />
                <Phone className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>

              <div className="relative">
                <Label htmlFor="address" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  Address
                </Label>
                <Input
                  id="address"
                  type="text"
                  value={mechanic.address}
                  readOnly
                  className="mt-1 pr-10 bg-gray-50"
                />
                <MapPin className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Emergency Contact Details Card */}
      <Card className="shadow-sm mb-6">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
            <Users className="w-5 h-5 mr-2 text-earth-dark" />
            Emergency Contact Details
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Emergency Contact Name */}
            <div className="relative">
              <Label htmlFor="emergencyName" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Emergency Contact Person Name
              </Label>
              <Input
                id="emergencyName"
                type="text"
                value={mechanic.emergencyContact.name}
                readOnly
                className="mt-1 pr-10 bg-gray-50"
              />
              <User className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
            </div>

            {/* Relationship and Phone Number Row */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Label htmlFor="relationship" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  Relationship
                </Label>
                <Input
                  id="relationship"
                  type="text"
                  value={mechanic.emergencyContact.relationship}
                  readOnly
                  className="mt-1 pr-10 bg-gray-50"
                />
                <Users className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>

              <div className="relative">
                <Label htmlFor="emergencyPhone" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  Phone Number
                </Label>
                <Input
                  id="emergencyPhone"
                  type="tel"
                  value={mechanic.emergencyContact.phoneNumber}
                  readOnly
                  className="mt-1 pr-10 bg-gray-50"
                />
                <Phone className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Proof of Verification Card */}
      <Card className="shadow-sm">
        <CardHeader>
          <CardTitle className="text-lg font-semibold text-gray-900 flex items-center">
            <FileText className="w-5 h-5 mr-2 text-earth-dark" />
            Proof of Verification
          </CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-6">
            {/* Verification Type */}
            <div className="relative">
              <Label htmlFor="verificationType" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Verification Type
              </Label>
              <Input
                id="verificationType"
                type="text"
                value={mechanic.verification.type === 'driverLicense' ? 'Driver License' : 'Passport'}
                readOnly
                className="mt-1 pr-10 bg-gray-50"
              />
              <FileText className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
            </div>

            {/* Driver License Details */}
            {mechanic.verification.type === 'driverLicense' && mechanic.verification.driverLicense && (
              <>
                {/* DL Number and Issue State Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="relative">
                    <Label htmlFor="dlNumber" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                      DL Number
                    </Label>
                    <Input
                      id="dlNumber"
                      type="text"
                      value={mechanic.verification.driverLicense.number}
                      readOnly
                      className="mt-1 pr-10 bg-gray-50"
                    />
                    <CreditCard className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                  </div>

                  <div className="relative">
                    <Label htmlFor="issueState" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                      Issue State
                    </Label>
                    <Input
                      id="issueState"
                      type="text"
                      value={mechanic.verification.driverLicense.issueState}
                      readOnly
                      className="mt-1 pr-10 bg-gray-50"
                    />
                    <MapPin className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                  </div>
                </div>

                {/* Issue Date and Expiry Date Row */}
                <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                  <div className="relative">
                    <Label htmlFor="issueDate" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                      Issue Date
                    </Label>
                    <Input
                      id="issueDate"
                      type="date"
                      value={mechanic.verification.driverLicense.issueDate}
                      readOnly
                      className="mt-1 pr-10 bg-gray-50"
                    />
                    <Calendar className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                  </div>

                  <div className="relative">
                    <Label htmlFor="expiryDate" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                      Expiry Date
                    </Label>
                    <Input
                      id="expiryDate"
                      type="date"
                      value={mechanic.verification.driverLicense.expiryDate}
                      readOnly
                      className="mt-1 pr-10 bg-gray-50"
                    />
                    <Calendar className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                  </div>
                </div>
              </>
            )}

            {/* Passport Details */}
            {mechanic.verification.type === 'passport' && mechanic.verification.passport && (
              <div className="relative">
                <Label htmlFor="passportNumber" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  Passport Number
                </Label>
                <Input
                  id="passportNumber"
                  type="text"
                  value={mechanic.verification.passport.number}
                  readOnly
                  className="mt-1 pr-10 bg-gray-50"
                />
                <CreditCard className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
              </div>
            )}

            {/* Document Images Section */}
            <div className="space-y-4">
              <div className="flex items-center gap-2 pt-4 border-t border-gray-200">
                <Image className="w-5 h-5 text-earth-dark" />
                <h3 className="text-base font-semibold text-gray-900">Document Images</h3>
              </div>
              
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {/* Front View */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Front View
                  </Label>
                  <div className="relative border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
                    <img
                      src={mechanic.verification.images.frontView}
                      alt="Document Front View"
                      className="w-full h-48 object-contain rounded-md bg-white shadow-sm"
                      onError={(e) => {
                        e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iI2Y5ZmFmYiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOWNhM2FmIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNHB4Ij5Gcm9udCBWaWV3PC90ZXh0Pjwvc3ZnPg=='
                      }}
                    />
                    <div className="absolute inset-0 pointer-events-none" />
                  </div>
                </div>

                {/* Back View */}
                <div className="space-y-2">
                  <Label className="text-sm font-medium text-gray-700">
                    Back View
                  </Label>
                  <div className="relative border-2 border-dashed border-gray-300 rounded-lg p-4 bg-gray-50">
                    <img
                      src={mechanic.verification.images.backView}
                      alt="Document Back View"
                      className="w-full h-48 object-contain rounded-md bg-white shadow-sm"
                      onError={(e) => {
                        e.currentTarget.src = 'data:image/svg+xml;base64,PHN2ZyB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgeG1sbnM9Imh0dHA6Ly93d3cudzMub3JnLzIwMDAvc3ZnIj48cmVjdCB3aWR0aD0iMjAwIiBoZWlnaHQ9IjE1MCIgZmlsbD0iI2Y5ZmFmYiIvPjx0ZXh0IHg9IjUwJSIgeT0iNTAlIiBkb21pbmFudC1iYXNlbGluZT0ibWlkZGxlIiB0ZXh0LWFuY2hvcj0ibWlkZGxlIiBmaWxsPSIjOWNhM2FmIiBmb250LWZhbWlseT0ic2Fucy1zZXJpZiIgZm9udC1zaXplPSIxNHB4Ij5CYWNrIFZpZXc8L3RleHQ+PC9zdmc+'
                      }}
                    />
                    <div className="absolute inset-0 pointer-events-none" />
                  </div>
                </div>
              </div>
            </div>
          </div>
        </CardContent>
      </Card>

      {/* Action Button */}
      <div className="flex justify-start mt-6">
        <Button 
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={handleBack}
        >
          <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
          <span className="hidden sm:inline">Back to Mechanics List</span>
          <span className="sm:hidden">Back</span>
        </Button>
      </div>
    </div>
  )
}
