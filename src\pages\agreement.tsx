import React, { useState } from 'react';
import { Textarea } from '@/components/ui/textarea';
import Skecth from '../assets/vehicleClass_Skecthes/Skecth.png'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';

export function AgreementPage() {
  const [signature, setSignature] = useState('');
  const [date, setDate] = useState('');
  const [customerSignature, setCustomerSignature] = useState('');
  const [customerDate, setCustomerDate] = useState('');
  const [additionalDriverSignature, setAdditionalDriverSignature] = useState('');
  const [additionalDriverDate, setAdditionalDriverDate] = useState('');
  const [lcrSignature, setLcrSignature] = useState('');
  const [lcrDate, setLcrDate] = useState('');

  const termsData: { clause: string; subject: string; summary: string }[] = [
    {
      clause: "2.1",
      subject: "Authorised Drivers",
      summary: "Only drivers authorised by Us before the Start of the Rental are permitted to drive the Vehicle. Allowing an unauthorised driver to drive the Vehicle is a Major Breach of the Rental Contract which excludes You, an Authorised Driver and the unauthorised driver from the benefit of Damage Cover."
    },
    {
      clause: "2.3, 2.4",
      subject: "License requirements",
      summary: "A valid license not subject to any restrictions, Early Termination or suspension is required."
    },
    {
      clause: "3.1, 3.2, 3.3, 3.4",
      subject: "Prohibited use",
      summary: "The Vehicle must never be driven under the influence of drugs or alcohol or in excess of alcohol/drug limits set by law. Failing to undergo a preliminary breath test, illegal use, using the vehicle to move dangerous or hazardous goods and use of a mobile phone whilst driving are all prohibited"
    },
    {
      clause: "4.1(a), 4.1(b), 4.2, 4.3",
      subject: "Prohibited areas",
      summary: "Use of the Vehicle on Unsealed Roads and Off Road is prohibited at all times, as is use of the vehicle in a prohibited area, including flooded roads, beaches, driving through though rivers or streams and any areas prohibited by the police."
    },
    {
      clause: "5.1, 5.2(b), 5.5, 5.6",
      subject: "Your obligations",
      summary: "These include inspecting the Vehicle at the Start of the Rental for pre-existing damage and paying the Rental Charges and the Security deposit. During your rental You must take reasonable care of the Vehicle and that includes making sure the Vehicle is locked and the keys are kept in your possession at all times. Smoking in the Vehicle is prohibited as is the carriage of animals, except assistance animals."
    },
    {
      clause: "6.2, 6.4, 6.5, 6.6, 6.8, 7.1, 7.2",
      subject: "Rental Period, costs and charges",
      summary: "Extensions of the Rental period must be notified no less than 24 hours prior to the End of the Rental. Tolls, parking and speeding infringements and fines incur an administration fee and are always Your responsibility. Daily use is restricted to daily mileage limit. Any use over and above that limit will be charged at the rate of 20 cents per excess kilometre. The Vehicle must be returned on time, in clean and good repair and with a full tank of fuel or additional Fees will apply. Unless otherwise stated, the Rental Period is 24 hours. If the Vehicle is returned more than one hour after it is due, you will be charged $25 per hour up to a full day's rental and a further full day's rental for each 24- hour period or part thereof."
    },
    {
      clause: "8.1, 8.2",
      subject: "Damage Cover, Damage Excess",
      summary: "If there is Damage to the Vehicle, it is stolen or there is any Third Party Loss, You must pay the Damage Excess shown on the Rental Agreement."
    },
    {
      clause: "9.1(b), 9.1(c), 12.1",
      subject: "Damage Cover Exclusions",
      summary: "Damage Cover is excluded if there is a Major Breach of the Rental Contract. Overhead Damage and Underbody Damage, are excluded, as is Damage caused by immersion of the Vehicle in water and use of the wrong fuel type. Personal items are also not subject to Damage Cover"
    },
    {
      clause: "10",
      subject: "Breakdowns",
      summary: "You must contact us immediately on 0330037447 if there is a breakdown and we will arrange roadside assistance, subject to some exceptions"
    },
    {
      clause: "11",
      subject: "Accident reporting",
      summary: "Any damage to the Vehicle during the Rental Period must be reported to Us as soon as practicable and You must also report an accident to the police if any person is injured, a party leaves the accident scene without exchanging details or the other party is affected by alcohol or drugs."
    },
    {
      clause: "12.1, 12.3, 14",
      subject: "Major Breach consequences",
      summary: "Committing a Major Breach means all entitlements to Damage Cover are excluded and the Vehicle may be repossessed by Us."
    },
    {
      clause: "13.2",
      subject: "Privacy",
      summary: "A Tracking Device may be fitted to the Vehicle to track its location, and which provides other data about the use of the Vehicle"
    },
    {
      clause: "",
      subject: "Other",
      summary: "All the vehicles must be returned to the office for an inspection at least every 28 days. Vehicle must be returned on time when it is due for service. Any damage caused due to not servicing on time is customer's responsibility. Payments must be done on time in advance. All the payments must be paid in advance any delays will attract $10.00 late payment fee for a week on long term contracts and $5.00 daily fee for short term contracts."
    }
  ];

  return (
    <div className="w-[210mm] min-h-[297mm] mx-auto bg-white p-4 text-sm font-sans box-border">
      {/* Header */}
      <div className="flex items-center justify-between mb-4">
        <div className="flex items-center Aquatic Solutions gap-4">
          <img src="/logo.png" className="w-35 h-20" alt="Lion Logo" />
        </div>
        <div className="text-center">
          <h2 className="text-lg font-bold">LION RENTALS PTY LTD</h2>
          <p className="text-xs">ABN: ***********</p>
          <p className="text-xs">2/85 Hume Hwy, Somerton, VIC, 3062</p>
          <p className="text-xs">03 9303 7447 | <EMAIL></p>
        </div>
        <div className="text-right">
          <p className="text-xs">Rental Agreement <strong>#27068</strong></p>
        </div>
      </div>

      {/* Main Table */}
      <Table className="mb-4 border-collapse border border-gray-800">
        <TableHeader>
          <TableRow>
            <TableHead className="bg-gray-200 p-2 text-center font-bold w-1/2 border border-gray-800">RENTAL INFORMATION</TableHead>
            <TableHead className="bg-gray-200 p-2 text-center font-bold w-1/2 border border-gray-800">RENTER INFORMATION</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell className="p-2 align-top border border-gray-800">
              <Table className="w-full text-xs">
                <TableBody>
                  <TableRow>
                    <TableCell className="py-1 font-medium">Date Out</TableCell>
                    <TableCell className="py-1">16/06/2025</TableCell>
                    <TableCell className="py-1">12:00 PM</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="py-1 font-medium">Date Due</TableCell>
                    <TableCell className="py-1">17/06/2025</TableCell>
                    <TableCell className="py-1">12:00 PM</TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="py-1 font-medium">Pickup Location</TableCell>
                    <TableCell className="py-1">Somerton</TableCell>
                    <TableCell className="py-1"></TableCell>
                  </TableRow>
                  <TableRow>
                    <TableCell className="py-1 font-medium">Return Location</TableCell>
                    <TableCell className="py-1">Somerton</TableCell>
                    <TableCell className="py-1"></TableCell>
                  </TableRow>
                </TableBody>
              </Table>
            </TableCell>
            <TableCell className="p-2 align-top border border-gray-800">
              <div className="space-y-1">
                <div>Test 123 123 - Drivers License, 1</div>
                <div><span className="font-medium">Company name:</span></div>
                <div><span className="font-medium">License Issue country:</span></div>
                <div><span className="font-medium">Date of Birth:</span></div>
                <div><span className="font-medium">Email:</span></div>
                <div><span className="font-medium">Phone Number:</span> 04123456</div>
                <div><span className="font-medium">Address:</span> Australia</div>
              </div>
            </TableCell>
          </TableRow>
          <TableRow >
            <TableHead className="bg-gray-200 p-2 text-center font-bold border border-gray-800">VEHICLE INFORMATION</TableHead>
            <TableHead className="bg-gray-200 p-2 text-center font-bold border border-gray-800">CHARGE INFORMATION</TableHead>
          </TableRow>
          <TableRow>
            <TableCell className="p-2 align-top border border-gray-800">
              <div className="space-y-1">
                <div><span className="font-medium">Unit#:</span> 2020554 LVL5BD</div>
                <div className="flex items-center gap-2">
                  <span className="font-medium">Vehicle Class:</span>
                  <span>Delivery Van 1</span>
                  <span className="bg-green-600 text-white px-1 py-0.5 rounded text-[10px]">Active</span>
                </div>
                <div>Tonne - Premium</div>
                <div><span className="font-medium">Brand Model:</span> Toyota Hiace</div>
                <div><span className="font-medium">Class:</span> 49 Delivery Van 1</div>
                <div><span className="font-medium">Odom Out:</span></div>
                <div><span className="font-medium">Odom In:</span></div>
                <div><span className="font-medium">Fuel Out:</span> / 8</div>
                <div><span className="font-medium">Fuel In:</span> / 8</div>
              </div>
            </TableCell>
            <TableCell className="p-2 align-top border border-gray-800">
              <div className="space-y-2">
                <div>
                  <div className="font-medium mb-1">Time Charges</div>
                  <Table className="w-full text-xs">
                    <TableBody>
                      <TableRow>
                        <TableCell>1x Days:</TableCell>
                        <TableCell className="text-right">AUD 81.82</TableCell>
                        <TableCell className="text-right">AUD 81.82</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
                <div>
                  <div className="font-medium mb-1">Protections & Coverages</div>
                  <Table className="w-full text-xs">
                    <TableBody>
                      <TableRow>
                        <TableCell>Bond Compulsory - 500</TableCell>
                        <TableCell className="text-right">AUD 500.00</TableCell>
                        <TableCell className="text-right">AUD 500.00</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
                <div>
                  <div className="font-medium mb-1">Miscellaneous</div>
                  <Table className="w-full text-xs">
                    <TableBody>
                      <TableRow>
                        <TableCell>GST</TableCell>
                        <TableCell className="text-right">10%</TableCell>
                        <TableCell className="text-right">AUD 8.18</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
                <div className="border-t border-gray-800 pt-2">
                  <Table className="w-full text-xs">
                    <TableBody>
                      <TableRow className="font-bold">
                        <TableCell>Total Charges</TableCell>
                        <TableCell className="text-right" colSpan={2}>AUD 590.00</TableCell>
                      </TableRow>
                      <TableRow>
                        <TableCell>Amount Outstanding:</TableCell>
                        <TableCell className="text-right" colSpan={2}>AUD 590.00</TableCell>
                      </TableRow>
                    </TableBody>
                  </Table>
                </div>
              </div>
            </TableCell>
          </TableRow>
          <TableRow className=''>
            <TableHead className="bg-gray-200 p-2 text-center font-bold border border-gray-800">TERMS AND CONDITIONS</TableHead>
            <TableHead className="bg-gray-200 p-2 text-center font-bold border border-gray-800">SIGNATURE</TableHead>
          </TableRow>
          <TableRow >
            <TableCell className="p-2 text-[10px] leading-tight align-top border border-gray-800">
              <div className="space-y-2">
                <p>
                  By signing this contract, both the renter and the additional driver affirm that they are 
                  fully aware that this motor vehicle can only been designated for rental 
                  liabilities and does not cover any damages or injuries sustained by the renter and/or 
                  the additional driver and/or other passengers in this vehicle at the time of an accident. 
                  Moreover, the renter or the additional driver shall be fully responsible and agrees to 
                  compensate Lion Rentals PTY LTD for any damages caused to the vehicle should it 
                  have been used in contravention with the terms and conditions of this contract as 
                  stipulated.
                </p>
                <p>
                  Standard Insurance excess is $5000/. By purchasing Insurance Excess Reduction, 
                  Standard Insurance Excess could be reduced to $2,000/. If the renter is below 25 years 
                  of age and $1,000/ if renter is 25 years or above.
                </p>
                <p>
                  Further, the renter is fully aware that the refundable deposit amount if paid by credit 
                  card is $250, by debit card is $500 and by cash $1000. Exception: International driver's 
                  license holders without permanent address in Australia where the minimum bond is 
                  $500. Bond shall be held for a period of 60 days from the return day of the rental 
                  vehicle.
                </p>
                <p>
                  Full terms and conditions displayed in the company website and in the office.
                </p>
                <p>
                  All vehicles rented subject to mileage restrictions as follows:
                </p>
                <ul className="list-disc ml-4 space-y-1">
                  <li>Passenger Vehicles 250 kms per Day</li>
                  <li>Commercial Vehicles 100 kms per Day</li>
                  <li>Unlimited mileage option is available at extra cost</li>
                </ul>
                <p>
                  Please enquire for the unlimited option
                </p>
                <p>
                  Received the Deposit back:
                </p>
                <p>
                  (Signature)
                </p>
              </div>
            </TableCell>
            <TableCell className="p-2 align-top border border-gray-800">
              <div className="space-y-2">
                <div>
                  <p className="font-medium mb-2 text-xs">
                    I accept the Terms and Conditions applicable to this 
                    Rental Agreement without any exception or 
                    reservation.
                  </p>
                </div>
                <div className="space-y-2">
                  <div>
                    <p className="font-medium mb-1 text-xs">Date:</p>
                    <input
                      type="text"
                      value={date}
                      onChange={(e) => setDate(e.target.value)}
                      className="border border-gray-800 w-full h-8 text-xs focus:outline-none focus:border-blue-500"
                    />
                  </div>
                  <div>
                    <p className="font-medium mb-1 text-xs">Signature:</p>
                    <input
                      type="text"
                      value={signature}
                      onChange={(e) => setSignature(e.target.value)}
                      className="border border-gray-800 w-full h-8 text-xs focus:outline-none focus:border-blue-500"
                    />
                  </div>
                </div>
                <div>
                  <p className="font-medium mb-1 text-xs">Rental Agent Signature:</p>
                  <div className="border border-gray-800 h-12"></div>
                </div>
              </div>
            </TableCell>
          </TableRow>
        </TableBody>
      </Table>

      {/* Vehicle Information Table */}
      <Table className="mb-4">
        <TableHeader>
          <TableRow>
            <TableHead className="p-1 bg-gray-100 text-center font-medium">Description</TableHead>
            <TableHead className="p-1 bg-gray-100 text-center font-medium">Make</TableHead>
            <TableHead className="p-1 bg-gray-100 text-center font-medium">Model</TableHead>
            <TableHead className="p-1 bg-gray-100 text-center font-medium">YOM</TableHead>
            <TableHead className="p-1 bg-gray-100 text-center font-medium">Colour</TableHead>
            <TableHead className="p-1 bg-gray-100 text-center font-medium">Rego</TableHead>
          </TableRow>
        </TableHeader>
        <TableBody>
          <TableRow>
            <TableCell className="p-1 text-center"></TableCell>
            <TableCell className="p-1 text-center">Toyota</TableCell>
            <TableCell className="p-1 text-center">Hiace</TableCell>
            <TableCell className="p-1 text-center">2020554</TableCell>
            <TableCell className="p-1 text-center">White</TableCell>
            <TableCell className="p-1 text-center">1VL5BD</TableCell>
          </TableRow>
        </TableBody>
      </Table>

      {/* Vehicle Diagram */}
      <div className="flex justify-center mb-4">
        <img src={Skecth} alt="Vehicle Sketch" className="w-[150mm] h-auto" />
      </div>

      {/* Signature Section */}
      <div className="flex justify-between items-end mb-4">
        <div className="w-[80mm]">
          <div className="text-center mb-1 text-xs">Test 123 123</div>
          <div className="border-b border-gray-800 h-6"></div>
          <div className="text-center text-[10px] mt-1">( Customer Name )</div>
        </div>
        <div className="w-[80mm]">
          <div className="text-center mb-1 text-xs">Test 123 123</div>
          <div className="border-b border-gray-800 h-6"></div>
          <div className="text-center text-[10px] mt-1">( Customer Signature )</div>
        </div>
      </div>

      {/* Comment */}
      <Textarea
        placeholder="Add your comments here..."
        className="w-full mb-4 focus:outline-none focus:ring-0 focus:border-none "
      />

      {/* Credit/Debit Card Authority Form */}
      <div className="border-2 border-gray-800 p-4">
        <div className="mb-4">
          <h1 className="text-blue-600 text-base font-bold mb-2">CREDIT/DEBIT CARD AUTHORITY FORM</h1>
          <div className="flex justify-between items-start text-xs">
            <div>
              <div className="font-bold">COMPANY NAME: LION RENTALS PTY LTD</div>
              <div>ADDRESS: 2/85 Hume Highway, Somerton VIC 3062</div>
              <div className="ml-4">470 Geelong Road, West Footscray VIC 3012</div>
            </div>
            <div className="text-right">
              <div>Tel. 03-9303 7447</div>
              <div>Tel. 03-9314 0741</div>
            </div>
          </div>
        </div>

        <hr className="border-gray-800 mb-4" />

        <div className="mb-4">
          <div className="text-center font-medium mb-2 text-xs">
            Request and Authority to debit the account named below to pay
          </div>
          <div className="text-center font-bold text-base mb-2">
            LION RENTALS PTY LTD
          </div>
          <hr className="border-gray-800 mb-2" />
          <div className="font-bold mb-2 text-xs">Request and Authority to debit</div>
          <div className="space-y-2 text-xs">
            <div className="flex items-center">
              <span className="mr-2">Your Surname or company name</span>
              <div className="flex-1 border-b border-gray-800 h-6"></div>
            </div>
            <div className="flex items-center">
              <span className="mr-2">Your Given names or ABN /ARBN</span>
              <div className="flex-1 border-b border-gray-800 h-6 mr-2"></div>
              <span className="font-bold">"You"</span>
            </div>
            <p className="leading-relaxed">
              request and authorise <span className="font-bold">LION RENTALS PTY LTD</span> 533919 to arrange, through its own financial institution, a debit to Your 
              nominated account any amount related to the vehicle rental <span className="font-bold">LION RENTALS PTY LTD</span>, has deemed payable by You.
            </p>
            <p className="leading-relaxed">
              This debit or charge may be made through the Bulk Electronic Clearing System (BECS) from Your account held at the 
              financial institution You have nominated below and will be subject to the terms and conditions of the Direct Debit 
              Request Service Agreement.
            </p>
          </div>
        </div>

        <hr className="border-gray-800 mb-4" />

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">Insert the name and address of financial institution at which account is held</div>
          <div className="space-y-2 text-xs">
            <div className="flex items-center">
              <span className="mr-2">Financial institution name</span>
              <div className="flex-1 border-b border-gray-800 h-6"></div>
            </div>
            <div className="flex items-center">
              <span className="mr-2">Address</span>
              <div className="flex-1 border-b border-gray-800 h-6"></div>
            </div>
          </div>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">Insert details of account to be Debited</div>
          <div className="space-y-2 text-xs">
            <div className="flex items-center">
              <span className="mr-2">Name/s on Card</span>
              <div className="w-24 border-b border-gray-800 h-6 mr-2"></div>
              <span className="text-[10px]">$[field_453]</span>
              <div className="flex-1 border-b border-gray-800 h-6 ml-2"></div>
            </div>
            <div className="flex items-center space-x-4">
              <div className="flex items-center">
                <span className="mr-2">Expiry Date (Must be 4 Digits)</span>
                <div className="w-8 border-b border-gray-800 h-6 mr-1"></div>
                <span>/</span>
                <div className="w-8 border-b border-gray-800 h-6 ml-1"></div>
              </div>
              <div className="flex items-center">
                <span className="mr-2">CVV No.</span>
                <div className="w-12 border-b border-gray-800 h-6"></div>
              </div>
            </div>
            <div className="flex items-center">
              <span className="mr-2">Card Number</span>
              <div className="w-24 border-b border-gray-800 h-6 mr-2"></div>
              <span className="text-[10px]">$[field_454]</span>
              <div className="flex-1 border-b border-gray-800 h-6 ml-2"></div>
            </div>
          </div>
        </div>

        <hr className="border-gray-800 mb-4" />

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">Acknowledgment</div>
          <p className="text-xs leading-relaxed mb-2">
            By signing and/or providing us with a valid instruction in respect to Your Direct Debit Request, You have understood and 
            agreed to the terms and conditions governing the rental and debit
          </p>
          <p className="text-xs leading-relaxed">
            arrangements between You and <span className="font-bold">LION RENTALS PTY LTD</span> as set out in this Request and in Your Direct Debit Request 
            Service Agreement.
          </p>
        </div>

        <hr className="border-gray-800 mb-4" />

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">Insert Your signature and address</div>
          <div className="space-y-2 text-xs">
            <div className="flex items-center">
              <span className="mr-2">Signature</span>
              <div className="flex-1 border-b border-gray-800 h-6"></div>
            </div>
            <div className="text-[10px] text-gray-600">(If signing for a company, sign and print full name and capacity for signing eg. director)</div>
            <div className="flex items-center">
              <span className="mr-2">Address</span>
              <div className="flex-1 border-b border-gray-800 h-6"></div>
            </div>
            <div className="flex items-center">
              <span className="mr-2">Australia</span>
              <div className="flex-1 border-b border-gray-800 h-6"></div>
            </div>
            <div className="flex items-center">
              <span className="mr-2">Date</span>
              <div className="w-8 border-b border-gray-800 h-6 mr-1"></div>
              <span>/</span>
              <div className="w-8 border-b border-gray-800 h-6 mx-1"></div>
              <span>/</span>
              <div className="w-12 border-b border-gray-800 h-6 ml-1"></div>
            </div>
          </div>
        </div>

        <hr className="border-gray-800 mb-4" />

        <div className="mb-4">
          <p className="text-xs leading-relaxed mb-2">
            This is Your Direct Debit Service Agreement with <span className="font-bold">LION RENTALS PTY LTD</span> ABN **************. It explains what Your 
            obligations are when undertaking a Direct Debit arrangement with us. It also details what our obligations are to You as 
            Your Direct Debit provider.
          </p>
          <p className="text-xs leading-relaxed">
            Please keep this agreement for future reference. It forms part of the terms and conditions of Your Direct Debit Request 
            (DDR) and should be read in conjunction with Your DDR authorisation.
          </p>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">Definitions</div>
          <div className="space-y-1 text-xs">
            <div>
              <span className="font-bold">account</span> means the account held at Your financial institution from which we are authorised to arrange for funds to be 
              debited.
            </div>
            <div>
              <span className="font-bold">agreement</span> means this Direct Debit Request Service Agreement between You and us.
            </div>
            <div>
              <span className="font-bold">banking day</span> means a day other than a Saturday or a Sunday or a public holiday listed throughout Australia.
            </div>
            <div>
              <span className="font-bold">debit day</span> means the day that payment by You to us is due.
            </div>
            <div>
              <span className="font-bold">debit payment</span> means a particular transaction where a debit is made.
            </div>
            <div>
              <span className="font-bold">direct debit request</span> means the Direct Debit Request between us and You.
            </div>
            <div>
              <span className="font-bold">us or we</span> means <span className="font-bold">LION RENTALS PTY LTD</span>, (the Debit User) You have authorised by requesting a Direct Debit Request.
            </div>
            <div>
              <span className="font-bold">You</span> means the customer who has signed or authorised by other means the Direct Debit Request.
            </div>
            <div>
              <span className="font-bold">Your financial institution</span> means the financial institution nominated by You on the DDR at which the account is 
              maintained.
            </div>
          </div>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">1. Debiting Your account</div>
          <div className="space-y-2 text-xs">
            <p>
              By signing a Direct Debit Request or by providing us with a valid instruction, You have authorised us to arrange for 
              funds to be debited from Your account related to the vehicle rental. You should refer to the Direct Debit Request and 
              this agreement for the terms of the arrangement between us and You.
            </p>
            <p className="ml-2">
              1.2 We will only arrange for funds to be debited from Your account as authorised in the Direct Debit Request.
            </p>
            <p className="ml-2">
              Or
            </p>
            <p>
              We will only arrange for funds to be debited from Your account if we have sent to the address nominated by You in the 
              Direct Debit Request, a billing advice which specifies the amount payable by You to us and when it is due.
            </p>
            <p>
              If the debit day falls on a day that is not a banking day, we may direct Your financial institution to debit Your account on 
              the following banking day. If You are unsure about which day Your account has or will be debited You should ask Your 
              financial institution.
            </p>
          </div>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">2. Amendments by us</div>
          <p className="text-xs">
            We may vary any details of this agreement or a Direct Debit Request at any time by giving You at least fourteen (14) days 
            written notice.
          </p>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">3. Amendments by You</div>
          <div className="space-y-2 text-xs">
            <p>
              You may change*, stop or defer a debit payment, or terminate this agreement by providing us with at least 14 days 
              notification by writing to: <span className="font-bold">LION CAR RENTALS</span> 2/85 HUME HWY, SOMERTON VIC 3062
            </p>
            <p>
              by telephoning us on <span className="font-bold">03-********</span> during business hours;
            </p>
            <p>or</p>
            <p>
              arranging it through Your own financial institution, which is required to act promptly on Your instructions.
            </p>
            <p>
              *Note: in relation to the above reference to 'change' , Your financial institution may 'change' Your debit payment only to 
              the extent of advising us <span className="font-bold">LION CAR RENTALS</span> of Your new account details.
            </p>
          </div>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">4. Your obligations</div>
          <div className="space-y-2 text-xs">
            <p>
              It is Your responsibility to ensure that there are sufficient clear funds available in Your account to allow a debit payment 
              to be made in accordance with the Direct Debit Request.
            </p>
            <div>
              <p className="mb-1">4.2 If there are insufficient clear funds in Your account to meet a debit payment:</p>
              <div className="ml-4 space-y-1">
                <p>(a) You may be charged a fee and/or interest by Your financial institution;</p>
                <p>(b) You may also incur fees or charges imposed or incurred by us; and</p>
                <p>(c) You must arrange for the debit payment to be made by another method or arrange for sufficient clear</p>
              </div>
              <p className="ml-2">funds to be in Your account by an agreed time so that we can process the debit payment.</p>
            </div>
            <p>4.3 You should check Your account statement to verify that the amounts debited from Your account are correct</p>
          </div>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">5 Dispute</div>
          <div className="space-y-2 text-xs">
            <p>
              If You believe that there has been an error in debiting Your account, You should notify us directly on <span className="font-bold">03-********</span> and 
              confirm that notice in writing with us as soon as possible so that we can resolve Your query more quickly. Alternatively, 
              You can take it up directly with Your financial institution.
            </p>
            <p>
              If we conclude as a result of our investigations that Your account has been incorrectly debited we will respond to Your 
              query by arranging for Your financial institution to adjust Your account (including interest and charges) accordingly. 
              We will also notify You in writing of the amount by which Your account has been adjusted.
            </p>
            <p>
              If we conclude as a result of our investigations that Your account has not been incorrectly debited we will respond to 
              Your query by providing You with reasons and any evidence for this finding in writing.
            </p>
          </div>
        </div>

        <div className="mb-4">
          <div className="font-bold mb-2 text-xs">6. Accounts</div>
          <div className="space-y-2 text-xs">
            <p>You should check:</p>
            <p>
              with Your financial institution whether direct debiting is available from Your account as direct debiting is not available on 
              all accounts offered by financial institutions.
            </p>
            <p>
              Your account details which You have provided to us are correct by checking them against a recent account statement; 
              and
            </p>
            <p>
              with Your financial institution before completing the Direct Debit Request if You have any queries about how to complete 
              the Direct Debit Request.
            </p>
          </div>
        </div>

        <div className="mb-4">
          <h3 className="font-bold text-sm mb-2">7. Confidentiality</h3>
          <p className="text-xs leading-relaxed mb-2">
            We will keep any information (including Your account details) in Your Direct Debit Request confidential. We will make 
            reasonable efforts to keep any such information that we have about You secure and to ensure that any of our employees 
            or agents who have access to information about You do not make any unauthorised use, modification, reproduction or 
            disclosure of that information.
          </p>
          <p className="text-xs leading-relaxed mb-2">
            7.2 We will only disclose information that we have about You:
          </p>
          <div className="ml-4 mb-2">
            <p className="text-xs mb-1">(a) to the extent specifically required by law; or</p>
            <p className="text-xs">(b) for the purposes of this agreement (including disclosing information in connection with any query or claim).</p>
          </div>
        </div>

        <hr className="border-gray-400 mb-4" />

        <div className="mb-4">
          <h3 className="font-bold text-sm mb-2">8. Notice</h3>
          <p className="text-xs leading-relaxed mb-2">
            If You wish to notify us in writing about anything relating to this agreement, You should write to
          </p>
          <p className="text-xs font-semibold mb-2">
            LION CAR RENTALS, 2/85 HUME HWY, SOMERTON VIC 3062
          </p>
          <p className="text-xs leading-relaxed mb-2">
            We will notify You by sending a notice in the ordinary post to the address You have given us in the Direct Debit Request.
          </p>
          <p className="text-xs leading-relaxed">
            8.3 Any notice will be deemed to have been received on the third banking day after posting.
          </p>
        </div>

        <hr className="border-gray-400 mb-4" />

        <div className="flex justify-between items-center mb-4">
          <div className="flex items-center">
            <label className="text-xs font-medium mr-2">Signature</label>
            <input
              type="text"
              value={signature}
              onChange={(e) => setSignature(e.target.value)}
              className="border-b-2 border-gray-800 bg-transparent focus:outline-none focus:border-blue-500 w-48 pb-1 text-xs"
              placeholder=""
            />
          </div>
          <div className="flex items-center">
            <label className="text-xs font-medium mr-2">Date</label>
            <input
              type="text"
              value={date}
              onChange={(e) => setDate(e.target.value)}
              className="border-b-2 border-gray-800 bg-transparent focus:outline-none focus:border-blue-500 w-32 pb-1 text-xs"
              placeholder=""
            />
          </div>
        </div>

        <div className="text-center mb-4">
          <h2 className="text-red-600 font-bold text-xs mb-2">
            SOME IMPORTANT TERMS AND CONDITIONS THAT MAY AFFECT YOUR LIABILITY
          </h2>
          <p className="text-[10px] leading-relaxed">
            This is a summary of some, but not all, of the Terms and Conditions that may affect Your liability. You must read the 
            Terms and Conditions mentioned in the LCR website or displayed in the branch office in full to fully understand your 
            obligations when renting the Vehicle.
          </p>
        </div>

        <div className="mt-4">
          <Table className="border border-gray-800">
            <TableHeader>
              <TableRow className="bg-yellow-300">
                <TableHead className="border border-gray-800 p-2 text-left font-bold w-16">CLAUSE</TableHead>
                <TableHead className="border border-gray-800 p-2 text-left font-bold w-24">SUBJECT</TableHead>
                <TableHead className="border border-gray-800 p-2 text-left font-bold">SUMMARY</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {termsData.map((term, index) => (
                <TableRow key={index} className="hover:bg-gray-50">
                  <TableCell className="border border-gray-800 p-2 font-medium align-top">
                    {term.clause}
                  </TableCell>
                  <TableCell className="border border-gray-800 p-2 font-medium align-top">
                    {term.subject}
                  </TableCell>
                  <TableCell className="border border-gray-800 p-2 leading-relaxed align-top">
                    {term.summary}
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        <div className="mb-4">
          <p className="text-xs leading-relaxed">
            I accept the Terms and Conditions applicable to this Rental Agreement without any exception or reservation
          </p>
        </div>

        <div className="mb-4">
          <div className="flex items-center mb-2">
            <span className="text-xs font-medium mr-2 w-32">Customer Signature:</span>
            <div className="flex-1 border-b border-gray-400 mx-2">
              <input
                type="text"
                value={customerSignature}
                onChange={(e) => setCustomerSignature(e.target.value)}
                className="w-full bg-transparent focus:outline-none focus:border-blue-500 py-1 text-xs"
                style={{ borderBottom: 'none' }}
              />
            </div>
            <span className="text-xs font-medium mx-2">Date:</span>
            <div className="w-32 border-b border-gray-400">
              <input
                type="text"
                value={customerDate}
                onChange={(e) => setCustomerDate(e.target.value)}
                className="w-full bg-transparent focus:outline-none focus:border-blue-500 py-1 text-xs"
                style={{ borderBottom: 'none' }}
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <div className="flex items-center mb-2">
            <span className="text-xs font-medium mr-2 w-32">Additional Driver Signature:</span>
            <div className="flex-1 border-b border-gray-400 mx-2">
              <input
                type="text"
                value={additionalDriverSignature}
                onChange={(e) => setAdditionalDriverSignature(e.target.value)}
                className="w-full bg-transparent focus:outline-none focus:border-blue-500 py-1 text-xs"
                style={{ borderBottom: 'none' }}
              />
            </div>
            <span className="text-xs font-medium mx-2">Date:</span>
            <div className="w-32 border-b border-gray-400">
              <input
                type="text"
                value={additionalDriverDate}
                onChange={(e) => setAdditionalDriverDate(e.target.value)}
                className="w-full bg-transparent focus:outline-none focus:border-blue-500 py-1 text-xs"
                style={{ borderBottom: 'none' }}
              />
            </div>
          </div>
        </div>

        <div className="mb-4">
          <div className="flex items-center mb-2">
            <span className="text-xs font-medium mr-2 w-32">On Behalf of LCR Signature:</span>
            <div className="flex-1 border-b border-gray-400 mx-2">
              <input
                type="text"
                value={lcrSignature}
                onChange={(e) => setLcrSignature(e.target.value)}
                className="w-full bg-transparent focus:outline-none focus:border-blue-500 py-1 text-xs"
                style={{ borderBottom: 'none' }}
              />
            </div>
            <span className="text-xs font-medium mx-2">Date:</span>
            <div className="w-32 border-b border-gray-400">
              <input
                type="text"
                value={lcrDate}
                onChange={(e) => setLcrDate(e.target.value)}
                className="w-full bg-transparent focus:outline-none focus:border-blue-500 py-1 text-xs"
                style={{ borderBottom: 'none' }}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}