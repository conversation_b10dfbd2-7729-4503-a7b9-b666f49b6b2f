import React, { useState } from 'react';
import { Search, Edit, Users2, ChevronDown } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Card } from '@/components/ui/card';

interface Vendor {
  id: string;
  vendorName: string;
  phone: string;
  email: string;
  address: string;
}

export const VendorPage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const vendors: Vendor[] = [
    {
      id: 'VID001',
      vendorName: 'ARB Somerton',
      phone: '0421 458 431',
      email: '<EMAIL>',
      address: '26 Somerton Road, Somerton VIC 3062'
    },
    {
      id: 'VID002',
      vendorName: 'RPM Somerton',
      phone: '0421 871 031',
      email: '<EMAIL>',
      address: '26/34-36 Somerton Road, Somerton VIC 3062'
    },
    {
      id: 'VID003',
      vendorName: 'Melbourne Car Removals',
      phone: '0421 300 530',
      email: '<EMAIL>',
      address: '1-6 Somerton Park Drive, Somerton VIC 3062'
    }
  ];

  const navigate = useNavigate();
  
  const handleIdClick = (id: string) => {
    navigate(`/admin/workshopMasterAdmin/vendor-edit/${id}`);
  };

  const filteredVendors = vendors.filter(vendor =>
    vendor.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.address.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalEntries = filteredVendors.length;
  const paginatedVendors = filteredVendors.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  );

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">

      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center mb-4 sm:mb-6">
            <Users2 className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
            <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Vendors</h1>
          </div>
          <Button
            onClick={() => navigate('/admin/workshopMasterAdmin/vendor-add')}
            className="px-4 py-2 bg-[#330101] text-white rounded transition-colors w-full sm:w-auto"
          >
            Add New Vendor
          </Button>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          {/* Filter button - full width on mobile, chevron right aligned */}
          <div className="relative w-full sm:w-auto order-1">
            <button className="flex items-center justify-between gap-2 px-3 py-2 border border-gray-300 rounded-md bg-white hover:bg-gray-50 text-sm w-full sm:w-auto">
              <span>Filter</span>
              <ChevronDown className="w-4 h-4" />
            </button>
          </div>
          {/* Search input - full width on mobile */}
          <div className="relative flex-1 order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing a name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>
          {/* Save button */}
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
      </div>

      {/* Table for Desktop */}
      <div className="hidden md:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className='bg-gray-50 uppercase'>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vendor Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Phone Number</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Email</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Address</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedVendors.map((vendor) => (
                <TableRow key={vendor.id} className="hover:bg-gray-50">
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vendor.vendorName}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vendor.phone}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vendor.email}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vendor.address}</TableCell>
                  <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                    <div className="flex items-center">
                      <Button
                        onClick={() => handleIdClick(vendor.id)}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                      >
                        <Edit className="w-3 h-3 sm:w-4 sm:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="md:hidden space-y-4 mb-4">
        {filteredVendors.length > 0 ? (
          filteredVendors.map((vendor) => (
            <Card key={vendor.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
              {/* Card content */}
              <div className="mb-3">
                <span className="text-sm font-medium text-gray-700 break-words whitespace-pre-line">
                  {vendor.vendorName}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Phone Number</div>
                  <div className="flex items-center text-sm">{vendor.phone}</div>
                </div>
                
                {/* Address: always below email, takes full width if email wraps */}
                <div className="col-span-2">
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Address</div>
                  <div className="flex items-center text-sm break-words">{vendor.address}</div>
                </div>
                {/* Email and Address: if email is long, it will wrap, and address will move below */}
                <div className="col-span-1">
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Email</div>
                  <div className="flex items-center text-sm ">{vendor.email}</div>
                </div>
              </div>
              {/* Action button at the bottom */}
              <div className="flex justify-end mt-4">
                <Button
                  onClick={() => handleIdClick(vendor.id)}
                  variant="ghost"
                  className="text-gray-600 hover:text-gray-800 p-1"
                >
                  <Edit className="w-4 h-4" />
                  <span className="ml-2 text-xs">Edit</span>
                </Button>
              </div>
            </Card>
          ))
        ) : (
          <div className="text-center text-sm text-gray-500 py-4">No vendors found.</div>
        )}
      </div>

      {/* Pagination only for md and up */}
      <div className="mt-4 sm:mt-6 hidden md:block">
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalEntries / entriesPerPage)}
          totalRecords={totalEntries}
          recordsPerPage={entriesPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setEntriesPerPage(records);
            setCurrentPage(1);
          }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4"
        />
      </div>
    </div>
  );
};