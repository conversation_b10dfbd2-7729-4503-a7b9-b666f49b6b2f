import { Dispatch, SetStateAction } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { ChangeRequest } from '../type/reception-type';

export const filterChangeRequests = (
  requests: ChangeRequest[],
  searchTerm: string,
  filterStatus: string
): ChangeRequest[] => {
  return requests.filter((request) => {
    const matchesSearch =
      request.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.rentalId.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.vehicleClass.toLowerCase().includes(searchTerm.toLowerCase()) ||
      request.requestType.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || request.status === filterStatus;
    return matchesSearch && matchesFilter;
  });
};

export const getStatusBadge = (status: string): string => {
  switch (status) {
    case 'In-Progress':
      return 'bg-green-500 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap';
    case 'Approved':
      return 'bg-blue-500 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap';
    default:
      return 'bg-gray-500 text-white px-2 py-1 rounded text-xs font-medium whitespace-nowrap';
  }
};

export const handleEditClick = (request: ChangeRequest, navigate: NavigateFunction) => {
  const cleanId = request.rentalId.replace('#', '');
  navigate(`/reception/reception-changerequest/reception-editrequest/${cleanId}`);
};

export const handleViewClick = (request: ChangeRequest, navigate: NavigateFunction) => {
  const cleanId = request.rentalId.replace('#', '');
  navigate(`/reception/reception-changerequest/reception-viewrequest/${cleanId}`);
};

export const handleAddRequestClick = (navigate: NavigateFunction) => {
  navigate('/reception/reception-addrequest');
};

export const handleSearchChange = (
  value: string,
  setSearchTerm: Dispatch<SetStateAction<string>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setSearchTerm(value);
  setCurrentPage(1);
};

export const handleFilterStatusChange = (
  value: string,
  setFilterStatus: Dispatch<SetStateAction<string>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setFilterStatus(value);
  setCurrentPage(1);
};

export const handleRecordsPerPageChange = (
  records: number,
  setRecordsPerPage: Dispatch<SetStateAction<number>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setRecordsPerPage(records);
  setCurrentPage(1);
};