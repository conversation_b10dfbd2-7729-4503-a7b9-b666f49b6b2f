import React from "react";
import { useActivityLog } from "./hook/useactivity-log";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { But<PERSON> } from "@/components/ui/button";
import { ScrollText } from "lucide-react";

export function ActivityLog() {
  const {
    search,
    setSearch,
    filterType,
    setFilterType,
    allTypes,
    filteredActivities,
  } = useActivityLog();

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title */}
      <div className="flex items-center mb-4 sm:mb-6">
        <ScrollText
          className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3"
          style={{ color: "#4A0000" }}
        />
        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
          Activity Log
        </h1>
      </div>

      {/* Search and Filter Bar */}
      {/* Mobile: filter, search, save (vertical); Desktop: existing order */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        {/* Filter always first */}
        <div className="relative w-full sm:w-auto order-1">
          <Select
            value={filterType}
            onValueChange={(value) => setFilterType(value)}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {allTypes
                .filter((type) => type !== "All")
                .map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
        {/* Search second on mobile, flex-1 on desktop */}
        <div className="relative w-full sm:flex-1 order-2">
          <Input
            placeholder="Start typing a name...."
            value={search}
            onChange={(e) => setSearch(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        {/* Save button last */}
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap w-full sm:w-auto">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Activity List */}
      <div className="space-y-3 sm:space-y-4">
        {filteredActivities.length > 0 ? (
          filteredActivities.map((activity, idx) => (
            <div
              key={idx}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow"
            >
              <div className="flex items-start space-x-2 sm:space-x-3">
                <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#4A0000] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 sm:mt-0">
                  <span className="text-white text-xs sm:text-sm font-medium">
                    i
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-gray-800 text-sm sm:text-base leading-relaxed break-words">
                    {activity.message}
                  </p>
                </div>
              </div>
            </div>
          ))
        ) : (
          <div className="text-center py-6 sm:py-8">
            <p className="text-gray-500 text-sm sm:text-base">
              No activities found.
            </p>
          </div>
        )}
      </div>
    </div>
  );
}