import { useState } from 'react';
import { NavigateFunction, useNavigate, useParams } from 'react-router-dom';
import { mockBookingsData } from '../common/mockdata';

export const useBookingHistory = () => {
  const { bookingId } = useParams<{ bookingId: string }>();
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const navigate = useNavigate();

  const filteredBookings = mockBookingsData.filter(booking => {
    const matchesSearch = booking.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.id.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.returnDate.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || booking.status === filterStatus;
    return matchesSearch && matchesFilter;
  });

  const mobileBookings = filteredBookings;

  const totalRecords = filteredBookings.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentBookings = filteredBookings.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const getStatusBadge = (status: string): string => {
    switch (status) {
      case 'Rental':
        return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Open':
        return 'bg-yellow-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Completed':
        return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };

  const getLoanVehicleBadge = (loanVehicle: string): string => {
    return loanVehicle === 'Yes'
      ? 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium'
      : 'bg-red-500 text-white px-3 py-1 rounded text-sm font-medium';
  };

  const handleBookingClick = (bookingId: string): void => {
    const cleanId = bookingId.replace('#', '');
    navigate(`/customer/booking-summary/${cleanId}`);
  };

  const handleViewAgreement = (id: string) => {
    const cleanId = id.replace('#', '');
    navigate(`/customer/booking-summary/agreement/${cleanId}`);
  };

  const handleViewReceipt = (id: string) => {
    const cleanId = id.replace('#', '');
    navigate(`/customer/booking-summary/customer-receipt/${cleanId}`);
  };

  return {
    bookingId,
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    mobileBookings,
    currentBookings,
    totalPages,
    totalRecords,
    getStatusBadge,
    getLoanVehicleBadge,
    handleBookingClick,
    handleViewAgreement,
    handleViewReceipt,
    navigate,
  };
};