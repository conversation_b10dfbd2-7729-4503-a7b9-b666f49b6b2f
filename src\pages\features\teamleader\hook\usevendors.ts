import { NavigateFunction } from 'react-router-dom';
import { Vendor } from '../type/teamleadertype';

export const handleIdClick = (id: string, navigate: NavigateFunction): void => {
  navigate(`/teamleader/vendor-edit/${id}`);
};

export const filterVendors = (vendors: Vendor[], searchTerm: string): Vendor[] => {
  return vendors.filter(vendor =>
    vendor.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.email.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
    vendor.address.toLowerCase().includes(searchTerm.toLowerCase())
  );
};