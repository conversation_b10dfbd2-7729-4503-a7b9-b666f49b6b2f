import React from "react";
import { useNavigate } from "react-router-dom";
import { useAuditTrailAdd } from "./hook/useaudit&trail-add";
import { Button } from "../../../components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../components/ui/select";
import { Input } from "../../../components/ui/input";

export function AuditTrailAdd() {
  const navigate = useNavigate();
  const {
    rego,
    setRego,
    serviceType,
    description,
    setDescription,
    regoOptions,
    handleAdd,
    handleCancel,
  } = useAuditTrailAdd({ navigate });

  return (
    <div className="w-full min-h-screen bg-white flex flex-col items-start pt-10 px-4">
      <form
        className="w-full max-w-2xl"
        onSubmit={e => {
          e.preventDefault();
          handleAdd();
        }}
      >
        {/* Rego */}
        <div className="relative mb-8">
          <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
            Rego
          </label>
          <Select value={rego} onValueChange={setRego}>
            <SelectTrigger className="w-full h-12 border border-gray-300 rounded px-4 text-base bg-white focus:border-[#ffde5c] focus:ring-2 focus:ring-[#ffde5c] transition-colors">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {regoOptions.map(opt => (
                <SelectItem key={opt} value={opt}>
                  {opt}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Service Type */}
        <div className="relative mb-8">
          <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
            Service Type
          </label>
          <Input
            className="w-full h-12 border border-gray-300 rounded px-4 text-base bg-white focus:border-[#ffde5c] focus:ring-2 focus:ring-[#ffde5c] transition-colors"
            value={serviceType}
            placeholder="Service type"
            readOnly
          />
        </div>

        {/* Description */}
        <div className="relative mb-12">
          <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
            Description
          </label>
          <Input
            as="textarea"
            className="w-full min-h-[70px] h-24 border border-gray-300 rounded px-4 py-3 text-base bg-white focus:border-[#ffde5c] focus:ring-2 focus:ring-[#ffde5c] transition-colors resize-none"
            placeholder="Type here ......"
            value={description}
            onChange={e => setDescription(e.target.value)}
          />
        </div>

        {/* Buttons */}
        <div className="flex justify-end gap-3">
          <Button
            type="submit"
            className="bg-[#330101] hover:bg-[#220000] text-white px-8 py-2 w-28"
          >
            Add
          </Button>
          <Button
            type="button"
            className="bg-gray-400 hover:bg-gray-500 text-white px-8 py-2 w-28"
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}
