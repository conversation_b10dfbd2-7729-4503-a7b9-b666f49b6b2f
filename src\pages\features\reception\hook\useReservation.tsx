import { Dispatch, SetStateAction } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { ReservationManagement } from '../type/reception-type';

// Helper function to check if a date is today
export const isToday = (dateString: string): boolean => {
  const today = new Date();
  const date = new Date(dateString.split(' ')[0].split('-').reverse().join('-'));
  return date.toDateString() === today.toDateString();
};

// Helper function to check if a date is tomorrow
export const isTomorrow = (dateString: string): boolean => {
  const tomorrow = new Date();
  tomorrow.setDate(tomorrow.getDate() + 1);
  const date = new Date(dateString.split(' ')[0].split('-').reverse().join('-'));
  return date.toDateString() === tomorrow.toDateString();
};

// Filter data based on active tab and filterType
export const getFilteredDataByTabAndFilter = (
  reservationData: ReservationManagement[],
  activeTab: string,
  filterType: string
): ReservationManagement[] => {
  let data = reservationData;

  // Step 1: Filter by active tab
  switch (activeTab) {
    case 'Website Bookings':
      data = data.filter(item => ['Open', 'Rental'].includes(item.status));
      break;
    case "Today's Pickups":
      data = data.filter(item => 
        isToday(item.pickupDate) && ['Rental', 'Open', 'Cancelled'].includes(item.status)
      );
      break;
    case "Tomorrow's Pickups":
      data = data.filter(item => 
        isTomorrow(item.pickupDate) && item.status === 'Open'
      );
      break;
    case "Today's Returns":
      data = data.filter(item => 
        isToday(item.returnDate) && ['Completed', 'Rental', 'Cancelled'].includes(item.status)
      );
      break;
    case "Tomorrow's Returns":
      data = data.filter(item => 
        isTomorrow(item.returnDate)
      );
      break;
    case 'On Rent':
      data = data.filter(item => item.status === 'Rental');
      break;
    case 'Completed':
      data = data.filter(item => item.status === 'Completed');
      break;
    case 'Cancelled':
      data = data.filter(item => item.status === 'Cancelled');
      break;
    case 'Outstanding Payment':
      data = data.filter(item => 
        parseFloat(item.outstandingBalance.replace('$', '')) > 0 && 
        ['Open', 'Cancelled'].includes(item.status)
      );
      break;
    default: // 'All'
      break;
  }

  // Step 2: Filter by filterType
  switch (filterType) {
    /* case 'Passenger':
      data = data.filter(item => item.category === 'Passenger');
      break;
    case 'Commercial':
      data = data.filter(item => item.category === 'Commercial');
      break; */
    case 'Rental':
      data = data.filter(item => item.status === 'Rental');
      break;
    case 'Completed':
      data = data.filter(item => item.status === 'Completed');
      break;
    case 'Overdue':
      data = data.filter(item => {
        const returnDate = new Date(item.returnDate.split(' ')[0].split('-').reverse().join('-'));
        const today = new Date();
        return returnDate < today && item.status !== 'Completed';
      });
      break;
    default: // 'All'
      break;
  }

  return data;
};

// Get status color classes
export const getStatusColor = (status: string): string => {
  switch (status) {
    case 'Rental':
      return 'bg-[#1FA042] text-white rounded-md';
    case 'Open':
      return 'bg-[#F2CF05] text-white rounded-md';
    case 'Completed':
      return 'bg-[#2B409A] text-white rounded-md';
    case 'Cancelled':
      return 'bg-[#BD2D2D] text-white rounded-md';
    default:
      return 'bg-gray-100 text-black rounded-md';
  }
};

// Get Yes/No color classes
export const getYesNoColor = (value: string): string => {
  return value === 'Yes' 
    ? 'bg-[#1FA042] text-white rounded-md' 
    : 'bg-[#BD2D2D] text-white rounded-md';
};

// Handle navigation to availability check
export const handleCheckAvailability = (navigate: NavigateFunction) => {
  navigate('/reception/reception-checkavailability');
};

// Handle navigation to add new reservation
export const handleAddNewReservation = (navigate: NavigateFunction) => {
  navigate('/reception/newReservation');
};

// Handle navigation to booking summary
export const handleViewBookingSummary = (rentalId: string, navigate: NavigateFunction) => {
  navigate(`/reception/reception-bookingsummary/${rentalId}`);
};

// Handle navigation to outstanding balance
export const handleViewOutstanding = (rentalId: string, navigate: NavigateFunction) => {
  navigate(`/reception/reception-outstanding/${rentalId}`);
};

// Handle search term change
export const handleSearchChange = (
  value: string,
  setSearchTerm: Dispatch<SetStateAction<string>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setSearchTerm(value);
  setCurrentPage(1);
};

// Handle tab change
export const handleTabChange = (
  tab: string,
  setActiveTab: Dispatch<SetStateAction<string>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setActiveTab(tab);
  setCurrentPage(1);
};

// Handle filter type change
export const handleFilterTypeChange = (
  value: string,
  setFilterType: Dispatch<SetStateAction<string>>,
  setCurrentPage: Dispatch<SetStateAction<number>>
) => {
  setFilterType(value);
  setCurrentPage(1);
};