import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; 
import { CheckCircle, LifeBuoy, Snowflake, Music, Power, Box, Banknote, Baby, Pencil, ChevronDown } from 'lucide-react';
import SearchHeader from '@/components/layout/SearchHeader';
import economyCar from '@/assets/economyCar.png';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuTrigger,
} from '@/components/ui/dropdown-menu';

const useAuth = () => {
  const [role] = useState<'admin' | 'customer'>('admin'); 
  return { role };
};

interface Step {
  number: number;
  label: string;
  active: boolean;
}

interface CustomerInfo {
  corporateCustomer: boolean;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  address: string;
  postcode: string;
  country: string;
  birthday: string;
  companyName: string;
}

interface DrivingLicense {
  type: string;
  idNumber: string;
  issueDate: string;
  expireDate: string;
  issueCountry: string;
}

interface EmergencyContact {
  name: string;
  phone: string;
}

interface RentalDetails {
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  pickupLocation: string;
  returnLocation: string;
   rateType?: string;
}

interface VehicleFeature {
  name: string;
  icon: React.ComponentType<any>;
}

interface VehicleClass {
  name: string;
  features: VehicleFeature[];
  pricePerDay: number;
  totalPrice: number;
  days: number;
}

interface PricingBreakdown {
  vehiclePrice: number;
  bondCompulsory: number;
  childSeat: number;
  insurance: number;
  discount: number;
  total: number;
  amountRequired: number;
}

export function ConfirmPage(): JSX.Element {
  const navigate = useNavigate(); 
  const { role } = useAuth();

  const [sendConfirmationEmail, setSendConfirmationEmail] = useState<boolean>(false);
  const [comments, setComments] = useState<string>('');
  const [savedComments, setSavedComments] = useState<string[]>([]);
  const [acceptTerms, setAcceptTerms] = useState<boolean>(false);
  const [rentalDetails, setRentalDetails] = useState<RentalDetails>({
    pickupDate: '13/06/2025',
    pickupTime: '11:30',
    returnDate: '14/06/2025',
    returnTime: '10:30',
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    rateType: 'New Year - 2021'
  });
  const [isPopupOpen, setIsPopupOpen] = useState<boolean>(false);

const handleSaveComment = (): void => {
    if (comments.trim()) {
      setSavedComments([...savedComments, comments]);
      setComments('');
    }
  };

  const steps: Step[] = role === 'admin' ? [
    { number: 1, label: 'Dates', active: false },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: false },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: true },
    { number: 6, label: 'Payment', active: false },
    { number: 7, label: 'Receipt', active: false },
    { number: 8, label: 'Pickup', active: false },
    { number: 9, label: 'Agreement', active: false },
    { number: 10, label: 'Return', active: false },
  ] : [
    { number: 1, label: 'Dates', active: false },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: false },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: true },
    { number: 6, label: 'Payment', active: false },
    { number: 7, label: 'Receipt', active: false },
  ];

  const activeIndex = steps.findIndex(step => step.active);

  const getVisibleSteps = () => {
    const visible = [activeIndex];
    if (activeIndex - 1 >= 0) visible.unshift(activeIndex - 1);
    if (activeIndex - 2 >= 0) visible.unshift(activeIndex - 2);
    if (activeIndex + 1 < steps.length) visible.push(activeIndex + 1);
    if (activeIndex + 2 < steps.length) visible.push(activeIndex + 2);
    return visible.sort((a, b) => a - b);
  };

  const visibleStepIndices = getVisibleSteps();
  
  const vehicleClass: VehicleClass = {
    name: 'Eco Plus Car',
    features: [
      { name: 'Automatic Transmission', icon: LifeBuoy },
      { name: 'Air Conditioning', icon: Snowflake },
      { name: 'Petrol CD Player', icon: Music },
      { name: 'Power Steering', icon: Power },
      { name: '5+ Doors', icon: Box }
    ],
    pricePerDay: 42.00,
    totalPrice: 42.00,
    days: 1
  };

  const customerInfo: CustomerInfo = {
    corporateCustomer: true,
    firstName: 'Emma',
    lastName: 'Shopria',
    email: '<EMAIL>',
    phone: '0412 587 369',
    address: '2/85 Hume Hwy,',
    postcode: '3091',
    country: 'Australia',
    birthday: '12/12/2005',
    companyName: 'ABC Company'
  };

  const drivingLicense: DrivingLicense = {
    type: 'Class C (Car License)',
    idNumber: '12345678',
    issueDate: '20/04/2024',
    expireDate: '20/04/2028',
    issueCountry: 'Australia'
  };

  const emergencyContact: EmergencyContact = {
    name: 'Rose Pereira',
    phone: '0412 785 412'
  };

  const pricing: PricingBreakdown = {
    vehiclePrice: 38.18,
    bondCompulsory: 500.00,
    childSeat: 36.36,
    insurance: 13.64,
    discount: 3.00,
    total: 594.00,
    amountRequired: 100.00
  };

  const handleTermsChange = (checked: boolean): void => {
    setAcceptTerms(checked);
  };

  const handleConfirmReservation = (): void => {
    if (acceptTerms) {
      navigate('/search/payment');
    }
  };

  const handleEditClick = (type: 'pickup' | 'return') => {
    setIsPopupOpen(true);
  };

  const handleSave = (newPickupDate: string, newPickupTime: string, newReturnDate: string, newReturnTime: string) => {
    setRentalDetails({
      ...rentalDetails,
      pickupDate: newPickupDate,
      pickupTime: newPickupTime,
      returnDate: newReturnDate,
      returnTime: newReturnTime
    });
    setIsPopupOpen(false);
  };

  const handleQuoteClick = (option: string) => {
    if (option === 'Print Quote') {
      // Handle print quote
    } else if (option === 'Send Quote') {
      // Handle send quote
    }
  };

  return (
    <div className="min-h-screen">
      <SearchHeader />
      <div className="w-full px-32 py-6">
        {/* Steps Bar */}
        <div style={{ marginLeft: '32px', marginRight: '32px' }}>
        <div className="p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
          <div className="flex items-center justify-center mb-2 sm:mb-4 md:mb-6 lg:mb-8 gap-1 sm:gap-2 md:gap-3 lg:gap-4">
            {steps.map((step, index) => {
              const isVisibleOnXs = visibleStepIndices.includes(index);
              const isVisibleOnSm = visibleStepIndices.includes(index);
              const isVisibleOnMd = visibleStepIndices.includes(index) || index <= activeIndex + 3;
              const isVisibleOnLg = true;

              return (
                <div
                  key={step.number}
                  className={`flex items-center ${!isVisibleOnXs ? 'hidden' : ''} ${isVisibleOnSm ? 'sm:flex' : 'sm:hidden'} ${isVisibleOnMd ? 'md:flex' : 'md:hidden'} ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-full flex items-center justify-center text-xs sm:text-sm md:text-base lg:text-base font-semibold ${
                        step.active ? 'bg-amber-500 text-white' : 'bg-gray-300 text-gray-600'
                      }`}
                    >
                      {step.number}
                    </div>
                    <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm mt-0.5 sm:mt-1">{step.label}</span>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`w-3 sm:w-4 md:w-6 lg:w-8 h-0.5 bg-gray-300 mx-0.5 sm:mx-1 md:mx-2 lg:mx-3 mt-[-12px] sm:mt-[-16px] md:mt-[-18px] lg:mt-[-20px] ${
                        !isVisibleOnXs || !visibleStepIndices.includes(index + 1) ? 'hidden' : ''
                      } ${isVisibleOnSm && visibleStepIndices.includes(index + 1) ? 'sm:flex' : 'sm:hidden'} ${
                        isVisibleOnMd && (isVisibleOnMd || index + 1 <= activeIndex + 3) ? 'md:flex' : 'md:hidden'
                      } ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                    ></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

        {/* Content area */}
        <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
          {/* Main content */}
          <div className="md:col-span-2 lg:col-span-3 space-y-4 xs:space-y-6 sm:space-y-8">
            <div className="bg-blue-50 p-2 xs:p-3 sm:p-4 md:p-6 rounded-lg border border-blue-200">
              <p className="text-xs xs:text-sm sm:text-base text-blue-800">
                Please bring your <strong className="text-xs xs:text-sm sm:text-base text-red-600">Driver License</strong> and the <strong className="text-xs xs:text-sm sm:text-base text-red-600">Bank Card</strong> when you come to pick up the vehicle.  
                (If you hold an international driver license please bring your passport and valid address document).
              </p>
              <p className="text-xs xs:text-sm sm:text-base text-red-600 mt-1 xs:mt-2">
                <strong>The AUD 100 reservation fee will be non-refundable in case of any cancellation. Please visit our 
                <a href="https://www.lioncarrentals.com.au/wp-content/uploads/2022/08/Lion-Rentals-Terms-and-Conditions-TG-19.07.19-69314648v1.pdf" className="underline"> T&C page</a> for more information.</strong>
              </p>
            </div>

            {/* Selected pickup & return details */}
            <div>
              <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Date & Time</h2>
              <hr className="border-gray-400 mb-2 xs:mb-3 sm:mb-4" />
              <div className="grid grid-cols-2 xs:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                <div>
                  <h3 className="font-medium text-gray-700 text-xs xs:text-sm sm:text-base">Pickup Date</h3>
                  <p className="text-xs xs:text-sm sm:text-base text-gray-600">{rentalDetails.pickupDate}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 text-xs xs:text-sm sm:text-base">Pickup Time</h3>
                  <p className="text-xs xs:text-sm sm:text-base text-gray-600">{rentalDetails.pickupTime}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 text-xs xs:text-sm sm:text-base">Return Date</h3>
                  <p className="text-xs xs:text-sm sm:text-base text-gray-600">{rentalDetails.returnDate}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 text-xs xs:text-sm sm:text-base">Return Time</h3>
                  <p className="text-xs xs:text-sm sm:text-base text-gray-600">{rentalDetails.returnTime}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 text-xs xs:text-sm sm:text-base">Pickup Location</h3>
                  <p className="text-xs xs:text-sm sm:text-base text-gray-600">{rentalDetails.pickupLocation}</p>
                </div>
                <div>
                  <h3 className="font-medium text-gray-700 text-xs xs:text-sm sm:text-base">Return Location</h3>
                  <p className="text-xs xs:text-sm sm:text-base text-gray-600">{rentalDetails.returnLocation}</p>
                </div>
              </div>
            </div>

            {/* Selected vehicle class details */}
            <div>
              <h2 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold mb-2 xs:mb-3 sm:mb-4 md:mb-5">
                Selected Vehicle Class
              </h2>
              <hr className="border-gray-400 mb-2 xs:mb-3 sm:mb-4 md:mb-5" />
              <div className="flex flex-col xs:flex-row items-start xs:items-center gap-2 xs:gap-3 sm:gap-4 md:gap-6">
                <div className="flex items-center justify-between w-full xs:w-auto">
                  <div className="w-full xs:w-32 sm:w-40 md:w-48 lg:w-56 h-24 xs:h-32 sm:h-36 md:h-40 lg:h-44 rounded flex items-center justify-center overflow-hidden">
                    <img
                      src={economyCar}
                      alt="Vehicle Class"
                      className="w-full h-full object-contain xs:h-24 sm:h-28 md:h-32 lg:h-36"
                    />
                  </div>
                  <div className="text-right xs:ml-4 sm:ml-6 md:ml-8 xs:w-auto">
                    <p className="text-base xs:text-lg sm:text-xl md:text-2xl font-bold">
                      AUD {vehicleClass.pricePerDay.toFixed(2)}
                      <span className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-500"> /Per Day</span>
                    </p>
                    <p className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-500">
                      AUD {vehicleClass.totalPrice.toFixed(2)} total for {vehicleClass.days} day{vehicleClass.days > 1 ? 's' : ''}
                      <br />
                      <span>including GST 10%</span>
                    </p>
                  </div>
                </div>
                <div className="flex-1 mt-2 xs:mt-0">
                  <h3 className="text-base xs:text-lg sm:text-xl md:text-2xl font-medium">
                    {vehicleClass.name}
                  </h3>
                  <div className="flex flex-wrap items-center gap-1 xs:gap-2 sm:gap-3 md:gap-4 text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600 mt-1 xs:mt-2 sm:mt-3">
                    {vehicleClass.features.map((feature: VehicleFeature, index: number) => (
                      <span key={index} className="flex items-center gap-1">
                        <feature.icon className="w-3 xs:w-4 sm:w-5 md:w-6 h-3 xs:h-4 sm:h-5 md:h-6 text-[#EBBB4E]" />
                        {feature.name}
                      </span>
                    ))}
                  </div>
                  <p className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600 mt-1 xs:mt-2 sm:mt-3">
                    Nissan Micra - 1AF1AW
                  </p>
                </div>
              </div>
            </div>

            {/* Bond Compulsory */}
            <div>
              <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Protection & Coverages</h2>
              <hr className="border-gray-400 mb-2 xs:mb-3 sm:mb-4" />
              <div className="space-y-3 xs:space-y-4 sm:space-y-6">
                <div className="flex items-center gap-2 xs:gap-3 sm:gap-4 p-2 xs:p-3 sm:p-4">
                  <Banknote className="w-10 xs:w-12 sm:w-14 h-10 xs:h-12 sm:h-14 text-gray-600" />
                  <span className="font-medium text-sm xs:text-base sm:text-lg">Bond Compulsory - 500</span>
                </div>
                <p className="text-xs xs:text-sm sm:text-base text-gray-600">
                  Provide your <strong>Credit Card</strong> when you pick up the vehicle and this will be reduced to AUD 250.
                </p>
                <p className="text-xs xs:text-sm sm:text-base text-red-600">
                  <strong>Please note if you want to pay the bond by Cash, it is AUD 1000.</strong>
                </p>
                <div className="mt-2 xs:mt-3 sm:mt-4">
                  <div className="inline-flex items-center justify-between p-2 xs:p-3 sm:p-4 border-2 border-gray-300 rounded-lg min-w-[16rem] xs:min-w-[18rem] sm:min-w-[20rem]">
                    <span className="font-medium text-xs xs:text-sm sm:text-base">AUD {pricing.bondCompulsory.toFixed(2)} - Debit</span>
                    <div className="w-5 xs:w-6 sm:w-6 h-5 xs:h-6 sm:h-6 bg-black rounded flex items-center justify-center">
                      <CheckCircle className="w-3 xs:w-4 sm:w-4 h-3 xs:h-4 sm:h-4 text-white" />
                    </div>
                  </div>
                </div>
                <div>
                  <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Insurance - 15</h2>
                  <div className="inline-flex items-center justify-between p-2 xs:p-3 sm:p-4 border-2 border-gray-300 rounded-lg min-w-[16rem] xs:min-w-[18rem] sm:min-w-[20rem] bg-green-100">
                    <span className="text-xs xs:text-sm sm:text-base">AUD {pricing.insurance.toFixed(2)}/Day</span>
                    <div className="w-5 xs:w-6 sm:w-6 h-5 xs:h-6 sm:h-6 bg-black rounded flex items-center justify-center">
                      <CheckCircle className="w-3 xs:w-4 sm:w-4 h-3 xs:h-4 sm:h-4 text-white" />
                    </div>
                  </div>
                </div>
              </div>
            </div>

            {/* Selected Equipment & Services */}
            <div>
              <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Equipment & Services</h2>
              <hr className="border-gray-400 mb-2 xs:mb-3 sm:mb-4" />
              <div className="flex items-centergap-2 xs:gap-3 sm:gap-4 p-2 xs:p-3 sm:p-4">
               <div className="flex items-center gap-2 xs:gap-3 sm:gap-4">
                  <Baby className="w-8 xs:w-10 sm:w-12 h-8 xs:h-10 sm:h-12 text-gray-600" />
                  <span className="text-xs xs:text-sm sm:text-base">Child Seat</span>
                </div>
                <div className="inline-flex items-center justify-between p-2 xs:p-3 sm:p-4 border-2 border-gray-300 rounded-lg min-w-[12rem] xs:min-w-[14rem] sm:min-w-[16rem]">
                  <span className="text-xs xs:text-sm sm:text-base">AUD {pricing.childSeat.toFixed(2)}/Day</span>
                  <div className="w-5 xs:w-6 sm:w-6 h-5 xs:h-6 sm:h-6 bg-black rounded flex items-center justify-center">
                    <CheckCircle className="w-3 xs:w-4 sm:w-4 h-3 xs:h-4 sm:h-4 text-white" />
                  </div>
                </div>
              </div>
            </div> 

            {/* Customer Details */}
            <div>
              <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Customer Information</h2>
              <hr className="border-gray-400 mb-2 xs:mb-3 sm:mb-4" />
              <div className="grid grid-cols-1 xs:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 text-xs xs:text-sm sm:text-base">
                <div>
                  <p className="font-medium text-gray-700">Corporate Customer</p>
                  <p className="text-gray-600">{customerInfo.corporateCustomer ? 'Yes' : 'No'}</p>
                </div>
                <div></div>
                <div>
                  <p className="font-medium text-gray-700">First Name</p>
                  <p className="text-gray-600">{customerInfo.firstName}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Last Name</p>
                  <p className="text-gray-600">{customerInfo.lastName}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Email Address</p>
                  <p className="text-gray-600">{customerInfo.email}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Phone Number</p>
                  <p className="text-gray-600">{customerInfo.phone}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Address</p>
                  <p className="text-gray-600">{customerInfo.address}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Postcode</p>
                  <p className="text-gray-600">{customerInfo.postcode}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Country</p>
                  <p className="text-gray-600">{customerInfo.country}</p>
                </div>
                <div></div>
                <div>
                  <p className="font-medium text-gray-700">Birthday</p>
                  <p className="text-gray-600">{customerInfo.birthday}</p>
                </div>
                <div></div>
                <div>
                  <p className="font-medium text-gray-700">Company Name</p>
                  <p className="text-gray-600">{customerInfo.companyName}</p>
                </div>
                <div></div>
              </div>
            </div>

            {/* Driving License details */}
            <div>
              <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Extra Information</h2>
              <hr className="border-gray-400 mb-2 xs:mb-3 sm:mb-4" />
              <div>
                <h3 className="font-medium text-gray-700 text-xs xs:text-sm sm:text-base mb-1 xs:mb-2 sm:mb-3">Driving License</h3>
                <div className="grid grid-cols-1 xs:grid-cols-2 sm:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 text-xs xs:text-sm sm:text-base">
                  <div>
                    <p className="font-medium text-gray-600">Type</p>
                    <p>{drivingLicense.type}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">ID Number</p>
                    <p>{drivingLicense.idNumber}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">Issue Date</p>
                    <p>{drivingLicense.issueDate}</p>
                  </div>
                  <div>
                    <p className="font-medium text-gray-600">Expire Date</p>
                    <p>{drivingLicense.expireDate}</p>
                  </div>
                </div>
                <div className="mt-1 xs:mt-2 sm:mt-3">
                  <p className="font-medium text-gray-600 text-xs xs:text-sm sm:text-base">Issue Country</p>
                  <p className="text-xs xs:text-sm sm:text-base">{drivingLicense.issueCountry}</p>
                </div>
              </div>
            </div>

            {/* Emergency Contact details */}
            <div>
              <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Emergency Contact Details</h2>
              <hr className="border-gray-400 mb-2 xs:mb-3 sm:mb-4" />
              <div className="grid grid-cols-1 xs:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 text-xs xs:text-sm sm:text-base">
                <div>
                  <p className="font-medium text-gray-700">Emergency Contact Person Name</p>
                  <p className="text-gray-600">{emergencyContact.name}</p>
                </div>
                <div>
                  <p className="font-medium text-gray-700">Emergency Contact Number</p>
                  <p className="text-gray-600">{emergencyContact.phone}</p>
                </div>
              </div>
            </div>

            {/* Admin Actions or Customer Terms */}
            {role === 'admin' ? (
              <div>
                {/* Comment Section */}
                <div>
                  <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Comments</h2>
                  <hr className="border-gray-400 mb-2 xs:mb-3 sm:mb-4" />
                  <Textarea
                    value={comments}
                    onChange={(e) => setComments(e.target.value)}
                    placeholder="Add your comments here..."
                    className="focus:outline-none focus:ring-0 focus:border-none "
                  />
                  <Button
                    onClick={handleSaveComment}
                    className="mt-2 xs:mt-3 sm:mt-4 "
                  >
                    Save Comment
                  </Button>
                </div>

                <div className="flex flex-col gap-2 mt-4 xs:mt-6 sm:mt-8">
                  {/* Send Confirmation Email */}
                  <div className="flex items-center gap-2">
                    <Checkbox
                      id="sendConfirmationEmail"
                      checked={sendConfirmationEmail}
                      onCheckedChange={(checked) => setSendConfirmationEmail(checked as boolean)}
                      className="w-4 sm:w-5 md:w-6 h-4 sm:h-5 md:h-6"
                    />
                    <Label htmlFor="sendConfirmationEmail" className="text-gray-500">
                      Send Confirmation Email
                    </Label>
                  </div>
                  {/* Buttons stacked vertically on xs and md, two-column on lg/xl */}
                  <div className="space-y-2 xs:space-y-3 sm:space-y-4 md:space-y-2 lg:grid lg:grid-cols-2 lg:gap-2 lg:space-y-0">
                    <Button
                      className="bg-[#330101] text-white hover:bg-[#660404] w-full px-4 py-2"
                      onClick={() => navigate('/search/payment')}>
                      Confirm Reservation
                    </Button>
                    <Button
                      variant="outline"
                      className="border-yellow-400 w-full px-4 py-2"
                      onClick={() => { /* Handle */ }}>
                      Confirm as Pending
                    </Button>
                    <Button
                      variant="outline"
                      className="border-yellow-400 w-full px-4 py-2"
                      onClick={() => { /* Handle */ }}>
                      Confirm & Clone
                    </Button>
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button
                          variant="outline"
                          className="border-yellow-400 w-full px-4 py-2">
                          Quote
                          <ChevronDown className="w-3 h-3 text-gray-500 ml-2" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent>
                        <DropdownMenuItem onClick={() => handleQuoteClick('Print Quote')}>Print Quote</DropdownMenuItem>
                        <DropdownMenuItem onClick={() => handleQuoteClick('Send Quote')}>Send Quote</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </div>
                </div>
              </div>
            ) : (
              <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-1 sm:gap-2 md:gap-3 lg:gap-4">
                <div className="flex items-center gap-1 sm:gap-2 md:gap-3">
                  <Checkbox
                    id="terms"
                    checked={acceptTerms}
                    onCheckedChange={handleTermsChange}
                    className="w-4 sm:w-5 md:w-6 h-4 sm:h-5 md:h-6"
                  />
                  <Label
                    htmlFor="terms"
                    className="text-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base leading-tight sm:leading-normal"
                  >
                    By clicking here, I confirm that I accept the{' '}
                    <a href="https://www.lioncarrentals.com.au/wp-content/uploads/2022/08/Lion-Rentals-Terms-and-Conditions-TG-19.07.19-69314648v1.pdf" className="underline hover:text-blue-600">
                      Terms and Conditions
                    </a>
                    .
                  </Label>
                </div>
                
                <Button
                  className={`w-full sm:w-auto px-3 sm:px-4 md:px-6 lg:px-8 py-1 sm:py-2 md:py-3 font-semibold text-[10px] sm:text-xs md:text-sm lg:text-base ${
                    acceptTerms
                      ? 'bg-[#330101] text-white hover:bg-amber-800'
                      : 'bg-gray-300 text-gray-500 cursor-not-allowed'
                  }`}
                  disabled={!acceptTerms}
                  onClick={handleConfirmReservation}
                >
                  Confirm Reservation
                </Button>
              </div>
            )}
          </div>

          {/* Summary section */}
          <div className="md:col-span-1">
            <div className="p-2 xs:p-3 sm:p-4 md:p-6 rounded-lg shadow-sm top-4 xs:top-6">

              {role === 'admin' && (
              <div className="mb-4">
                <label className="block text-sm sm:text-base md:text-lg lg:text-lg font-semibold mb-2">Rate Type</label>
                <select
                  value={rentalDetails.rateType || ''}
                  onChange={(e) => setRentalDetails({ ...rentalDetails, rateType: e.target.value })}
                  className="w-auto p-2 border rounded text-sm sm:text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                >
                  <option value="New Year - 2021">New Year - 2021</option>
                  <option value="Long Term Rental">Long Term Rental</option>
                </select>
              </div>
            )}

              <h2 className="text-base xs:text-lg sm:text-xl font-semibold mb-4 xs:mb-6">Summary</h2>
              <span className="bg-gray-500 text-white text-xs xs:text-sm sm:text-base font-semibold px-2 xs:px-3 py-1 xs:py-2 rounded-md">Quote</span>
              <div className="space-y-2 xs:space-y-3 sm:space-y-4 text-xs xs:text-sm sm:text-base">
                <div className="mt-2 xs:mt-3 sm:mt-4">
                  <div className="flex items-center gap-2 xs:gap-3">
                    <h2 className="text-sm xs:text-base sm:text-lg font-semibold">Pickup</h2>
                    <Button
                      variant="ghost"
                      onClick={() => handleEditClick('pickup')}
                      className="p-1 hover:bg-transparent"
                    >
                      <Pencil 
                        size={14}
                        className="text-gray-500 hover:text-blue-600" 
                      />
                    </Button>
                  </div>
                  <p className="text-xs xs:text-sm sm:text-base">{`Friday, ${rentalDetails.pickupDate} at ${rentalDetails.pickupTime}`}</p>
                  <p className="text-xs xs:text-sm sm:text-base">{rentalDetails.pickupLocation}</p>
                </div>

                <div>
                  <div className="flex items-center gap-2 xs:gap-3">
                    <h3 className="text-sm xs:text-base sm:text-lg font-semibold">Return</h3>
                    <Button
                      variant="ghost"
                      onClick={() => handleEditClick('return')}
                      className="p-1 hover:bg-transparent"
                    >
                      <Pencil 
                        size={14} 
                        className="text-gray-500 hover:text-blue-600"
                      />
                    </Button>
                  </div>
                  <p className="text-xs xs:text-sm sm:text-base">{`Saturday, ${rentalDetails.returnDate} at ${rentalDetails.returnTime}`}</p>
                  <p className="text-xs xs:text-sm sm:text-base">{rentalDetails.returnLocation}</p>
                </div>

                <hr className="my-2 xs:my-3 sm:my-4" />

                <div>
                  <p className="text-sm xs:text-base sm:text-lg font-semibold">{vehicleClass.name}</p>
                  <div className="flex justify-between">
                    <span className="text-xs xs:text-sm sm:text-base">{vehicleClass.days} Day</span>
                    <span className="text-xs xs:text-sm sm:text-base">AUD {pricing.vehiclePrice.toFixed(2)}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm xs:text-base sm:text-lg font-semibold">Protection & Coverages</p>
                  <div className="flex justify-between">
                    <span className="text-xs xs:text-sm sm:text-base">Bond Compulsory - 500</span>
                    <span className="text-xs xs:text-sm sm:text-base">AUD {pricing.bondCompulsory.toFixed(2)}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm xs:text-base sm:text-lg font-semibold">Equipment & Services</p>
                  <div className="flex justify-between">
                    <span className="text-xs xs:text-sm sm:text-base">Child Seat</span>
                    <span className="text-xs xs:text-sm sm:text-base">AUD {pricing.childSeat.toFixed(2)}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm xs:text-base sm:text-lg font-semibold">Miscellaneous</p>
                  <div className="flex justify-between">
                    <span className="text-xs xs:text-sm sm:text-base">Insurance</span>
                    <span className="text-xs xs:text-sm sm:text-base">AUD {pricing.insurance.toFixed(2)}</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm xs:text-base sm:text-lg font-semibold">Included Distance</p>
                  <div className="flex justify-between">
                    <span className="text-xs xs:text-sm sm:text-base">200 km</span>
                  </div>
                </div>

                <div>
                  <p className="text-sm xs:text-base sm:text-lg font-semibold">Discount</p>
                  <div className="flex justify-between">
                    <span className="text-xs xs:text-sm sm:text-base">10%</span>
                    <span className="text-xs xs:text-sm sm:text-base">AUD {pricing.discount.toFixed(2)}</span>
                  </div>
                </div>

                <hr className="my-2 xs:my-3 sm:my-4" />

                <div>
                  <p className="text-sm xs:text-base sm:text-lg font-semibold">Total</p>
                  <div className="flex justify-between font-bold">
                    <span className="text-xs xs:text-sm sm:text-base"></span>
                    <span className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-semibold">AUD {pricing.total.toFixed(2)}</span>
                  </div>
                </div>

                <div>
                  <p className="font-medium text-gray-700 text-sm xs:text-base sm:text-lg">Amount Required</p>
                  <div className="flex justify-between">
                    <span className="text-xs xs:text-sm sm:text-base"></span>
                    <span className="text-lg xs:text-xl sm:text-2xl md:text-3xl">AUD {pricing.amountRequired.toFixed(2)}</span>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Popup for editing dates and times */}
        {isPopupOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl">
              <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold mb-2 sm:mb-4">Update Date and Times</h2>
              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                <div>
                  <Label>Pickup Date </Label>
                  <Input
                    value={rentalDetails.pickupDate}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, pickupDate: e.target.value })}
                    className="focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label>Pickup Time</Label>
                  <Input
                    value={rentalDetails.pickupTime}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, pickupTime: e.target.value })}
                    className="focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label>Return Date</Label>
                  <Input
                    value={rentalDetails.returnDate}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, returnDate: e.target.value })}
                    className="focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label>Return Time</Label>
                  <Input
                    value={rentalDetails.returnTime}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, returnTime: e.target.value })}
                    className="focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
              </div>
              <div className="mt-2 sm:mt-4 flex justify-end gap-2 sm:gap-4">
                <Button
                  className="bg-gray-200 hover:bg-gray-300"
                  onClick={() => setIsPopupOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  className="bg-[#330101] text-white hover:bg-amber-800"
                  onClick={() => handleSave(
                    rentalDetails.pickupDate,
                    rentalDetails.pickupTime,
                    rentalDetails.returnDate,
                    rentalDetails.returnTime
                  )}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>
    </div>
  );
}

