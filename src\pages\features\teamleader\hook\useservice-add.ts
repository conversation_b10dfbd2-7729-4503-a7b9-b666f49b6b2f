import { NavigateFunction } from 'react-router-dom';
import { ServicesFormData, ServiceItem } from '../type/teamleadertype';

export const handleInputChange = (
  field: keyof ServicesFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<ServicesFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};

export const handleServiceChange = (
  id: number,
  field: keyof ServiceItem,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<ServicesFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    services: prev.services.map(service =>
      service.id === id ? { ...service, [field]: value } : service
    )
  }));
};

export const addService = (
  setFormData: React.Dispatch<React.SetStateAction<ServicesFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    services: [...prev.services, { id: Date.now(), serviceName: '', description: '' }]
  }));
};

export const removeService = (
  id: number,
  setFormData: React.Dispatch<React.SetStateAction<ServicesFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    services: prev.services.filter(service => service.id !== id)
  }));
};

export const handleSave = (navigate: NavigateFunction): void => {
  navigate('/teamleader/services');
};