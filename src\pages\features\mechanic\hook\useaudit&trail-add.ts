import { useState } from "react";

// Mock data: rego to service type mapping
const regoServiceTypeMap: Record<string, string> = {
  "1PX 1ZR": "General Maintenance",
  "1PX 5ZP": "Accident Repair",
  "2AB 3CD": "Tyre Replacement",
  "4EF 5GH": "Battery Replacement",
};

const regoOptions = Object.keys(regoServiceTypeMap);

export function useAuditTrailAdd({ navigate }: { navigate: (path: string) => void }) {
  const [rego, setRegoState] = useState("");
  const [serviceType, setServiceType] = useState("");
  const [description, setDescription] = useState("");

  // When rego changes, update serviceType automatically
  const setRego = (value: string) => {
    setRegoState(value);
    setServiceType(regoServiceTypeMap[value] || "");
  };

  const handleAdd = () => {
    // Submit logic here if needed
    navigate("/mechanic/audit-trail");
  };

  const handleCancel = () => {
    navigate("/mechanic/audit-trail");
  };

  return {
    rego,
    setRego,
    serviceType,
    description,
    setDescription,
    regoOptions,
    handleAdd,
    handleCancel,
  };
}