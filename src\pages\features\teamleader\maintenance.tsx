import React, { useState } from 'react';
import { Search, Edit, Wrench } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Pagination } from '@/components/layout/Pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { MaintenanceRecord } from './type/teamleadertype';
import { handleVehicleIdClick, getStatusColor } from './hook/usemaintenance';
import { maintenanceData } from './common/mockData';

export const MaintenancePage = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [activeTab, setActiveTab] = useState('General Maintenance');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const navigate = useNavigate();

  const filteredData = maintenanceData.filter(
    (record) =>
      record.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.maintenanceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.actualEndDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.estimatedEndDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      record.mechanic.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const paginatedData = filteredData.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  );

  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      setCurrentPage(page);
    }
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center mb-4 sm:mb-0">
            <Wrench className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-gray-700" />
            <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">General Maintenance</h1>
          </div>
        </div>

        {/* Tab Bar */}
        <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
          {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
            <Button
              key={tab}
              variant={activeTab === tab ? 'default' : 'outline'}
              onClick={() => {
              if (tab === 'All') {
                navigate('/teamleader/service-management');
              } else {
                setActiveTab(tab);
              }
              if (tab === 'Accident') {
                navigate('/teamleader/service-accident');
              } else {
                setActiveTab(tab);
              }
              if (tab === 'Breakdowns') {
                navigate('/teamleader/service-breakdown');
              } else {
                setActiveTab(tab);
              }
              if (tab === 'Damage') {
                navigate('/teamleader/service-damage');
              } else {
                setActiveTab(tab);
              }
             }}
            className="text-sm md:text-base"
            >
          {tab}
        </Button>
       ))}
      </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          <div className="relative w-full sm:w-auto">
            <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
              <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
                <SelectValue placeholder="Filter" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All</SelectItem>
                <SelectItem value="Pending">Pending</SelectItem>
                <SelectItem value="Inprogress">InProgress</SelectItem>
                <SelectItem value="Done">Done</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="relative flex-1 order-3 sm:order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing a name..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
      </div>

      {/* Table */}
      <div className="rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle ID</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Maintenance Type</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Estimated End Date</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Actual End Date</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Status</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Status of the Vehicle</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle Current Renter</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Mechanic Assigned</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedData.map((record: MaintenanceRecord) => (
                <TableRow key={record.vehicleId}>
                  <TableCell className="text-sm px-3 py-4">{record.vehicleId}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.vehicle}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.maintenanceType}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.estimatedEndDate}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.actualEndDate}</TableCell>
                  <TableCell className="px-3 py-4">
                    <span className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(record.status)}`}>
                      {record.status}
                    </span>
                  </TableCell>
                  <TableCell className="px-3 py-4">
                    <span className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(record.statusOfVehicle)}`}>
                      {record.statusOfVehicle}
                    </span>
                  </TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.vehicleCurrentRenter}</TableCell>
                  <TableCell className="text-sm px-3 py-4">{record.mechanic}</TableCell>
                  <TableCell className="px-3 py-4">
                    <Button
                      onClick={() => handleVehicleIdClick(record.vehicleId, navigate)}
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={entriesPerPage}
        onPageChange={handlePageChange}
        onRecordsPerPageChange={(records) => {
          setEntriesPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-4 sm:mt-6"
      />
    </div>
  );
};