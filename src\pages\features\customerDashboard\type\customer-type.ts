export interface ProfileProps {
  firstName: string;
  lastName: string;
  email: string;
  address: string;
  postCode: string;
  country: string;
  birthday: string;
  phonenumber: number;
  companyName: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  licenseNumber: string;
  licenseIssueDate: string;
  licenseExpiryDate: string;
  licenseCountry: string;
  licenseType: string;
}

export interface ProfileProps {
  firstName: string;
  lastName: string;
  email: string;
  birthday: string;
  phonenumber: number;
  address: string;
  postCode: string;
  country: string;
  companyName: string;
  emergencyContactName: string;
  emergencyContactPhone: string;
  licenseNumber: string;
  licenseIssueDate: string;
  licenseExpiryDate: string;
  licenseCountry: string;
  licenseType: string;
}

export interface EditprofileProps extends ProfileProps {}

export interface LicenseProps {
  permitNumber: string;
  issueCountry: string;
  issueDate: Date | undefined;
  expiryDate: Date | undefined;
  licenseType: string;
  frontView: File | null;
  backView: File | null;
}

export interface AddNewLicenseProps extends LicenseProps {}

export interface Booking {
  id: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  vehicle: string;
  totalPrice: number;
  outstandingBalance: number;
  status: 'Rental' | 'Open' | 'Completed';
  loanVehicle: 'Yes' | 'No';
}

export interface BookingHistoryProps {}

export interface Booking {
  id: string;
  outstandingBalance: number;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  vehicle: string;
  totalPrice: number;
  loanVehicle: 'Yes' | 'No';
}

export interface InvoiceItem {
  description: string;
  rate: string;
  days: string;
  amount: string;
}

export interface BookingSummaryProps {}

export interface Booking {
  id: string;
  vehicle: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  incidentDate: string;
  reportType: 'Accident' | 'Breakdown' | 'General Maintenance' | 'Damage';
  vehicleReplaced?: 'Yes' | 'No';
  replacedVehicle: string;
  replacedDate: string;
}

export interface IncidentReportingProps {}

export interface IncidentReportFormProps {}

export interface Reservation {
  rego: string;
  rentalId: string;
  pickupDate: Date;
  returnDate: Date;
  agreementNo: string;
}

export interface AccidentReportFields {
  payment: string;
  insuranceExcessCover: string;
  driverName: string;
  phoneNumber: string;
  email: string;
  birthday: Date | undefined;
  address: string;
  postcode: string;
  country: string;
  drugsAlcoholIncident: string;
  licenseNumber: string;
  issueDate: Date | undefined;
  expiryDate: Date | undefined;
  conditions: string;
  frontView: File | null;
  backView: File | null;
  nextDue: Date | undefined;
  obtainDetails: string;
  isDrugsAlcoholConsumed: boolean | undefined;
}

export interface AdditionalAccidentFields {
  accidentLocation: string;
  damageDescription: string;
  policeReport: File | null;
  witnessDetails: string;
}

export interface BreakdownMaintenanceFields {
  incidentDate: Date | undefined;
  daysLeft: number;
  odometerReading: string;
  fuelGauge: string;
  note: string;
  interiorImages: File | null;
  exteriorImages: File | null;
}

export interface AccidentFormSecondData {
  damageType: 'vehicle' | 'property' | '';
  driverName: string;
  phoneNumber: string;
  address: string;
  postCode: string;
  country: string;
  email: string;
  dlNumber: string;
  issueCountry: string;
  issueDate: string;
  expiryDate: string;
  conditions: string;
  frontViewImage: File | null;
  backViewImage: File | null;
  vehicleType: string;
  rego: string;
  color: string;
  interiorImages: File[];
  exteriorImages: File[];
  leftSideImages: File[];
  rightSideImages: File[];
  frontSideImages: File[];
  backSideImages: File[];
  sideMirrorImages: File[];
  vehicleBodyImages: File[];
}

export interface AccidentFormSecondProps {}

export interface AccidentPoliceDetailsData {
  policeNotified: 'yes' | 'no' | '';
  policeInformation: string;
  policeAction: string;
  officerBatchNo: string;
  policeStation: string;
  stationPhoneNumber: string;
  reportNumber: string;
}

export interface AccidentPoliceDetailsProps {}

export interface AccidentSignatureFormData {
  completedBy: string;
  submissionDate: string;
  submissionTime: string;
}

export interface AccidentSignatureFormProps {}

export interface ChangeRequest {
  id: string;
  vehicle: string;
  vehicleClass: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  requestType: string;
  status: string;
  note?: string;
}

export interface ChangeRequestPageProps {}

export interface FormData {
  rego: string;
  rentalId: string;
  vehicleClass: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  requestType: string;
  extensionFromDate: string;
  extensionToDate: string;
  newReturnDate: string;
  newReturnTime: string;
  note: string;
}

export interface AddRequestPageProps {}

export interface ChangeRequest {
  id: string;
  vehicle: string;
  vehicleClass: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  requestType: string;
  status: string;
  note?: string;
  extensionFromDate?: string;
  extensionToDate?: string;
  newReturnDate?: string;
  newReturnTime?: string;
}

export interface FormErrors {
  [key: string]: string;
}

export interface Invoice {
  id: string;
  rentalId: string;
  type: string;
  totalAmount: number;
  receivedAmount: number;
  dueAmount: number;
  paymentType: string;
  status: 'Completed' | 'Pending Payment';
}

export interface InvoiceItem {
  description: string;
  rate: string;
  days: string;
  amount: string;
}

export interface Notification {
  id: number;
  type: string;
  message: string;
  date: string;
}

export interface ActivityEntry {
  id: number;
  type: string;
  message: string;
  date: string;
}

export interface PasswordFormData {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}