import { NavigateFunction } from 'react-router-dom';
import { CustomerFormData } from '../type/reception-type';
import { bookingsData } from '../common/mockData';

export const handleInputChange = (
  field: keyof CustomerFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<CustomerFormData>>
) => {
  setFormData((prev) => ({ ...prev, [field]: value }));
};

export const handleBirthdayChange = (
  field: keyof CustomerFormData['birthday'],
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<CustomerFormData>>
) => {
  setFormData((prev) => ({
    ...prev,
    birthday: { ...prev.birthday, [field]: value }
  }));
};

export const handleFrontViewChange = (
  e: React.ChangeEvent<HTMLInputElement>,
  setFrontViewFile: React.Dispatch<React.SetStateAction<File | null>>
) => {
  const file = e.target.files?.[0] || null;
  setFrontViewFile(file);
};

export const handleBackViewChange = (
  e: React.ChangeEvent<HTMLInputElement>,
  setBackViewFile: React.Dispatch<React.SetStateAction<File | null>>
) => {
  const file = e.target.files?.[0] || null;
  setBackViewFile(file);
};

export const handleSave = (
  navigate: NavigateFunction,
  formData: CustomerFormData,
  frontViewFile: File | null,
  backViewFile: File | null
): void => {
  bookingsData.push({
    id: formData.customerId,
    customerName: `${formData.firstName} ${formData.lastName}`,
    customerPhone: formData.phone,
    obligtionNo: 'N/A',
    penaltyAmount: 0,
    dueDate: 'N/A',
    vehicle: 'N/A',
    offenseDate: 'N/A',
    offenseTime: 'N/A',
    type: formData.isCorporate === 'true' ? 'Cooperate' : 'Normal',
    license: formData.dlNumber,
    expireDate: formData.expiryDate,
    reservationCount: '0',
    status: 'Active',
    frontLicenseImage: frontViewFile ? `/assets/frontView${formData.customerId}.png` : undefined,
    backLicenseImage: backViewFile ? `/assets/backView${formData.customerId}.png` : undefined
  });

  navigate('/reception/reception-customer');
};