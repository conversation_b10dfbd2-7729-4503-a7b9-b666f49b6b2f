import React, { useState, useEffect } from 'react';
import SearchHeader from '@/components/layout/SearchHeader';
import { useNavigate } from 'react-router-dom';
import { CalendarDays, ChevronDown, X } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from '@/components/ui/popover';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuItem,
} from '@/components/ui/dropdown-menu';
import car from '@/assets/car.png';
import van from '@/assets/van.png';


const useAuth = () => {
  const [role] = useState<'admin' | 'customer'>('admin'); 
  return { role };
};

interface Step {
  number: number;
  label: string;
  active: boolean;
}

type FleetType = 'passenger' | 'commercial';

export function SearchFormPage(): JSX.Element {
  const navigate = useNavigate();
  const { role } = useAuth();
  const handleNextStep = (): void => {
    navigate('/search/vehicle-class');
  };

  const [selectedFleet, setSelectedFleet] = useState<FleetType>('passenger');
  const [pickupDateObj, setPickupDateObj] = useState<Date>(new Date());
  const [dropoffDateObj, setDropoffDateObj] = useState<Date>(() => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    return tomorrow;
  });

  const [pickupLocation, setPickupLocation] = useState<string>('Somerton');
  const [dropoffLocation, setDropoffLocation] = useState<string>('Somerton');
  const [pickupAddress, setPickupAddress] = useState<string>('');
  const [returnAddress, setReturnAddress] = useState<string>('');
  const [pickupDateInput, setPickupDateInput] = useState<string>('');
  const [dropoffDateInput, setDropoffDateInput] = useState<string>('');
  const [pickupTime, setPickupTime] = useState<string>('11.30 am');
  const [dropoffTime, setDropoffTime] = useState<string>('10.30 am');
  const [pickupCalendarOpen, setPickupCalendarOpen] = useState<boolean>(false);
  const [dropoffCalendarOpen, setDropoffCalendarOpen] = useState<boolean>(false);
  const [age, setAge] = useState<string>('Age 25 +');
  const [residency, setResidency] = useState<string>('Residency: Australia');
  const [walkInCustomer, setWalkInCustomer] = useState<boolean>(false);
  const [comments, setComments] = useState<string>('');
  const [savedComments, setSavedComments] = useState<string[]>([]);
  const locationOptions: string[] = ['Somerton', 'Melbourne', 'Sydney', 'Pickup Address'];

  useEffect(() => {
    setPickupDateInput(formatDateForInput(pickupDateObj));
    setDropoffDateInput(formatDateForInput(dropoffDateObj));
  }, []);

  const formatDateForInput = (date: Date): string => {
    const day = date.getDate().toString().padStart(2, '0');
    const month = (date.getMonth() + 1).toString().padStart(2, '0');
    const year = date.getFullYear();
    return `${day}/${month}/${year}`;
  };

  const parseDateFromInput = (dateString: string): Date | null => {
    const parts = dateString.split('/');
    if (parts.length === 3) {
      const day = parseInt(parts[0], 10);
      const month = parseInt(parts[1], 10) - 1;
      const year = parseInt(parts[2], 10);
      const date = new Date(year, month, day);
      if (!isNaN(date.getTime())) {
        return date;
      }
    }
    return null;
  };

  const isWeekend = (date: Date): boolean => {
    const day = date.getDay();
    return day === 0 || day === 6;
  };

  const checkOfficeHours = (time: string, date: Date): void => {
    if (isNaN(date.getTime())) {
      console.error('Invalid date:', date);
      return;
    }
    const [hourStr, period] = time.split(' ');
    if (!hourStr || !period) {
      console.error('Invalid time format:', time);
      return;
    }
    const [hoursStr, minutesStr] = hourStr.split('.');
    const hours = parseInt(hoursStr, 10);
    const minutes = parseInt(minutesStr || '0', 10);
    if (isNaN(hours) || isNaN(minutes)) {
      console.error('Failed to parse hours or minutes:', { hoursStr, minutesStr });
      return;
    }
    let adjustedHours = hours;
    if (period.toLowerCase() === 'pm' && hours !== 12) adjustedHours += 12;
    if (period.toLowerCase() === 'am' && hours === 12) adjustedHours = 0;

    const selectedTime = adjustedHours * 60 + minutes;
    const formattedDate = formatDateForInput(date);

    if (isWeekend(date)) {
      const weekendStart = 8 * 60 + 30; // 08:30
      const weekendEnd = 12 * 60 + 30; // 12:30
      if (selectedTime < weekendStart || selectedTime > weekendEnd) {
        alert(`We are sorry! Our office is closed at this time\nOur opening hours on ${formattedDate} are: 08:30 - 12:30`);
      }
    } else {
      const weekdayStart = 7 * 60 + 30; // 07:30
      const weekdayEnd = 17 * 60 + 30; // 17:30
      if (selectedTime < weekdayStart || selectedTime > weekdayEnd) {
        alert(`We are sorry! Our office is closed at this time\nOur opening hours on ${formattedDate} are: 07:30 - 17:30`);
      }
    }
  };

  const handlePickupDateSelect = (date: Date | undefined): void => {
    if (date) {
      setPickupDateObj(date);
      setPickupDateInput(formatDateForInput(date));
      setPickupCalendarOpen(false);
      checkOfficeHours(pickupTime, date);
    }
  };

  const handleDropoffDateSelect = (date: Date | undefined): void => {
    if (date) {
      setDropoffDateObj(date);
      setDropoffDateInput(formatDateForInput(date));
      setDropoffCalendarOpen(false);
      checkOfficeHours(dropoffTime, date);
    }
  };

  const handlePickupInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setPickupDateInput(value);
    if (value.length === 10) {
      const parsedDate = parseDateFromInput(value);
      if (parsedDate) {
        setPickupDateObj(parsedDate);
        checkOfficeHours(pickupTime, parsedDate);
      }
    }
  };

  const handleDropoffInputChange = (e: React.ChangeEvent<HTMLInputElement>): void => {
    const value = e.target.value;
    setDropoffDateInput(value);
    if (value.length === 10) {
      const parsedDate = parseDateFromInput(value);
      if (parsedDate) {
        setDropoffDateObj(parsedDate);
        checkOfficeHours(dropoffTime, parsedDate);
      }
    }
  };

  const handlePickupTimeSelect = (time: string): void => {
    setPickupTime(time);
    checkOfficeHours(time, pickupDateObj);
  };

  const handleDropoffTimeSelect = (time: string): void => {
    setDropoffTime(time);
    checkOfficeHours(time, dropoffDateObj);
  };

  const [discountCodeExpanded, setDiscountCodeExpanded] = useState<boolean>(false);
  const [awdNumber, setAwdNumber] = useState<string>('');
  const [couponCode, setCouponCode] = useState<string>('');
  const [rateCode, setRateCode] = useState<string>('');

  const timeOptions: string[] = ['06.00 am', '11.30 am', '12.00 pm', '12.30 pm', '1.00 pm', '6.00 pm'];
  const dropoffTimeOptions: string[] = ['06.00 am', '10.30 am', '11.00 am', '11.30 am', '12.00 pm', '6.00 pm'];
  const ageOptions: string[] = ['Age 25 +', 'Age 21-24', 'Age 18-20'];
  const countryOptions: string[] = ['Australia', 'Sri Lanka', 'New Zealand'];

  const handleSaveComment = (): void => {
    if (comments.trim()) {
      setSavedComments([...savedComments, comments]);
      setComments('');
    }
  };

  const steps: Step[] = role === 'admin' ? [
    { number: 1, label: 'Dates', active: true },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: false },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: false },
    { number: 6, label: 'Payment', active: false },
    { number: 7, label: 'Receipt', active: false },
    { number: 8, label: 'Pickup', active: false },
    { number: 9, label: 'Agreement', active: false },
    { number: 10, label: 'Return', active: false },
  ] : [
    { number: 1, label: 'Dates', active: true },
    { number: 2, label: 'Vehicles', active: false },
    { number: 3, label: 'Extras', active: false },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: false },
    { number: 6, label: 'Payment', active: false },
    { number: 7, label: 'Receipt', active: false },
  ];

  const activeIndex = steps.findIndex(step => step.active);

  const getVisibleSteps = () => {
    const visible = [activeIndex];
    if (activeIndex - 1 >= 0) visible.unshift(activeIndex - 1);
    if (activeIndex - 2 >= 0) visible.unshift(activeIndex - 2);
    if (activeIndex + 1 < steps.length) visible.push(activeIndex + 1);
    if (activeIndex + 2 < steps.length) visible.push(activeIndex + 2);
    return visible.sort((a, b) => a - b);
  };

  const visibleStepIndices = getVisibleSteps();
  const showPickupAddressFields = role === 'admin' && pickupLocation === 'Pickup Address';
  const showReturnAddressFields = role === 'admin' && dropoffLocation === 'Pickup Address';

  return (
    <div className="min-h-screen">
      <SearchHeader />

      {/* Steps Bar */}
      <div style={{ marginLeft: '32px', marginRight: '32px' }}>
        <div className="p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
          <div className="flex items-center justify-center mb-2 sm:mb-4 md:mb-6 lg:mb-8 gap-1 sm:gap-2 md:gap-3 lg:gap-4">
            {steps.map((step, index) => {
              const isVisibleOnXs = visibleStepIndices.includes(index);
              const isVisibleOnSm = visibleStepIndices.includes(index);
              const isVisibleOnMd = visibleStepIndices.includes(index) || index <= activeIndex + 3;
              const isVisibleOnLg = true;

              return (
                <div
                  key={step.number}
                  className={`flex items-center ${!isVisibleOnXs ? 'hidden' : ''} ${isVisibleOnSm ? 'sm:flex' : 'sm:hidden'} ${isVisibleOnMd ? 'md:flex' : 'md:hidden'} ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-full flex items-center justify-center text-xs sm:text-sm md:text-base lg:text-base font-semibold ${
                        step.active ? 'bg-amber-500 text-white' : 'bg-gray-300 text-gray-600'
                      }`}
                    >
                      {step.number}
                    </div>
                    <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm mt-0.5 sm:mt-1">{step.label}</span>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`w-3 sm:w-4 md:w-6 lg:w-8 h-0.5 bg-gray-300 mx-0.5 sm:mx-1 md:mx-2 lg:mx-3 mt-[-12px] sm:mt-[-16px] md:mt-[-18px] lg:mt-[-20px] ${
                        !isVisibleOnXs || !visibleStepIndices.includes(index + 1) ? 'hidden' : ''
                      } ${isVisibleOnSm && visibleStepIndices.includes(index + 1) ? 'sm:flex' : 'sm:hidden'} ${
                        isVisibleOnMd && (isVisibleOnMd || index + 1 <= activeIndex + 3) ? 'md:flex' : 'md:hidden'
                      } ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                    ></div>
                  )}
                </div>
              );
            })}
          </div>
        </div>
      </div>

      <div className="flex flex-col xs:flex-col sm:flex-row gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-4 xs:mb-6 sm:mb-8 md:mb-10 justify-center px-2 xs:px-4 sm:px-6 md:px-8">
        <Button
          variant="outline"
          size="lg"
          onClick={() => setSelectedFleet('passenger')}
          className={`w-full xs:w-full sm:w-auto px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 py-2 xs:py-3 sm:py-4 rounded-lg border-2 transition-all
            ${selectedFleet === 'passenger' ? 'bg-amber-500 text-white border-amber-500' : 'bg-white text-gray-700 border-gray-200 hover:border-amber-300'}`}
        >
          <img src={car} alt="Passenger Fleet" className="h-3 xs:h-4 sm:h-5 md:h-6 mr-1 xs:mr-2" />
          <span className="font-semibold text-yellow-700 text-xs xs:text-sm sm:text-base md:text-lg">
            PASSENGER FLEET
          </span>
        </Button>

        <Button
          variant="outline"
          size="lg"
          onClick={() => setSelectedFleet('commercial')}
          className={`w-full xs:w-full sm:w-auto px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 py-2 xs:py-3 sm:py-4 rounded-lg border-2 transition-all
            ${selectedFleet === 'commercial' ? 'bg-amber-500 text-white border-amber-500' : 'bg-white text-gray-700 border-gray-200 hover:border-amber-300'}`}
        >
          <img src={van} alt="Commercial Fleet" className="h-3 xs:h-4 sm:h-5 md:h-6 mr-1 xs:mr-2" />
          <span className="font-semibold text-yellow-700 text-xs xs:text-sm sm:text-base md:text-lg">
            COMMERCIAL FLEET
          </span>
        </Button>
      </div>

      <div className="max-w-full xs:max-w-[95%] sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl mx-auto">
        <div className="bg-white rounded-lg shadow-sm border p-3 xs:p-4 sm:p-6 md:p-8 mb-4 xs:mb-6 sm:mb-8">
          {/* Pickup Details */}
          <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4 md:gap-6 items-center p-2 xs:p-3 sm:p-4 md:p-6 border-b border-gray-200">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-none text-xs xs:text-sm sm:text-base md:text-lg">
                  {pickupLocation}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {locationOptions.map((loc) => (
                  <DropdownMenuItem
                    key={loc}
                    onSelect={() => setPickupLocation(loc)}
                    className={`text-xs xs:text-sm sm:text-base ${pickupLocation === loc ? 'bg-amber-100 font-semibold' : ''}`}
                  >
                    {loc}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex items-center gap-1 xs:gap-2">
              <div className="relative flex-1">
                <Input
                  value={pickupDateInput}
                  onChange={handlePickupInputChange}
                  placeholder="dd/mm/yyyy"
                  className="pr-8 xs:pr-10 border-0 text-xs xs:text-sm sm:text-base md:text-lg"
                  maxLength={10}
                  required
                />
                <Popover open={pickupCalendarOpen} onOpenChange={setPickupCalendarOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-1 xs:px-2 sm:px-3 py-1 xs:py-2 hover:bg-transparent"
                      onClick={() => setPickupCalendarOpen(!pickupCalendarOpen)}
                    >
                      <CalendarDays className="h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5 text-gray-500" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={pickupDateObj}
                      onSelect={handlePickupDateSelect}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="w-full justify-between border-none text-xs xs:text-sm sm:text-base md:text-lg">
                  {pickupTime}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {timeOptions.map((time) => (
                  <DropdownMenuItem
                    key={time}
                    onSelect={() => handlePickupTimeSelect(time)}
                    className={`text-xs xs:text-sm sm:text-base ${pickupTime === time ? 'bg-amber-100 font-semibold' : ''}`}
                  >
                    {time}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        

          {/* Pickup Address Fields */}
          {showPickupAddressFields && (
            <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 p-2 xs:p-3 sm:p-4 md:p-6 border-b border-gray-200">
              <Input
                placeholder="Pickup Address"
                value={pickupAddress}
                onChange={(e) => setPickupAddress(e.target.value)}
                className="focus:outline-none focus:ring-0 focus:border-none "
                required
              />
              <Input
                placeholder="Return Address"
                value={returnAddress}
                onChange={(e) => setReturnAddress(e.target.value)}
                className="focus:outline-none focus:ring-0 focus:border-none "
                required
              />
            </div>
          )}

          {/* Return Details */}
          <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4 md:gap-6 items-center p-2 xs:p-3 sm:p-4 md:p-6 border-b border-gray-200">
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-none text-xs xs:text-sm sm:text-base md:text-lg" required>
                  {dropoffLocation}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {locationOptions.map((loc) => (
                  <DropdownMenuItem
                    key={loc}
                    onSelect={() => setDropoffLocation(loc)}
                    className={`text-xs xs:text-sm sm:text-base ${dropoffLocation === loc ? 'bg-amber-100 font-semibold' : ''}`}
                  >
                    {loc}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>

            <div className="flex items-center gap-1 xs:gap-2">
              <div className="relative flex-1">
                <Input
                  value={dropoffDateInput}
                  onChange={handleDropoffInputChange}
                  placeholder="dd/mm/yyyy"
                  className="pr-8 xs:pr-10 border-0 text-xs xs:text-sm sm:text-base md:text-lg"
                  maxLength={10}
                  required
                />
                <Popover open={dropoffCalendarOpen} onOpenChange={setDropoffCalendarOpen}>
                  <PopoverTrigger asChild>
                    <Button
                      variant="ghost"
                      size="sm"
                      className="absolute right-0 top-0 h-full px-1 xs:px-2 sm:px-3 py-1 xs:py-2 hover:bg-transparent"
                      onClick={() => setDropoffCalendarOpen(!dropoffCalendarOpen)}
                    >
                      <CalendarDays className="h-3 xs:h-4 sm:h-5 w-3 xs:w-4 sm:w-5 text-gray-500" />
                    </Button>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <Calendar
                      mode="single"
                      selected={dropoffDateObj}
                      onSelect={handleDropoffDateSelect}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
              </div>
            </div>

            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="ghost" className="w-full justify-between border-none text-xs xs:text-sm sm:text-base md:text-lg" required>
                  {dropoffTime}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {dropoffTimeOptions.map((time) => (
                  <DropdownMenuItem
                    key={time}
                    onSelect={() => handleDropoffTimeSelect(time)}
                    className={`text-xs xs:text-sm sm:text-base ${dropoffTime === time ? 'bg-amber-100 font-semibold' : ''}`}
                  >
                    {time}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {/* Return Address Fields */}
          {showReturnAddressFields && (
            <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 p-2 xs:p-3 sm:p-4 md:p-6 border-b border-gray-200">
              <Input
                placeholder="Pickup Address"
                value={pickupAddress}
                onChange={(e) => setPickupAddress(e.target.value)}
                className="focus:outline-none focus:ring-0 focus:border-none "
                required
              />
              <Input
                placeholder="Return Address"
                value={returnAddress}
                onChange={(e) => setReturnAddress(e.target.value)}
                className="focus:outline-none focus:ring-0 focus:border-none "
                required
              />
            </div>
          )}
        </div>

        <div className="mb-4 xs:mb-6 sm:mb-8 md:mb-10">
          <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-4 xs:mb-6 sm:mb-8 px-2 xs:px-4 sm:px-6">
            <div className="w-full">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between text-xs xs:text-sm sm:text-base md:text-lg" required>
                    {age}
                    <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  {ageOptions.map((item) => (
                    <DropdownMenuItem
                      key={item}
                      onSelect={() => setAge(item)}
                      className={`text-xs xs:text-sm sm:text-base ${age === item ? 'bg-amber-100 font-semibold' : ''}`}
                    >
                      {item}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="w-full">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between text-xs xs:text-sm sm:text-base md:text-lg" required>
                    Residency: {residency.replace('Residency: ', '')}
                    <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500 flex-shrink-0" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full text-xs xs:text-sm sm:text-base">
                  {countryOptions.map((country) => (
                    <DropdownMenuItem
                      key={country}
                      onSelect={() => setResidency(`Residency: ${country}`)}
                      className={`text-xs xs:text-sm sm:text-base ${residency === `Residency: ${country}` ? 'bg-amber-100 font-semibold' : ''}`}
                    >
                      {country}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            <div className="w-full sm:col-span-2 lg:col-span-1">
              <Button
                onClick={() => setDiscountCodeExpanded(!discountCodeExpanded)}
                variant="outline"
                className="w-full justify-between text-xs xs:text-sm sm:text-base md:text-lg"
              >
                Discount Code *
                {discountCodeExpanded ? (
                  <X className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                ) : (
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                )}
              </Button>
            </div>
          </div>

          {discountCodeExpanded && (
            <div className="bg-white rounded-lg shadow-md border p-2 xs:p-3 sm:p-4 md:p-6 mb-4 xs:mb-6 sm:mb-8">
              <h3 className="text-gray-700 font-medium mb-2 xs:mb-3 sm:mb-4 text-xs xs:text-sm sm:text-base md:text-lg">
                Enter a Discount Code
              </h3>
              <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
                <Input
                  placeholder="AWD #"
                  value={awdNumber}
                  onChange={(e) => setAwdNumber(e.target.value)}
                  className="focus:outline-none focus:ring-0 focus:border-none "
                />
                <Input
                  placeholder="Coupon Code"
                  value={couponCode}
                  onChange={(e) => setCouponCode(e.target.value)}
                  className="focus:outline-none focus:ring-0 focus:border-none "
                />
                <Input
                  placeholder="Rate Code"
                  value={rateCode}
                  onChange={(e) => setRateCode(e.target.value)}
                  className="focus:outline-none focus:ring-0 focus:border-none  sm:col-span-2 md:col-span-1"
                />
              </div>
            </div>
          )}
        </div>

        {role === 'admin' && (
          <div className="p-3 xs:p-4 sm:p-6 md:p-8 mb-4 xs:mb-6 sm:mb-8">
            <h3 className="text-gray-700 font-medium mb-2 xs:mb-3 sm:mb-4 text-xs xs:text-sm sm:text-base md:text-lg">
              Walk in Customer?
            </h3>
            <div className="flex gap-4">
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="walkInCustomer"
                  checked={walkInCustomer}
                  onChange={() => setWalkInCustomer(true)}
                  className="h-4 w-4"
                  required
                />
                <span className="text-xs xs:text-sm sm:text-base">Yes</span>
              </label>
              <label className="flex items-center gap-2">
                <input
                  type="radio"
                  name="walkInCustomer"
                  checked={!walkInCustomer}
                  onChange={() => setWalkInCustomer(false)}
                  className="h-4 w-4"
                  required
                />
                <span className="text-xs xs:text-sm sm:text-base">No</span>
              </label>
            </div>
          </div>
        )}

        {role === 'admin' && (
          <div className="p-3 xs:p-4 sm:p-6 md:p-8 mb-4 xs:mb-6 sm:mb-8">
            <h3 className="text-gray-700 font-medium mb-2 xs:mb-3 sm:mb-4 text-xs xs:text-sm sm:text-base md:text-lg">
              Comments
            </h3>
            <Textarea
              value={comments}
              onChange={(e) => setComments(e.target.value)}
              placeholder="Add comments here..."
              className="focus:outline-none focus:ring-0 focus:border-none "
              rows={4}
            />
            <Button
              onClick={handleSaveComment}
              className="mt-2 xs:mt-3 sm:mt-4"
            >
              Save Comment
            </Button>
            {savedComments.length > 0 && (
              <div className="mt-4">
                <h4 className="text-gray-700 font-medium text-xs xs:text-sm sm:text-base">Saved Comments:</h4>
                <ul className="list-disc pl-5 text-xs xs:text-sm sm:text-base">
                  {savedComments.map((comment, index) => (
                    <li key={index}>{comment}</li>
                  ))}
                </ul>
              </div>
            )}
          </div>
        )}

        <div className="text-right mb-2 xs:mb-3 sm:mb-4 md:mb-6 px-2 xs:px-4 sm:px-6">
          <span className="text-gray-500 text-xs xs:text-sm sm:text-base">* Optional</span>
        </div>

        <div className="flex justify-center sm:justify-end mb-4 xs:mb-6 sm:mb-0 mt-2 xs:mt-3 sm:mt-4 md:mt-6 px-2 xs:px-4 sm:px-6">
          <Button
            onClick={handleNextStep}
            className="xs:w-full sm:w-auto bg-[#330101] text-white xs:px-6 sm:px-8 md:px-10 lg:px-12 xs:py-3 sm:py-3 md:py-4 hover:bg-[#660404] transition-colors xs:text-sm sm:text-base md:text-lg"
          >
            Next Step
          </Button>
        </div>
        <br/>
      </div>
    </div>
  );
}