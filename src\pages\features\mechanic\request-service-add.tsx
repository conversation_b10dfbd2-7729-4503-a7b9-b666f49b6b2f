import { useEffect, useState } from "react";
import { useRequestServiceAdd } from "./hook/userequest-service-add";
import { Input } from "../../../components/ui/input";
import { Label } from "../../../components/ui/label";
import { Button } from "../../../components/ui/button";
import { useLocation } from 'react-router-dom';
import { Plus, Trash2, Edit } from "lucide-react";

export const RequestServiceAdd = () => {
  const location = useLocation();
  const params = new URLSearchParams(location.search);
  const serviceType = params.get('service'); // 'accident', 'regular', etc.

  const { form, handleInputChange, handleAdd, handleCancel } = useRequestServiceAdd();

  // Local state for added services
  const [services, setServices] = useState([]);
  const [nextServiceCode, setNextServiceCode] = useState(1);
  const [isEditing, setIsEditing] = useState<number | null>(null);
  const [editService, setEditService] = useState({ serviceName: "" });

  // Helper to get next service code as SC0001, SC0002, ...
  const getNextServiceCode = () => `SC${String(nextServiceCode).padStart(4, "0")}`;

  // Set initial service code on mount
  useEffect(() => {
    handleInputChange("serviceCode", getNextServiceCode());
    // eslint-disable-next-line
  }, [nextServiceCode]);

  // Add service to table (always use current code for batch)
  const handleAddService = () => {
    if (!form.serviceName) return;
    setServices([
      ...services,
      {
        serviceCode: form.serviceCode,
        serviceName: form.serviceName,
      },
    ]);
    handleInputChange("serviceName", "");
  };

  // Remove service from table
  const handleRemoveService = (idx) => {
    setServices(services.filter((_, i) => i !== idx));
  };

  // Edit service in table
  const handleEditService = (idx) => {
    setIsEditing(idx);
    setEditService({
      serviceName: services[idx].serviceName,
    });
  };

  // Save edited service
  const handleSaveEdit = (idx) => {
    const updatedServices = services.map((service, i) =>
      i === idx
        ? { ...service, serviceName: editService.serviceName }
        : service
    );
    setServices(updatedServices);
    setIsEditing(null);
    setEditService({ serviceName: "" });
  };

  // When user clicks "Add" at the bottom, increment code for next batch and clear services
  const handleAddBatch = () => {
    // You may want to do something with the services here (e.g., submit to backend)
    // For now, just increment code for next batch and clear services
    const newNext = nextServiceCode + 1;
    setNextServiceCode(newNext);
    handleInputChange("serviceCode", `SC${String(newNext).padStart(4, "0")}`);
    setServices([]);
    handleInputChange("serviceName", "");
    handleInputChange("description", "");
  };

  return (
    <div className="min-h-screen font-sans text-left flex">
      <div className="flex-1 p-4 sm:p-6 md:p-8 max-w-lg">
        <h2 className="text-xl font-bold mb-6">
          Request Services {serviceType ? `for ${serviceType.charAt(0).toUpperCase() + serviceType.slice(1)}` : ''}
        </h2>
        <div className="space-y-6">
          {/* Service Code and Service Name horizontally aligned */}
          <div className="flex gap-10 items-end">
            <div className="flex-1 flex flex-col gap-2">
              <Label className="text-sm">Service Code</Label>
              <Input value={form.serviceCode} readOnly className="bg-gray-50 text-sm w-full min-w-[200px]" />
            </div>
            <div className="flex-1 flex flex-col gap-2">
              <Label className="text-sm">Service Name</Label>
              <Input
                value={form.serviceName}
                onChange={e => handleInputChange("serviceName", e.target.value)}
                placeholder="Enter service name"
                className="text-sm w-full min-w-[200px]"
              />
            </div>
            {/* Plus icon to add new service */}
            <Button
              className="bg-black text-white px-2 py-2 h-9 flex items-center"
              onClick={handleAddService}
              title="Add Service"
              type="button"
            >
              <Plus className="w-5 h-5" />
            </Button>
          </div>
          {/* Table of added services below */}
          {services.length > 0 && (
            <div className="overflow-x-auto">
              <table className="min-w-full border mt-4 text-sm">
                <thead>
                  <tr className="bg-gray-100">
                    <th className="px-3 py-2 border">Service Code</th>
                    <th className="px-3 py-2 border">Service Name</th>
                    <th className="px-3 py-2 border"></th>
                  </tr>
                </thead>
                <tbody>
                  {services.map((service, idx) => (
                    <tr key={service.serviceCode + idx}>
                      <td className="px-3 py-2 border">{service.serviceCode}</td>
                      <td className="px-3 py-2 border">
                        {isEditing === idx ? (
                          <Input
                            value={editService.serviceName}
                            onChange={e => setEditService({ serviceName: e.target.value })}
                            className="text-sm"
                          />
                        ) : (
                          service.serviceName
                        )}
                      </td>
                      <td className="px-2 py-2 border text-center flex gap-2 justify-center">
                        {isEditing === idx ? (
                          <Button
                            size="sm"
                            className="px-2 py-1 bg-green-600 text-white"
                            onClick={() => handleSaveEdit(idx)}
                            type="button"
                          >
                            Save
                          </Button>
                        ) : (
                          <>
                            <button
                              type="button"
                              className="text-blue-500 hover:text-blue-700"
                              onClick={() => handleEditService(idx)}
                              title="Edit"
                            >
                              <Edit className="w-4 h-4" />
                            </button>
                            <button
                              type="button"
                              className="text-red-500 hover:text-red-700"
                              onClick={() => handleRemoveService(idx)}
                              title="Remove"
                            >
                              <Trash2 className="w-4 h-4" />
                            </button>
                          </>
                        )}
                      </td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          )}
          {/* Description below table */}
          <div className="flex flex-col gap-2">
            <Label className="text-sm">Description</Label>
            <Input
              value={form.description}
              onChange={e => handleInputChange("description", e.target.value)}
              placeholder="Add description"
              className="text-sm w-full min-w-[200px]"
            />
          </div>
          {/* Add and Cancel buttons right aligned */}
          <div className="flex justify-end gap-4 mt-8">
            <Button
              className="bg-black text-white px-6 text-sm"
              onClick={handleAddBatch}
              type="button"
            >
              Add
            </Button>
            <Button variant="outline" className="px-6 text-sm" onClick={handleCancel}>
              Cancel
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
};