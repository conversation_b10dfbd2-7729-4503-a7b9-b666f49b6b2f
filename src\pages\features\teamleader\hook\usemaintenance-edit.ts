import { NavigateFunction } from 'react-router-dom';
import { toast, useToast } from '@/components/ui/use-toast';
import { VehicleData, WorkshopData, ImageData, ServiceTypeOption } from '../type/teamleadertype';

export const handleVehicleDataChange = (
  field: keyof VehicleData,
  value: string,
  setVehicleData: React.Dispatch<React.SetStateAction<VehicleData>>
): void => {
  setVehicleData((prev) => ({
    ...prev,
    [field]: value,
  }));
};

export const handleWorkshopDataChange = (
  field: keyof WorkshopData,
  value: string | string[],
  setWorkshopData: React.Dispatch<React.SetStateAction<WorkshopData>>
): void => {
  setWorkshopData((prev) => ({
    ...prev,
    [field]: value,
  }));
};

export const handleMechanicChange = (
  value: string,
  workshopData: WorkshopData,
  setWorkshopData: React.Dispatch<React.SetStateAction<WorkshopData>>
): void => {
  const updatedMechanics = workshopData.mechanicAssigned.includes(value)
    ? workshopData.mechanicAssigned.filter((m) => m !== value)
    : [...workshopData.mechanicAssigned, value];
  handleWorkshopDataChange('mechanicAssigned', updatedMechanics, setWorkshopData);
};

export const handleImageUpload = (
  field: keyof ImageData,
  files: FileList | null,
  setImageData: React.Dispatch<React.SetStateAction<ImageData>>,
  toast: ReturnType<typeof useToast>['toast']
): void => {
  if (files && files.length > 0) {
    const validFiles = Array.from(files).filter(
      (file) => file.type.startsWith('image/') && file.size < 5 * 1024 * 1024
    );
    if (validFiles.length === 0) {
      toast({
        title: 'Error',
        description: 'Please upload valid images (max 5MB).',
        variant: 'destructive',
      });
      return;
    }
    const fileNames = validFiles.map((file) => file.name);
    setImageData((prev) => ({
      ...prev,
      [field]: fileNames,
    }));
  }
};

export const handleUpdate = async (
  vehicleId: string | undefined,
  vehicleData: VehicleData,
  workshopData: WorkshopData,
  serviceType: ServiceTypeOption,
  imageData: ImageData,
  navigate: NavigateFunction,
  toast: ReturnType<typeof useToast>['toast'],
  setIsLoading: React.Dispatch<React.SetStateAction<boolean>>
): Promise<void> => {
  setIsLoading(true);
  try {
    if (!vehicleData.status) {
      toast({
        title: "Error",
        description: "Please select a status.",
        variant: "destructive",
      });
      return;
    }
    if (vehicleData.actualEndDate && new Date(vehicleData.actualEndDate) < new Date(vehicleData.estimatedEndDate)) {
      toast({
        title: "Error",
        description: "Actual end date cannot be before estimated end date.",
        variant: "destructive",
      });
      return;
    }

    const cleanedWorkshopData = {
      ...workshopData,
      comment: workshopData.comment === 'Type here...' ? '' : workshopData.comment,
    };
    
    const payload = {
      ...vehicleData,
      ...cleanedWorkshopData,
      serviceType,
      ...(serviceType === 'Accident' || serviceType === 'Damage' ? { imageData } : {}),
    };

    await fetch(`/api/vehicles/${vehicleId}`, {
      method: 'PUT',
      body: JSON.stringify(payload),
      headers: { 'Content-Type': 'application/json' },
    });
    toast({
      title: "Success",
      description: "Vehicle maintenance details updated successfully.",
    });
    navigate('/teamleader/maintenance');
  } catch (error) {
    console.error('Failed to save:', error);
    toast({
      title: "Error",
      description: "Failed to update vehicle maintenance details. Please try again.",
      variant: "destructive",
    });
  } finally {
    setIsLoading(false);
  }
};

export const handleCancel = (navigate: NavigateFunction): void => {
  navigate('/teamleader/maintenance');
};