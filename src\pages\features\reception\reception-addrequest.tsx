import React, { useState } from 'react';
import { ArrowLeft, FileText, Calendar as CalendarIcon } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { Calendar } from '@/components/ui/calendar';
import { useNavigate } from 'react-router-dom';
import {
  DropdownMenu,
  DropdownMenuTrigger,
  DropdownMenuContent,
  DropdownMenuRadioGroup,
  DropdownMenuRadioItem,
} from '@/components/ui/dropdown-menu';
import { AddRequestFormData } from './type/reception-type';
import { bookingsData, mockVehicles } from './common/mockData';
import {
  initializeFormData,
  handlePhoneChange,
  handleRequestTypeChange,
  handleInputChange,
  handleValidate,
  handleGoBack,
  handleGenerateInvoice,
} from './hook/useAddRequest';

export function ReceptionAddrequest() {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<AddRequestFormData>(initializeFormData());
  const [showValidationResult, setShowValidationResult] = useState(false);
  const [extensionGranted, setExtensionGranted] = useState('');
  const [showIssueCalendar, setShowIssueCalendar] = useState(false);
  const [showExpiryCalendar, setShowExpiryCalendar] = useState(false);

  return (
    <div className="min-h-screen">
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="flex items-center space-x-4">
            <Button
              className="flex items-center space-x-2 text-white bg-[#330101] px-4 py-2 rounded hover:bg-red-800"
              onClick={() => handleGoBack(navigate)}
            >
              <ArrowLeft className="w-4 h-4" />
              <span>Go Back</span>
            </Button>
          </div>
          <div className="flex items-center space-x-2 mt-4">
            <FileText className="w-6 h-6 text-red-900" />
            <h1 className="text-2xl font-bold text-gray-800">Change Requests</h1>
          </div>
        </div>
      </div>
      <div className="max-w-7xl mx-auto px-4 py-6">
        <div className="bg-white rounded-lg shadow-sm p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-10 mb-5">
            <div className="space-y-2 relative">
              <Label htmlFor="customerPhone" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Customer Phone Number
              </Label>
              <Select
                value={formData.customerPhone}
                onValueChange={(phone) => handlePhoneChange(phone, setFormData, bookingsData, mockVehicles)}
              >
                <SelectTrigger className="w-full h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none">
                  <SelectValue placeholder="Select phone number" />
                </SelectTrigger>
                <SelectContent>
                  {bookingsData.map(booking => (
                    <SelectItem key={booking.customerPhone} value={booking.customerPhone}>
                      {booking.customerPhone}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-5">
            <div className="space-y-2 relative">
              <Label htmlFor="customerName" className="absolute bg-white text-xs text-gray-600 z-10 left-3">
                Customer Name
              </Label>
              <Input
                id="customerName"
                type="text"
                value={formData.customerName}
                readOnly
                className="w-full h-12 border border-gray-500 bg-gray-50 focus:outline-none focus:ring-0 focus:border-none"
              />
            </div>
            <div className="space-y-2 relative">
              <Label htmlFor="rentalId" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Rental ID
              </Label>
              <Input
                id="rentalId"
                type="text"
                value={formData.rentalId}
                readOnly
                className="w-full h-12 border border-gray-500 bg-gray-50 focus:outline-none focus:ring-0 focus:border-none"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-5">
            <div className="space-y-2 relative">
              <Label htmlFor="vehicleClass" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Vehicle Class
              </Label>
              <Input
                id="vehicleClass"
                type="text"
                value={formData.vehicleClass}
                readOnly
                className="w-full h-12 border border-gray-500 bg-gray-50 focus:outline-none focus:ring-0 focus:border-none"
              />
            </div>
            <div className="space-y-2 relative">
              <Label htmlFor="vehicle" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Vehicle
              </Label>
              <Input
                id="vehicle"
                type="text"
                value={formData.vehicle}
                readOnly
                className="w-full h-12 bg-gray-50 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-5">
            <div className="space-y-2 relative">
              <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Pickup Date
              </Label>
              <div className="flex space-x-2">
                <Input
                  value={formData.pickupDate}
                  readOnly
                  className="bg-gray-50 flex-1 h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none"
                  placeholder="Date"
                />
                <Input
                  value={formData.pickupTime}
                  readOnly
                  className="bg-gray-50 w-24 border border-gray-500 h-12 focus:outline-none focus:ring-0 focus:border-none"
                  placeholder="Time"
                />
              </div>
            </div>
            <div className="space-y-2 relative">
              <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Return Date
              </Label>
              <div className="flex space-x-2">
                <Input
                  value={formData.returnDate}
                  readOnly
                  className="bg-gray-50 flex-1 h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none"
                  placeholder="Date"
                />
                <Input
                  value={formData.returnTime}
                  readOnly
                  className="bg-gray-50 w-24 h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none"
                  placeholder="Time"
                />
              </div>
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-5">
            <Label htmlFor="requestType" className="absolute bg-white px-2 text-xs text-gray-600 z-10">
              Request Type
            </Label>
            <Select
              value={formData.requestType}
              onValueChange={(type) => handleRequestTypeChange(type, setFormData, setShowValidationResult, setExtensionGranted)}
            >
              <SelectTrigger className="w-full h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none">
                <SelectValue placeholder="Select request type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Extension of Duration">Extension of Duration</SelectItem>
                <SelectItem value="Early Termination">Early Termination</SelectItem>
                <SelectItem value="Add New License">Add New License</SelectItem>
              </SelectContent>
            </Select>
          </div>
          {formData.requestType === 'Extension of Duration' && (
            <div className="md:col-span-2 space-y-4 mb-5">
              <Label className="text-sm font-medium text-gray-700 block">
                Date Range
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 relative">
                  <Label htmlFor="dateRangeFrom" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    From Date
                  </Label>
                  <Input
                    id="dateRangeFrom"
                    type="date"
                    value={formData.dateRangeFrom}
                    onChange={(e) => handleInputChange('dateRangeFrom', e.target.value, setFormData)}
                    className="h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none"
                  />
                </div>
                <div className="space-y-2 relative">
                  <Label htmlFor="dateRangeTo" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    To Date
                  </Label>
                  <Input
                    id="dateRangeTo"
                    type="date"
                    value={formData.dateRangeTo}
                    onChange={(e) => handleInputChange('dateRangeTo', e.target.value, setFormData)}
                    className="h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none"
                  />
                </div>
              </div>
            </div>
          )}
          {formData.requestType === 'Early Termination' && (
            <div className="md:col-span-2 space-y-4 mb-5">
              <Label className="text-sm font-medium text-gray-700 block">
                New Return Date
              </Label>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 relative">
                  <Label htmlFor="newReturnDate" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    Date
                  </Label>
                  <Input
                    id="newReturnDate"
                    type="date"
                    value={formData.newReturnDate}
                    onChange={(e) => handleInputChange('newReturnDate', e.target.value, setFormData)}
                    className="h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none"
                  />
                </div>
                <div className="space-y-2 relative">
                  <Label htmlFor="newReturnTime" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    Time
                  </Label>
                  <Input
                    id="newReturnTime"
                    type="time"
                    value={formData.newReturnTime}
                    onChange={(e) => handleInputChange('newReturnTime', e.target.value, setFormData)}
                    className="h-12 border border-gray-500 focus:outline-none focus:ring-0 focus:border-none"
                  />
                </div>
              </div>
            </div>
          )}
          {formData.requestType === 'Add New License' && (
            <div className="md:col-span-2 space-y-4 mb-5">
              <h2 className="text-xl font-semibold text-gray-800">Driver's License</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 relative">
                  <Label htmlFor="permitNumber" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    DL Number*
                  </Label>
                  <Input
                    id="permitNumber"
                    type="text"
                    value={formData.permitNumber}
                    onChange={(e) => handleInputChange('permitNumber', e.target.value, setFormData)}
                    className="h-12 border border-gray-500 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-transparent"
                    placeholder="Enter DL Number"
                    disabled
                  />
                </div>
                <div className="space-y-2 relative">
                  <Label htmlFor="issueCountry" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    Issue Country*
                  </Label>
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button
                        variant="outline"
                        className="w-full h-12 justify-between border border-gray-500 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-red-500"
                      >
                        {formData.issueCountry}
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent>
                      <DropdownMenuRadioGroup
                        value={formData.issueCountry}
                        onValueChange={(value) => handleInputChange('issueCountry', value, setFormData)}
                      >
                        <DropdownMenuRadioItem value="Australia">Australia</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="USA">USA</DropdownMenuRadioItem>
                        <DropdownMenuRadioItem value="UK">UK</DropdownMenuRadioItem>
                      </DropdownMenuRadioGroup>
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
                <div className="space-y-2 relative">
                  <Label htmlFor="issueDate" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    Issue Date*
                  </Label>
                  <Input
                    id="issueDate"
                    type="text"
                    value={formData.issueDate ? new Date(formData.issueDate).toLocaleDateString() : ''}
                    readOnly
                    className="h-12 border border-gray-500 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-transparent pr-10"
                    onClick={() => setShowIssueCalendar(true)}
                  />
                  <CalendarIcon
                    className="absolute right-3 top-3 h-5 w-5 text-gray-600 cursor-pointer"
                    onClick={() => setShowIssueCalendar(true)}
                  />
                  {showIssueCalendar && (
                    <div className="mt-2">
                      <Calendar
                        mode="single"
                        selected={formData.issueDate ? new Date(formData.issueDate) : undefined}
                        onSelect={(date) => {
                          handleInputChange('issueDate', date ? date.toISOString().split('T')[0] : '', setFormData);
                          setShowIssueCalendar(false);
                        }}
                        onClickOutside={() => setShowIssueCalendar(false)}
                        className="border rounded-md p-2"
                        initialFocus
                      />
                    </div>
                  )}
                </div>
                <div className="space-y-2 relative">
                  <Label htmlFor="expiryDate" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    Expiry Date*
                  </Label>
                  <Input
                    id="expiryDate"
                    type="text"
                    value={formData.expiryDate ? new Date(formData.expiryDate).toLocaleDateString() : ''}
                    readOnly
                    className="h-12 border border-gray-500 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-transparent pr-10"
                    onClick={() => setShowExpiryCalendar(true)}
                  />
                  <CalendarIcon
                    className="absolute right-3 top-3 h-5 w-5 text-gray-600 cursor-pointer"
                    onClick={() => setShowExpiryCalendar(true)}
                  />
                  {showExpiryCalendar && (
                    <div className="mt-2">
                      <Calendar
                        mode="single"
                        selected={formData.expiryDate ? new Date(formData.expiryDate) : undefined}
                        onSelect={(date) => {
                          handleInputChange('expiryDate', date ? date.toISOString().split('T')[0] : '', setFormData);
                          setShowExpiryCalendar(false);
                        }}
                        onClickOutside={() => setShowExpiryCalendar(false)}
                        className="border rounded-md p-2"
                        initialFocus
                      />
                    </div>
                  )}
                </div>
                <div className="space-y-2 relative">
                  <Label htmlFor="licenseType" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    Condition*
                  </Label>
                  <Input
                    id="licenseType"
                    type="text"
                    value={formData.licenseType}
                    className="h-12 border border-gray-500 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-transparent"
                    disabled
                  />
                </div>
                <div className="space-y-2 relative">
                  <Label htmlFor="frontView" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    Upload Front View*
                  </Label>
                  <Input
                    id="frontView"
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleInputChange('frontView', e.target.files ? e.target.files[0] : null, setFormData)}
                    className="h-12 border border-gray-500 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-transparent"
                  />
                  {formData.frontView && <p className="text-xs text-gray-600 mt-1">{formData.frontView.name}</p>}
                </div>
                <div className="space-y-2 relative">
                  <Label htmlFor="backView" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                    Upload Back View*
                  </Label>
                  <Input
                    id="backView"
                    type="file"
                    accept="image/*"
                    onChange={(e) => handleInputChange('backView', e.target.files ? e.target.files[0] : null, setFormData)}
                    className="h-12 border border-gray-500 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-red-500 focus:border-transparent"
                  />
                  {formData.backView && <p className="text-xs text-gray-600 mt-1">{formData.backView.name}</p>}
                </div>
              </div>
            </div>
          )}
          {(formData.requestType === 'Extension of Duration' || formData.requestType === 'Early Termination') && (
            <div className="space-y-2 relative md:col-span-2 mb-5">
              <Label htmlFor="note" className="absolute bg-white text-xs text-gray-600 z-10 left-3">
                Note
              </Label>
              <Textarea
                id="note"
                value={formData.note}
                onChange={(e) => handleInputChange('note', e.target.value, setFormData)}
                rows={4}
                className="w-full border border-gray-500 min-h-[120px] resize-none focus:outline-none focus:ring-0 focus:border-none"
                placeholder="Enter any additional notes..."
              />
            </div>
          )}
          {formData.requestType === 'Extension of Duration' && (
            <div className="md:col-span-2 mb-5">
              <button
                onClick={() => handleValidate(setShowValidationResult)}
                className="bg-red-900 text-white px-8 py-2 rounded hover:bg-red-800 transition-colors"
              >
                Validate
              </button>
            </div>
          )}
          {showValidationResult && formData.requestType === 'Extension of Duration' && (
            <div className="md:col-span-2 space-y-4">
              <div className="flex items-center space-x-4">
                <Label className="text-sm font-medium text-gray-700">
                  Extension Can be Granted?
                </Label>
                <div className="flex items-center space-x-4">
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="extensionGranted"
                      value="yes"
                      checked={extensionGranted === 'yes'}
                      onChange={(e) => setExtensionGranted(e.target.value)}
                      className="text-red-600 focus:ring-red-500"
                    />
                    <span>Yes</span>
                  </label>
                  <label className="flex items-center space-x-2">
                    <input
                      type="radio"
                      name="extensionGranted"
                      value="no"
                      checked={extensionGranted === 'no'}
                      onChange={(e) => setExtensionGranted(e.target.value)}
                      className="text-red-600 focus:ring-red-500"
                    />
                    <span>No</span>
                  </label>
                </div>
              </div>
            </div>
          )}
          {formData.requestType === 'Extension of Duration' && (
            <div className="flex justify-end items-center mt-8">
              <Button
                className="bg-[#330101] text-white px-6 py-2 rounded hover:bg-red-800"
                onClick={() => handleGenerateInvoice(navigate)}
              >
                Generate Invoice
              </Button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}