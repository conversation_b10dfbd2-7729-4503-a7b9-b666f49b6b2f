import React from "react";
import { useNavigate } from "react-router-dom";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { useServiceManagementAllView } from "./hook/useservice-managemant-all-view";
import { ArrowLeft } from "lucide-react";

export function ServiceManagementAllViewPage() {
  const navigate = useNavigate();
  const { formData } = useServiceManagementAllView();

  if (!formData) {
    return <div className="p-4">No data found for this vehicle.</div>;
  }

  return (
    <div className="min-h-screen p-4">
      {/* Mobile/Tablet Card View */}
      <div className="block md:hidden">
        {/* Mobile Back Button */}
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={() => navigate("/mechanic/service-management-all")}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
        <div className="bg-white rounded-lg shadow-sm p-4">
          {/* Header: General Information and Status */}
          <div className="flex justify-between items-center mb-4">
            <div className="font-semibold text-base mt-2">General Information</div>
            <span
              className={`px-3 py-1 rounded text-sm font-medium w-24 flex justify-center items-center ${
                formData.status === "Pending"
                  ? "bg-gray-400 text-white"
                  : formData.status === "InProgress"
                  ? "bg-blue-500 text-white"
                  : formData.status === "Done"
                  ? "bg-green-500 text-white"
                  : "bg-gray-200 text-gray-700"
              }`}
            >
              {formData.status}
            </span>
          </div>
          {/* Info fields */}
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="col-span-2 grid grid-cols-1 gap-4">
              <div className="relative">
                <Input
                  value={formData.vehicleModel}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Vehicle Model
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.regoNumber}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Rego #
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.serviceType}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Service Type
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.reservation}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Reservation
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.totalLabor}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Total in Labour
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.totalParts}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Total in Parts
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.damages}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Damages
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.notes}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Notes
                </Label>
              </div>
            </div>
          </div>
          {/* Out Information */}
          <div className="space-y-3 mt-6">
            <div className="font-semibold text-base mb-2 mt-6">
              Vehicle Out Information
            </div>
            <div className="grid grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  value={formData.dateIn}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Date In
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.dateOut}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Date Out
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.timeIn}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Time In
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.timeOut}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Time Out
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.fuelIn}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Fuel In
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.fuelOut}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Fuel Out
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.odometerIn}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Odometer In
                </Label>
              </div>
              <div className="relative">
                <Input
                  value={formData.odometerOut}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Odometer Out
                </Label>
              </div>
              <div className="col-span-2 relative">
                <Input
                  value={formData.comments}
                  readOnly
                  className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                />
                <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                  Comments
                </Label>
              </div>
            </div>
          </div>
        </div>
      </div>
      {/* Desktop View */}
      <div className="hidden md:block">
        <div className="flex items-center mb-4">
          <Button
            className="bg-[#330101] text-white text-sm px-3 py-2"
            size="sm"
            onClick={() => navigate("/mechanic/service-management-all")}
          >
            <ArrowLeft className="w-4 h-4 mr-1" />
            Go Back
          </Button>
        </div>
        <div className="bg-white rounded-lg p-4">
          {/* General Information */}
          <h2 className="text-lg font-semibold mb-4">General Information</h2>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Input
                value={formData.vehicleModel}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Vehicle Model
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.regoNumber}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Rego #
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.serviceType}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Service Type
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.reservation}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Reservation
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.totalLabor}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Total in Labour
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.totalParts}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Total in Parts
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.status}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Status
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.damages}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Damages
              </Label>
            </div>
            <div className="col-span-2 relative">
              <Input
                value={formData.notes}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Notes
              </Label>
            </div>
          </div>
          {/* Vehicle Out Information */}
          <h2 className="text-lg font-semibold mb-4 mt-6">
            Vehicle Out Information
          </h2>
          <div className="grid grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Input
                value={formData.dateIn}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                placeholder="mm/dd/yyyy"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Date In
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.dateOut}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Date Out
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.timeIn}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                placeholder="01:43 PM"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Time In
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.timeOut}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                placeholder="01:43 PM"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Time Out
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.fuelIn}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Fuel In
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.fuelOut}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Fuel Out
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.odometerIn}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Odometer In
              </Label>
            </div>
            <div className="relative">
              <Input
                value={formData.odometerOut}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Odometer Out
              </Label>
            </div>
            <div className="col-span-2 relative">
              <Input
                value={formData.comments}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-500 bg-white px-1">
                Comments
              </Label>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
}