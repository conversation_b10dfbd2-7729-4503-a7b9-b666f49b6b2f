import React, { useState } from 'react';
import { Search, CalendarPlus } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import {
  handleSearchTermChange,
  handleRecordsPerPageChange,
  handlePageChange,
  handleVehicleIdClick,
  getStatusColor,
} from './hook/usepreventive-maintenance';
import { preventiveData } from './common/mockData';

export function PreventiveMaintenance() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter data based on search term and filter status
  const filteredData = preventiveData.filter((item) =>
    (item.vehicleId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.alertStatus.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || item.alertStatus === filterStatus)
  );

   // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <CalendarPlus className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Preventive Maintenance</h1>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Upcoming">Upcoming</SelectItem>
              <SelectItem value="Overdue">Overdue</SelectItem>
              <SelectItem value="Delay">Delay</SelectItem>
              </SelectContent>
              </Select>
          </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => handleSearchTermChange(e.target.value, setSearchTerm)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Table View */}
      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>Vehicle ID</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Next Service at</TableHead>
              <TableHead>Maintenance Types</TableHead>
              <TableHead>Alert Status</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item) => (
              <TableRow key={item.vehicleId} className="hover:bg-gray-50 transition-colors">
                <TableCell>
                  <span
                    className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                    onClick={() => handleVehicleIdClick(item.vehicleId, navigate)}
                  >
                    {item.vehicleId}
                  </span>
                </TableCell>
                <TableCell>{item.vehicle}</TableCell>
                <TableCell>{item.nextServiceAt}</TableCell>
                <TableCell>{item.serviceTypes}</TableCell>
                <TableCell>
                  <span className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.alertStatus)}`}>
                    {item.alertStatus}
                  </span>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={(page) => handlePageChange(page, setCurrentPage)}
        onRecordsPerPageChange={(records) => handleRecordsPerPageChange(records, setRecordsPerPage, setCurrentPage)}
        className="mt-6"
      />
    </div>
  );
}