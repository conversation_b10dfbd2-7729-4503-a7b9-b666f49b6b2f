import { useState, useMemo } from 'react'
import { FleetQualityData } from '../type/teamleadertype'

export function useFleetQuality() {
  const [currentPage, setCurrentPage] = useState(1)
  const [recordsPerPage, setRecordsPerPage] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('All')

  // Sample data - replace with actual data source
  const sampleData: FleetQualityData[] = [
    {
      id: '1',
      vehicleId: 'VH001',
      rego: '1PX 12R',
      lastDetailClean: '',
      lastService: 'Breakdowns',
      lastInspection: '2025-07-05'
    },
    {
      id: '2',
      vehicleId: 'VH002',
      rego: 'PCR 455',
      lastDetailClean: '',
      lastService: 'Accident',
      lastInspection: '2025-07-03'
    },
    {
      id: '4',
      vehicleId: 'VH004',
      rego: 'ASR 321',
      lastDetailClean: '',
      lastService: 'General Maintenance',
      lastInspection: '2025-07-06'
    },
    {
      id: '5',
      vehicleId: 'VH005',
      rego: 'QBV 233',
      lastDetailClean: '',
      lastService: 'Damage',
      lastInspection: '2025-07-01'
    },
  ]

  // Filter and search data using useMemo for performance
  const filteredData = useMemo(() => {
    return sampleData.filter((fleet) => {
      const matchesSearch = searchTerm === '' || 
        fleet.vehicleId.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fleet.rego.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fleet.lastDetailClean.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fleet.lastService.toLowerCase().includes(searchTerm.toLowerCase()) ||
        fleet.lastInspection.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesFilter = filterStatus === 'All' || fleet.rego === filterStatus
      
      return matchesSearch && matchesFilter
    })
  }, [sampleData, searchTerm, filterStatus])
  
  const filteredTotalRecords = filteredData.length
  const filteredTotalPages = Math.ceil(filteredTotalRecords / recordsPerPage)
  
  // Calculate current page data from filtered results
  const currentData = useMemo(() => {
    const startIndex = (currentPage - 1) * recordsPerPage
    const endIndex = startIndex + recordsPerPage
    return filteredData.slice(startIndex, endIndex)
  }, [filteredData, currentPage, recordsPerPage])

  const handleRecordsPerPageChange = (records: number) => {
    setRecordsPerPage(records)
    setCurrentPage(1) // Reset to first page when changing records per page
  }

  // Get unique rego values for filter options
  const getRegoOptions = () => {
    const uniqueRegos = [...new Set(sampleData.map(item => item.rego))]
    return uniqueRegos
  }

  return {
    // State
    currentPage,
    recordsPerPage,
    searchTerm,
    filterStatus,
    
    // Computed values
    currentData,
    filteredTotalRecords,
    filteredTotalPages,
    
    // Functions
    setCurrentPage,
    setSearchTerm,
    setFilterStatus,
    handleRecordsPerPageChange,
    getRegoOptions,
  }
}