import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { LifeBuoy, Snowflake, Music, Power, Box, Users, Wrench, Pencil } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import SearchHeader from '@/components/layout/SearchHeader';


interface VehicleFeatures {
  transmission: boolean;
  doors?: number;
  seats?: number;
  powerSteering: boolean;
  radioCd: boolean;
  airConditioning: boolean;
  hydraulicLift?: boolean;
  liftCapacity?: string;
}

interface VehicleOption {
  id: string;
  name: string;
  price: number;
  totalPrice: number;
  image: string;
  features: VehicleFeatures;
  available: boolean;
  recommended?: boolean;
  limitedAvailability?: boolean;
  vehiclesAvailable: number;
  availabilityPercentage?: string;
}

interface RentalDetails {
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  pickupLocation: string;
  returnLocation: string;
  rateType?: string;
}

const vehicleOptions: VehicleOption[] = [
  {
    id: 'eco-plus',
    name: 'Eco Plus Car',
    price: 42.00,
    totalPrice: 42.00,
    image: '/Eco Plus Car 1.png',
    features: {
      transmission: true,
      doors: 4,
      powerSteering: true,
      radioCd: true,
      airConditioning: true,
    },
    available: true,
    vehiclesAvailable: 2,
    availabilityPercentage: '33.3%',
  },
  {
    id: 'standard',
    name: 'Standard Car',
    price: 62.00,
    totalPrice: 62.00,
    image: '/Standard Car.png',
    features: {
      transmission: true,
      doors: 4,
      powerSteering: true,
      radioCd: true,
      airConditioning: true,
    },
    available: true,
    recommended: true,
    vehiclesAvailable: 13,
    availabilityPercentage: '72.2%',
  },
  {
    id: 'minibus',
    name: 'Minibus 7 Seats',
    price: 107.00,
    totalPrice: 107.00,
    image: '/Minibus.png',
    features: {
      transmission: true,
      powerSteering: true,
      radioCd: true,
      airConditioning: true,
    },
    available: false,
    vehiclesAvailable: 0,
    availabilityPercentage: '0%',
  },
  {
    id: 'delivery-van',
    name: 'Delivery Van 1.25 Tonne - With the Hydraulic Lift',
    price: 89.00,
    totalPrice: 89.00,
    image: '/Delivery Van.png',
    features: {
      transmission: true,
      seats: 3,
      powerSteering: true,
      radioCd: true,
      airConditioning: true,
      hydraulicLift: true,
      liftCapacity: '250kg',
    },
    available: false,
    limitedAvailability: true,
    vehiclesAvailable: 0,
    availabilityPercentage: '0%',
  },
];

const FeatureIcon: React.FC<{ feature: string }> = ({ feature }) => {
  const iconColor = 'text-[#EBBB4E]';
  const iconMap: Record<string, JSX.Element> = {
    transmission: <LifeBuoy className={`w-4 h-4 ${iconColor}`} />,
    doors: <Box className={`w-4 h-4 ${iconColor}`} />,
    seats: <Users className={`w-4 h-4 ${iconColor}`} />,
    powerSteering: <Power className={`w-4 h-4 ${iconColor}`} />,
    radioCd: <Music className={`w-4 h-4 ${iconColor}`} />,
    airConditioning: <Snowflake className={`w-4 h-4 ${iconColor}`} />,
    hydraulicLift: <Wrench className={`w-4 h-4 ${iconColor}`} />,
  };

  return iconMap[feature] || null;
};

export function VehicleClass() {
  const steps = [
    { number: 1, label: 'Dates', active: false },
    { number: 2, label: 'Vehicles', active: true },
    { number: 3, label: 'Extras', active: false },
    { number: 4, label: 'Customer', active: false },
    { number: 5, label: 'Confirm', active: false },
    { number: 6, label: 'Payment', active: false },
    { number: 7, label: 'Receipt', active: false },
  ];

  const [rentalDetails, setRentalDetails] = useState<RentalDetails>({
    pickupDate: '13/06/2025',
    pickupTime: '11:30',
    returnDate: '14/06/2025',
    returnTime: '10:30',
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    rateType: 'New Year - 2021',
  });

  const [isPopupOpen, setIsPopupOpen] = useState<boolean>(false);
  const [comment, setComment] = useState<string>('');

  // Find the index of the active step
  const activeIndex = steps.findIndex(step => step.active);

  // Determine visible steps: active step, up to 2 before, up to 2 after
  const getVisibleSteps = () => {
    const visible = [activeIndex];
    if (activeIndex - 1 >= 0) visible.unshift(activeIndex - 1);
    if (activeIndex - 2 >= 0) visible.unshift(activeIndex - 2);
    if (activeIndex + 1 < steps.length) visible.push(activeIndex + 1);
    if (activeIndex + 2 < steps.length) visible.push(activeIndex + 2);
    return visible.sort((a, b) => a - b);
  };

  const visibleStepIndices = getVisibleSteps();

  const navigate = useNavigate();

  const handleRentVehicle = (): void => {
      navigate('/search/extras');
    };

  const handleEditClick = (type: 'pickup' | 'return') => {
    setIsPopupOpen(true);
  };

  const handleSave = (newPickupDate: string, newPickupTime: string, newReturnDate: string, newReturnTime: string) => {
    setRentalDetails({
      ...rentalDetails,
      pickupDate: newPickupDate,
      pickupTime: newPickupTime,
      returnDate: newReturnDate,
      returnTime: newReturnTime,
    });
    setIsPopupOpen(false);
  };

  const handleCommentSave = () => {
    console.log('Comment saved:', comment);
    setComment(''); 
  };

  return (
    <>
      <SearchHeader />
      <div style={{ marginLeft: '32px', marginRight: '32px' }}>
        <div className="p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
          <div className="flex items-center justify-center mb-2 sm:mb-4 md:mb-6 lg:mb-8 gap-1 sm:gap-2 md:gap-3 lg:gap-4">
            {steps.map((step, index) => {
              const isVisibleOnXs = visibleStepIndices.includes(index);
              const isVisibleOnSm = visibleStepIndices.includes(index);
              const isVisibleOnMd = visibleStepIndices.includes(index) || index <= activeIndex + 3;
              const isVisibleOnLg = true;

              return (
                <div
                  key={step.number}
                  className={`flex items-center ${!isVisibleOnXs ? 'hidden' : ''} ${isVisibleOnSm ? 'sm:flex' : 'sm:hidden'} ${isVisibleOnMd ? 'md:flex' : 'md:hidden'} ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                >
                  <div className="flex flex-col items-center">
                    <div
                      className={`w-6 h-6 sm:w-8 sm:h-8 md:w-10 md:h-10 lg:w-12 lg:h-12 rounded-full flex items-center justify-center text-xs sm:text-sm md:text-base lg:text-base font-semibold ${
                        step.active ? 'bg-amber-500 text-white' : 'bg-gray-300 text-gray-600'
                      }`}
                    >
                      {step.number}
                    </div>
                    <span className="text-[10px] sm:text-xs md:text-sm lg:text-sm mt-0.5 sm:mt-1">{step.label}</span>
                  </div>
                  {index < steps.length - 1 && (
                    <div
                      className={`w-3 sm:w-4 md:w-6 lg:w-8 h-0.5 bg-gray-300 mx-0.5 sm:mx-1 md:mx-2 lg:mx-3 mt-[-12px] sm:mt-[-16px] md:mt-[-18px] lg:mt-[-20px] ${
                        !isVisibleOnXs || !visibleStepIndices.includes(index + 1) ? 'hidden' : ''
                      } ${isVisibleOnSm && visibleStepIndices.includes(index + 1) ? 'sm:flex' : 'sm:hidden'} ${
                        isVisibleOnMd && (isVisibleOnMd || index + 1 <= activeIndex + 3) ? 'md:flex' : 'md:hidden'
                      } ${isVisibleOnLg ? 'lg:flex' : 'lg:hidden'}`}
                    ></div>
                  )}
                </div>
              );
            })}
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            {/* Main Content */}
            <div className="lg:col-span-3">
              <div className="max-w-full xs:max-w-2xl sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-6xl mx-auto px-4 xs:px-4 sm:px-6 md:px-6 lg:px-8 xl:px-8 py-6 xs:py-6 sm:py-8 md:py-8 lg:py-10 xl:py-10">
                {/* Disclaimer */}
                <div className="mb-6 xs:mb-6 sm:mb-8 md:mb-8 lg:mb-10 xl:mb-10 text-center text-sm xs:text-sm sm:text-base md:text-base lg:text-lg xl:text-lg text-gray-600">
                  Price may change without notice.{' '}
                  <span className="text-red-600">Pictures are for reference only.</span>{' '}
                  Terms and Conditions Apply
                </div>

                {/* Available Vehicle Classes */}
                <h2 className="text-lg xs:text-lg sm:text-xl md:text-xl lg:text-2xl xl:text-2xl font-semibold mb-6 xs:mb-6 sm:mb-8 md:mb-8 lg:mb-10 xl:mb-10">
                  Available Vehicle Classes
                </h2>

                <div className="space-y-6 xs:space-y-6 sm:space-y-8 md:space-y-8 lg:space-y-10 xl:space-y-10">
                  {vehicleOptions.map((vehicle) => (
                    <div
                      key={vehicle.id}
                      className="bg-white rounded-lg border border-gray-200 p-4 xs:p-4 sm:p-6 md:p-6 lg:p-8 xl:p-8 relative"
                    >
                      {/* Recommended Badge */}
                      {vehicle.recommended && (
                        <div className="w-auto absolute top-0 right-0 bg-green-500 text-white px-2 xs:px-2 sm:px-3 md:px-3 lg:px-4 xl:px-4 py-1 text-xs xs:text-xs sm:text-sm md:text-sm lg:text-base xl:text-base font-medium">
                          Recommended
                        </div>
                      )}

                      <div className="flex flex-col xs:flex-col sm:flex-col md:flex-row lg:flex-row xl:flex-row gap-4 xs:gap-4 sm:gap-6 md:gap-6 lg:gap-8 xl:gap-8">
                        {/* Vehicle Image */}
                        <div className="w-full xs:w-full sm:w-full md:w-1/3 lg:w-1/3 xl:w-1/3">
                          <img
                            src={vehicle.image}
                            alt={vehicle.name}
                            className="w-full h-35 xs:h-35 sm:h-39 md:h-39 lg:h-43 xl:h-43 object-cover rounded-lg bg-gray-100"
                          />
                        </div>

                        {/* Vehicle Details */}
                        <div className="xs:w-full sm:w-full md:w-1/3 lg:w-1/3 xl:w-1/3 space-y-4 xs:space-y-4 sm:space-y-4 md:space-y-4 lg:space-y-6 xl:space-y-6">
                          <h3 className="text-base xs:text-base sm:text-lg md:text-lg lg:text-xl xl:text-xl font-semibold">{vehicle.name}</h3>
                          <div className="grid grid-cols-1 xs:grid-cols-1 sm:grid-cols-2 md:grid-cols-2 lg:grid-cols-2 xl:grid-cols-2 gap-3 xs:gap-3 sm:gap-3 md:gap-3 lg:gap-4 xl:gap-4 text-sm xs:text-sm sm:text-sm md:text-sm lg:text-base xl:text-base">
                            <div className="flex items-center space-x-2 xs:space-x-2 sm:space-x-2 md:space-x-2 lg:space_x-3 xl:space_x-3">
                              <FeatureIcon feature="transmission" />
                              <span>Automatic Transmission</span>
                            </div>
                             {vehicle.features.doors && (
                            <div className="flex items-center space-x-2 xs:space_x-2 sm:space_x-2 md:space_x-2 lg:space_x-3 xl:space_x-3">
                              <FeatureIcon feature="doors" />
                              <span>{vehicle.features.doors}-doors</span>
                            </div>
                             )}
                             {vehicle.features.seats && (
                              <div className="flex items-center space-x-2 xs:space_x-2 sm:space_x-2 md:space_x-2 lg:space_x-3 xl:space_x-3">
                                <FeatureIcon feature="seats" />
                                <span>{vehicle.features.seats} Seats</span>
                              </div>
                            )}
                            <div className="flex items-center space-x-2 xs:space_x-2 sm:space_x-2 md:space_x-2 lg:space_x-3 xl:space_x-3">
                              <FeatureIcon feature="powerSteering" />
                              <span>Power Steering</span>
                            </div>
                            <div className="flex items-center space-x-2 xs:space_x-2 sm:space_x-2 md:space_x-2 lg:space_x-3 xl:space_x-3">
                              <FeatureIcon feature="radioCd" />
                              <span>Radio/CD Player</span>
                            </div>
                            <div className="flex items-center space-x-2 xs:space_x-2 sm:space_x-2 md:space_x-2 lg:space_x-3 xl:space_x-3">
                              <FeatureIcon feature="airConditioning" />
                              <span>Air Conditioning</span>
                            </div>
                            {vehicle.features.hydraulicLift && (
                              <div className="flex items-center space-x-2 xs:space_x-2 sm:space_x-2 md:space_x-2 lg:space_x-3 xl:space_x-3">
                                <FeatureIcon feature="hydraulicLift" />
                                <span>Hydraulic Lift ({vehicle.features.liftCapacity})</span>
                              </div>
                            )}
                            
                          </div>
                        </div>

                        {/* Pricing and Actions */}
                        <div className=" w-full xs:w-full sm:w-full md:w-1/3 lg:w-1/3 xl:w-1/3 text-right space-y-4 xs:space-y-4 sm:space-y-4 md:space-y-4 lg:space-y-6 xl:space-y-6">
                          <div>
                            <div className="text-lg text-center xs:text-lg sm:text-xl md:text-xl lg:text-2xl xl:text-2xl font-medium">
                              AUD {vehicle.price.toFixed(2)}
                              <span className="text-sm text-center xs:text-sm sm:text-sm md:text-sm lg:text-base xl:text-base font-normal text-gray-500 ml-1">/ day</span>
                            </div>
                            <div className="text-sm text-center xs:text-sm sm:text-sm md:text-sm lg:text-base xl:text-base text-gray-600">
                              AUD {vehicle.totalPrice.toFixed(2)} total for 1 Day
                            </div>
                            <div className="text-sm text-center xs:text-sm sm:text-sm md:text-sm lg:text-base xl:text-base text-gray-600">Including GST</div>
                            {vehicle.available && (
                            <div className="text-xs text-center xs:text-xs sm:text-sm md:text-sm lg:text-base xl:text-base text-blue-600">
                              Vehicle Available: {vehicle.vehiclesAvailable} ({vehicle.availabilityPercentage})
                           </div>
                          )}
                          {!vehicle.available && (
                           <div className="text-xs text-center xs:text-xs sm:text-sm md:text-sm lg:text-base xl:text-base text-blue-600">
                             Vehicle Available: {vehicle.vehiclesAvailable} ({vehicle.availabilityPercentage})
                           </div>
                          )}
                          </div>
                         
                          {/* ActionButtons */}
                          <div className="space-y-2 xs:space-y-2 sm:space-y-2 md:space-y-2 lg:space-y-3 xl:space-y-3">
                            {vehicle.available ? (
                              <>
                                {vehicle.recommended ? (
                                  <Button
                                    variant="outline"
                                    onClick={handleRentVehicle}
                                    className="w-full border border-[#ebbb4e] py-2 xs:py-2 sm:py-2 md:py-2 lg:py-3 xl:py-3 px-4 xs:px-4 sm:px-4 md:px-4 lg:px-6 xl:px-6 hover:bg-[#ebbb4e] xs:text-sm sm:text-sm md:text-sm lg:text-base xl:text-base"
                                  >
                                  Rent this vehicle
                                  </Button>
                                ) : (
                                  <Button
                                    variant="outline"
                                    onClick={handleRentVehicle}
                                    className="w-full border border-[#ebbb4e] py-2 xs:py-2 sm:py-2 md:py-2 lg:py-3 xl:py-3 px-4 xs:px-4 sm:px-4 md:px-4 lg:px-6 xl:px-6 hover:bg-[#ebbb4e] xs:text-sm sm:text-sm md:text-sm lg:text-base xl:text-base"
                                  >
                                    Rent this vehicle
                                  </Button>
                                )}
                              </>
                            ) : (
                              <>
                              {vehicle.limitedAvailability && (
                                <div className="text-xs text-center xs:text-xs sm:text-sm md:text-sm lg:text-base xl:text-base text-white font-medium mb-2 xs:mb-2 sm:mb-2 md:mb-2 lg:mb-3 xl:mb-3 bg-blue-400 xs:px-2 sm:px-2 md:px-2 lg:px-3 xl:px-3 py-1">
                                  Limited Availability
                                </div>
                              )}
                              <Button
                                disabled
                                className="w-full bg-red-600 text-white py-2 xs:py-2 sm:py-2 md:py-2 lg:py-3 xl:py-3 px-4 xs:px-4 sm:px-4 md:px-4 lg:px-6 xl:px-6 xs:text-sm sm:text-sm md:text-sm lg:text-base xl:text-base"
                              >
                                Not Available
                              </Button>
                        </>
                            )}
                          </div>
                        </div>
                      </div>

                    </div>
                  ))}

                  <div className="mt-4">
                    <h4 className="text-sm font-semibold mb-2">Comments</h4>
                      <div className="flex flex-col xs:flex-col sm:flex-row md:flex-row lg:flex-row xl:flex-row xs:space-y-2 sm:space-x-2 md:space-x-2 lg:space-x-2 xl:space-x-2 py-2 ">
                        <Input
                          value={comment}
                          onChange={(e) => setComment(e.target.value)}
                          placeholder="Add your comment here"
                          className="flex-1 h-28 w-full xs:w-full sm:w-auto md:w-auto lg:w-auto xl:w-auto"
                        />
                        
                      </div>                   
                      <Button
                          onClick={handleCommentSave}
                          className="bg-yellow-600 text-white px-4 rounded w-auto xs:w-auto sm:w-auto md:w-auto lg:w-auto xl:w-auto"
                          >
                          Save Comment
                        </Button>
                  </div>
                </div>
              </div>
            </div>
 
            <div className="lg:col-span-1">
              <div className="p-4 sm:p-6 md:p-6 lg:p-6 rounded-lg shadow-sm top-6">
                {/* Rate Type Section */}
                <div className="mb-4">
                  <label className="block text-sm sm:text-base md:text-lg lg:text-lg font-semibold mb-2">Rate Type</label>
                  <select
                    value={rentalDetails.rateType || ''}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, rateType: e.target.value })}
                    className="w-auto p-2 border rounded text-sm sm:text-base border-gray-300 focus:outline-none focus:ring-2 focus:ring-blue-500"
                  >
                    <option value="New Year - 2021">New Year - 2021</option>
                    <option value="Long Term Rental">Long Term Rental</option>
                  </select>
                </div>
                {/* Summary Section */}
                <h2 className="text-base sm:text-lg md:text-xl lg:text-xl font-semibold mb-2 sm:mb-4">Summary</h2>
               <span className="bg-gray-500 text-white text-[10px] sm:text-xs md:text-sm font-semibold px-2 sm:px-3 py-1 sm:py-2 rounded-md">Quote </span>
                <div className="space-y-2 sm:space-y-3 text-[10px] sm:text-xs md:text-sm">
                  <div className="flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-1 sm:gap-2 mt-2 sm:mt-4">
                        <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Pickup</h2>
                        <Button
                          variant="ghost"
                          onClick={() => handleEditClick('pickup')}
                          className="p-1"
                        >
                          <Pencil 
                            size={14}
                            className="text-gray-500 hover:text-blue-600" 
                          />
                        </Button>
                      </div>
                      <p>{`Friday, ${rentalDetails.pickupDate} at ${rentalDetails.pickupTime}`}</p>
                      <p>{rentalDetails.pickupLocation}</p>
                    </div>
                  </div>

                  <div className="flex justify-between items-center">
                    <div>
                      <div className="flex items-center gap-1 sm:gap-2">
                        <h3 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold">Return</h3>
                        <Button
                          variant="ghost"
                          onClick={() => handleEditClick('return')}
                          className="p-1"
                        >
                          <Pencil 
                            size={14} 
                            className="text-gray-500 hover:text-blue-600"
                          />
                        </Button>
                      </div>
                      <p>{`Saturday, ${rentalDetails.returnDate} at ${rentalDetails.returnTime}`}</p>
                      <p>{rentalDetails.returnLocation}</p>
                    </div>
                  </div>

                  <hr className="my-2 sm:my-4" />
                </div>
              </div>
            </div>
        
        </div>
        {isPopupOpen && (
          <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
            <div className="bg-white p-4 sm:p-6 rounded-lg shadow-lg w-full max-w-md sm:max-w-lg md:max-w-xl">
              <h2 className="text-sm sm:text-base md:text-lg lg:text-lg font-semibold mb-2 sm:mb-4">Update Date and Times</h2>

              <div className="grid grid-cols-2 gap-2 sm:gap-4">
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Pickup Date</Label>
                  <Input
                    type="text"
                    value={rentalDetails.pickupDate}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, pickupDate: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Pickup Time</Label>
                  <Input
                    type="text"
                    value={rentalDetails.pickupTime}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, pickupTime: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Return Date</Label>
                  <Input
                    type="text"
                    value={rentalDetails.returnDate}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, returnDate: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
                <div>
                  <Label className="block text-[10px] sm:text-xs md:text-sm lg:text-sm font-medium text-gray-700">Return Time</Label>
                  <Input
                    type="text"
                    value={rentalDetails.returnTime}
                    onChange={(e) => setRentalDetails({ ...rentalDetails, returnTime: e.target.value })}
                    className="mt-1 p-1 sm:p-2 w-full border rounded text-[10px] sm:text-xs md:text-sm lg:text-sm focus:outline-none focus:ring-0 focus:border-none "
                  />
                </div>
              </div>

              <div className="mt-2 sm:mt-4 flex justify-end gap-2 sm:gap-4">
                <Button
                  className="px-2 sm:px-4 py-1 sm:py-2 bg-gray-200 rounded hover:bg-gray-300 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                  onClick={() => setIsPopupOpen(false)}
                >
                  Cancel
                </Button>
                <Button
                  className="px-2 sm:px-4 py-1 sm:py-2 bg-[#330101] text-white rounded hover:bg-amber-800 text-[10px] sm:text-xs md:text-sm lg:text-sm"
                  onClick={() => handleSave(
                    rentalDetails.pickupDate,
                    rentalDetails.pickupTime,
                    rentalDetails.returnDate,
                    rentalDetails.returnTime
                  )}
                >
                  Save
                </Button>
              </div>
            </div>
          </div>
        )}
      </div>

         </div>
    </>
  );
}