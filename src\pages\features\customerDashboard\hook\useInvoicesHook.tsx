import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { invoicesData } from '../common/mockdata';


export const useInvoicesHook = () => {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(5);

  const filteredInvoices = invoicesData.filter(invoice => {
    const matchesSearch = invoice.type.toLowerCase().includes(searchTerm.toLowerCase()) || 
      invoice.paymentType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.rentalId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || invoice.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  const totalRecords = filteredInvoices.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentInvoices = filteredInvoices.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const mobileRequests = filteredInvoices;

  const handleViewInvoice = (rentalId: string) => {
    console.log('View invoice:', rentalId);
    const cleanRentalId = rentalId.replace('#', '');
    navigate(`/customer/invoices/${cleanRentalId}`);
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Pending Payment':
        return 'bg-green-600 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    currentPage,
    setCurrentPage,
    recordsPerPage,
    setRecordsPerPage,
    filteredInvoices,
    totalRecords,
    totalPages,
    currentInvoices,
    mobileRequests,
    handleViewInvoice,
    getStatusBadgeStyle,
  };
};