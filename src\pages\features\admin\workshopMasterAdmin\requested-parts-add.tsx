import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; 
import { ArrowLeft, ChevronDown, Users, Plus, X } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Card } from '@/components/ui/card';

interface Part {
  id: number;
  partName: string;
  quantity: string;
  comment?: string;
}

interface CustomerFormData {
  partNo: string;
  vendorName: string;
  parts: Part[];
}

export function RequestedAddParts() {
  const navigate = useNavigate(); 
  const [formData, setFormData] = useState<CustomerFormData>({
    partNo: 'PN001',
    vendorName: '',
    parts: [{ id: Date.now(), partName: '', quantity: '', comment: '' }],
  });

  const handleSave = (): void => {
    navigate('/admin/workshopMasterAdmin/requested-parts');
  };

  const handleInputChange = (field: keyof CustomerFormData, value: string): void => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handlePartChange = (id: number, field: keyof Part, value: string): void => {
    setFormData(prev => ({
      ...prev,
      parts: prev.parts.map(part =>
        part.id === id ? { ...part, [field]: value } : part
      )
    }));
  };

  const addPart = (): void => {
    setFormData(prev => ({
      ...prev,
      parts: [...prev.parts, { id: Date.now(), partName: '', quantity: '', comment: '' }]
    }));
  };

  const removePart = (id: number): void => {
    setFormData(prev => ({
      ...prev,
      parts: prev.parts.filter(part => part.id !== id)
    }));
  };

  const vendorOptions: string[] = ['', 'ARB Somerton', 'RPM Somerton', 'Melbourne Car Removals'];

  // Desktop View
  const DesktopView = () => (
    <div className="hidden md:block min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <Button
          className='bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2'
          size="sm"
          onClick={() => navigate('/admin/workshopMasterAdmin/requested-parts')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="flex items-center mb-4 sm:mb-6">
        <Users className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-medium text-earth-dark">Add Parts</h1>
      </div>
      
      <div className="bg-white rounded-lg p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
        {/* Common Fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.partNo}
              onChange={(e) => handleInputChange('partNo', e.target.value)}
              className="bg-gray-300 w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              placeholder="PN001"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Part No
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">Vendor</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {formData.vendorName || 'Select Vendor'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {vendorOptions.map((vendorName) => (
                  <DropdownMenuItem
                    key={vendorName}
                    onSelect={() => handleInputChange('vendorName', vendorName)}
                    className={formData.vendorName === vendorName ? 'bg-amber-100 font-regular' : ''}
                  >
                    {vendorName || 'Select Vendor'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="relative col-span-1 sm:col-span-2">
            <Input
              type="text"
              value={formData.parts[0].comment || ''}
              onChange={(e) => handlePartChange(formData.parts[0].id, 'comment', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
              placeholder="Type here...."
              required
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Comment
            </Label>
          </div>
        </div>

        {/* Parts List */}
        {formData.parts.map((part, index) => (
          <div key={part.id} className="pb-4 mb-4">
            <div className="flex justify-end mb-2">
              {index > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removePart(part.id)}
                  className="text-red-500 hover:text-red-700"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 sm:gap-4 md:gap-6 mb-4">
              <div className="relative">
                <Input
                  type="text"
                  value={part.partName}
                  onChange={(e) => handlePartChange(part.id, 'partName', e.target.value)}
                  className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
                  placeholder="Doors"
                  required
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Part Name
                </Label>
              </div>
              <div className="relative flex items-center">
                <Input
                  type="text"
                  value={part.quantity}
                  onChange={(e) => handlePartChange(part.id, 'quantity', e.target.value)}
                  className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base"
                  placeholder="10"
                  required
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Quantity
                </Label>
                {index === formData.parts.length - 1 && (
                  <Button
                    variant="ghost"
                    onClick={addPart}
                    className="ml-4 bg-[#330101] text-white px-2 py-2 h-9 flex items-center"
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          </div>
        ))}
      </div>

      <div className="flex justify-end mt-4">
        <Button 
          onClick={handleSave}
          className="px-2 sm:px-3 md:px-4 py-1 sm:py-2 bg-[#330101] text-white rounded-md transition-colors text-[10px] sm:text-xs md:text-sm lg:text-base"
        >
          Save Parts
        </Button>
      </div>
    </div>
  );

  // Mobile View
  const MobileView = () => (
    <div className="block md:hidden min-h-screen p-2">
      <div className="mb-4">
        <Button
          className="bg-[#330101] text-white w-full text-sm px-3 py-2 flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/admin/workshopMasterAdmin/requested-parts')}
        >
          <ArrowLeft className="w-3 h-3 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="flex items-center mb-4">
        <Users className="w-5 h-5 mr-2 text-earth-dark" />
        <h1 className="text-xl font-medium text-earth-dark">Add Parts</h1>
      </div>
      <Card className="bg-white rounded-lg p-3 mb-4">
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.partNo}
            onChange={(e) => handleInputChange('partNo', e.target.value)}
            className="bg-gray-300 w-full p-1 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-xs"
            disabled
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Part No
          </Label>
        </div>
        <div className="mb-3 relative">
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <Button variant="outline" className="w-full justify-between border-gray-500 text-xs">
                {formData.vendorName || 'Select Vendor'}
                <ChevronDown className="w-4 h-4 text-gray-500" />
              </Button>
            </DropdownMenuTrigger>
            <DropdownMenuContent className="w-full">
              {vendorOptions.map((vendorName) => (
                <DropdownMenuItem
                  key={vendorName}
                  onSelect={() => handleInputChange('vendorName', vendorName)}
                  className={formData.vendorName === vendorName ? 'bg-amber-100 font-regular' : ''}
                >
                  {vendorName || 'Select Vendor'}
                </DropdownMenuItem>
              ))}
            </DropdownMenuContent>
          </DropdownMenu>
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Vendor
          </Label>
        </div>
        <div className="mb-3 relative">
          <Input
            type="text"
            value={formData.parts[0].comment || ''}
            onChange={(e) => handlePartChange(formData.parts[0].id, 'comment', e.target.value)}
            className="w-full p-1 border border-gray-500 rounded-md text-xs"
          />
          <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
            Comment
          </Label>
        </div>
        {formData.parts.map((part, index) => (
          <div key={part.id} className="mb-4 border-t pt-3">
            <div className="flex justify-between items-center mb-2">
              <span className="text-xs font-semibold text-gray-700">Part {index + 1}</span>
              {index > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={() => removePart(part.id)}
                  className="text-red-500 hover:text-red-700 p-1"
                >
                  <X className="w-4 h-4" />
                </Button>
              )}
            </div>
            <div className="mb-2 relative">
              <Input
                type="text"
                value={part.partName}
                onChange={(e) => handlePartChange(part.id, 'partName', e.target.value)}
                className="w-full p-1 border border-gray-500 rounded-md text-xs"
              />
              <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                Part Name
              </Label>
            </div>
            <div className="mb-2 flex items-center">
              <div className="flex-1 relative">
                <Input
                  type="text"
                  value={part.quantity}
                  onChange={(e) => handlePartChange(part.id, 'quantity', e.target.value)}
                  className="w-full p-1 border border-gray-500 rounded-md text-xs"
                />
                <Label className="absolute left-2 -top-2 text-[10px] text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Quantity
                </Label>
              </div>
              {index === formData.parts.length - 1 && (
                <Button
                  variant="ghost"
                  onClick={addPart}
                  className="ml-2 bg-[#330101] text-white px-2 py-2 h-8 flex items-center"
                >
                  <Plus className="w-4 h-4" />
                </Button>
              )}
            </div>
          </div>
        ))}
      </Card>
      <div className="flex justify-end mt-4">
        <Button 
          onClick={handleSave}
          className="bg-[#330101] text-white w-1/3 text-sm px-3 py-2 flex items-center justify-center"
        >
          Save Parts
        </Button>
      </div>
    </div>
  );

  return (
    <>
      <MobileView />
      <DesktopView />
    </>
  );
}