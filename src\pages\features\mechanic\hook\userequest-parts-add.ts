import { useState } from "react";
import { useNavigate } from "react-router-dom";
import { RequestPart } from "./type/mechanictype";

export function useRequestPartsAdd() {
  const navigate = useNavigate();
  const [form, setForm] = useState<RequestPart>({
    partNumber: "PN" + String(Math.floor(Math.random() * 900) + 100), // e.g. PN123
    partName: "",
    quantity: 1,
    comment: "",
  });

  function handleInputChange(field: keyof RequestPart, value: string | number) {
    setForm(prev => ({ ...prev, [field]: value }));
  }

  function handleAdd() {
    // Submit logic here
    navigate(-1);
  }

  function handleCancel() {
    navigate(-1);
  }

  return { form, handleInputChange, handleAdd, handleCancel };
}