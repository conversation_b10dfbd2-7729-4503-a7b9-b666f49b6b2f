import React, { useEffect, useState, useRef } from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Logo } from '../components/auth/logo';
import { BackButton } from '../components/auth/back-button';
export function VerifyOtpPage() {
  const navigate = useNavigate();
  const [otp, setOtp] = useState(['', '', '', '']);
  const inputRefs = [useRef(null), useRef(null), useRef(null), useRef(null)];
  const [timer, setTimer] = useState(59);
  useEffect(() => {
    if (timer > 0) {
      const interval = setInterval(() => {
        setTimer(prev => prev - 1);
      }, 1000);
      return () => clearInterval(interval);
    }
  }, [timer]);
  const handleChange = (index, value) => {
    if (value.length <= 1) {
      const newOtp = [...otp];
      newOtp[index] = value;
      setOtp(newOtp);
      // Move to next input if value is entered
      if (value !== '' && index < 3) {
        inputRefs[index + 1].current.focus();
      }
    }
  };
  const handleKeyDown = (index, e) => {
    // Move to previous input on backspace if current input is empty
    if (e.key === 'Backspace' && index > 0 && otp[index] === '') {
      inputRefs[index - 1].current.focus();
    }
  };
  const handleSubmit = () => {
    // Check if OTP is complete
    if (otp.every(digit => digit !== '')) {
      // Navigate to reset password page or wherever needed
      navigate('/reset-password');
    }
  };
  const handleResend = () => {
    setTimer(59);
    // Logic to resend OTP
  };
  return <div className="auth-container">
      <BackButton />
      <div className="auth-form">
        <Logo />
        <h1 className="auth-heading">
          Please enter 4-digit code we sent to your email address
        </h1>
        <div className="otp-input-container">
          {otp.map((digit, index) => <input key={index} ref={inputRefs[index]} type="text" maxLength={1} value={digit} onChange={e => handleChange(index, e.target.value)} onKeyDown={e => handleKeyDown(index, e)} className="otp-input" />)}
        </div>
        <Button onClick={handleSubmit} className="auth-button">
          Submit
        </Button>
        <div className="text-center mt-4">
          <button onClick={handleResend} disabled={timer > 0} className="auth-link text-sm">
            Resend Code{' '}
            {timer > 0 && `- 00:${timer.toString().padStart(2, '0')}`}
          </button>
        </div>
      </div>
    </div>;
}