import React from 'react';
import { Textarea } from '@/components/ui/textarea';
import { SectionProps } from '../type/reception-type';

const CommentSection: React.FC<SectionProps> = ({ formData, handleInputChange }) => (
  <div className="space-y-4 mt-8">
    <h3 className="text-xl font-semibold text-gray-900 pb-2">Comment</h3>
    <div className="space-y-2">
      <Textarea
        id="comment"
        value={formData.comment}
        onChange={(e) => handleInputChange('comment', e.target.value)}
        className="bg-white min-h-[100px] focus:outline-none focus:ring-0 focus:border-none"
        placeholder="Type Here ---"
      />
    </div>
  </div>
);

export default CommentSection;
