import React, { useState, useMemo } from 'react';
import { Search, Eye, Trash2, Car, Plus, ChevronDown, Edit } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Pagination } from '@/components/layout/Pagination';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useNavigate } from 'react-router-dom';

interface Vehicle {
  id: string;
  rego: string;
  vin: string;
  class: string;
  type: string;
  currentRenter: string;
  lastMileage: number;
  fuel: string;
  status: 'Dirty' | 'Rental' | 'Available';
  available: boolean;
}

const mockVehicles: Vehicle[] = [
  {
    id: '1',
    rego: 'PXT 983',
    vin: 'ELVLJN388QHLFLZEI',
    class: 'Economy Plus',
    type: 'Car',
    currentRenter: 'Not Assigned',
    lastMileage: 65945,
    fuel: '8/8',
    status: 'Dirty',
    available: true
  },
  {
    id: '2',
    rego: 'ASX 181',
    vin: 'MZKWLJHNKGUWLUOI',
    class: 'Economy',
    type: 'Car',
    currentRenter: 'Sam Perera',
    lastMileage: 227891,
    fuel: '8/8',
    status: 'Rental',
    available: false
  },
  {
    id: '3',
    rego: 'PVG 430',
    vin: 'VZ7OZSNYQXUSYSLYF',
    class: 'Mini Van',
    type: 'Van',
    currentRenter: 'Not Assigned',
    lastMileage: 456780,
    fuel: '8/8',
    status: 'Dirty',
    available: true
  },
  {
    id: '4',
    rego: 'ABC 123',
    vin: 'TESTVIN123456789',
    class: 'Premium',
    type: 'Car',
    currentRenter: 'Not Assigned',
    lastMileage: 12345,
    fuel: '8/8',
    status: 'Available',
    available: true
  },
  {
    id: '5',
    rego: 'XYZ 789',
    vin: 'ANOTHERVIN987654',
    class: 'Economy',
    type: 'Car',
    currentRenter: 'John Smith',
    lastMileage: 98765,
    fuel: '7/8',
    status: 'Rental',
    available: false
  },
  {
    id: '313',
    rego: 'XYZ 456',
    vin: 'MERCEDESVIN313',
    class: 'Premium',
    type: 'Car',
    currentRenter: 'Richard Smith',
    lastMileage: 43607,
    fuel: '8/8',
    status: 'Available',
    available: true
  }
];

type FilterTab = 'All' | 'Available' | 'Dirty' | 'On Rent';

export function ReceptionVehiclePage() {
  const [activeTab, setActiveTab] = useState<FilterTab>('All');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [searchTerm, setSearchTerm] = useState('');
  const [currentPage, setCurrentPage] = useState<number>(1);
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);

  const filteredVehicles = useMemo(() => {
    let filtered = mockVehicles;

    switch (activeTab) {
      case 'Available':
        filtered = filtered.filter(vehicle => vehicle.status === 'Available');
        break;
      case 'Dirty':
        filtered = filtered.filter(vehicle => vehicle.status === 'Dirty');
        break;
      case 'On Rent':
        filtered = filtered.filter(vehicle => vehicle.status === 'Rental');
        break;
    }

    if (searchTerm) {
      filtered = filtered.filter(vehicle =>
        vehicle.rego.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vehicle.vin.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vehicle.class.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vehicle.type.toLowerCase().includes(searchTerm.toLowerCase()) ||
        vehicle.currentRenter.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return filtered;
  }, [activeTab, searchTerm]);

  const totalRecords = filteredVehicles.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentVehicle = filteredVehicles.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const getStatusBadge = (status: string) => {
    switch (status) {
      case 'Dirty':
        return 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Rental':
        return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Available':
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };

  const getAvailabilityBadge = (available: boolean) => {
    return available
      ? 'bg-green-500 text-white px-3 py-1 rounded text-sm font-medium'
      : 'bg-red-500 text-white px-3 py-1 rounded text-sm font-medium';
  };

  const navigate = useNavigate();

  return (
    <div className="min-h-screen">
      <div className="flex">
        <div className="flex-1 p-6">
          {/* Header */}
          <div className="flex items-center justify-between mb-6">
            <div className="flex items-center space-x-2">
              <Car className="w-6 h-6 text-gray-600" />
              <h1 className="text-2xl font-semibold text-gray-800">Vehicles</h1>
            </div>
            <Button
              className="px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors w-full sm:w-auto"
              onClick={() => navigate('/admin/receptionMasterAdmin/fleet/vehicles/add-vehicle')}
            >
              Add Vehicle
            </Button>
          </div>

          {/* Filter Tabs */}
          <div className="flex space-x-1 mb-6">
            {(['All', 'Available', 'Dirty', 'On Rent'] as FilterTab[]).map((tab) => (
              <Button
                key={tab}
                variant={activeTab === tab ? 'default' : 'outline'}
                className={`${
                  activeTab === tab 
                    ? 'bg-amber-900 hover:bg-amber-800 text-white' 
                    : 'bg-white border-gray-300 text-gray-700 hover:bg-gray-50'
                }`}
                onClick={() => setActiveTab(tab)}
              >
                {tab}
              </Button>
            ))}
          </div>

          {/* Search and Filter Bar */}
          <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
            <div className="relative w-full sm:w-auto">
              <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
                <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
                  <SelectValue placeholder="Filter" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Start typing here..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
              />
            </div>

            <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
              Save this search
            </Button>
          </div>

          {/* Table */}
          <div className="hidden md:block">
            <div className="rounded-md border bg-white">
              <Table className="w-full">
                <TableHeader>
                  <TableRow className="bg-gray-50 uppercase">
                    <TableHead>Rego</TableHead>
                    <TableHead>VIN</TableHead>
                    <TableHead>Class</TableHead>
                    <TableHead>Type</TableHead>
                    <TableHead>Current Renter</TableHead>
                    <TableHead>Last Mileage</TableHead>
                    <TableHead>Fuel</TableHead>
                    <TableHead>Status</TableHead>
                    <TableHead>Available?</TableHead>
                    <TableHead>Action</TableHead>
                  </TableRow>
                </TableHeader>
                <TableBody className="bg-white divide-y divide-gray-200">
                  {currentVehicle.map((vehicle) => (
                    <TableRow key={vehicle.id} className="hover:bg-gray-50">
                      <TableCell>{vehicle.rego}</TableCell>
                      <TableCell>{vehicle.vin}</TableCell>
                      <TableCell>{vehicle.class}</TableCell>
                      <TableCell>{vehicle.type}</TableCell>
                      <TableCell>{vehicle.currentRenter}</TableCell>
                      <TableCell>{vehicle.lastMileage.toLocaleString()}</TableCell>
                      <TableCell>{vehicle.fuel}</TableCell>
                      <TableCell>
                        <span className={getStatusBadge(vehicle.status)}>
                          {vehicle.status}
                        </span>
                      </TableCell>
                      <TableCell>
                        <span className={getAvailabilityBadge(vehicle.available)}>
                          {vehicle.available ? 'Yes' : 'No'}
                        </span>
                      </TableCell>
                      <TableCell>
                        <div className="flex space-x-2">
                         
                          <Button
                            variant="ghost"
                            size="sm"
                            onClick={() => navigate(`/admin/receptionMasterAdmin/fleet/vehicles/edit-vehicle/${vehicle.id}`)}
                          >
                            <Eye className="w-4 h-4" />
                          </Button>
                          <Button variant="ghost" size="sm">
                            <Trash2 className="w-4 h-4" />
                          </Button>
                        </div>
                      </TableCell>
                    </TableRow>
                  ))}
                </TableBody>
              </Table>
            </div>

            {/* Pagination */}
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalRecords={totalRecords}
              recordsPerPage={recordsPerPage}
              onPageChange={setCurrentPage}
              onRecordsPerPageChange={(records) => {
                setRecordsPerPage(records);
                setCurrentPage(1);
              }}
              className="mt-6"
            />
          </div>
        </div>
      </div>
    </div>
  );
}