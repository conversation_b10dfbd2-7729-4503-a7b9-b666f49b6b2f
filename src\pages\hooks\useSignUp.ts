import { useState , useEffect} from 'react';
import { useNavigate } from 'react-router-dom';
import { format } from 'date-fns';
import useFetch from '../../hooks/useFetch';
import alert from '../../utils/alert';
import { FormData } from '../types/auth';

interface UseSignUpReturn {
  formData: FormData;
  setFormData: React.Dispatch<React.SetStateAction<FormData>>;
  birthday: Date | null;
  setBirthday: React.Dispatch<React.SetStateAction<Date | null>>;
  issueDate: Date | null;
  setIssueDate: React.Dispatch<React.SetStateAction<Date | null>>;
  expireDate: Date | null;
  setExpireDate: React.Dispatch<React.SetStateAction<Date | null>>;
  showPassword: boolean;
  setShowPassword: React.Dispatch<React.SetStateAction<boolean>>;
  showConfirmPassword: boolean;
  setShowConfirmPassword: React.Dispatch<React.SetStateAction<boolean>>;
  errors: { [key: string]: string };
  setErrors: React.Dispatch<React.SetStateAction<{ [key: string]: string }>>;
  handleInputChange: (e: React.ChangeEvent<HTMLInputElement>) => void;
  handlePaste: (e: React.ClipboardEvent<HTMLInputElement>) => void; // New handler
  handlePhoneChange: (value: string, field: string) => void;
  handleSubmit: () => Promise<void>;
  handleLogin: () => void;
  formatDate: (date: Date | null) => string;
  fetchData: (options: { data: any }) => Promise<any>;
  loading: boolean;
  error: string | null;
  data: any;
}

export const useSignUp = (): UseSignUpReturn => {
  const navigate = useNavigate();

  // Form state
  const [formData, setFormData] = useState<FormData>({
    firstName: '',
    lastName: '',
    address: '',
    postCode: '',
    country: '',
    email: '',
    confirmEmail: '',
    phone: '',
    companyName: '',
    driverLicense: '',
    issueCountry: '',
    emergencyContactName: '',
    emergencyContactNumber: '',
    password: '',
    confirmPassword: '',
  });

  const [birthday, setBirthday] = useState<Date | null>(null);
  const [issueDate, setIssueDate] = useState<Date | null>(null);
  const [expireDate, setExpireDate] = useState<Date | null>(null);
  const [showPassword, setShowPassword] = useState(false);
  const [showConfirmPassword, setShowConfirmPassword] = useState(false);
  const [errors, setErrors] = useState<{ [key: string]: string }>({});

  
// useEffect to log formData changes
  useEffect(() => {
    console.log('FormData updated:', formData);
  }, [formData]);

  // Use useFetch hook
  const { fetchData, loading, error, data } = useFetch('/api/auth/register', {
    method: 'POST',
    contentType: 'application/json',
    successMessage: 'Registration successful! The OTP has been sent to your email or phone.',
  });

useEffect(() => {
console.log('API Response:', data);
    if (data?.success) {
      setFormData({
        firstName: '',
        lastName: '',
        address: '',
        postCode: '',
        country: '',
        email: '',
        confirmEmail: '',
        phone: '',
        companyName: '',
        driverLicense: '',
        issueCountry: '',
        emergencyContactName: '',
        emergencyContactNumber: '',
        password: '',
        confirmPassword: '',
      });
      setBirthday(null);
      setIssueDate(null);
      setExpireDate(null);
      setShowPassword(false);
      setShowConfirmPassword(false);
      setErrors({});
    }
  }, [data]);

  // Format date to YYYY-MM-DD
  const formatDate = (date: Date | null): string => {
    return date ? format(date, 'yyyy-MM-dd') : '';
  };

  // Handle input changes
  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
  const target = e.target as HTMLInputElement;
  const { id, value } = target;
  setFormData((prev) => ({ ...prev, [id]: value }));
  setErrors((prev) => ({ ...prev, [id]: '' }));
};

const handlePhoneChange = (value: string, field: string) => {
    setFormData((prev) => ({ ...prev, [field]: value }));
    setErrors((prev) => ({ ...prev, [field]: '' }));
  };

// Handle paste event to prevent pasting in email, confirmEmail, password, and confirmPassword
  const handlePaste = (e: React.ClipboardEvent<HTMLInputElement>) => {
    const target = e.target as HTMLInputElement;
    if (['email', 'confirmEmail', 'password', 'confirmPassword'].includes(target.id)) {
      e.preventDefault();
      const fieldName = {
        email: 'Email',
        confirmEmail: 'Confirm Email',
        password: 'Password',
        confirmPassword: 'Confirm Password'
      }[target.id];
      setErrors((prev) => ({ ...prev, [target.id]: `${fieldName}  field cannot be copy-pasted!` }));
      alert.warn(`${fieldName}  field cannot be copy-pasted!`);
    }
  };

  // Handle form submission
  const handleSubmit = async () => {
    setErrors({});

    // Validate email
    if (!formData.email) {
      setErrors({ email: 'Email is required!' });
      return;
    }

    // Validate email format
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
    if (!emailRegex.test(formData.email)) {
      setErrors({ email: 'Please enter a valid email!" or "Enter a valid email!' });
      return;
    }

    // Validate confirmEmail
    if (!formData.confirmEmail) {
      setErrors({ confirmEmail: 'Confirm Email is required!' });
      return;
    }

    // Validate emails match
    if (formData.email !== formData.confirmEmail) {
      setErrors({ confirmEmail: 'Email and Confirm Email should be identical!' });
        alert.warn('Email and Confirm Email should be identical!');
      return;
    }

    // Frontend validation for confirmPassword
     if (formData.password !== formData.confirmPassword) {
      setErrors({ confirmPassword: 'Password does not match' });
      return;
    }

    // Frontend validation for required date fields
    if (!birthday) {
      setErrors({ birthday: 'Enter your date of birth' });
      return;
    }
    if (!issueDate) {
      setErrors({ issueDate: 'Enter the issued date' });
      return;
    }
    if (!expireDate) {
      setErrors({ expiryDate: 'Enter the expiry date' });
      return;
    }


   // Phone number validation (handled by react-phone-input-2, but ensure it's not empty)
    if (!formData.phone) {
      setErrors({ phone: 'Enter a valid phone number' });
      return;
    }
    if (!formData.emergencyContactNumber) {
      setErrors({ emergencyContactNumber: 'Enter a valid emergency contact number' });
      return;
    }

    // Prepare request body
    const requestBody = {
      email: formData.email,
      password: formData.password,
      firstName: formData.firstName,
      lastName: formData.lastName,
      address: formData.address,
      postCode: formData.postCode,
      country: formData.country,
      phoneNumber: formData.phone,
      birthday: formatDate(birthday),
      companyName: formData.companyName || undefined,
      emergencyContactPersonName: formData.emergencyContactName || undefined,
      emergencyContactPhoneNumber: formData.emergencyContactNumber || undefined,
      emergencyContactRelationship: undefined,
      dlNumber: formData.driverLicense,
      issueCountry: formData.issueCountry,
      issueDate: formatDate(issueDate),
      expiryDate: formatDate(expireDate),
      otpDeliveryMethod: 'email', // Hardcoded to email
    };

    const response = await fetchData({ data: requestBody });

    if (response && !response.status && response.errors) {
      const errorMap: { [key: string]: string } = {};
      response.errors.forEach((err: any) => {
        errorMap[err.field] = err.message;
      });
      setErrors(errorMap);
      errorMap && Object.values(errorMap).forEach((msg) => alert.warn(msg));
    } 
  };

  // Handle login link click
  const handleLogin = () => {
    navigate('/login');
  };

  return {
    formData,
    setFormData,
    birthday,
    setBirthday,
    issueDate,
    setIssueDate,
    expireDate,
    setExpireDate,
    showPassword,
    setShowPassword,
    showConfirmPassword,
    setShowConfirmPassword,
    errors,
    setErrors,
    handleInputChange,
    handlePhoneChange,
    handlePaste,
    handleSubmit,
    handleLogin,
    formatDate,
    fetchData,
    loading,
    error,
    data,
  };
};