import { NavigateFunction } from 'react-router-dom';
import { Service } from '../type/teamleadertype';

export const handleServiceCodeClick = (serviceCode: string, navigate: NavigateFunction): void => {
  navigate(`/teamleader/services-edit/${serviceCode}`);
};

export const filterServices = (services: Service[], searchTerm: string): Service[] => {
  return services.filter(
    (service) =>
      service.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      service.mechanicName.toLowerCase().includes(searchTerm.toLowerCase())
  );
};

export const paginateServices = (
  filteredServices: Service[],
  currentPage: number,
  entriesPerPage: number
): { paginatedServices: Service[]; totalPages: number; startEntry: number; endEntry: number } => {
  const totalEntries = filteredServices.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const paginatedServices = filteredServices.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  );
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return { paginatedServices, totalPages, startEntry, endEntry };
};