import React, { useState, useEffect } from 'react';
import { ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { Button } from '@/components/ui/button';
import { useNavigate, useParams } from 'react-router-dom';

interface FormData {
  rentalId: string;
  customer: string;
  returnDate: string;
  returnMonth: string;
  returnYear: string;
  returnTime: string;
  totalPrice: string;
  totalPaid: string;
  outstandingBalance: string;
  paymentMethod: string;
  amountBeingPaid: string;
  remark: string;
}

export function ReceptionOutstanding() {
  const navigate = useNavigate();
  const { rentalId } = useParams<{ rentalId: string }>();

  const reservationData = [
    {
      rentalId: 'R0026',
      customer: '<PERSON> Thomson',
      pickupDate: '15-06-2025 11:30',
      returnDate: '14-06-2025 14:30',
      category: 'Passenger',
      vehicleClass: 'Economy Plus Car',
      vehicle: 'PCB 456',
      totalPrice: '$592.00',
      totalRevenue: '$592.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$492.00',
      status: 'Rental',
      walkInCustomer: 'No',
      loanVehicle: 'Yes',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0009',
      customer: 'Igor Peets',
      pickupDate: '02-03-2025 06:16',
      returnDate: '10-03-2025 04:30',
      category: 'Passenger',
      vehicleClass: 'Economy Premium',
      vehicle: 'Isuzu Nhr 45 150 - 2AC5GR',
      totalPrice: '$550.00',
      totalRevenue: '$550.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$450.00',
      status: 'Open',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0015',
      customer: 'Alyssa Dorschad',
      pickupDate: '11-01-2025 07:30',
      returnDate: '24-01-2025 11:30',
      category: 'Commercial',
      vehicleClass: 'SUV 7 Seats Premium',
      vehicle: 'Toyota Hiace Commuter - 2BH8BD',
      totalPrice: '$592.00',
      totalRevenue: '$592.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$492.00',
      status: 'Completed',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0017',
      customer: 'Jaymar Elvin',
      pickupDate: '20-12-2024 21:16',
      returnDate: '24-12-2024 21:16',
      category: 'Passenger',
      vehicleClass: 'Mini Bus 12 Seats',
      vehicle: 'DEF 456',
      totalPrice: '$548.00',
      totalRevenue: '$548.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$448.00',
      status: 'Open',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0022',
      customer: 'Juliet Semathrna',
      pickupDate: '11-10-2024 21:16',
      returnDate: '14-11-2024 21:16',
      category: 'Passenger',
      vehicleClass: 'Economy Plus',
      vehicle: 'GHI 987',
      totalPrice: '$548.00',
      totalRevenue: '$548.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$448.00',
      status: 'Open',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    }
  ];

  const [formData, setFormData] = useState<FormData>({
    rentalId: '',
    customer: '',
    returnDate: '',
    returnMonth: '',
    returnYear: '',
    returnTime: '',
    totalPrice: '',
    totalPaid: '',
    outstandingBalance: '',
    paymentMethod: '',
    amountBeingPaid: '$00.00',
    remark: ''
  });

  useEffect(() => {
    if (rentalId) {
      const reservation = reservationData.find(item => item.rentalId === rentalId);
      if (reservation) {
        const [returnDate, returnTime] = reservation.returnDate.split(' ');
        const [day, month, year] = returnDate.split('-');
        setFormData({
          rentalId: reservation.rentalId,
          customer: reservation.customer,
          returnDate: day,
          returnMonth: month,
          returnYear: year,
          returnTime: returnTime,
          totalPrice: reservation.totalPrice,
          totalPaid: reservation.totalPaid,
          outstandingBalance: reservation.outstandingBalance,
          paymentMethod: '',
          amountBeingPaid: '$00.00',
          remark: ''
        });
      }
    }
  }, [rentalId]);

  const handleInputChange = (field: keyof FormData, value: string) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleCancel = () => {
    // Handle cancel logic
    console.log('Cancel clicked');
    navigate('/reception/reception-reservantionmanagement');
  };

  const handleConfirmUpdate = () => {
    // Handle confirm & update logic
    console.log('Confirm & Update clicked', formData);
    navigate('/reception/reception-reservantionmanagement');
  };

  return (
    <div>
      {/* Go Back Button */}
      <div className="bg-white p-4">
        <Button className="flex items-center space-x-2 text-white bg-[#330101] px-4 py-2 rounded hover:bg-red-800" 
        onClick={() => navigate('/reception/reception-reservantionmanagement')}>
          <ArrowLeft className="w-4 h-4" />
          <span>Go Back</span>
        </Button>
      </div>

      {/* Main Content */}
      <div className="max-w-8xl mx-auto p-6">
        <h1 className="text-2xl font-bold text-gray-800 mb-6">Outstanding Balance Payment</h1>

        <div className="bg-white rounded-lg shadow-sm p-6">
          {/* First Row - Rental ID, Customer, Return Date, Outstanding Balance */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            {/* Rental ID */}
            <div className="space-y-2 relative">
              <Label htmlFor="rentalId" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Rental ID
              </Label>
              <Input
                id="rentalId"
                type="text"
                value={formData.rentalId}
                onChange={(e) => handleInputChange('rentalId', e.target.value)}
                className="w-full h-12 border-2 border-gray-400 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent"
              />
            </div>

            {/* Customer */}
            <div className="space-y-2 relative">
              <Label htmlFor="customer" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Customer
              </Label>
              <Input
                id="customer"
                type="text"
                value={formData.customer}
                onChange={(e) => handleInputChange('customer', e.target.value)}
                className="w-full h-12 border-2 border-gray-400 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent"
              />
            </div>

            {/* Return Date */}
            <div className="space-y-2 relative">
              <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Return Date
              </Label>
              <div className="flex space-x-1">
                <Input
                  value={formData.returnDate}
                  onChange={(e) => handleInputChange('returnDate', e.target.value)}
                  className="w-26 h-12 border-2 border-gray-400 rounded px-2 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent text-center"
                />
              
                <Input
                  value={formData.returnTime}
                  onChange={(e) => handleInputChange('returnTime', e.target.value)}
                  className="flex-1 w-8 h-12 border-2 border-gray-400 rounded px-2 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent text-center"
                />
              </div>
            </div>

         
          </div>

          {/* Second Row - Total Price, Total Paid */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
            {/* Total Price */}
            <div className="space-y-2 relative">
              <Label htmlFor="totalPrice" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Total Price
              </Label>
              <Input
                id="totalPrice"
                type="text"
                value={formData.totalPrice}
                onChange={(e) => handleInputChange('totalPrice', e.target.value)}
                className="w-full h-12 border-2 border-gray-400 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent"
              />
            </div>

            {/* Total Paid */}
            <div className="space-y-2 relative">
              <Label htmlFor="totalPaid" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Total Paid
              </Label>
              <Input
                id="totalPaid"
                type="text"
                value={formData.totalPaid}
                onChange={(e) => handleInputChange('totalPaid', e.target.value)}
                className="w-full h-12 border-2 border-gray-400 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent"
              />
            </div>

               {/* Outstanding Balance */}
            <div className="space-y-2 relative">
              <Label htmlFor="outstandingBalance" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                Outstanding Balance
              </Label>
              <Input
                id="outstandingBalance"
                type="text"
                value={formData.outstandingBalance}
                onChange={(e) => handleInputChange('outstandingBalance', e.target.value)}
                className="w-full h-12 border-2 border-gray-400 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Payment Method Section */}
          <div className="mb-8">
            <h2 className="text-lg font-semibold text-gray-800 mb-4">Payment Method</h2>
            
            <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
              {/* Payment Method Dropdown */}
              <div className="space-y-2 relative">
                <Label htmlFor="paymentMethod" className="absolute bg-white px-2 text-xs text-gray-600 z-10 left-3">
                  Payment Method
                </Label>
                <Select value={formData.paymentMethod} onValueChange={(value) => handleInputChange('paymentMethod', value)}>
                  <SelectTrigger className="w-full h-12 border-2 border-gray-400 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent">
                    <SelectValue placeholder="Select Method" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="online-transfer">Online Transfer</SelectItem>
                    <SelectItem value="cash">Cash</SelectItem>
                  </SelectContent>
                </Select>
              </div>
            </div>
          </div>

          {/* Amount Being Paid Now */}
          <div className="mb-6">
            <h3 className="text-md font-medium text-gray-800 mb-3">Amount Being Paid Now:</h3>
            <div className="max-w-md">
              <Input
                value={formData.amountBeingPaid}
                onChange={(e) => handleInputChange('amountBeingPaid', e.target.value)}
                className="w-[360px] h-12 border-2 border-gray-400 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent"
              />
            </div>
          </div>

          {/* Remark */}
          <div className="mb-8">
            <h3 className="text-md font-medium text-gray-800 mb-3">Remark</h3>
            <Textarea
              value={formData.remark}
              onChange={(e) => handleInputChange('remark', e.target.value)}
              rows={3}
              className="w-full border-2 border-gray-400 rounded px-3 py-2 text-gray-900 focus:outline-none focus:ring-1 focus:ring-orange-500 focus:border-transparent resize-none"
            />
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end space-x-4">
            <Button
              onClick={handleCancel}
              variant="outline"
              className="px-8 py-2 border-2 border-gray-300 text-gray-700 hover:bg-gray-50"
            >
              Cancel
            </Button>
            <Button
              onClick={handleConfirmUpdate}
              className="px-8 py-2 bg-[#330101] text-white"
            >
              Confirm & Update
            </Button>
          </div>
        </div>
      </div>
    </div>
  );
}