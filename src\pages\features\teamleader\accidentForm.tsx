import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft } from 'lucide-react';
import React, { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';

interface AccidentFormData {
  dateOfLoss: string;
  timeOfLoss: string;
  numberOfDaysLeft: string;
  incidentLocation: string;
  state: string;
  incidentDetails: string;
  incidentType: string;
  weather: string;
  roadSurface: string;
  witnessDetails: string;
  injuries: string;
  damageSeverity: string;
  interiorImages: File[];
  exteriorImages: File[];
  leftSideImages: File[];
  rightSideImages: File[];
  frontSideImages: File[];
  backSideImages: File[];
  sideMirrorImages: File[];
  vehicleBodyImages: File[];
}

export const AccidentForm: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<AccidentFormData>({
    dateOfLoss: '',
    timeOfLoss: '',
    numberOfDaysLeft: '',
    incidentLocation: '',
    state: 'Victoria',
    incidentDetails: '',
    incidentType: '',
    weather: '',
    roadSurface: '',
    witnessDetails: '',
    injuries: '',
    damageSeverity: '',
    interiorImages: [],
    exteriorImages: [],
    leftSideImages: [],
    rightSideImages: [],
    frontSideImages: [],
    backSideImages: [],
    sideMirrorImages: [],
    vehicleBodyImages: []
  });

  // Calculate days left when date of loss changes
  useEffect(() => {
    if (formData.dateOfLoss) {
      const lossDate = new Date(formData.dateOfLoss);
      const today = new Date();
      const timeDiff = today.getTime() - lossDate.getTime();
      const daysDiff = Math.ceil(timeDiff / (1000 * 3600 * 24));
      
      setFormData(prev => ({
        ...prev,
        numberOfDaysLeft: daysDiff.toString()
      }));
    }
  }, [formData.dateOfLoss]);

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleFileUpload = (field: keyof AccidentFormData, files: FileList | null) => {
    if (files) {
      const fileArray = Array.from(files);
      setFormData(prev => ({
        ...prev,
        [field]: fileArray
      }));
    }
  };

  const handleGoBack = () => {
    navigate('/teamleader/incidentReportingEdit');
  };

  const handleNext = () => {
    // Navigate to next form or submit
    navigate('/teamleader/accidentForm-second'); // Adjust path as needed
  };

  function handleMultipleFileUpload(arg0: string, files: FileList | null): void {
    throw new Error('Function not implemented.');
  }

  return (
    <div className="min-h-screen">
      {/* Go Back Button */}
      <div className="flex items-center mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2'
          size="sm"
          onClick={() => navigate('/teamleader/incidentReporting-edit')}
          >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
            Go Back
          </Button>
        </div>

      {/* Main Content */}
      <div className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8 p-2 xs:p-3 sm:p-4 md:p-6">
        <h1 className="text-base xs:text-lg sm:text-xl md:text-xl lg:text-3xl font-bold text-gray-800 mb-4 xs:mb-5 sm:mb-6 md:mb-8">Incident Details</h1>

        <form className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8">
          {/* Date and Time Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
            <div className="relative">
              <Input
                type="date"
                id="dateOfLoss"
                name="dateOfLoss"
                value={formData.dateOfLoss}
                onChange={handleInputChange}
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                required
              />
              <Label htmlFor="dateOfLoss" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Date of Loss*
              </Label>
            </div>
            <div className="relative">
              <Input
                type="time"
                id="timeOfLoss"
                name="timeOfLoss"
                value={formData.timeOfLoss}
                onChange={handleInputChange}
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                required
              />
              <Label htmlFor="timeOfLoss" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Time of Loss*
              </Label>
            </div>
          </div>

          {/* Days Left and State Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
            <div className="relative">
              <Input
                type="text"
                id="numberOfDaysLeft"
                name="numberOfDaysLeft"
                value={formData.numberOfDaysLeft}
                readOnly
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent bg-gray-100 cursor-not-allowed"
              />
              <Label htmlFor="numberOfDaysLeft" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Number of Days Left
              </Label>
            </div>
            <div className="relative">
              <select
                id="state"
                name="state"
                value={formData.state}
                onChange={handleInputChange}
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent appearance-none"
              >
                <option value="Victoria">Victoria</option>
                <option value="NSW">New South Wales</option>
                <option value="Queensland">Queensland</option>
                <option value="South Australia">South Australia</option>
                <option value="Western Australia">Western Australia</option>
                <option value="Tasmania">Tasmania</option>
                <option value="Northern Territory">Northern Territory</option>
                <option value="ACT">Australian Capital Territory</option>
              </select>
              <Label htmlFor="state" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                State*
              </Label>
            </div>
          </div>

          {/* Incident Location */}
          <div className="relative">
            <Input
              type="text"
              id="incidentLocation"
              name="incidentLocation"
              value={formData.incidentLocation}
              onChange={handleInputChange}
              placeholder="25, Yellowbox dr, Melbourne , 3064"
              className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
              required
            />
            <Label htmlFor="incidentLocation" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
              Incident Location*
            </Label>
          </div>

          {/* Incident Details */}
          <div className="relative">
            <textarea
              id="incidentDetails"
              name="incidentDetails"
              value={formData.incidentDetails}
              onChange={handleInputChange}
              placeholder="I was reversing out of the factory, and I accidently reversed into our work van parked on the street."
              rows={4}
              className="w-full min-h-[80px] xs:min-h-[100px] sm:min-h-[120px] border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-2 sm:py-3 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent resize-none"
              required
            />
            <Label htmlFor="incidentDetails" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
              Incident Details*
            </Label>
          </div>

          {/* Incident Type and Road Surface Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
            <div className="relative">
              <Input
                type="text"
                id="incidentType"
                name="incidentType"
                value={formData.incidentType}
                onChange={handleInputChange}
                placeholder="Multi Vehicle Accident"
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                required
              />
              <Label htmlFor="incidentType" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Incident Type*
              </Label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="roadSurface"
                name="roadSurface"
                value={formData.roadSurface}
                onChange={handleInputChange}
                placeholder="Sealed"
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                required
              />
              <Label htmlFor="roadSurface" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Road Surface*
              </Label>
            </div>
          </div>

          {/* Weather and Injuries Row */}
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
            <div className="relative">
              <Input
                type="text"
                id="weather"
                name="weather"
                value={formData.weather}
                onChange={handleInputChange}
                placeholder="Dry"
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                required
              />
              <Label htmlFor="weather" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Weather*
              </Label>
            </div>
            <div className="relative">
              <Input
                type="text"
                id="injuries"
                name="injuries"
                value={formData.injuries}
                onChange={handleInputChange}
                placeholder="Type Here..."
                className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
              />
              <Label htmlFor="injuries" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Injuries
              </Label>
            </div>
          </div>

          {/* Witness Details */}
          <div className="relative">
            <textarea
              id="witnessDetails"
              name="witnessDetails"
              value={formData.witnessDetails}
              onChange={handleInputChange}
              placeholder="Type Here..."
              rows={3}
              className="w-full min-h-[60px] xs:min-h-[80px] sm:min-h-[100px] border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-2 sm:py-3 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent resize-none"
            />
            <Label htmlFor="witnessDetails" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
              Witness Details
            </Label>
          </div>

          <div className="mt-4 xs:mt-5 sm:mt-6 md:mt-8">
            <h2 className="text-base xs:text-lg sm:text-xl md:text-xl lg:text-2xl font-bold text-gray-800 mb-3 xs:mb-4 sm:mb-5 md:mb-6">Rental Vehicle Damage Details</h2>
            <h3 className="text-sm xs:text-base sm:text-lg md:text-lg font-semibold text-gray-700 mb-3 xs:mb-4 sm:mb-5 md:mb-6">Upload Images</h3>

            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
              {/* Interior Images */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Interior Images
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('interiorImages', e.target.files)}
                    className="hidden"
                    id="interior-images"
                  />
                  <label htmlFor="interior-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-base md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Exterior Images */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Exterior Images
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('exteriorImages', e.target.files)}
                    className="hidden"
                    id="exterior-images"
                  />
                  <label htmlFor="exterior-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-base md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Left Side Doors */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Left Side Doors
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('leftSideImages', e.target.files)}
                    className="hidden"
                    id="left-side-images"
                  />
                  <label htmlFor="left-side-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-base md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Right Side Doors */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Right Side Doors
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('rightSideImages', e.target.files)}
                    className="hidden"
                    id="right-side-images"
                  />
                  <label htmlFor="right-side-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-base md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Front Side Images */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Front Side Images
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('frontSideImages', e.target.files)}
                    className="hidden"
                    id="front-side-images"
                  />
                  <label htmlFor="front-side-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-base md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Back Side Images */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Back Side Images
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('backSideImages', e.target.files)}
                    className="hidden"
                    id="back-side-images"
                  />
                  <label htmlFor="back-side-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-base md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Side Mirrors */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Side Mirrors
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('sideMirrorImages', e.target.files)}
                    className="hidden"
                    id="side-mirror-images"
                  />
                  <label htmlFor="side-mirror-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-base md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>

              {/* Vehicle Body */}
              <div className="relative">
                <label className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Vehicle Body
                </label>
                <div className="border border-gray-500 rounded-lg p-2 xs:p-3 sm:p-4 text-center hover:border-red-500 transition-colors">
                  <input
                    type="file"
                    multiple
                    accept="image/*"
                    onChange={(e) => handleMultipleFileUpload('vehicleBodyImages', e.target.files)}
                    className="hidden"
                    id="vehicle-body-images"
                  />
                  <label htmlFor="vehicle-body-images" className="cursor-pointer text-gray-500 text-xs xs:text-sm sm:text-base md:text-sm">
                    + Add Photo
                  </label>
                </div>
              </div>
            </div>

            {/* Damage Severity */}
            <div className="relative mt-4 xs:mt-5 sm:mt-6 md:mt-8">
              <textarea
                id="damageSeverity"
                name="damageSeverity"
                value={formData.damageSeverity}
                onChange={handleInputChange}
                placeholder="Type Here..."
                rows={3}
                className="w-full min-h-[60px] xs:min-h-[80px] sm:min-h-[100px] border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-2 sm:py-3 text-xs xs:text-sm sm:text-base md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent resize-none"
              />
              <Label htmlFor="damageSeverity" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                Damage Severity
              </Label>
            </div>
          </div>

          {/* Next Button */}
          <div className="flex justify-end mt-4 xs:mt-5 sm:mt-6 md:mt-8">
            <Button
              type="button"
              onClick={handleNext}
              className="bg-[#330101] text-white px-4 xs:px-6 sm:px-8 md:px-10 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm rounded transition-colors"
            >
              Next
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};