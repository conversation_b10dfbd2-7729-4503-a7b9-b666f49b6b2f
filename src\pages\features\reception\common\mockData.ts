import { Booking, ReservationManagement, RentalData, Interaction, IncidentBooking, Reservation, RentalDetails, Vehicle, ChangeRequest, InvoiceItem, AssignVehicleFormData, ImageCategory } from '../type/reception-type';

export const bookingsData: Booking[] = [
  {
    id: '#0034',
    customerName: '<PERSON>',
    customerPhone: '**********',
    obligationNo: '111237474',
    penaltyAmount: 500.0,
    dueDate: '10-07-2025',
    vehicle: 'PAC 200',
    vehicleClass: 'Sedan',
    offenseDate: '20-06-2025',
    offenseTime: '14:30',
    type: 'Corporate',
    license: '1234590',
    expireDate: '10-07-2025',
    reservationCount: '14',
    status: 'Active',
    frontLicenseImage: '/assets/frontView1.png',
    backLicenseImage: '/assets/backView1.png'
  },
  {
    id: '#0025',
    customerName: '<PERSON>',
    customerPhone: '**********',
    obligationNo: '122375521',
    penaltyAmount: 600.0,
    dueDate: '12-05-2025',
    vehicle: 'ABC 550',
    vehicleClass: 'SUV',
    offenseDate: '20-04-2025',
    offenseTime: '10:30',
    type: 'Corporate',
    license: '1224008',
    expireDate: '10-07-2026',
    reservationCount: '2',
    status: 'Active',
    frontLicenseImage: '/assets/frontView1.png',
    backLicenseImage: '/assets/backView1.png'
  },
  {
    id: '#0015',
    customerName: 'Alice Johnson',
    customerPhone: '**********',
    obligationNo: '123456789',
    penaltyAmount: 400.0,
    dueDate: '11-02-2025',
    vehicle: 'QBV 233',
    vehicleClass: 'Sedan',
    offenseDate: '20-01-2025',
    offenseTime: '12:05',
    type: 'Corporate',
    license: '1224008',
    expireDate: '10-07-2026',
    reservationCount: '2',
    status: 'Active',
    frontLicenseImage: '/assets/frontView1.png',
    backLicenseImage: '/assets/backView1.png'
  }
];


export const reservationData: ReservationManagement[] = [
  {
    id: 'R0035',
    rentalId: 'R0035',
    pickupDate: '18-07-2025',
    pickupTime: '11:30 AM',
    returnDate: '20-07-2025',
    returnTime: '02:30 PM',
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    branch: 'Sydney',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Economy Plus Car',
      category: 'Passenger',
      features: ['Air Conditioning', 'Bluetooth', '4 Doors'],
      doors: '4',
      vehicleId: 'AWZ 802',
      price: '$650.00',
      priceNote: 'Includes taxes and fees'
    },
    protections: [
      { name: 'Collision Damage Waiver', price: '$50.00' },
      { name: 'Theft Protection', price: '$20.00' }
    ],
    equipment: [
      { name: 'Child Seat', price: '$36.36' },
      { name: 'GPS', price: '$15.00' }
    ],
    customer: {
      cooperate: 'Yes',
      walkIn: 'No',
      firstName: 'John',
      lastName: 'Smith',
      email: '<EMAIL>',
      phone: '0411 111 111',
      address: '2185 Hume Hwy',
      postcode: '2091',
      country: 'Australia',
      birthday: '02/01/1960'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '12345678',
      issueDate: '20/04/2024',
      expireDate: '20/04/2028',
      country: 'Australia'
    },
    emergency: {
      name: 'Rose Perera',
      number: '0412 456 789'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: '38.18',rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: '500.00', days: '' },
      insurance: { description: 'Insurance', amount: '13.64', days: '' },
      childSeat: { description: 'Child Seat', amount: '36.36',rate: 'AUD 38.18 Per Day', days: '1 Day' },
      subtotal: '$588.18',
      gst: '$58.82',
      discount: '$0.00',
      total: '$647.00',
      securityDeposit: '$500.00',
      amountDue: '$147.00'
    },
    totalPrice: '$650.00',
    totalRevenue: '$650.00',
    totalRefunded: '$0.00',
    outstandingBalance: '$550.00',
    status: 'Open',
    loanVehicle: 'No'
  },
  {
    id: 'R0034',
    rentalId: 'R0034',
    pickupDate: '17-07-2025',
    pickupTime: '11:30 AM',
    returnDate: '18-07-2025',
    returnTime: '02:30 PM',
    pickupLocation: 'Sydney Downtown',
    returnLocation: 'Sydney Downtown',
    branch: 'Sydney',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Economy Plus Car',
      category: 'Passenger',
      features: ['Air Conditioning', 'Bluetooth', '4 Doors'],
      doors: '4',
      vehicleId: 'QCB 456',
      price: '$592.00',
      priceNote: 'Includes taxes and fees'
    },
    protections: [
      { name: 'Collision Damage Waiver', price: '$50.00' },
      { name: 'Theft Protection', price: '$20.00' }
    ],
    equipment: [
      { name: 'Child Seat', price: '$36.36' },
      { name: 'GPS', price: '$15.00' }
    ],
    customer: {
      cooperate: 'No',
      walkIn: 'No',
      firstName: 'Julia',
      lastName: 'Fernando',
      email: '<EMAIL>',
      phone: '0422 222 222',
      address: '123 Galle Rd',
      postcode: '2092',
      country: 'Australia',
      birthday: '15/03/1975'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '87654321',
      issueDate: '10/05/2023',
      expireDate: '10/05/2027',
      country: 'Australia'
    },
    emergency: {
      name: 'Michael Fernando',
      number: '0423 789 123'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: '38.18', rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: '500.00', days: '' },
      insurance: { description: 'Insurance', amount: '13.64', days: '' },
      childSeat: { description: 'Child Seat', amount: '36.36', rate: 'AUD 36.36 Per Day', days: '1 Day' },
      subtotal: '$588.18',
      gst: '$58.82',
      discount: '$0.00',
      total: '$647.00',
      securityDeposit: '$500.00',
      amountDue: '$147.00'
    },
    totalPrice: '$592.00',
    totalRevenue: '$592.00',
    totalRefunded: '$0.00',
    outstandingBalance: '$492.00',
    status: 'Rental',
    loanVehicle: 'No'
  },
  {
    id: 'R0033',
    rentalId: 'R0033',
    pickupDate: '15-07-2025',
    pickupTime: '11:30 AM',
    returnDate: '17-07-2025',
    returnTime: '02:30 PM',
    pickupLocation: 'Sydney Airport',
    returnLocation: 'Sydney Airport',
    branch: 'Sydney',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Economy Plus Car',
      category: 'Passenger',
      features: ['Air Conditioning', 'Bluetooth', '4 Doors'],
      doors: '4',
      vehicleId: 'QOB 115',
      price: '$502.00',
      priceNote: 'Includes taxes and fees'
    },
    protections: [
      { name: 'Collision Damage Waiver', price: '$50.00' },
      { name: 'Theft Protection', price: '$20.00' }
    ],
    equipment: [
      { name: 'Child Seat', price: '$36.36' },
      { name: 'GPS', price: '$15.00' }
    ],
    customer: {
      cooperate: 'Yes',
      walkIn: 'No',
      firstName: 'Thisara',
      lastName: 'Perera',
      email: '<EMAIL>',
      phone: '0433 333 333',
      address: '456 Kandy Rd',
      postcode: '2093',
      country: 'Australia',
      birthday: '22/07/1980'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '11223344',
      issueDate: '15/06/2022',
      expireDate: '15/06/2026',
      country: 'Australia'
    },
    emergency: {
      name: 'Samantha Perera',
      number: '0434 123 456'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: '38.18', rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: '500.00', days: '' },
      insurance: { description: 'Insurance', amount: '13.64', days: '' },
      childSeat: { description: 'Child Seat', amount: '36.36', rate: 'AUD 36.36 Per Day', days: '1 Day' },
      subtotal: '$588.18',
      gst: '$58.82',
      discount: '$0.00',
      total: '$647.00',
      securityDeposit: '$500.00',
      amountDue: '$147.00'
    },
    totalPrice: '$502.00',
    totalRevenue: '$502.00',
    totalRefunded: '$0.00',
    outstandingBalance: '$402.00',
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: 'R0026',
    rentalId: 'R0026',
    pickupDate: '15-06-2025',
    pickupTime: '11:30 AM',
    returnDate: '14-06-2025',
    returnTime: '02:30 PM',
    pickupLocation: 'Sydney Downtown',
    returnLocation: 'Sydney Downtown',
    branch: 'Sydney',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Economy Plus Car',
      category: 'Passenger',
      features: ['Air Conditioning', 'Bluetooth', '4 Doors'],
      doors: '4',
      vehicleId: 'PCB 456',
      price: '$592.00',
      priceNote: 'Includes taxes and fees'
    },
    protections: [
      { name: 'Collision Damage Waiver', price: '$50.00' },
      { name: 'Theft Protection', price: '$20.00' }
    ],
    equipment: [
      { name: 'Child Seat', price: '$36.36' },
      { name: 'GPS', price: '$15.00' }
    ],
    customer: {
      cooperate: 'No',
      walkIn: 'No',
      firstName: 'Davidson',
      lastName: 'Thomson',
      email: '<EMAIL>',
      phone: '0444 444 444',
      address: '789 Bondi Rd',
      postcode: '2094',
      country: 'Australia',
      birthday: '10/09/1965'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '55667788',
      issueDate: '25/07/2021',
      expireDate: '25/07/2025',
      country: 'Australia'
    },
    emergency: {
      name: 'Emily Thomson',
      number: '0445 789 123'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: '38.18', rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: '500.00', days: '' },
      insurance: { description: 'Insurance', amount: '13.64', days: '' },
      childSeat: { description: 'Child Seat', amount: '36.36', rate: 'AUD 36.36 Per Day', days: '1 Day' },
      subtotal: '$588.18',
      gst: '$58.82',
      discount: '$0.00',
      total: '$647.00',
      securityDeposit: '$500.00',
      amountDue: '$147.00'
    },
    totalPrice: '$592.00',
    totalRevenue: '$592.00',
    totalRefunded: '$0.00',
    outstandingBalance: '$492.00',
    status: 'Rental',
    loanVehicle: 'Yes'
  },
  {
    id: 'R0009',
    rentalId: 'R0009',
    pickupDate: '02-03-2025',
    pickupTime: '06:16 AM',
    returnDate: '10-03-2025',
    returnTime: '04:30 AM',
    pickupLocation: 'Melbourne Airport',
    returnLocation: 'Melbourne Airport',
    branch: 'Melbourne',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Economy Premium',
      category: 'Passenger',
      features: ['Air Conditioning', 'Bluetooth', '4 Doors'],
      doors: '4',
      vehicleId: 'Isuzu Nhr 45 150 - 2AC5GR',
      price: '$550.00',
      priceNote: 'Includes taxes and fees'
    },
    protections: [
      { name: 'Collision Damage Waiver', price: '$50.00' },
      { name: 'Theft Protection', price: '$20.00' }
    ],
    equipment: [
      { name: 'Child Seat', price: '$36.36' },
      { name: 'GPS', price: '$15.00' }
    ],
    customer: {
      cooperate: 'Yes',
      walkIn: 'No',
      firstName: 'Igor',
      lastName: 'Peets',
      email: '<EMAIL>',
      phone: '0455 555 555',
      address: '101 Collins St',
      postcode: '2095',
      country: 'Australia',
      birthday: '05/11/1970'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '99887766',
      issueDate: '30/08/2020',
      expireDate: '30/08/2024',
      country: 'Australia'
    },
    emergency: {
      name: 'Anna Peets',
      number: '0456 123 456'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: '38.18', rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: '500.00', days: '' },
      insurance: { description: 'Insurance', amount: '13.64', days: '' },
      childSeat: { description: 'Child Seat', amount: '36.36', rate: 'AUD 36.36 Per Day', days: '1 Day' },
      subtotal: '$588.18',
      gst: '$58.82',
      discount: '$0.00',
      total: '$647.00',
      securityDeposit: '$500.00',
      amountDue: '$147.00'
    },
    totalPrice: '$550.00',
    totalRevenue: '$550.00',
    totalRefunded: '$0.00',
    outstandingBalance: '$450.00',
    status: 'Open',
    loanVehicle: 'No'
  },
  {
    id: 'R0015',
    rentalId: 'R0015',
    pickupDate: '11-01-2025',
    pickupTime: '07:30 AM',
    returnDate: '24-01-2025',
    returnTime: '11:30 AM',
    pickupLocation: 'Sydney Airport',
    returnLocation: 'Sydney Airport',
    branch: 'Sydney',
    reservationType: 'Short Term',
    vehicle: {
      class: 'SUV 7 Seats Premium',
      category: 'Commercial',
      features: ['Air Conditioning', 'Bluetooth', '7 Seats'],
      doors: '4',
      vehicleId: 'Toyota Hiace Commuter - 2BH8BD',
      price: '$592.00',
      priceNote: 'Includes taxes and fees'
    },
    protections: [
      { name: 'Collision Damage Waiver', price: '$50.00' },
      { name: 'Theft Protection', price: '$20.00' }
    ],
    equipment: [
      { name: 'Child Seat', price: '$36.36' },
      { name: 'GPS', price: '$15.00' }
    ],
    customer: {
      cooperate: 'No',
      walkIn: 'No',
      firstName: 'Alyssa',
      lastName: 'Dorschad',
      email: '<EMAIL>',
      phone: '0466 666 666',
      address: '202 George St',
      postcode: '2096',
      country: 'Australia',
      birthday: '20/12/1985'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '44332211',
      issueDate: '12/09/2019',
      expireDate: '12/09/2023',
      country: 'Australia'
    },
    emergency: {
      name: 'James Dorschad',
      number: '0467 789 123'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: '38.18', rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: '500.00', days: '' },
      insurance: { description: 'Insurance', amount: '13.64', days: '' },
      childSeat: { description: 'Child Seat', amount: '36.36', rate: 'AUD 36.36 Per Day', days: '1 Day' },
      subtotal: '$588.18',
      gst: '$58.82',
      discount: '$0.00',
      total: '$647.00',
      securityDeposit: '$500.00',
      amountDue: '$147.00'
    },
    totalPrice: '$592.00',
    totalRevenue: '$592.00',
    totalRefunded: '$0.00',
    outstandingBalance: '$492.00',
    status: 'Completed',
    loanVehicle: 'No'
  },
  {
    id: 'R0017',
    rentalId: 'R0017',
    pickupDate: '20-12-2024',
    pickupTime: '09:16 PM',
    returnDate: '24-12-2024',
    returnTime: '09:16 PM',
    pickupLocation: 'Sydney Downtown',
    returnLocation: 'Sydney Downtown',
    branch: 'Sydney',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Mini Bus 12 Seats',
      category: 'Passenger',
      features: ['Air Conditioning', 'Bluetooth', '12 Seats'],
      doors: '4',
      vehicleId: 'DEF 456',
      price: '$548.00',
      priceNote: 'Includes taxes and fees'
    },
    protections: [
      { name: 'Collision Damage Waiver', price: '$50.00' },
      { name: 'Theft Protection', price: '$20.00' }
    ],
    equipment: [
      { name: 'Child Seat', price: '$36.36' },
      { name: 'GPS', price: '$15.00' }
    ],
    customer: {
      cooperate: 'Yes',
      walkIn: 'No',
      firstName: 'Jaymar',
      lastName: 'Elvin',
      email: '<EMAIL>',
      phone: '0477 777 777',
      address: '303 Pitt St',
      postcode: '2097',
      country: 'Australia',
      birthday: '15/02/1990'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '66778899',
      issueDate: '18/10/2021',
      expireDate: '18/10/2025',
      country: 'Australia'
    },
    emergency: {
      name: 'Sarah Elvin',
      number: '0478 123 456'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: '38.18', rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: '500.00', days: '' },
      insurance: { description: 'Insurance', amount: '13.64', days: '' },
      childSeat: { description: 'Child Seat', amount: '36.36', rate: 'AUD 36.36 Per Day', days: '1 Day' },
      subtotal: '$588.18',
      gst: '$58.82',
      discount: '$0.00',
      total: '$647.00',
      securityDeposit: '$500.00',
      amountDue: '$147.00'
    },
    totalPrice: '$548.00',
    totalRevenue: '$548.00',
    totalRefunded: '$0.00',
    outstandingBalance: '$448.00',
    status: 'Cancelled',
    loanVehicle: 'No'
  },
  {
    id: 'R0022',
    rentalId: 'R0022',
    pickupDate: '11-10-2024',
    pickupTime: '09:16 PM',
    returnDate: '14-11-2024',
    returnTime: '09:16 PM',
    pickupLocation: 'Sydney Airport',
    returnLocation: 'Sydney Airport',
    branch: 'Sydney',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Economy Plus',
      category: 'Passenger',
      features: ['Air Conditioning', 'Bluetooth', '4 Doors'],
      doors: '4',
      vehicleId: 'GHI 987',
      price: '$548.00',
      priceNote: 'Includes taxes and fees'
    },
    protections: [
      { name: 'Collision Damage Waiver', price: '$50.00' },
      { name: 'Theft Protection', price: '$20.00' }
    ],
    equipment: [
      { name: 'Child Seat', price: '$36.36' },
      { name: 'GPS', price: '$15.00' }
    ],
    customer: {
      cooperate: 'No',
      walkIn: 'No',
      firstName: 'Juliet',
      lastName: 'Semathrna',
      email: '<EMAIL>',
      phone: '0488 888 888',
      address: '404 Oxford St',
      postcode: '2098',
      country: 'Australia',
      birthday: '25/04/1982'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '22334455',
      issueDate: '22/11/2020',
      expireDate: '22/11/2024',
      country: 'Australia'
    },
    emergency: {
      name: 'Mark Semathrna',
      number: '0489 123 456'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: '38.18', rate: 'AUD 38.18 Per Day', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: '500.00', days: '' },
      insurance: { description: 'Insurance', amount: '13.64', days: '' },
      childSeat: { description: 'Child Seat', amount: '36.36', rate: 'AUD 36.36 Per Day', days: '1 Day' },
      subtotal: '$588.18',
      gst: '$58.82',
      discount: '$0.00',
      total: '$647.00',
      securityDeposit: '$500.00',
      amountDue: '$147.00'
    },
    totalPrice: '$548.00',
    totalRevenue: '$548.00',
    totalRefunded: '$0.00',
    outstandingBalance: '$448.00',
    status: 'Open',
    loanVehicle: 'No'
  }
];
export const invoiceItems: InvoiceItem[] = [
  { description: 'Eco Plus Car', rate: 'AUD 38.18 Per Day', days: '1 Day', amount: '38.18' },
  { description: 'Bond Compulsory - 500', rate: '', days: '', amount: '500.00' },
  { description: 'Insurance', rate: '', days: '', amount: '13.64' },
  { description: 'Child Seat', rate: 'AUD 36.36 Per Day', days: '1 Day', amount: '36.36' },
];

export const incidentBookingsData: IncidentBooking[] = [
  {
    id: '#0036',
    customerName: 'Kasun Perera',
    vehicle: 'PCR 455',
    pickupDate: '09/07/2025',
    pickupTime: '10:30',
    returnDate: '10/07/2025',
    returnTime: '11:30',
    incidentDate: '10/07/2025',
    reportType: 'Breakdown',
    vehicleReplaced: 'Yes',
    replacedVehicle: 'ACD 300',
    replacedDate: '10/07/2025',
    insuranceEx: 'No',
    paymentDue: '$ 00.00',
    status: 'Accept'
  },
  {
    id: '#0034',
    customerName: 'Sheno Dias',
    vehicle: 'XBB 211',
    pickupDate: '08/07/2025',
    pickupTime: '12:30',
    returnDate: '10/07/2025',
    returnTime: '10:00',
    incidentDate: '10/07/2025',
    reportType: 'Accident',
    vehicleReplaced: 'No',
    replacedVehicle: '-',
    replacedDate: '-',
    insuranceEx: 'Yes',
    paymentDue: '$ 250.00',
    status: '-'
  },
  {
    id: '#0033',
    customerName: 'Shenon Vas',
    vehicle: 'PPR 255',
    pickupDate: '07/07/2025',
    pickupTime: '09:00',
    returnDate: '09/07/2025',
    returnTime: '12:00',
    incidentDate: '09/07/2025',
    reportType: 'General Maintenance',
    vehicleReplaced: 'Yes',
    replacedVehicle: 'KAB 344',
    replacedDate: '09/07/2025',
    insuranceEx: 'Yes',
    paymentDue: '$ 100.00',
    status: '-'
  },
  {
    id: '#0032',
    customerName: 'Dunno David',
    vehicle: 'NAA 455',
    pickupDate: '07/07/2025',
    pickupTime: '10:30',
    returnDate: '08/07/2025',
    returnTime: '12:30',
    incidentDate: '08/07/2025',
    reportType: 'Damage',
    vehicleReplaced: 'No',
    replacedVehicle: '-',
    replacedDate: '-',
    insuranceEx: 'No',
    paymentDue: '$ 00.00',
    status: 'Decline'
  },
  {
    id: '#0031',
    customerName: 'Stephan Peter',
    vehicle: 'PCR 455',
    pickupDate: '07/07/2025',
    pickupTime: '10:00',
    returnDate: '07/07/2025',
    returnTime: '16:00',
    incidentDate: '07/07/2025',
    reportType: 'Accident',
    vehicleReplaced: 'Yes',
    replacedVehicle: 'ACD 300',
    replacedDate: '07/07/2025',
    insuranceEx: 'Yes',
    paymentDue: '$ 00.00',
    status: 'InProgress'
  }
];

export const rentalsData: RentalData[] = [
  {
    id: 'RENT001',
    customerName: 'Suresh Perera',
    phoneNumber: '0421 475 120',
    vehicle: 'Atom - PCR 455'
  },
  {
    id: 'RENT002',
    customerName: 'Kamal Silva',
    phoneNumber: '0711 234 567',
    vehicle: 'Honda - ABC 123'
  },
  {
    id: 'RENT003',
    customerName: 'Nimal Fernando',
    phoneNumber: '0777 890 123',
    vehicle: 'Toyota - XYZ 789'
  }
];

export const conditionsOptions: string[] = ['', 'Light Vehicles', 'Heavy Vehicles', 'Motorcycles'];
export const countryOptions: string[] = ['', 'Australia', 'New Zealand', 'United Kingdom', 'United States'];
export const phoneOptions: string[] = ['', 'Land', 'Mobile'];
export const discountOptions: string[] = ['', 'Percentage', 'Amount'];

export const generalMaintenance = [
  {
    id: 'GM0001',
    vehicleClass: 'Economy',
    vehicle: 'Toyota Corolla - T71 230',
    maintenanceType: 'General Maintenance every 5000 km',
    nextService: '15/06/2025',
  },
  {
    id: 'GM0002',
    vehicleClass: 'Mini Bus',
    vehicle: 'Toyota Hiace - AZR 207',
    maintenanceType: 'General Maintenance every 2500 km',
    nextService: '20/06/2025',
  },
  {
    id: 'GM0003',
    vehicleClass: 'Economy Plus',
    vehicle: 'Honda Civic - QW5 P91',
    maintenanceType: 'General Maintenance every 5000 km',
    nextService: '25/06/2025',
  },
];

export const accident = [
  {
    id: '1',
    vehicleClass: 'Heavy Duty',
    vehicle: 'Isuzu - AFS 009',
    maintenanceType: 'Accident',
    incidentDate: '03/05/2025',
    status: 'Initial Review'
  },
  {
    id: '2',
    vehicleClass: 'Heavy Duty',
    vehicle: 'Isuzu - AAB 004',
    maintenanceType: 'Accident',
    incidentDate: '25/04/2025',
    status: 'Insurance Accepted'
  },
  {
    id: '3',
    vehicleClass: 'Economy',
    vehicle: 'Toyota - BCS 903',
    maintenanceType: 'Accident',
    incidentDate: '10/03/2025',
    status: 'Insurance Declined'
  }
];

export const breakdown = [
  {
    id: 'B001',
    vehicle: 'Toyota Corolla-T71 230',
    incident: 'Engine Failure',
    location: 'Colombo CBD',
    insuranceClaimed: 'Yes',
    insuranceCompany: 'AIA Insurance',
    claimedNumber: 'CL2025001'
  },
  {
    id: 'B002',
    vehicle: 'Toyota Hiace-AZR 207',
    incident: 'Transmission Issue',
    location: 'Galle Road',
    insuranceClaimed: 'No',
    insuranceCompany: '-',
    claimedNumber: '-'
  },
  {
    id: 'B003',
    vehicle: 'Honda Civic-QW5 P91',
    incident: 'Brake Failure',
    location: 'Kandy Road',
    insuranceClaimed: 'Yes',
    insuranceCompany: 'Ceylinco Insurance',
    claimedNumber: 'CL2025002'
  }
];

export const damage = [
  {
    id: 'D001',
    vehicle: 'Toyota Corolla-T71 230',
    damageType: 'Exterior Damage',
    severity: 'Minor',
    estimatedCost: 'Rs. 25,000',
    status: 'Pending Assessment'
  },
  {
    id: 'D002',
    vehicle: 'Toyota Hiace-AZR 207',
    damageType: 'Interior Damage',
    severity: 'Major',
    estimatedCost: 'Rs. 150,000',
    status: 'Under Repair'
  },
  {
    id: 'D003',
    vehicle: 'Honda Civic-QW5 P91',
    damageType: 'Mechanical Damage',
    severity: 'Severe',
    estimatedCost: 'Rs. 300,000',
    status: 'Completed'
  }
];

export const interactions: Interaction[] = [
  {
    id: 'INT0005',
    customerName: 'Chenul Thimoth',
    phoneNumber: '0412 699 433',
    mode: 'Email',
    dateTime: '07/05/2025 10:30',
    purpose: 'New Inquiry',
    status: 'In Progress',
    email: '<EMAIL>',
  },
  {
    id: 'INT0004',
    customerName: 'David Dunno',
    phoneNumber: '0412 002 544',
    mode: 'Call',
    dateTime: '07/05/2025 13:05',
    purpose: 'Existing Inquiry',
    status: 'Resolved',
    callType: 'Incoming',
    callDuration: 12,
  },
  {
    id: 'INT0003',
    customerName: 'Harry Samson',
    phoneNumber: '0417 691 782',
    mode: 'Walk-ins',
    dateTime: '06/05/2025 11:44',
    purpose: 'Service Reporting',
    status: 'Escalated',
  },
  {
    id: 'INT0002',
    customerName: 'James Peter',
    phoneNumber: '0423 850 117',
    mode: 'SMS',
    dateTime: '05/05/2025 10:24',
    purpose: 'Service Due',
    status: 'In Progress',
  },
  {
    id: 'INT0001',
    customerName: 'Timothy Hash',
    phoneNumber: '0421 458 774',
    mode: 'Call',
    dateTime: '05/05/2025 14:33',
    purpose: 'New Inquiry',
    status: 'In Progress',
    callType: 'Outgoing',
    callDuration: 8,
  },
];

export const interactionModes = [
  'Call',
  'Walk-ins',
  'Online Booking',
  'Customer App',
  'SMS',
  'Email',
  'Whatsapp',
  'Messenger',
  'Instagram'
];

export const purposes = [
  'Service Reporting',
  'New Inquiry',
  'Existing Inquiry',
  'Service Due',
  'Incident Reporting'
];

export const reportingTypes = [
  'Routine Service Completed Report',
  'Unexpected Repair Request',
  'Vehicle Condition Issue Report',
  'Service Delay Report',
  'Maintenance History Summary',
  'Vehicle Unavailable for Service Report',
  'Customer-Reported Service Complaint',
  'Missed Service Alert Report',
  'Other'
];

export const newInquiryTypes = [
  'Booking Process Inquiries',
  'Vehicle Availability Inquiry',
  'Vehicle Rental Inquiry',
  'Pricing Information Request',
  'Vehicle Specifications ',
  'Long-term Rental Inquiry',
  'Teams & Conditions Inquiry',
  'Additional Equipment',
  'Rent to Buy',
  'Corporate or Business Rental',
  'Location-Specific Inquiries',
  'Other'
];

export const existingInquiryTypes = [
  'Faults or Mechanical Issues',
  'Extending the Rental Term',
  'Early Termination of Rental',
  'Cancellations',
  'Billing or Payment Issues',
  'Accidents or Collisions',
  'Buy a Vehicle',
  'Damage Waivers and Liability',
  'Vehicle Return Process',
  'Fuel Issues',
  'Vehicle Swap or Replacement',
  'Billing & Payment',
  'Mileage and Usage Concerns',
  'Quote Clarification',
  'Terms and Conditions Query',
  'Payment Options Inquiry',
  'Contract Amendment Request',
  'Other'
];

export const serviceDueTypes = [
  'Routine Maintenance Due',
  'Tire Rotation/Replacement Due',
  'Brake Inspection Due',
  'Battery Check/Replacement Due',
  'Engine Service Due',
  'Emission Test Due',
  'AC or Cooling System Check Due',
  'Insurance & Registration Renewal',
  'Cleaning & Sanitization Due',
  'Transmission/Fluid Change Due',
  'Oil Change Reminder',
  'Brake System Check',
  'Engine Diagnostic Due',
  'Safety Inspection Required',
  'Other'
];

export const incidentReportTypes = [
  'Vehicle Accident Report',
  'Theft or Vandalism Report',
  'Mechanical Breakdown Report',
  'Customer Injury Report',
  'Property Damage Report',
  'Traffic Violation Report',
  'Emergency Response Report',
  'Insurance Claim Report'
];

export const statuses = ['Resolved', 'In Progress', 'Escalated'];

export const staffMembers = ['Admin', 'TeamLeader', 'Mechanic'];

export const reservationsData: Reservation[] = [
  { rego: 'SQA 758', rentalId: '#0145', pickupDate: new Date('2025-05-14T14:45:00'), returnDate: new Date('2025-05-20T14:45:00') },
  { rego: 'ABC 123', rentalId: '#0146', pickupDate: new Date('2025-06-01T09:00:00'), returnDate: new Date('2025-06-07T09:00:00') },
  { rego: 'XYZ 789', rentalId: '#0147', pickupDate: new Date('2025-07-01T12:00:00'), returnDate: new Date('2025-07-08T12:00:00') },
];

export const rentalDetailsData: RentalDetails[] = [
  { phoneNumber: '0421 800 566', email: '<EMAIL>', customerName: 'John Doe', agreementNo: 'AGR001' },
  { phoneNumber: '0421 322 400', email: '<EMAIL>', customerName: 'Peter Smith', agreementNo: 'AGR002' },
];
/* export const mockVehicles: Vehicle[] = [
  { id: '#V001', rego: 'PXT 983', vin: '1HGCM82633A123456', class: 'Economy', type: 'Sedan', currentRenter: 'John Doe', lastMileage: 15000, fuel: 'Full', status: 'Operational', availability: 'Available' },
  { id: '#V002', rego: 'PVG 430', vin: '2HGFG12658H123457', class: 'Economy Plus', type: 'SUV', currentRenter: 'Jane Smith', lastMileage: 23000, fuel: 'Half', status: 'Operational', availability: 'Rented' },
  { id: '#V003', rego: 'XYZ 123', vin: 'JHMCM56313C123458', class: 'Mid-size', type: 'Van', currentRenter: '', lastMileage: 18000, fuel: 'Full', status: 'Under Maintenance', availability: 'Unavailable' },
  { id: '#V004', rego: 'ABC 456', vin: '1HGCM82633A123459', class: 'SUV', type: 'SUV', currentRenter: '', lastMileage: 12000, fuel: 'Full', status: 'Operational', availability: 'Available' },
  { id: '#V005', rego: 'LMN 789', vin: '2HGFG12658H123460', class: 'Luxury', type: 'Sedan', currentRenter: 'Alice Brown', lastMileage: 30000, fuel: 'Half', status: 'Operational', availability: 'Rented' },
]; */
export const mockChangeRequests: ChangeRequest[] = [
  {
    rentalId: '#0001',
    customerName: 'Chenul Thimod',
    customerPhone: '0423 850 117',
    vehicle: 'PXT 983',
    vehicleClass: 'Economy Plus',
    pickupDate: '28-05-2025',
    pickupTime: '9:16',
    returnDate: '28-06-2025',
    returnTime: '9:16',
    requestType: 'Extension of Duration',
    status: 'In-Progress',
    note: 'Because the picnic we went to was extended for two more days.',
  },
  {
    rentalId: '#0007',
    customerName: 'Shenon Fernando',
    customerPhone: '0423 850 447',
    vehicle: 'Isuzu Nnr 45 150 - 2BY15U',
    vehicleClass: 'Economy',
    pickupDate: '10-04-2025',
    pickupTime: '10:20',
    returnDate: '22-04-2025',
    returnTime: '10:20',
    requestType: 'Extension of Duration',
    status: 'Approved',
  },
  {
    rentalId: '#0014',
    customerName: 'Jessie Lappen',
    customerPhone: '0423 850 217',
    vehicle: 'PVG 430',
    vehicleClass: 'Economy',
    pickupDate: '02-04-2025',
    pickupTime: '10:10',
    returnDate: '03-04-2025',
    returnTime: '10:10',
    requestType: 'Extension of Duration',
    status: 'Approved',
  },
];

export const mockAssignVehicleFormData: AssignVehicleFormData[] = reservationData.map((reservation) => ({
  rentalId: reservation.rentalId,
  vehicle: reservation.vehicle,
  pickupLocation: reservation.pickupLocation,
  returnLocation: reservation.returnLocation,
  pickupDate: reservation.pickupDate,
  pickupTime: reservation.pickupTime,
  returnDate: reservation.returnDate,
  returnTime: reservation.returnTime,
  reservationType: reservation.reservationType,
  odometerAtPickup: '15000', // Default value, can be customized per vehicle
  includedDistance: '300km', // Default value, can be customized per reservation
  fuelLevelAtPickup: 'Full', // Default value
  fuelSameAsPickup: 'Yes', // Default value
  fuelLevelAtReturn: '', // Empty as it's filled upon return
  comment: `Vehicle inspected for rental ${reservation.rentalId}.`, // Dynamic comment
  paymentDue: reservation.summary.amountDue, // Reusing amountDue from summary
  excessCoverObtained: reservation.protections.some(p => p.name === 'Collision Damage Waiver') ? 'Yes' : 'No' // Based on protections
}));
export const mockImageCategories: ImageCategory[] = [
  { title: 'Interior Images', items: ['interior1.jpg'] },
  { title: 'Exterior Images', items: ['exterior1.jpg'] },
  { title: 'Left Side Doors', items: ['left_door1.jpg'] },
  { title: 'Right Side Doors', items: ['right_door1.jpg'] },
  { title: 'Front Side Images', items: ['front1.jpg'] },
  { title: 'Back Side Images', items: ['rear1.jpg'] },
  { title: 'Side Mirrors', items: ['mirror1.jpg'] },
  { title: 'Other', items: ['other1.jpg'] }
];

export const mockVehicles: string[] = reservationData.map(reservation => reservation.vehicle.vehicleId);