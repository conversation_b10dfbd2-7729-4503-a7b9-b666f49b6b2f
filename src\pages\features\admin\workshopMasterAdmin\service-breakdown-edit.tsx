import React, { useState } from 'react';
import { useNavigate, useParams } from 'react-router-dom';
import { ArrowLeft, ChevronDown } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Textarea } from '@/components/ui/textarea';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';
import { Badge } from '@/components/ui/badge';
import { useToast } from '@/components/ui/use-toast';

interface VehicleData {
  vehicleId: string;
  model: string;
  regNo: string;
  incidentDate: string;
  description: string;
  estimatedEndDate: string;
  actualEndDate: string;
  status: string;
  mechanic: string;
  branch: string;
  comment: string;
  // Accident-specific fields
  insuranceStatus?: string;
  insuranceClaimNumber?: string;
  make: string;
  fault: string;
  driver: string;
  otherParty: string;
  accidentDate: string;
  quote: string;
  rentalLoss: string;
  towingCharges: string;
  grandTotal: string;
  gst: string;
  phone: string;
  incidentState: string;
  // Maintenance-specific fields
  maintenanceType?: string;
  typeInterval?: string;
  odometerAtDueDate?: string;
  currentOdometer?: string;
  statusOfVehicle?: string;
  vehicleCurrentRenter?: string;
  vehicleCurrentLocation?: string;
}

interface WorkshopData {
  branch: string;
  mechanicAssigned: string[];
  comment: string;
}

interface ImageData {
  interiorImages: string[];
  exteriorImages: string[];
  leftSideDoors: string[];
  rightSideDoors: string[];
  frontSideImages: string[];
  backSideImages: string[];
  sideMirrors: string[];
  other: string[];
}

export function ServiceBreakdownEdit() {
  const navigate = useNavigate();
  const { vehicleId } = useParams<{ vehicleId: string }>();
  const { toast } = useToast();
  const [isLoading, setIsLoading] = useState(false);

  // Vehicle data state
  const [vehicleData, setVehicleData] = useState<VehicleData>({
    vehicleId: 'VID0012',
    model: 'Civic Hybrid Sedan',
    regNo: '1PX 1ZR',
    incidentDate: '21-02-2025',
    description: 'Engine failure during operation',
    estimatedEndDate: '2025-05-26',
    actualEndDate: '2025-05-31',
    status: 'Pending',
    mechanic: '',
    branch: 'Somerton',
    comment: '',
    insuranceStatus: '',
    insuranceClaimNumber: '',
    maintenanceType: '',
    typeInterval: '',
    odometerAtDueDate: '',
    currentOdometer: '',
    statusOfVehicle: '',
    vehicleCurrentRenter: '',
    vehicleCurrentLocation: '',
    make: 'Isuzu',
    fault: '',
    driver: '',
    otherParty: '',
    accidentDate: '',
    quote: '',
    rentalLoss: '',
    towingCharges: '',
    grandTotal: '',
    gst: '',
    phone: '',
    incidentState: ''
  });

  // Workshop details state
  const [workshopData, setWorkshopData] = useState<WorkshopData>({
    branch: 'Somerton',
    mechanicAssigned: ['John Doe', 'Mike Smith'],
    comment: 'Type here...',
  });

  // Image data state for Accident and Damage service types
  const [imageData, setImageData] = useState<ImageData>({
    interiorImages: ['image.jpg'],
    exteriorImages: ['image.jpg'],
    leftSideDoors: ['image.jpg'],
    rightSideDoors: ['image.jpg'],
    frontSideImages: ['image.jpg'],
    backSideImages: ['image.jpg'],
    sideMirrors: ['image.jpg'],
    other: ['image.jpg'],
  });

  // State for service type change
  const [changeServiceType, setChangeServiceType] = useState(false);
  const [serviceType, setServiceType] = useState('');

  // Handle vehicle data changes
  const handleVehicleDataChange = (field: keyof VehicleData, value: string) => {
    setVehicleData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle workshop data changes
  const handleInputChange = (field: keyof WorkshopData, value: string | string[]) => {
    setWorkshopData((prev) => ({
      ...prev,
      [field]: value,
    }));
  };

  // Handle mechanic assigned change
  const handleMechanicChange = (value: string) => {
    const updatedMechanics = workshopData.mechanicAssigned.includes(value)
      ? workshopData.mechanicAssigned.filter((m) => m !== value)
      : [...workshopData.mechanicAssigned, value];
    handleInputChange('mechanicAssigned', updatedMechanics);
  };

  // Handle image upload for Accident and Damage service types
  const handleImageUpload = (field: keyof ImageData, files: FileList | null) => {
    if (files && files.length > 0) {
      const validFiles = Array.from(files).filter(
        (file) => file.type.startsWith('image/') && file.size < 5 * 1024 * 1024
      );
      if (validFiles.length === 0) {
        toast({
          title: 'Error',
          description: 'Please upload valid images (max 5MB).',
          variant: 'destructive',
        });
        return;
      }
      const fileNames = validFiles.map((file) => file.name);
      setImageData((prev) => ({
        ...prev,
        [field]: fileNames,
      }));
    }
  };

  // Handle update submission
  const handleUpdate = async () => {
    setIsLoading(true);
    try {
      if (!vehicleData.status) {
        toast({
          title: "Error",
          description: "Please select a status.",
          variant: "destructive",
        });
        return;
      }
      if (new Date(vehicleData.actualEndDate) < new Date(vehicleData.estimatedEndDate)) {
        toast({
          title: "Error",
          description: "Actual end date cannot be before estimated end date.",
          variant: "destructive",
        });
        return;
      }

      const cleanedWorkshopData = {
        ...workshopData,
        comment: workshopData.comment === 'Type here...' ? '' : workshopData.comment,
      };
      const payload = {
        ...vehicleData,
        ...cleanedWorkshopData,
        serviceType,
        ...(serviceType === 'Accident' || serviceType === 'Damage' ? { imageData } : {}),
      };
      await fetch(`/api/vehicles/${vehicleId}`, {
        method: 'PUT',
        body: JSON.stringify(payload),
        headers: { 'Content-Type': 'application/json' },
      });
      toast({
        title: "Success",
        description: "Vehicle maintenance details updated successfully.",
      });
      navigate('/admin/workshopMasterAdmin/service-breakdown');
    } catch (error) {
      console.error('Failed to save:', error);
      toast({
        title: "Error",
        description: "Failed to update vehicle maintenance details. Please try again.",
        variant: "destructive",
      });
    } finally {
      setIsLoading(false);
    }
  };

  const statusOptions: string[] = ['', 'Pending', 'InProgress', 'Done'];
  const branchOptions: string[] = ['', 'Somerton'];
  const mechanicOptions: string[] = ['', 'John Doe', 'Mike Smith', 'Robert Brown'];
  const serviceTypeOptions: string[] = ['', 'General Maintenance', 'Accident', 'Damage'];
  const insuranceStatusOptions: string[] = ['', 'Initial Review', 'Accept', 'Decline'];
  const statusOfVehicleOptions: string[] = ['', 'Dirty', 'Cleaned'];
  const otherPartyOptions: string[] = ['', 'Renter', 'Third Party'];

  return (
    <div className="min-h-screen">
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <Button
          className="bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2"
          size="sm"
          onClick={() => navigate('/admin/workshopMasterAdmin/service-breakdown')}
        >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="flex items-center mb-4 sm:mb-6">
        <h3 className="text-xl sm:text-2xl font-medium text-earth-dark">Vehicle Details</h3>
      </div>

      {/* Vehicle Details Section */}
      <div className="bg-white rounded-lg mb-6">
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.vehicleId}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Vehicle ID
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.model}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Model
            </Label>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.regNo}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Reg No
            </Label>
          </div>
          {(!changeServiceType || (changeServiceType && serviceType !== 'General Maintenance' && serviceType !== 'Accident'  && serviceType !== 'Damage' )) && (
          <div className="relative">
            <Input
              type="text"
              value={vehicleData.incidentDate}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Incident Date
            </Label>
          </div>
          )}
        </div>

        <div className="grid grid-cols-1 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Textarea
              value={vehicleData.description}
              className="bg-gray-100 w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md opacity-70 cursor-not-allowed text-[10px] sm:text-xs md:text-sm lg:text-base h-20 resize-none"
              required
              disabled
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Description
            </Label>
          </div>
        </div>
      </div>

      {/* Service Type Change Section */}
      <div className="mb-6">
        <div className="flex items-center gap-4 mb-4">
          <label className="text-sm sm:text-base">Do You Want to Change Service Type?</label>
          <div className="flex items-center gap-4">
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="changeServiceType"
                checked={changeServiceType}
                onChange={() => setChangeServiceType(true)}
              /> Yes
            </label>
            <label className="flex items-center gap-2">
              <input
                type="radio"
                name="changeServiceType"
                checked={!changeServiceType}
                onChange={() => setChangeServiceType(false)}
              /> No
            </label>
          </div>
        </div>
        {changeServiceType && (
          <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-8">
            <div className="relative">
              <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Service Types</Label>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                    {serviceType || 'Select Service Type'}
                    <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent className="w-full">
                  {serviceTypeOptions.map((type) => (
                    <DropdownMenuItem
                      key={type}
                      onSelect={() => setServiceType(type)}
                      className={serviceType === type ? 'bg-amber-100 font-regular' : ''}
                    >
                      {type || 'Select Service Type'}
                    </DropdownMenuItem>
                  ))}
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        )}
        {changeServiceType && serviceType === 'General Maintenance' && (
          <div className="bg-white rounded-lg mb-6">
            <h3 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Maintenance Details</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
              <div className="relative">
                <Input
                  type="text"
                  value={vehicleData.maintenanceType}
                  onChange={(e) => handleVehicleDataChange('maintenanceType', e.target.value)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Maintenance Type
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={vehicleData.typeInterval}
                  onChange={(e) => handleVehicleDataChange('typeInterval', e.target.value)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Maintenance Type Interval
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
              <div className="relative">
                <Input
                  type="text"
                  value={vehicleData.odometerAtDueDate}
                  onChange={(e) => handleVehicleDataChange('odometerAtDueDate', e.target.value)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Odometer at Maintenance Due Date
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={vehicleData.currentOdometer}
                  onChange={(e) => handleVehicleDataChange('currentOdometer', e.target.value)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Current Odometer
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
              <div className="relative">
                <Input
                  type="text"
                  value={vehicleData.vehicleCurrentRenter}
                  onChange={(e) => handleVehicleDataChange('vehicleCurrentRenter', e.target.value)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Vehicle Current Renter
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={vehicleData.vehicleCurrentLocation}
                  onChange={(e) => handleVehicleDataChange('vehicleCurrentLocation', e.target.value)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Vehicle Current Location
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-1 gap-3 sm:gap-6 md:gap-8 mb-4">
              <div className="relative">
                <Textarea
                  value={vehicleData.description}
                  onChange={(e) => handleVehicleDataChange('description', e.target.value)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base h-20 resize-none"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Description
                </Label>
              </div>
            </div>
            <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
              <div className="relative">
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Vehicle Status</Label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                      {vehicleData.statusOfVehicle || 'Select Vehicle Status'}
                      <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-full">
                    {statusOfVehicleOptions.map((statusOfVehicle) => (
                      <DropdownMenuItem
                        key={statusOfVehicle}
                        onSelect={() => handleVehicleDataChange('statusOfVehicle', statusOfVehicle)}
                        className={vehicleData.statusOfVehicle === statusOfVehicle ? 'bg-amber-100 font-regular' : ''}
                      >
                        {statusOfVehicle || 'Select Vehicle Status'}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
            </div>
          </div>
        )}

        {changeServiceType && serviceType === 'Accident' && (
                <div className="bg-white rounded-lg mb-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Accident Details</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.make}
                        onChange={(e) => handleVehicleDataChange('make', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Make
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.driver}
                        onChange={(e) => handleVehicleDataChange('driver', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Driver
                      </Label>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.phone}
                        onChange={(e) => handleVehicleDataChange('phone', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Phone
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.accidentDate}
                        onChange={(e) => handleVehicleDataChange('accidentDate', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Accident Date
                      </Label>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.incidentState}
                        onChange={(e) => handleVehicleDataChange('incidentState', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Incident State
                      </Label>
                    </div>
                    <div className="relative">
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Whose Fault</Label>
                        <DropdownMenu>
                          <DropdownMenuTrigger asChild>
                            <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                              {vehicleData.fault || 'Select'}
                              <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                            </Button>
                          </DropdownMenuTrigger>
                          <DropdownMenuContent className="w-full">
                            {otherPartyOptions.map((fault) => (
                            <DropdownMenuItem
                              key={fault}
                              onSelect={() => handleVehicleDataChange('fault', fault)}
                              className={vehicleData.fault === fault ? 'bg-amber-100 font-regular' : ''}
                            >
                            {fault || 'Select'}
                          </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.otherParty}
                        onChange={(e) => handleVehicleDataChange('otherParty', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Other Party
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.quote}
                        onChange={(e) => handleVehicleDataChange('quote', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Quote
                      </Label>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.rentalLoss}
                        onChange={(e) => handleVehicleDataChange('rentalLoss', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Rental Loss
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.towingCharges}
                        onChange={(e) => handleVehicleDataChange('towingCharges', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Towing Charges
                      </Label>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.grandTotal}
                        onChange={(e) => handleVehicleDataChange('grandTotal', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Grand Total
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.gst}
                        onChange={(e) => handleVehicleDataChange('gst', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        GST
                      </Label>
                    </div>
                  </div>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                    <div className="relative">
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Insurance Status</Label>
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                            {vehicleData.insuranceStatus || 'Select Status'}
                            <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent className="w-full">
                          {insuranceStatusOptions.map((insuranceStatus) => (
                            <DropdownMenuItem
                              key={insuranceStatus}
                              onSelect={() => handleVehicleDataChange('insuranceStatus', insuranceStatus)}
                              className={vehicleData.insuranceStatus === insuranceStatus ? 'bg-amber-100 font-regular' : ''}
                            >
                              {insuranceStatus || 'Select Status'}
                            </DropdownMenuItem>
                          ))}
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </div>
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.insuranceClaimNumber}
                        onChange={(e) => handleVehicleDataChange('insuranceClaimNumber', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Insurance Claim Number
                      </Label>
                    </div>
                  </div>
                  <h4 className="text-lg sm:text-xl font-medium text-earth-dark mb-4">Upload Images</h4>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8">
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleImageUpload('interiorImages', e.target.files)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Interior Images
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleImageUpload('exteriorImages', e.target.files)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Exterior Images
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleImageUpload('leftSideDoors', e.target.files)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Left Side Doors
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleImageUpload('rightSideDoors', e.target.files)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Right Side Doors
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleImageUpload('frontSideImages', e.target.files)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Front Side Images
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleImageUpload('backSideImages', e.target.files)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Back Side Images
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleImageUpload('sideMirrors', e.target.files)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Side Mirrors
                      </Label>
                    </div>
                    <div className="relative">
                      <Input
                        type="file"
                        accept="image/*"
                        multiple
                        onChange={(e) => handleImageUpload('other', e.target.files)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Other
                      </Label>
                    </div>
                  </div>
                </div>
              )}
              {changeServiceType && serviceType === 'Breakdowns' && (
                <div className="bg-white rounded-lg mb-6">
                  <h3 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Breakdown Details</h3>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
                    <div className="relative">
                      <Input
                        type="text"
                        value={vehicleData.incidentDate}
                        onChange={(e) => handleVehicleDataChange('incidentDate', e.target.value)}
                        className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                      />
                      <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                        Incident Date
                      </Label>
                    </div>
                  </div>
                </div>
              )}
        
        {changeServiceType && serviceType === 'Damage' && (
          <div className="bg-white rounded-lg mb-6">
            <h3 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Damage Details</h3>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
              <div className="relative">
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Insurance Status</Label>
                <DropdownMenu>
                  <DropdownMenuTrigger asChild>
                    <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                      {vehicleData.insuranceStatus || 'Select Status'}
                      <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                    </Button>
                  </DropdownMenuTrigger>
                  <DropdownMenuContent className="w-full">
                    {insuranceStatusOptions.map((insuranceStatus) => (
                      <DropdownMenuItem
                        key={insuranceStatus}
                        onSelect={() => handleVehicleDataChange('insuranceStatus', insuranceStatus)}
                        className={vehicleData.insuranceStatus === insuranceStatus ? 'bg-amber-100 font-regular' : ''}
                      >
                        {insuranceStatus || 'Select Status'}
                      </DropdownMenuItem>
                    ))}
                  </DropdownMenuContent>
                </DropdownMenu>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  value={vehicleData.insuranceClaimNumber}
                  onChange={(e) => handleVehicleDataChange('insuranceClaimNumber', e.target.value)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Insurance Claim Number
                </Label>
              </div>
            </div>
            <h4 className="text-lg sm:text-xl font-medium text-earth-dark mb-4">Upload Images</h4>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8">
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload('interiorImages', e.target.files)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Interior Images
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload('exteriorImages', e.target.files)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Exterior Images
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload('leftSideDoors', e.target.files)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Left Side Doors
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload('rightSideDoors', e.target.files)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Right Side Doors
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload('frontSideImages', e.target.files)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Front Side Images
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload('backSideImages', e.target.files)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Back Side Images
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload('sideMirrors', e.target.files)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Side Mirrors
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="file"
                  accept="image/*"
                  multiple
                  onChange={(e) => handleImageUpload('other', e.target.files)}
                  className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
                />
                <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
                  Other
                </Label>
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Workshop Details Section */}
      <div className="bg-white rounded-lg mb-6">
        <h3 className="text-xl sm:text-2xl font-medium text-earth-dark mb-4">Workshop Details</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Branch</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {workshopData.branch || 'Select Branch'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {branchOptions.map((branch) => (
                  <DropdownMenuItem
                    key={branch}
                    onSelect={() => handleInputChange('branch', branch)}
                    className={workshopData.branch === branch ? 'bg-amber-100 font-regular' : ''}
                  >
                    {branch || 'Select Branch'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Mechanic Assigned</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {workshopData.mechanicAssigned.length > 0 ? (
                    <div className="flex flex-wrap gap-1">
                      {workshopData.mechanicAssigned.map((mechanic) => (
                        <Badge key={mechanic} variant="secondary">
                          {mechanic}
                        </Badge>
                      ))}
                    </div>
                  ) : (
                    'Select Mechanic'
                  )}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {mechanicOptions.map((mechanic) => (
                  <DropdownMenuItem
                    key={mechanic}
                    onSelect={() => handleMechanicChange(mechanic)}
                    className={workshopData.mechanicAssigned.includes(mechanic) ? 'bg-amber-100 font-regular' : ''}
                  >
                    {mechanic || 'Select Mechanic'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Input
              type="date"
              value={vehicleData.estimatedEndDate}
              onChange={(e) => handleVehicleDataChange('estimatedEndDate', e.target.value)}
              className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Estimated End Date
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={vehicleData.actualEndDate}
              onChange={(e) => handleVehicleDataChange('actualEndDate', e.target.value)}
              className="bg-white w-full p-1 sm:p-2 md:p-3 border border-gray-400 rounded-md text-[10px] sm:text-xs md:text-sm lg:text-base"
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Actual End Date
            </Label>
          </div>
        </div>
        
        <div className="grid grid-cols-2 gap-3 sm:gap-6 md:gap-8 mb-4">
          <div className="relative">
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 sm:text-xs md:text-sm lg:text-sm text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none text-xs">Status</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border-gray-500 text-[10px] sm:text-xs md:text-sm lg:text-base">
                  {vehicleData.status || 'Select Status'}
                  <ChevronDown className="w-4 sm:w-5 h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {statusOptions.map((status) => (
                  <DropdownMenuItem
                    key={status}
                    onSelect={() => handleVehicleDataChange('status', status)}
                    className={vehicleData.status === status ? 'bg-amber-100 font-regular' : ''}
                  >
                    {status || 'Select Status'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>
        
        <div className="grid grid-cols-1 gap-3 sm:gap-6 md:gap-8 mt-4">
          <div className="relative">
            <Input
              type="text"
              value={workshopData.comment}
              onChange={(e) => handleInputChange('comment', e.target.value)}
              className="w-full p-1 sm:p-2 md:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-[10px] sm:text-xs md:text-sm lg:text-base h-20 resize-none"
              placeholder="Type here..."
            />
            <Label className="absolute left-2 sm:left-3 -top-2 sm:-top-2.5 text-[10px] sm:text-xs md:text-sm lg:text-sm text-gray-700 bg-white px-1 transition-all duration-200 pointer-events-none">
              Comment
            </Label>
          </div>
        </div>
      </div>

      {/* Update and Cancel Buttons */}
      <div className="flex justify-end gap-4">
        <Button
          className="bg-[#330101] text-white px-4 py-2 text-sm sm:text-base"
          onClick={handleUpdate}
          disabled={isLoading}
        >
          {isLoading ? 'Updating...' : 'Update'}
        </Button>
        <Button
          variant="outline"
          className="border-gray-500 text-gray-700 px-4 py-2 text-sm sm:text-base"
          onClick={() => navigate('/admin/workshopMasterAdmin/service-breakdown')}
        >
          Cancel
        </Button>
      </div>
    </div>
  );
}