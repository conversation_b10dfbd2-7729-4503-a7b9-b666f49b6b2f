{"name": "lion-Rentals-Front-End", "version": "0.0.1", "private": true, "type": "module", "scripts": {"dev": "npx vite", "build": "npx vite build", "lint": "eslint . --ext .js,.jsx,.ts,.tsx", "preview": "npx vite preview"}, "dependencies": {"@hookform/resolvers": "^5.1.1", "@radix-ui/react-accordion": "latest", "@radix-ui/react-alert-dialog": "latest", "@radix-ui/react-aspect-ratio": "latest", "@radix-ui/react-avatar": "latest", "@radix-ui/react-checkbox": "latest", "@radix-ui/react-dialog": "latest", "@radix-ui/react-dropdown-menu": "latest", "@radix-ui/react-hover-card": "^1.1.14", "@radix-ui/react-label": "latest", "@radix-ui/react-menubar": "^1.1.15", "@radix-ui/react-navigation-menu": "^1.2.13", "@radix-ui/react-popover": "latest", "@radix-ui/react-progress": "^1.1.7", "@radix-ui/react-radio-group": "latest", "@radix-ui/react-scroll-area": "^1.2.9", "@radix-ui/react-select": "latest", "@radix-ui/react-separator": "latest", "@radix-ui/react-slot": "latest", "@radix-ui/react-switch": "latest", "@radix-ui/react-tabs": "latest", "@radix-ui/react-tooltip": "latest", "@types/html2canvas": "^1.0.0", "@types/jspdf": "^2.0.0", "@types/react-datepicker": "^7.0.0", "@types/react-router-dom": "^5.3.3", "@vitejs/plugin-react": "latest", "axios": "^1.10.0", "class-variance-authority": "latest", "clsx": "latest", "date-fns": "^4.1.0", "html2canvas": "^1.4.1", "js-cookie": "^3.0.5", "jspdf": "^3.0.1", "jspdf-autotable": "^5.0.2", "libphonenumber-js": "^1.12.10", "lucide-react": "^0.441.0", "path": "^0.12.7", "react": "^18.3.1", "react-day-picker": "^9.3.0", "react-dom": "^18.3.1", "react-hook-form": "^7.60.0", "react-phone-input-2": "^2.15.1", "react-router-dom": "^6.26.2", "react-toastify": "^11.0.5", "recharts": "^3.1.0", "sonner": "^2.0.6", "tailwind-merge": "latest", "zod": "^4.0.5"}, "devDependencies": {"@types/js-cookie": "^3.0.6", "@types/node": "^20.11.18", "@types/react": "^18.3.1", "@types/react-dom": "^18.3.1", "@typescript-eslint/eslint-plugin": "^5.54.0", "@typescript-eslint/parser": "^5.54.0", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.21", "eslint": "^8.50.0", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.1", "postcss": "^8.5.6", "tailwindcss": "^3.4.17", "typescript": "^5.5.4", "vite": "^4.5.14"}}