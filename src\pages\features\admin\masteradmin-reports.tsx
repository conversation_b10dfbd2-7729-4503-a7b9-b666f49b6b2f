import React, { useState } from 'react';
import { Link } from 'react-router-dom';
import {
  FileText as PdfIcon,
  GaugeCircle,
  FileText,
  Ban,
  ClockArrowUp,
  DollarSign,
} from 'lucide-react';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';

// Helper function to create URL-friendly slugs
const slugify = (text: string) =>
  text
    .toLowerCase()
    .replace('$', '')
    .trim()
    .replace(/\s+/g, '-')
    .replace(/[^\w-]+/g, '');

// Type for a single report item
type ReportItem = {
  title: string;
  description: string;
  icon: React.ElementType;
  path: string;
};


const carRentalReportsData: ReportItem[] = [
  {
    title: 'Revenue Based on Returns',
    description: 'Shows an overview of revenues based on completed Reservations (returned...',
    icon: GaugeCircle,
    path: '/reports/rental/revenue-based-on-returns',
  },
  {
    title: 'Revenue per Payment Option',
    description: 'Shows an overview of revenues per payment option.',
    icon: GaugeCircle,
    path: '/reports/rental/revenue-per-payment-option',
  },
  {
    title: 'Monthly Overview',
    description: 'Shows an overview of revenues per month.',
    icon: GaugeCircle,
    path: '/reports/rental/monthly-overview',
  },
  {
    title: 'Location Performance',
    description: 'Shows an overview of reservations between a range of dates.',
    icon: GaugeCircle,
    path: '/reports/rental/location-performance',
  },
  {
    title: 'End of the Day Overview',
    description: 'Shows an overview of revenues until end of the day.',
    icon: GaugeCircle,
    path: '/reports/rental/end-of-the-day-overview',
  },
  {
    title: 'Daily Manifest',
    description: 'A daily report of all pickups, returns, overdues and scheduled repairs',
    icon: FileText,
    path: '/reports/rental/daily-manifest',
  },
  {
    title: 'Sales Tax',
    description: 'A Sales Tax report of all Reservations',
    icon: GaugeCircle,
    path: '/reports/rental/sales-tax',
  },
  {
    title: 'Additional Charges',
    description: 'Shows an overview of Additional Charges and their sold days.',
    icon: GaugeCircle,
    path: '/reports/rental/additional-charges',
  },
  {
    title: 'Damages',
    description: 'Shows an overview of damage charges in reservations',
    icon: GaugeCircle,
    path: '/reports/rental/damages',
  },
  {
    title: 'Cancellations',
    description: 'Shows an overview of cancelled Reservations',
    icon: Ban,
    path: '/reports/rental/cancellations',
  },
  {
    title: 'Extensions Report',
    description: 'Show an overview of the extensions',
    icon: ClockArrowUp,
    path: '/reports/rental/extensions-report',
  },
  {
    title: 'Daily Report',
    description: 'Todays report',
    icon: DollarSign,
    path: `/reports/rental/${slugify('$ Daily Report')}`,
  },
];


const fleetReportsData: ReportItem[] = [
  {
    title: 'Expenses Overview',
    description: 'Shows an overview of expenses per Vehicle.',
    icon: GaugeCircle,
    path: '/reports/fleet/expenses-overview',
  },
  {
    title: 'Expense Types per Payment Option',
    description: 'Shows an overview of expenses per payment option.',
    icon: GaugeCircle,
    path: '/reports/fleet/expense-types-per-payment-option',
  },
  {
    title: 'Vehicle Profitability',
    description: 'Shows an overview of revenues, expenses and profit per Vehicle.',
    icon: GaugeCircle,
    path: '/reports/fleet/vehicle-profitability',
  },
  {
    title: 'Vehicle Daily Revenue',
    description: 'Shows an overview of revenue per Vehicle prorated per day.',
    icon: GaugeCircle,
    path: '/reports/fleet/vehicle-daily-revenue',
  },
  {
    title: 'Vehicle Depreciation',
    description: 'Shows an overview of the depreciation per Vehicle.',
    icon: GaugeCircle,
    path: '/reports/fleet/vehicle-depreciation',
  },
  {
    title: 'Vehicle Depreciation Schedule',
    description: 'Shows an overview of the depreciation schedule per Vehicle.',
    icon: GaugeCircle,
    path: '/reports/fleet/vehicle-depreciation-schedule',
  },
  {
    title: 'Vehicle Utilization',
    description: 'Shows an overview of the utilization per Vehicle.',
    icon: GaugeCircle,
    path: '/reports/fleet/vehicle-utilization',
  },
  {
    title: 'Vehicle Reservations',
    description: 'Shows an overview of the reservations of each vehicle',
    icon: GaugeCircle,
    path: '/reports/fleet/vehicle-reservations',
  },
  {
    title: 'Vehicle Location',
    description: 'Shows an overview of the distance of vehicles to a given point in time. Can be used to determine what vehicles were present at a given time.',
    icon: GaugeCircle,
    path: '/reports/fleet/vehicle-location',
  },
];

const MasterAdminReports = () => {
  const [activeTab, setActiveTab] = useState('carRental'); 

  const reportsToDisplay = activeTab === 'carRental' ? carRentalReportsData : fleetReportsData;
  
  const getTabClassName = (tabName: string) => {
    return activeTab === tabName
      ? 'py-2 px-1 text-[#2c3e50] border-b-2 border-[#2c3e50] font-semibold text-lg'
      : 'py-2 px-1 text-gray-500 hover:text-gray-700 font-semibold text-lg';
  };

  return (
    <div className="bg-gray-100 p-4 sm:p-6 md:p-8 min-h-screen">
      
      <div className="flex items-center space-x-3 mb-4">
        <PdfIcon className="h-8 w-8 text-gray-700" />
        <h1 className="text-3xl font-bold text-gray-800">Reports</h1>
      </div>

      {/* Tabs Navigation */}
      <div className="border-b border-gray-300 mb-6">
        <nav className="flex space-x-8">
          <button
            className={getTabClassName('carRental')}
            onClick={() => setActiveTab('carRental')}
          >
            Car Rental
          </button>
          <button
            className={getTabClassName('fleet')}
            onClick={() => setActiveTab('fleet')}
          >
            Fleet
          </button>
        </nav>
      </div>

     
      <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
        {reportsToDisplay.map((report, index) => {
          const IconComponent = report.icon;
          return (
            <Card key={index} className="shadow-md hover:shadow-lg transition-shadow bg-white rounded-lg h-full flex flex-col">
              <CardHeader className="bg-[#2c3e50] text-white p-4 rounded-t-md">
                <CardTitle className="text-base text-white font-semibold">
                  <Link
                    to={report.path}
                    className="flex items-center text-inherit no-underline hover:underline focus:outline-none focus:ring-2 focus:ring-white rounded"
                  >
                    <IconComponent className="h-5 w-5 mr-3 flex-shrink-0" />
                    <span>{report.title}</span>
                  </Link>
                </CardTitle>
              </CardHeader>
              <CardContent className="p-4 flex-grow">
                <p className="text-sm text-gray-600">{report.description}</p>
              </CardContent>
            </Card>
          );
        })}
      </div>
    </div>
  );
};

export default MasterAdminReports;