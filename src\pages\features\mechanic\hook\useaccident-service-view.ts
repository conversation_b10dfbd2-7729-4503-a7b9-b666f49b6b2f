import { useState } from "react";
import { MechanicAccidentServiceData } from "../type/mechanictype";

const MOCK_DATA: MechanicAccidentServiceData = {
  model: "Isuzu",
  regNo: "AFS 009",
  description: "I was reversing out of the factory, and I accidentally reversed into our work van parked on the street.",
  insuranceStatus: "Initial Review",
  insuranceClaimNumber: "",
  estimatedEndDate: "2025-05-26",
  actualEndDate: "2025-05-31",
  status: "Pending",
  images: {
    interiorImages: ["interior.jpg"],
    exteriorImages: ["exterior.jpg"],
    leftSideDoors: ["left.jpg"],
    rightSideDoors: ["right.jpg"],
    frontSideImages: ["front.jpg"],
    backSideImages: ["back.jpg"],
    sideMirrors: ["mirrors.jpg"],
    other: ["other.jpg"],
  },
  branch: "Somerton",
  mechanicAssigned: ["<PERSON>", "<PERSON>"],
  comment: "Type here...",
  mechanicNoteStatus: "Pending",
  mechanicNoteComment: "Needs further inspection.",
  vehicleId: ""
};

export function useAccidentServiceView() {
  const [formData] = useState<MechanicAccidentServiceData>(MOCK_DATA);
  return { formData };
}