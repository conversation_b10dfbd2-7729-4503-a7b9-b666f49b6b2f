import React from 'react';
import { Eye, EyeOff } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { useSettingHook } from './hook/useSettingHook';

export const SettingPage: React.FC = () => {
  const {
    formData,
    setFormData,
    showPasswords,
    setShowPasswords,
    handleInputChange,
    togglePasswordVisibility,
    handleChangePassword,
    handleCancel,
  } = useSettingHook();

  return (
    <div className="min-h-screen p-2 sm:p-4 md:p-8">
      <div className="max-w-md mx-auto bg-white rounded-lg shadow-sm p-2 sm:p-4 md:p-8">
        <h2 className="text-2xl font-bold text-gray-800 mb-1 sm:mb-2 md:mb-2">Change Password</h2>
        <p className="text-gray-600 mb-4 sm:mb-6 md:mb-8">
          Enter your current password and new password to Change the password
        </p>

        <div className="space-y-3 sm:space-y-4 md:space-y-6">
          {/* Current Password */}
          <div className="relative">
            <input
              type={showPasswords.current ? 'text' : 'password'}
              name="currentPassword"
              value={formData.currentPassword}
              onChange={handleInputChange}
              className="w-full border border-gray-300 rounded-md px-2 sm:px-3 md:px-3 py-2 sm:py-3 md:py-3 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent peer"
            />
            <Label htmlFor="currentPassword" className="absolute left-2 top-[-6px] sm:top-[-8px] md:top-[-8px] bg-white px-1 text-xs text-gray-600 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-focus:top-[-6px] sm:peer-focus:top-[-8px] md:peer-focus:top-[-8px] transition-all duration-200">
              Current Password
            </Label>
            <button
              type="button"
              onClick={() => togglePasswordVisibility('current')}
              className="absolute right-2 sm:right-3 md:right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showPasswords.current ? <EyeOff size={16} sm:size={18} md:size={18} /> : <Eye size={16} sm:size={18} md:size={18} />}
            </button>
          </div>

          {/* New Password */}
          <div className="relative">
            <input
              type={showPasswords.new ? 'text' : 'password'}
              name="newPassword"
              value={formData.newPassword}
              onChange={handleInputChange}
              placeholder="••••••••••••"
              className="w-full border border-gray-300 rounded-md px-2 sm:px-3 md:px-3 py-2 sm:py-3 md:py-3 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent peer"
            />
            <Label htmlFor="newPassword" className="absolute left-2 top-[-6px] sm:top-[-8px] md:top-[-8px] bg-white px-1 text-xs text-gray-600 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-focus:top-[-6px] sm:peer-focus:top-[-8px] md:peer-focus:top-[-8px] transition-all duration-200">
              New Password
            </Label>
            <button
              type="button"
              onClick={() => togglePasswordVisibility('new')}
              className="absolute right-2 sm:right-3 md:right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showPasswords.new ? <EyeOff size={16} sm:size={18} md:size={18} /> : <Eye size={16} sm:size={18} md:size={18} />}
            </button>
          </div>

          {/* Confirm Password */}
          <div className="relative">
            <input
              type={showPasswords.confirm ? 'text' : 'password'}
              name="confirmPassword"
              value={formData.confirmPassword}
              onChange={handleInputChange}
              placeholder="Enter again"
              className="w-full border border-gray-300 rounded-md px-2 sm:px-3 md:px-3 py-2 sm:py-3 md:py-3 text-sm focus:outline-none focus:ring-2 focus:ring-amber-500 focus:border-transparent peer"
            />
            <Label htmlFor="confirmPassword" className="absolute left-2 top-[-6px] sm:top-[-8px] md:top-[-8px] bg-white px-1 text-xs text-gray-600 peer-placeholder-shown:top-1/2 peer-placeholder-shown:-translate-y-1/2 peer-focus:top-[-6px] sm:peer-focus:top-[-8px] md:peer-focus:top-[-8px] transition-all duration-200">
              Confirm Password
            </Label>
            <button
              type="button"
              onClick={() => togglePasswordVisibility('confirm')}
              className="absolute right-2 sm:right-3 md:right-3 top-1/2 transform -translate-y-1/2 text-gray-500 hover:text-gray-700"
            >
              {showPasswords.confirm ? <EyeOff size={16} sm:size={18} md:size={18} /> : <Eye size={16} sm:size={18} md:size={18} />}
            </button>
          </div>

          {/* Buttons */}
          <div className="space-y-2 sm:space-y-3 md:space-y-3 pt-2 sm:pt-4 md:pt-4">
            <button
              onClick={handleChangePassword}
              className="w-full bg-[#330101] text-white py-2 sm:py-3 md:py-3 px-4 rounded-md font-medium transition-colors"
            >
              Changed Password
            </button>
            
            <button
              onClick={handleCancel}
              className="w-full bg-gray-400 text-white py-2 sm:py-3 md:py-3 px-4 rounded-md font-medium transition-colors"
            >
              Cancel
            </button>
          </div>
        </div>
      </div>
    </div>
  );
}