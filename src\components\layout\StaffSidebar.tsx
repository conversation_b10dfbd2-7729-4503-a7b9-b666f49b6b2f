import { NavLink } from 'react-router-dom';
import { 
  Home, 
  ClipboardCheck, 
  Headphones, 
  HandHeart, 
  AlertTriangle, 
  Wrench, 
  CheckSquare, 
  BarChart3,
  UserCog
} from 'lucide-react';

export function StaffSidebar() {
  const navItems = [
    {
      path: '/staff/dashboard',
      label: 'Staff Dashboard',
      icon: <Home className="w-5 h-5 mr-3" />
    },
    {
      path: '/staff/vehicle-inspection',
      label: 'Vehicle Inspection',
      icon: <ClipboardCheck className="w-5 h-5 mr-3" />
    },
    {
      path: '/staff/customer-support',
      label: 'Customer Support',
      icon: <Headphones className="w-5 h-5 mr-3" />
    },
    {
      path: '/staff/booking-assistance',
      label: 'Booking Assistance',
      icon: <HandHeart className="w-5 h-5 mr-3" />
    },
    {
      path: '/staff/incident-management',
      label: 'Incident Management',
      icon: <AlertTriangle className="w-5 h-5 mr-3" />
    },
    {
      path: '/staff/maintenance',
      label: 'Maintenance Schedule',
      icon: <Wrench className="w-5 h-5 mr-3" />
    },
    {
      path: '/staff/tasks',
      label: 'Task Management',
      icon: <CheckSquare className="w-5 h-5 mr-3" />
    },
    {
      path: '/staff/reports',
      label: 'Staff Reports',
      icon: <BarChart3 className="w-5 h-5 mr-3" />
    }
  ];

  return (
    <aside className="bg-blue-50 w-64 min-h-screen flex flex-col border-r border-blue-200">
      <nav className="flex-1">
        <div className="p-4 border-b border-blue-200">
          <div className="flex items-center">
            <UserCog className="w-6 h-6 mr-2 text-blue-600" />
            <h2 className="text-lg font-semibold text-blue-800">Staff Portal</h2>
          </div>
        </div>
        <ul className="space-y-1 pt-4">
          {navItems.map(item => (
            <li key={item.path}>
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-blue-700 hover:bg-blue-100 transition-colors ${
                    isActive ? 'bg-blue-100 border-l-4 border-blue-600 font-medium' : ''
                  }`
                }
              >
                {item.icon}
                <span>{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}
