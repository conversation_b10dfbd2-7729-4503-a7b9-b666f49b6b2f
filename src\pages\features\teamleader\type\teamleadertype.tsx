export interface FleetQualityData {
  id: string
  vehicleId: string
  rego: string
  lastDetailClean: string
  lastService: string
  lastInspection: string
}

export interface FleetSummaryData {
  id: string
  category: string
  vehicle: string
  lastServiceType: string
  lastOdometer: number
  status: string
  mechanicAssigned: string
}

export interface FleetTaskData {
  id: string
  taskId: string  // TO001, TO002, etc.
  rego: string
  lastService: string
  lastServiceDate: string
  status: string
}

// Mechanics related interfaces
export interface MechanicsData {
  id: string
  fullName: string
  phoneNumber: string
  address: string
  enabled: boolean
}

export interface EmergencyContact {
  name: string
  relationship: string
  phoneNumber: string
}

export interface VerificationImages {
  frontView: string
  backView: string
}

export interface DriverLicense {
  number: string
  issueState: string
  issueDate: string
  expiryDate: string
}

export interface Passport {
  number: string
}

export interface Verification {
  type: 'driverLicense' | 'passport'
  images: VerificationImages
  driverLicense?: DriverLicense
  passport?: Passport
}

export interface MechanicViewData {
  id: string
  firstName: string
  lastName: string
  phoneNumber: string
  address: string
  enabled: boolean
  emergencyContact: EmergencyContact
  verification: Verification
}


export interface PanelBeater {
  id: string;
  name: string;
  phone: string;
  address: string;
}

export interface PanelBeaterFormData {
  id: string;
  name: string;
  phone: string;
  address: string;
}

export interface PanelBeaterEditFormData {
  id: string;
  name: string;
  phone: string;
  address: string;
}

export interface Vendor {
  id: string;
  vendorName: string;
  phone: string;
  email: string;
  address: string;
}

export interface VendorFormData {
  id: string;
  vendorName: string;
  phone: string;
  email: string;
  address: string;
}

export interface VendorEditFormData {
  id: string;
  vendorName: string;
  phone: string;
  email: string;
  address: string;
}

export interface Parts {
  partNo: string;
  partName: string;
  quantity: string;
  vendorName: string;
  mechanicName?: string;
}

export interface Part {
  id: number;
  partName: string;
  quantity: string;
  comment?: string;
}

export interface PartsFormData {
  partNo: string;
  vendorName: string;
  parts: Part[];
}

export interface PartsEditFormData {
  partNo: string;
  vendorName: string;
  parts: Part[];
}

export interface Service {
  serviceCode: string;
  vehicle: string;
  serviceName: string;
  vendorName: string;
  description?: string;
  mechanicName: string;
}

export interface ServiceItem {
  id: number;
  serviceName: string;
  description?: string;
}

export interface ServicesFormData {
  serviceCode: string;
  vehicle: string;
  vendorName: string;
  services: ServiceItem[];
}

export interface ServicesEditFormData {
  serviceCode: string;
  vehicle: string;
  vendorName: string;
  services: ServiceItem[];
}

export interface Booking {
  id: string;
  vehicle: string;
  pickupDate: string;
  pickupTime: string;
  returnDate: string;
  returnTime: string;
  incidentDate: string;
  reportType: 'Accident' | 'Breakdown' | 'General Maintenance' | 'Damage';
}

export interface Management {
  vehicleId: string;
  vehicle: string;
  serviceTypes: string;
  totalInParts: string;
  totalInLabor: string;
  status: 'Pending' | 'InProgress' | 'Done';
  dateIn: string;
  dateOut: string;
}

export interface VehicleFormData {
  vehicleId: string;
  vehicleModel: string;
  regoNumber: string;
  serviceType: string;
  totalParts: string;
  totalLabor: string;
  damages: string;
  notes: string;
  dateIn: string;
  dateOut: string;
  timeOut: string;
  timeIn: string;
  comments: string;
  status: string;
  reservation: string;
  fuelIn: string;
  fuelOut: string;
  odometerIn: string;
  odometerOut: string;
}

export interface MaintenanceRecord {
  vehicleId: string;
  vehicle: string;
  maintenanceType: string;
  estimatedEndDate: string;
  actualEndDate: string;
  status: string;
  statusOfVehicle: string;
  vehicleCurrentRenter: string;
  mechanic: string;
}

export interface VehicleData {
  vehicleId: string;
  model: string;
  regNo: string;
  maintenanceType: string;
  typeInterval: string;
  estimatedEndDate: string;
  actualEndDate: string;
  odometerAtDueDate: string;
  currentOdometer: string;
  status: string;
  statusOfVehicle: string;
  vehicleCurrentRenter: string;
  vehicleCurrentLocation: string;
  description: string;
  mechanic: string;
  branch: string;
  comment: string;
  insuranceStatus?: string;
  insuranceClaimNumber?: string;
  incidentDate?: string;
}

export interface WorkshopData {
  branch: string;
  mechanicAssigned: string[];
  comment: string;
}

export interface ImageData {
  interiorImages: string[];
  exteriorImages: string[];
  leftSideDoors: string[];
  rightSideDoors: string[];
  frontSideImages: string[];
  backSideImages: string[];
  sideMirrors: string[];
  other: string[];
}

export type ServiceTypeOption = '' | 'General Maintenance' | 'Breakdowns' | 'Accident' | 'Damage';
export type StatusOption = '' | 'Pending' | 'InProgress' | 'Done';
export type StatusOfVehicleOption = '' | 'Dirty' | 'Cleaned';
export type BranchOption = '' | 'Somerton';
export type MechanicOption = '' | 'John Doe' | 'Mike Smith' | 'Robert Brown';
export type InsuranceStatusOption = '' | 'Initial Review' | 'Accepted' | 'Declined';

export interface AccidentRecord {
  vehicleId: string;
  vehicle: string;
  insuranceClaimNumber: string;
  actualDate: string;
  estimatedDate: string;
  insuranceStatus: InsuranceStatusOption;
  mechanicAssigned: string;
  status: StatusOption;
}

export interface PreventiveRecord {
  vehicleId: string;
  vehicle: string;
  nextServiceAt: string;
  serviceTypes: string;
  alertStatus: 'Upcoming' | 'Overdue' | 'Delay';
}

export interface MaintenanceHistoryRecord {
  serviceType: string;
  lastServicedate: string;
  lastServiceat: string;
  nextSrviceat: string; 
}

export interface ViewAuditTrailRecord {
  mechanicId: string;
  mechanicName: string;
  vehicle: string;
  serviceType: string;
  dateTime: string;
  description: string;
}

export interface Notifications {
  id: number;
  type: string;
  message: string;
  date: string;
}

export interface Activities {
  id: number;
  type: string;
  message: string;
  date: string;
}

