import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { useNavigate, useParams } from 'react-router-dom';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { Pagination } from '@/components/layout/Pagination';

interface BlockedPeriod {
  id: string;
  startDate: string;
  endDate: string;
  description: string;
  color: 'orange' | 'purple' | 'gray';
  createdBy: string;
  createdAt: string;
}

export function VehicleBlockPeriodPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(5);

  const currentTab = 'Blocked Periods';

  const blockedPeriods: BlockedPeriod[] = [
    {
      id: '3122',
      startDate: '06-02-2025 00:00',
      endDate: '13-02-2025 00:00',
      description: 'Service',
      color: 'orange',
      createdBy: '<PERSON><PERSON><PERSON>',
      createdAt: '06-02-2025 09:37'
    },
    {
      id: '3077',
      startDate: '27-12-2024 00:00',
      endDate: '01-01-2025 23:55',
      description: 'Tyres',
      color: 'orange',
      createdBy: 'Nalinda Randunne',
      createdAt: '27-12-2024 17:27'
    },
    {
      id: '2893',
      startDate: '07-10-2024 00:05',
      endDate: '15-10-2024 00:05',
      description: 'bearing issue fixed',
      color: 'purple',
      createdBy: 'Nalaka Dahanaka',
      createdAt: '07-10-2024 13:15'
    },
    {
      id: '2650',
      startDate: '29-04-2024 02:15',
      endDate: '14-05-2024 02:00',
      description: 'Heater fixed',
      color: 'purple',
      createdBy: 'Rajwinder Kaur',
      createdAt: '29-04-2024 12:03'
    },
    {
      id: '2630',
      startDate: '15-04-2024 08:00',
      endDate: '21-04-2024 18:00',
      description: 'Service done',
      color: 'purple',
      createdBy: 'Mahesh Silva (Marsh)',
      createdAt: '15-04-2024 16:32'
    },
    {
      id: '2187',
      startDate: '20-08-2023 08:00',
      endDate: '21-08-2023 08:00',
      description: 'Maintenance done',
      color: 'gray',
      createdBy: 'Mahesh Silva (Marsh)',
      createdAt: '21-08-2023 09:24'
    },
    {
      id: '2101',
      startDate: '17-07-2023 00:05',
      endDate: '19-07-2023 00:05',
      description: 'front tyres replacement',
      color: 'orange',
      createdBy: 'Pasindu Crishmal',
      createdAt: '17-07-2023 12:29'
    },
    {
      id: '1468',
      startDate: '19-09-2022 16:35',
      endDate: '26-09-2022 20:00',
      description: 'maintenance',
      color: 'orange',
      createdBy: 'Rajwinder Kaur',
      createdAt: '19-09-2022 17:37'
    },
    {
      id: '574',
      startDate: '18-01-2022 08:00',
      endDate: '19-01-2022 08:00',
      description: '',
      color: 'orange',
      createdBy: 'Mahesh Silva (Marsh)',
      createdAt: '18-01-2022 10:27'
    },
    {
      id: '573',
      startDate: '04-05-2021 00:00',
      endDate: '08-05-2021 00:00',
      description: 'Bold tyres. Vibrating over 40kmph',
      color: 'orange',
      createdBy: 'Suvin Rumal',
      createdAt: '04-05-2021 09:50'
    }
  ];

  const totalEntries = blockedPeriods.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);

  const getColorClass = (color: string) => {
    switch (color) {
      case 'orange':
        return 'bg-orange-700';
      case 'gray':
        return 'bg-zinc-700';
      case 'purple':
        return 'bg-violet-400';
      default:
        return 'bg-gray-500';
    }
  };

  const handleTabClick = (tab: string) => {
    if (!id) {
      console.error('Vehicle ID is undefined for tab navigation');
      navigate('/receptionMasterAdmin/fleet/vehicles');
      return;
    }

    const tabRoutes: { [key: string]: string } = {
      'Edit': `/admin/receptionMasterAdmin/fleet/vehicles/edit-vehicle/${id}`,
      'Reservations': `/admin/receptionMasterAdmin/fleet/vehicles/reservations/${id}`,
      'Damages': `/admin/receptionMasterAdmin/fleet/vehicles/damages/${id}`,
      'Blocked Periods': `/admin/receptionMasterAdmin/fleet/vehicles/blocked-periods/${id}`,
      'Expenses': `/admin/receptionMasterAdmin/fleet/vehicles/expenses/${id}`,
      'Relocations': `/admin/receptionMasterAdmin/fleet/vehicles/relocations/${id}`,
      'Repair Orders': `/admin/receptionMasterAdmin/fleet/vehicles/repair-orders/${id}`,
      'Files': `/admin/receptionMasterAdmin/fleet/vehicles/files/${id}`,
      'Check List': `/admin/receptionMasterAdmin/fleet/vehicles/check-list/${id}`
    };

    navigate(tabRoutes[tab]);
  };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-semibold text-gray-900">
                Mercedes-Benz E 280 CDI Classic - {id || 'Unknown'}
              </h1>
              <span className="bg-[#330101] text-white px-3 py-1 rounded text-sm font-medium">
                Rental
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button className="bg-[#330101] hover:bg-[#660404] text-white">
                Add Block Period
              </Button>
            </div>
          </div>
    
             {/* Tab Navigation */}
             <div className="flex space-x-1">
               {['Edit', 'Reservations', 'Damages', 'Blocked Periods', 'Expenses', 'Relocations', 'Repair Orders', 'Files', 'Check List'].map((tab) => (
                 <Button
                   key={tab}
                   onClick={() => handleTabClick(tab)}
                   className={`px-4 py-2 text-sm font-medium border border-gray-300 transition-colors hover:bg-gray-100 ${
                     tab === currentTab 
                       ? 'bg-white text-gray-900' 
                       : 'bg-gray-100 text-gray-600'
                   }`}
                 >
                   {tab}
                 </Button>
               ))}
             </div>
        </div>

        {/* Data Table */}
        <div className="hidden md:block rounded-md border overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className='bg-gray-50 uppercase'>
                  <TableHead>#</TableHead>
                  <TableHead>Start Date</TableHead>
                  <TableHead>End Date</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>Color</TableHead>
                  <TableHead>Created By</TableHead>
                  <TableHead>Created At</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {blockedPeriods.slice(
                  (currentPage - 1) * recordsPerPage,
                  currentPage * recordsPerPage
                ).map((period) => (
                  <TableRow key={period.id} className="hover:bg-gray-50">
                    <TableCell>
                      <span className="text-blue-400 cursor-pointer hover:text-blue-800">
                        {period.id}
                      </span>
                    </TableCell>
                    <TableCell>{period.startDate}</TableCell>
                    <TableCell>{period.endDate}</TableCell>
                    <TableCell>{period.description}</TableCell>
                    <TableCell>
                      <div className={`w-16 h-6 rounded ${getColorClass(period.color)}`}></div>
                    </TableCell>
                    <TableCell>{period.createdBy}</TableCell>
                    <TableCell className="p-4 text-sm">{period.createdAt}</TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination Controls */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={setRecordsPerPage}
          recordsPerPageOptions={[10, 25, 50, 100]}
          className="mt-4"
        />
      </div>
    </div>
  );
}