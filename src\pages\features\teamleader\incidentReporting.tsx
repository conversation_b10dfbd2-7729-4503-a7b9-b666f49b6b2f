import React, { useState } from 'react';
import { Search, ChevronDown, FileText, ClipboardList } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import { useNavigate } from 'react-router-dom';
import { bookingsData } from './common/mockData';
import  {handleViewDetails } from './hook/useincidentReporting';

export function IncidentReportingPage() {
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const navigate = useNavigate();

  const filterBookings = bookingsData.filter(booking =>
    (
      booking.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.reportType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.pickupDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.returnDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      booking.incidentDate.toLowerCase().includes(searchTerm.toLowerCase())
    ) && (filterStatus === 'All' || booking.reportType === filterStatus)
  );

  const totalEntries = filterBookings.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentBookings = filterBookings.slice(startIndex, endIndex);

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
        <div className="flex items-center">
          <ClipboardList className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
          <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Incident Reporting</h1>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Accident">Accident</SelectItem>
              <SelectItem value="Breakdown">Breakdown</SelectItem>
              <SelectItem value="Damage">Damage</SelectItem>
              <SelectItem value="General Maintenance">General Maintenance</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1 order-3 sm:order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Desktop Table View */}
      <div className="hidden sm:block">
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs sm:text-sm">Incident ID</TableHead>
                <TableHead className="text-xs sm:text-sm">Vehicle</TableHead>
                <TableHead className="text-xs sm:text-sm">Pickup Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Return Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Incident Date</TableHead>
                <TableHead className="text-xs sm:text-sm">Report Type</TableHead>
                <TableHead className="text-xs sm:text-sm">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentBookings.map((booking) => (
                <TableRow key={booking.id}>
                  <TableCell className="text-xs sm:text-sm">{booking.id}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.vehicle}</TableCell>
                  <TableCell className="text-xs sm:text-sm">
                    <div>
                      <div>{booking.pickupDate}</div>
                      <div className="text-gray-500 text-xs">{booking.pickupTime}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-xs sm:text-sm">
                    <div>
                      <div>{booking.returnDate}</div>
                      <div className="text-gray-500 text-xs">{booking.returnTime}</div>
                    </div>
                  </TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.incidentDate}</TableCell>
                  <TableCell className="text-xs sm:text-sm">{booking.reportType}</TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Button
                        onClick={() => handleViewDetails(booking.id, navigate)}
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                      >
                        <FileText className="w-3 h-3 sm:w-4 sm:h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries} 
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-4 sm:mt-6"
        />
    </div>
  );
}