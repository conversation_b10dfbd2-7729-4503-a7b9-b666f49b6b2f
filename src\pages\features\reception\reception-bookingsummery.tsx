import React, { useEffect, useState } from 'react';
import { ArrowLeft, Car, Shield, Baby, FileText, Phone, Calendar, MapPin, CreditCard, ChevronDown, User } from 'lucide-react';
import { But<PERSON> } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useParams, useNavigate } from 'react-router-dom';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableHeader, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import { getBookingById, handleGoBack, handleAssignVehicle, handleReturnVehicle, handleViewInvoice, handlePrintAgreement, handleEmailAgreement, handlePrintReceipt, handleEmailReceipt } from './hook/useBookingSummary';
import { BookingSummary } from './type/reception-type';
import economyCar from '@/assets/economyCar.png';

export function ReceptionBookingSummary() {
  const { rentalId } = useParams<{ rentalId: string }>();
  const navigate = useNavigate();
  const [booking, setBooking] = useState<BookingSummary | null>(null);
  const [loading, setLoading] = useState(true);

  useEffect(() => {
    if (rentalId) {
      const fetchedBooking = getBookingById(rentalId);
      setBooking(fetchedBooking);
      setLoading(false);
    } else {
      setLoading(false);
    }
  }, [rentalId]);

  if (loading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <p className="text-gray-600">Loading...</p>
      </div>
    );
  }

  if (!booking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Booking Not Found</h1>
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c]"
            onClick={() => handleGoBack(navigate)}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Reservations
          </Button>
        </div>
      </div>
    );
  }

  const invoiceItems = [
    { description: booking.summary.ecoPlus.description, rate:booking.summary.ecoPlus.rate, days: booking.summary.ecoPlus.days, amount: booking.summary.ecoPlus.amount.replace('AUD ', '') },
    { description: booking.summary.bond.description, days: booking.summary.bond.days, amount: booking.summary.bond.amount.replace('AUD ', '') },
    { description: booking.summary.insurance.description, days: booking.summary.insurance.days, amount: booking.summary.insurance.amount.replace('AUD ', '') },
    { description: booking.summary.childSeat.description, rate:booking.summary.ecoPlus.rate, days: booking.summary.childSeat.days, amount: booking.summary.childSeat.amount.replace('AUD ', '') },
  ];

  return (
    <div className="min-h-screen">
      {/* Header */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Button
                className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
                size="sm"
                onClick={() => handleGoBack(navigate)}
              >
                <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
                <span className="hidden md:inline">Go Back</span>
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <Button
                size="sm"
                className="bg-[#330101] text-white hover:bg-[#ffde5c]"
                onClick={() => handleAssignVehicle(navigate)}
              >
                Assign Vehicle
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white hover:bg-[#ffde5c]"
                onClick={() => handleReturnVehicle(navigate)}
              >
                Return Vehicle
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-[#330101] text-white hover:bg-[#ffde5c] focus:outline-none focus:ring-0 focus:border-none"
                  >
                    Agreement
                    <ChevronDown className="w-3 h-3 text-gray-500 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handlePrintAgreement(booking.id)}>
                    Print Agreement
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleEmailAgreement()}>
                    Email Agreement
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="bg-[#330101] text-white hover:bg-[#ffde5c] focus:outline-none focus:ring-0 focus:border-none"
                  >
                    Receipt
                    <ChevronDown className="w-3 h-3 text-gray-500 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={() => handlePrintReceipt(booking.id)}>
                    Print Receipt
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => handleEmailReceipt()}>
                    Email Receipt
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                size="sm"
                className="bg-[#330101] text-white hover:bg-[#ffde5c]"
                onClick={() => handleViewInvoice(navigate)}
              >
                Invoice
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        {/* Outstanding Balance */}
        <div className="text-right mb-4">
          <span className="text-base text-gray-600">Outstanding Balance: </span>
          <span className="font-semibold text-lg">{booking.outstandingBalance}</span>
        </div>

        {/* Date & Time Section */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <Calendar className="h-5 w-5 mr-2" />
              Date & Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4 mb-4">
              <div>
                <Label className="text-gray-700">Pickup Date</Label>
                <p className="text-sm">{booking.pickupDate}</p>
              </div>
              <div>
                <Label className="text-gray-700">Pickup Time</Label>
                <p className="text-sm">{booking.pickupTime}</p>
              </div>
              <div>
                <Label className="text-gray-700">Return Date</Label>
                <p className="text-sm">{booking.returnDate}</p>
              </div>
              <div>
                <Label className="text-gray-700">Return Time</Label>
                <p className="text-sm">{booking.returnTime}</p>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-4">
              <div>
                <Label className="text-gray-700">Pickup Location</Label>
                <p className="text-sm">{booking.pickupLocation}</p>
              </div>
              <div>
                <Label className="text-gray-700">Return Location</Label>
                <p className="text-sm">{booking.returnLocation}</p>
              </div>
              <div>
                <Label className="text-gray-700">Branch</Label>
                <p className="text-sm">{booking.branch}</p>
              </div>
              <div>
                <Label className="text-gray-700">Reservation Type</Label>
                <p className="text-sm">{booking.reservationType}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Selected Vehicle Class */}
        <Card className="mb-6 relative">
          <CardHeader className="relative">
            <CardTitle className="flex items-center text-black">
              <Car className="h-5 w-5 mr-2" />
              Selected Vehicle Class
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row md:items-start md:space-x-4 space-y-4 md:space-y-0">
              <div className="w-full md:w-32 h-auto md:h-24 flex items-center justify-center">
                <img
                  src={economyCar}
                  alt="Vehicle Class"
                  className="object-contain w-full h-auto max-h-24"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2">{booking.vehicle.class}</h3>
                <h4 className="text-base text-gray-600 mb-2">{booking.vehicle.category}</h4>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    {booking.vehicle.features.map((feature, index) => (
                      <p key={index} className="text-gray-600">{feature}</p>
                    ))}
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div
                  className={`absolute top-0 right-0 w-auto px-2 xs:px-2 sm:px-3 md:px-3 lg:px-4 xl:px-4 py-1 text-xs xs:text-xs sm:text-sm md:text-sm lg:text-base xl:text-base font-medium ${booking.loanVehicle === 'Yes' ? 'bg-green-600' : 'bg-red-600 hover:bg-red-700'} text-white`}
                >
                  {booking.loanVehicle === 'Yes' ? 'Loan Vehicle' : 'No Loan Vehicle'}
                </div>
                <div className="text-lg font-bold">{booking.vehicle.price}</div>
                <p className="text-xs text-gray-600 max-w-40">{booking.vehicle.priceNote}</p>
                <div className="mt-2">
                  <span className="text-sm text-gray-600">Vehicle: </span>
                  <span className="font-medium">{booking.vehicle.vehicleId}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Protection and Coverages */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <Shield className="h-5 w-5 mr-2" />
              Protection and Coverages
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {booking.protections.map((protection, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b last:border-b-0">
                  <span className="text-sm">{protection.name}</span>
                  <span className="text-sm font-medium">{protection.price}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Equipment & Services */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <Baby className="h-5 w-5 mr-2" />
              Equipment & Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {booking.equipment.map((item, index) => (
                <div key={index} className="flex justify-between items-center py-2">
                  <span className="text-sm">{item.name}</span>
                  <span className="text-sm font-medium text-green-600">{item.price}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <User className="h-5 w-5 mr-2" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <div>
                  <Label className="text-gray-700">Customer Type</Label>
                  <p className="text-sm">{booking.customer.cooperate}</p>
                </div>
              </div>
              <div>
                  <Label className="text-gray-700">Walk In</Label>
                  <p className="text-sm">{booking.customer.walkIn}</p>
                </div>
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <Label className="text-gray-700">First Name</Label>
                  <p className="text-sm">{booking.customer.firstName}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Last Name</Label>
                  <p className="text-sm">{booking.customer.lastName}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Phone Number</Label>
                  <p className="text-sm">{booking.customer.phone}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Email Address</Label>
                  <p className="text-sm">{booking.customer.email}</p>
                </div>
              </div>
              <div className="grid grid-cols-4 gap-4">
                <div>
                  <Label className="text-gray-700">Address</Label>
                  <p className="text-sm">{booking.customer.address}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Postcode</Label>
                  <p className="text-sm">{booking.customer.postcode}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Country</Label>
                  <p className="text-sm">{booking.customer.country}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Birthday</Label>
                  <p className="text-sm">{booking.customer.birthday}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Extra Information */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <FileText className="h-5 w-5 mr-2" />
              Extra Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <h4 className="font-medium mb-2">Driving License</h4>
              <div className="p-3 rounded">
                <div className="grid grid-cols-4 gap-4 text-sm">
                  <div>
                    <Label className="text-gray-700">Type</Label>
                    <p>{booking.license.type}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700">ID Number</Label>
                    <p>{booking.license.idNumber}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700">Issue Date</Label>
                    <p>{booking.license.issueDate}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700">Expire Date</Label>
                    <p>{booking.license.expireDate}</p>
                  </div>
                </div>
                <div className="mt-2">
                  <Label className="text-gray-700">Issue Country</Label>
                  <p className="text-sm">{booking.license.country}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Emergency Contact Details */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <Phone className="h-5 w-5 mr-2" />
              Emergency Contact Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-gray-700">Emergency Contact Person Name</Label>
                <p className="text-sm">{booking.emergency.name}</p>
              </div>
              <div>
                <Label className="text-gray-700">Emergency Contact Number</Label>
                <p className="text-sm">{booking.emergency.number}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Note */}
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">Note</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="..."
              className="focus:outline-none focus:ring-0 focus:border-none border-gray-500"
              disabled
            />
          </CardContent>
        </Card>

        {/* Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <CreditCard className="h-5 w-5 mr-2" />
              Summary
            </CardTitle>
          </CardHeader>
          <div className="p-2 sm:p-4 mb-4 sm:mb-6 md:mb-8">
            <Table className="w-full">
              <TableHeader>
                <TableRow>
                  <TableHead className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2">DESCRIPTION</TableHead>
                  <TableHead className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2 hidden sm:table-cell"></TableHead>
                  <TableHead className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2 hidden md:table-cell"></TableHead>
                  <TableHead className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2">AMOUNT (AUD)</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {invoiceItems.map((item, index) => (
                  <TableRow key={index} className="border-b border-gray-200">
                    <TableCell className="text-[10px] sm:text-xs md:text-sm lg:text-base py-1 sm:py-2">
                      <div>{item.description}</div>
                      {(item.rate || item.days) && (
                        <div className="text-[9px] sm:text-[10px] md:hidden">
                          <span>{item.rate} {item.days}</span>
                        </div>
                      )}
                    </TableCell>
                    <TableCell className="text-left text-[9px] sm:text-[10px] md:text-xs lg:text-sm py-1 sm:py-2 hidden sm:table-cell">
                      {item.rate && <div>{item.rate}</div>}
                    </TableCell>
                    <TableCell className="text-left text-[9px] sm:text-[10px] md:text-xs lg:text-sm py-1 sm:py-2 hidden md:table-cell">
                      {item.days && <div>{item.days}</div>}
                    </TableCell>
                    <TableCell className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 sm:py-2">{item.amount}</TableCell>
                  </TableRow>
                ))}
                <TableRow className="hidden md:table-row">
                  <TableCell className="py-1 sm:py-2" colSpan={2}></TableCell>
                  <TableCell className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Sub Total</TableCell>
                  <TableCell className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">{booking.summary.subtotal}</TableCell>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <TableCell className="py-1 sm:py-2" colSpan={2}></TableCell>
                  <TableCell className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Total GST 10%</TableCell>
                  <TableCell className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">{booking.summary.gst}</TableCell>
                </TableRow>
                <TableRow className="border-b border-gray-400 hidden md:table-row">
                  <TableCell className="py-1 sm:py-2" colSpan={2}></TableCell>
                  <TableCell className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Discount</TableCell>
                  <TableCell className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">{booking.summary.discount}</TableCell>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <TableCell className="py-1 sm:py-2" colSpan={2}></TableCell>
                  <TableCell className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Invoice Total AUD</TableCell>
                  <TableCell className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">{booking.summary.total}</TableCell>
                </TableRow>
                <TableRow className="border-b border-gray-400 hidden md:table-row">
                  <TableCell className="py-1 sm:py-2" colSpan={2}></TableCell>
                  <TableCell className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Security Deposit</TableCell>
                  <TableCell className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">{booking.summary.securityDeposit}</TableCell>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <TableCell className="py-1 sm:py-2" colSpan={2}></TableCell>
                  <TableCell className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-bold hidden md:table-cell">Amount Due AUD</TableCell>
                  <TableCell className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-bold hidden md:table-cell">{booking.summary.amountDue}</TableCell>
                </TableRow>
                {/* Mobile totals */}
                <TableRow className="md:hidden">
                  <TableCell className="py-1 sm:py-2" colSpan={4}>
                    <div className="text-[10px] sm:text-xs font-semibold">Sub Total: {booking.summary.subtotal}</div>
                    <div className="text-[10px] sm:text-xs font-semibold">Total GST 10%: {booking.summary.gst}</div>
                    <div className="text-[10px] sm:text-xs font-semibold">Discount: {booking.summary.discount}</div>
                    <div className="text-[10px] sm:text-xs font-semibold">Invoice Total AUD: {booking.summary.total}</div>
                    <div className="text-[10px] sm:text-xs font-semibold">Security Deposit: {booking.summary.securityDeposit}</div>
                    <div className="text-[10px] sm:text-xs font-bold">Amount Due AUD: {booking.summary.amountDue}</div>
                  </TableCell>
                </TableRow>
              </TableBody>
            </Table>
          </div>
          
        </Card>
      </div>
    </div>
  );
}