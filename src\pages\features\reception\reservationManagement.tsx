import React, { useState } from 'react';
import { Search, CalendarDays } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useNavigate } from 'react-router-dom';
import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { reservationData } from './common/mockData';
import { ReservationManagement } from './type/reception-type';
import {
  getFilteredDataByTabAndFilter,
  getStatusColor,
  getYesNoColor,
  handleCheckAvailability,
  handleAddNewReservation,
  handleViewBookingSummary,
  handleViewOutstanding,
  handleSearchChange,
  handleTabChange,
  handleFilterTypeChange,
} from './hook/useReservation';

export function ReceptionReservationManagement() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('All');
  const [filterType, setFilterType] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(5);

  const tabButtons = [
    'All', 'Website Bookings', "Today's Returns", "Tomorrow's Pickups",
    "Today's Pickups", "Tomorrow's Returns", 'On Rent', 'Completed',
    'Cancelled', 'Outstanding Payment'
  ];

  const tabAndFilterData = getFilteredDataByTabAndFilter(reservationData, activeTab, filterType);
  const filteredData = tabAndFilterData.filter((item: ReservationManagement) =>
    `${item.customer.firstName} ${item.customer.lastName}`.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.rentalId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.vehicle.vehicleId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const totalRecords = filteredData.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentReservations = filteredData.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  return (
    <div className="min-h-screen">
      <div className="bg-white rounded-lg">
        {/* Header */}
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <CalendarDays className="text-gray-600 w-6 h-6" />
              <h1 className="text-xl font-semibold text-gray-800">Reservation Management</h1>
            </div>
            <div className="flex items-center gap-3">
              <Button
                className="px-4 py-2 bg-white hover:bg-[#b56700] hover:text-white hover:border-none text-black border border-gray-400 rounded-md transition-colors flex items-center gap-2"
                onClick={() => handleCheckAvailability(navigate)}
              >
                <Search className="w-4 h-4" />
                Availability Check
              </Button>
              <Button
                className="px-4 py-2 bg-[#330101] hover:bg-[#b56700] text-white rounded-md transition-colors flex items-center gap-2"
                onClick={() => handleAddNewReservation(navigate)}
              >
                Add New Reservation
              </Button>
            </div>
          </div>

          {/* Tab Buttons */}
          <div className="flex flex-wrap gap-2 mb-4">
            {tabButtons.map((tab) => (
              <Button
                variant="ghost"
                key={tab}
                onClick={() => handleTabChange(tab, setActiveTab, setCurrentPage)}
                className={`px-3 py-1 rounded-md text-sm transition-colors ${
                  activeTab === tab
                    ? 'bg-white text-yellow-700 border border-[#FFE05C]'
                    : 'bg-gray-100 text-gray-700 hover:bg-[#b56700] hover:text-white'
                }`}
              >
                {tab}
              </Button>
            ))}
          </div>

          {/* Search and Filter */}
          <div className="flex items-center gap-4">
            <div className="relative">
              <Select
                value={filterType}
                onValueChange={(value) => handleFilterTypeChange(value, setFilterType, setCurrentPage)}
              >
                <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
                  <SelectValue placeholder="All" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="All">All</SelectItem>
                  <SelectItem value="Passenger">Passenger</SelectItem>
                  <SelectItem value="Commercial">Commercial</SelectItem>
                  <SelectItem value="Rental">Rental</SelectItem>
                  <SelectItem value="Completed">Completed</SelectItem>
                  <SelectItem value="Overdue">Overdue</SelectItem>
                </SelectContent>
              </Select>
            </div>

            <div className="relative flex-1">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                placeholder="Start typing here..."
                value={searchTerm}
                onChange={(e) => handleSearchChange(e.target.value, setSearchTerm, setCurrentPage)}
                className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
              />
            </div>

            <button className="px-4 py-2 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200 transition-colors">
              Save this search
            </button>
          </div>
        </div>

        {/* Table Container with Horizontal Scroll */}
        <div className="overflow-x-auto border rounded-lg">
          <Table className="w-full">
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead>Rental ID</TableHead>
                <TableHead>Customer</TableHead>
                <TableHead>Pickup Date & Time</TableHead>
                <TableHead>Return Date & Time</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Total Price</TableHead>
                <TableHead>Total Revenue</TableHead>
                <TableHead>Total Refunded</TableHead>
                <TableHead>Outstanding Balance</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Loan Vehicle</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white divide-y divide-gray-200">
              {currentReservations.length === 0 ? (
                <TableRow>
                  <TableCell colSpan={11} className="px-3 py-4 text-center text-gray-500">
                    No reservations found.
                  </TableCell>
                </TableRow>
              ) : (
                currentReservations.map((item: ReservationManagement, index: number) => (
                  <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                    <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.rentalId}</TableCell>
                    <TableCell
                      className="px-3 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer"
                      onClick={() => handleViewBookingSummary(item.rentalId, navigate)}
                    >
                      {`${item.customer.firstName} ${item.customer.lastName}`}
                    </TableCell>
                    <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{`${item.pickupDate} ${item.pickupTime}`}</TableCell>
                    <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{`${item.returnDate} ${item.returnTime}`}</TableCell>
                    <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.vehicle.vehicleId}</TableCell>
                    <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalPrice}</TableCell>
                    <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalRevenue}</TableCell>
                    <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalRefunded}</TableCell>
                    <TableCell
                      className="px-3 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer"
                      onClick={() => handleViewOutstanding(item.rentalId, navigate)}
                    >
                      {item.outstandingBalance}
                    </TableCell>
                    <TableCell className="px-3 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(item.status)}`}>
                        {item.status}
                      </span>
                    </TableCell>
                    <TableCell className="px-3 py-4 whitespace-nowrap">
                      <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getYesNoColor(item.loanVehicle)}`}>
                        {item.loanVehicle}
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalRecords}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-6"
      />
    </div>
  );
}