import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom'; 
import { ChevronDown, ArrowLeft } from 'lucide-react';
import { Label } from '@/components/ui/label';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from '@/components/ui/dropdown-menu';

interface CustomerFormData {
  customerId: string;
  firstName: string;
  lastName: string;
  address: string;
  postCode: string;
  country: string;
  email: string;
  phone: string;
  birthday: {
    day: string;
    month: string;
    year: string;
  };
  companyName: string;
  emergencyContactName: string;
  emergencyContactNumber: string;
  dlNumber: string;
  issueState: string;
  issueDate: string;
  expiryDate: string;
  conditions: string;
  password: string;
}

export function CustomerMAddNewCustomer() {
  const navigate = useNavigate(); 
  const handleSave = (): void => {
    navigate('');
  };

  const [formData, setFormData] = useState<CustomerFormData>({
    customerId: '',
    firstName: '',
    lastName: '',
    address: '',
    postCode: '',
    country: '',
    email: '',
    phone: '',
    birthday: { day: '', month: '', year: '' },
    companyName: '',
    emergencyContactName: '',
    emergencyContactNumber: '',
    dlNumber: '',
    issueState: '',
    issueDate: '',
    expiryDate: '',
    conditions: '',
    password: ''
  });

  const [frontViewFile, setFrontViewFile] = useState<File | null>(null);
  const [backViewFile, setBackViewFile] = useState<File | null>(null);

  const handleFrontViewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setFrontViewFile(file);
  };

  const handleBackViewChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0] || null;
    setBackViewFile(file);
  };

  const handleInputChange = (field: keyof CustomerFormData, value: string) => {
    setFormData(prev => ({ ...prev, [field]: value }));
  };

  const handleBirthdayChange = (field: keyof CustomerFormData['birthday'], value: string) => {
    setFormData(prev => ({
      ...prev,
      birthday: { ...prev.birthday, [field]: value }
    }));
  };

  const conditionsOptions: string[] = ['', 'Light Vehicles', 'Heavy Vehicles', 'Motorcycles'];
  const countryOptions: string[] = ['', 'Australia', 'New Zealand', 'United Kingdom', 'United Status'];
  const stateOptions: string[] = ['', 'QLD', 'NSW', 'ACT'];
  const phoneOptions: string[] = ['', 'Land'];

  return (
    <div className="min-h-screen px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
      <div className="flex items-center mb-3 xs:mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2'
          size="sm"
          onClick={() => navigate('/admin/customerMasterAdmin/customerM-customers')}
        >
          <ArrowLeft className="w-3 xs:w-4 h-3 xs:h-4 mr-1" />
          Go Back
        </Button>
      </div>
      <div className="bg-white rounded-lg shadow-sm p-3 xs:p-4 sm:p-6 md:p-8 lg:p-10">
        <h2 className="text-base xs:text-lg sm:text-xl md:text-2xl lg:text-3xl font-semibold mb-3 xs:mb-4 sm:mb-6 md:mb-8">Customer Information</h2>

        {/* Customer ID */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="customerId"
              value={formData.customerId}
              onChange={(e) => handleInputChange('customerId', e.target.value)}
              className="bg-gray-200 w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
              required
              disabled
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Customer ID
            </Label>
          </div>
        </div>

        {/* Name Fields */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.firstName}
              onChange={(e) => handleInputChange('firstName', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              First Name
            </Label>
          </div>
          <div className="relative">
            <Input
              type="text"
              value={formData.lastName}
              onChange={(e) => handleInputChange('lastName', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Last Name
            </Label>
          </div>
        </div>

        {/* Address */}
        <div className="mb-3 xs:mb-4 sm:mb-6 md:mb-8 relative">
          <Input
            type="text"
            value={formData.address}
            onChange={(e) => handleInputChange('address', e.target.value)}
            className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
            required
          />
          <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Address
          </Label>
        </div>

        {/* Post Code and Country */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.postCode}
              onChange={(e) => handleInputChange('postCode', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Post Code
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Country</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border border-gray-500 text-xs xs:text-sm sm:text-base">
                  {formData.country || 'Select Country'}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {countryOptions.map((country) => (
                  <DropdownMenuItem
                    key={country}
                    onSelect={() => handleInputChange('country', country)}
                    className={formData.country === country ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {country || 'Select Country'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Email */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="email"
              value={formData.email}
              onChange={(e) => handleInputChange('email', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Email
            </Label>
          </div>
        </div>

        {/* Phone and Birthday */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="tel"
              value={formData.phone}
              onChange={(e) => handleInputChange('phone', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Phone Number
            </Label>
          </div>
          <div className="relative">
            <Input
              type="date"
              value={`${formData.birthday.year}-${formData.birthday.month.padStart(2, '0')}-${formData.birthday.day.padStart(2, '0')}`}
              onChange={(e) => {
                const [year, month, day] = e.target.value.split('-');
                setFormData(prev => ({
                  ...prev,
                  birthday: { day, month, year }
                }));
              }}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
              required
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Birthday
            </Label>
          </div>
        </div>

        {/* Company Name */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.companyName}
              onChange={(e) => handleInputChange('companyName', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Company Name
            </Label>
          </div>
        </div>

        {/* Emergency Contact Details */}
        <h3 className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl font-semibold mb-3 xs:mb-4 sm:mb-6 md:mb-8">Emergency Contact Details</h3>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.emergencyContactName}
              onChange={(e) => handleInputChange('emergencyContactName', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Emergency Contact Person's Name
            </Label>
          </div>
          <div className="relative">
            <div className="flex">
              <div className="relative">
                <div className="relative">
                  <DropdownMenu>
                    <DropdownMenuTrigger asChild>
                      <Button variant="outline" className="w-full justify-between border border-gray-500 text-xs xs:text-sm sm:text-base">
                        {formData.phone || 'Mobile'}
                        <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                      </Button>
                    </DropdownMenuTrigger>
                    <DropdownMenuContent className="w-full">
                      {phoneOptions.map((phone) => (
                        <DropdownMenuItem
                          key={phone}
                          onSelect={() => handleInputChange('phone', phone)}
                          className={formData.phone === phone ? 'bg-amber-100 font-semibold' : ''}
                        >
                          {phone || 'Mobile'}
                        </DropdownMenuItem>
                      ))}
                    </DropdownMenuContent>
                  </DropdownMenu>
                </div>
              </div>
              <Input
                type="tel"
                value={formData.emergencyContactNumber}
                onChange={(e) => handleInputChange('emergencyContactNumber', e.target.value)}
                className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-r-md border-l-0 focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
              />
            </div>
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Emergency Contact Number
            </Label>
          </div>
        </div>

        {/* Driver's License */}
        <h3 className="text-sm xs:text-base sm:text-lg md:text-xl lg:text-2xl font-semibold mb-3 xs:mb-4 sm:mb-6 md:mb-8">Driver's License</h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="text"
              value={formData.dlNumber}
              onChange={(e) => handleInputChange('dlNumber', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              DL Number
            </Label>
          </div>
          <div className="relative">
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Issue State</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full justify-between border border-gray-500 text-xs xs:text-sm sm:text-base">
                  {formData.issueState || 'Select state'}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {stateOptions.map((issueState) => (
                  <DropdownMenuItem
                    key={issueState}
                    onSelect={() => handleInputChange('issueState', issueState)}
                    className={formData.issueState === issueState ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {issueState || 'Select state'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Input
              type="date"
              value={formData.issueDate}
              onChange={(e) => handleInputChange('issueDate', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Issue Date
            </Label>
          </div>
          
          <div className="relative">
            <Input
              type="date"
              value={formData.expiryDate}
              onChange={(e) => handleInputChange('expiryDate', e.target.value)}
              className="w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
            />
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Expiry Date
            </Label>
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">Conditions</Label>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <Button variant="outline" className="w-full border border-gray-500 justify-between text-xs xs:text-sm sm:text-base">
                  {formData.conditions || 'Select Conditions'}
                  <ChevronDown className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-gray-500" />
                </Button>
              </DropdownMenuTrigger>
              <DropdownMenuContent className="w-full">
                {conditionsOptions.map((condition) => (
                  <DropdownMenuItem
                    key={condition}
                    onSelect={() => handleInputChange('conditions', condition)}
                    className={formData.conditions === condition ? 'bg-amber-100 font-semibold' : ''}
                  >
                    {condition || 'Select Conditions'}
                  </DropdownMenuItem>
                ))}
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Upload Files */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
          <div className="relative">
            <div className="flex">
              <Input
                type="text"
                value={frontViewFile?.name || 'No file selected'}
                readOnly
                className="flex-1 p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-l-md bg-gray-50 text-xs xs:text-sm sm:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
              />
              <Label className="flex items-center px-2 xs:px-3 sm:px-4 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                <span className="text-xs xs:text-sm sm:text-base">Choose File</span>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleFrontViewChange}
                  className="hidden"
                />
              </Label>
            </div>
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Upload Front View
            </Label>
          </div>

          <div className="relative">
            <div className="flex">
              <Input
                type="text"
                value={backViewFile?.name || 'No file selected'}
                readOnly
                className="flex-1 p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-l-md bg-gray-50 text-xs xs:text-sm sm:text-base focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
              />
              <Label className="flex items-center px-2 xs:px-3 sm:px-4 bg-gray-200 border border-gray-300 border-l-0 rounded-r-md hover:bg-gray-300 transition-colors cursor-pointer">
                <span className="text-xs xs:text-sm sm:text-base">Choose File</span>
                <Input
                  type="file"
                  accept="image/*"
                  onChange={handleBackViewChange}
                  className="hidden"
                />
              </Label>
            </div>
            <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
              Upload Back View
            </Label>
          </div>
        </div>

        {/* Password */}
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
        <div className="relative">
          <Input
            type="text"
            value={formData.password}
            onChange={(e) => handleInputChange('password', e.target.value)}
            className="bg-gray-200 w-full p-1 xs:p-2 sm:p-3 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-xs xs:text-sm sm:text-base"
            disabled
          />
          <Label className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 text-xs xs:text-sm sm:text-base text-gray-500 bg-white px-1 transition-all duration-200 pointer-events-none">
            Password
          </Label>
        </div>
        </div>

        <div className="flex justify-end">
          <Button 
            onClick={handleSave}
            className="px-2 xs:px-3 sm:px-4 py-1 xs:py-2 bg-[#330101] text-white rounded-md transition-colors text-xs xs:text-sm sm:text-base"
          >
            Save Customer
          </Button>
        </div>
      </div>
    </div>
  );
}