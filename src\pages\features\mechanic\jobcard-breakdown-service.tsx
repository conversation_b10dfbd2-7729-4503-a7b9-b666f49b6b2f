import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { But<PERSON> } from '../../../components/ui/button';
import { Card } from '../../../components/ui/card';
import { Input } from '../../../components/ui/input';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../components/ui/table';
import { Badge } from '../../../components/ui/badge';
import { Search, Edit, Eye, ArrowLeft } from 'lucide-react';
import { Pagination } from '../../../components/layout/Pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select';
import { useJobcardBreakdownService } from './hook/usejobcard-breakdown-service';

export function JobcardBreakdownService() {
  const navigate = useNavigate();
  const {
    jobCards,
    getStatusColor
  } = useJobcardBreakdownService();

  // For dropdown filter options (mocked from jobCards)
  const vehicleClassOptions = [
    "All",
    ...Array.from(new Set(jobCards.map(card => card.vehicleClass)))
  ];

  const [filterVehicleClass, setFilterVehicleClass] = useState<string>('All');
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Filter job cards based on dropdown and search
  const filteredJobCards = jobCards.filter(card => {
    const matchesSearch =
      (card.rego?.toLowerCase() ?? '').includes(searchTerm.toLowerCase()) ||
      (card.vehicleClass?.toLowerCase() ?? '').includes(searchTerm.toLowerCase());
    const matchesFilter =
      filterVehicleClass === 'All' || card.vehicleClass === filterVehicleClass;
    return matchesSearch && matchesFilter;
  });

  const totalRecords = filteredJobCards.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentData = filteredJobCards.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const handleAddClick = () => {
    navigate('/mechanic/jobcard-breakdown-service-add');
  };

  // Responsive Card for Mobile/Tablet (xs, sm, md)
  const JobCard = ({ card }: { card: any }) => (
    <Card className="relative mb-4 p-4 shadow-md border border-gray-200 rounded-lg">
      {/* Status badge right aligned */}
      <div className="flex justify-between items-start mb-2">
        <div>
          <div className="font-medium text-base">{card.rego}</div>
        </div>
        {/* You can add a status badge here if needed */}
      </div>
      {/* Stacked info for mobile: Vehicle Class, Repaired By, Repair Completion Date */}
      <div className="flex flex-col gap-1 text-xs mb-2">
        <div>
          <span className="font-medium">Vehicle Class:</span> {card.vehicleClass}
        </div>
        <div>
          <span className="font-medium">Repaired By:</span> {card.repairedBy}
        </div>
        <div>
          <span className="font-medium">Repair Completion:</span> {card.repairCompletionDate}
        </div>
      </div>
      {/* Actions at the bottom */}
      <div className="flex justify-end gap-2 pt-3 border-t border-gray-100">
        <Button
          onClick={() => navigate(`/mechanic/jobcard-breakdown-service-view/${card.id}`)}
          variant="ghost"
          size="sm"
          className="text-gray-600 hover:text-gray-800 flex items-center"
        >
          <Eye className="w-4 h-4 mr-1" />
          View
        </Button>
        <Button
          onClick={() => navigate(`/mechanic/jobcard-breakdown-service-edit/${card.id}`)}
          variant="ghost"
          size="sm"
          className="text-gray-600 hover:text-gray-800 flex items-center"
        >
          <Edit className="w-4 h-4 mr-1" />
          Edit
        </Button>
      </div>
    </Card>
  );

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Mobile Back Button */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/mechanic/breakdown-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>
      {/* Desktop Back Button */}
      <Button
        className="hidden md:flex bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base items-center mb-4"
        size="sm"
        onClick={() => navigate('/mechanic/breakdown-service')}
      >
        <ArrowLeft className="h-4 w-4 mr-1" />
        <span className="hidden md:inline">Go Back</span>
      </Button>
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Job Card - Breakdown Service</h1>
        </div>
        <Button
          onClick={handleAddClick}
          className="w-full md:w-32 bg-[#330101] hover:bg-[#ffde5c] text-white px-4 py-2 md:px-8 md:py-2 mb-4 md:mb-0"
        >
          Add
        </Button>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 sm:mb-6">
        <div className="w-full sm:w-auto">
          <Select
            value={filterVehicleClass}
            onValueChange={value => {
              setFilterVehicleClass(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="h-12 w-full border border-gray-200 text-sm focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              {vehicleClassOptions.map(option => (
                <SelectItem key={option} value={option} className="text-sm">{option}</SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>
        <div className="relative w-full sm:flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={e => {
              setSearchTerm(e.target.value);
              setCurrentPage(1);
            }}
            className="w-full pl-10 pr-4 py-2 h-12 text-sm focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        <Button className="w-full sm:w-auto h-12 text-sm bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Responsive Card View for xs, sm, md */}
      <div className="block lg:hidden">
        {filteredJobCards.length > 0 ? (
          filteredJobCards.map((card) => (
            <JobCard key={card.id} card={card} />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            No breakdown service data available
          </div>
        )}
      </div>

      {/* Desktop Table View for lg and up */}
      <div className="hidden lg:block">
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="text-sm font-medium px-3 py-2">Vehicle Class</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Rego</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Repaired By</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Repair Completion Date</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length > 0 ? (
                currentData.map((card) => (
                  <TableRow key={card.id}>
                    <TableCell className="text-sm px-3 py-2">{card.vehicleClass}</TableCell>
                    <TableCell className="text-sm px-3 py-2">{card.rego}</TableCell>
                    <TableCell className="text-sm px-3 py-2">{card.repairedBy}</TableCell>
                    <TableCell className="text-sm px-3 py-2">{card.repairCompletionDate}</TableCell>
                    <TableCell className="text-sm px-3 py-2">
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-1 h-6 w-6 text-gray-600 hover:text-gray-800 transition-colors"
                          onClick={() => navigate(`/mechanic/jobcard-breakdown-service-view/${card.id}`)}
                          title="View"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-1 h-6 w-6 text-gray-600 hover:text-gray-800 transition-colors"
                          onClick={() => navigate(`/mechanic/jobcard-breakdown-service-edit/${card.id}`)}
                          title="Edit"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={5} className="text-center py-8 text-gray-500">
                    No breakdown service data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* Pagination */}
        <div className="mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="text-sm sm:text-base"
          />
        </div>
      </div>
    </div>
  );
  
}