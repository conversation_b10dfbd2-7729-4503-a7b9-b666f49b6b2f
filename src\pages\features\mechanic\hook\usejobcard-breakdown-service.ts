import { useState } from "react";
import { JobCardBreakdownServiceData, CustomerInfo, JobCardFilters } from "../type/mechanictype";

// Mock data
const mockJobCards: JobCardBreakdownServiceData[] = [
  {
    id: "1",
    vehicleClass: "Economy",
    rego: "YT1 230",
    breakdownDate: "20/07/2025",
    repairedBy: "<PERSON>",
    repairCompletionDate: "21/07/2025",
    status: "Completed"
  },
  {
    id: "2",
    vehicleClass: "SUV",
    rego: "AB2 456",
    breakdownDate: "18/07/2025",
    repairedBy: "<PERSON>",
    repairCompletionDate: "19/07/2025",
    status: "In Progress"
  },
];

const mockCustomerInfo: CustomerInfo = {
  name: "<PERSON>",
  address: "123 Main St",
  city: "Sydney",
  phone: "0400 123 456",
  email: "<EMAIL>"
};

const initialFilters: JobCardFilters = {
  searchTerm: "",
};

export function useJobcardBreakdownService() {
  const [jobCards] = useState<JobCardBreakdownServiceData[]>(mockJobCards);
  const [customerInfo] = useState<CustomerInfo>(mockCustomerInfo);
  const [filters, setFilters] = useState<JobCardFilters>(initialFilters);

  const updateFilters = (newFilters: Partial<JobCardFilters>) => {
    setFilters(prev => ({ ...prev, ...newFilters }));
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case "Completed":
        return "bg-green-100 text-green-700";
      case "In Progress":
        return "bg-yellow-100 text-yellow-700";
      case "Pending":
        return "bg-gray-100 text-gray-700";
      default:
        return "bg-gray-100 text-gray-700";
    }
  };

  // Filter job cards by search term
  const filteredJobCards = jobCards.filter(card =>
    card.rego.toLowerCase().includes(filters.searchTerm.toLowerCase()) ||
    card.vehicleClass.toLowerCase().includes(filters.searchTerm.toLowerCase())
  );

  return {
    jobCards: filteredJobCards,
    customerInfo,
    filters,
    updateFilters,
    getStatusColor
  };
}