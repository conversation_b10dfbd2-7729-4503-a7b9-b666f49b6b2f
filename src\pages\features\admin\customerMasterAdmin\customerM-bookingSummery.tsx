import React from 'react';
import { ArrowLeft, Car, Shield, Baby, FileText, Phone, Calendar, MapPin, CreditCard, Clock, User, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useParams, useNavigate } from 'react-router-dom';
import { Textarea } from '@/components/ui/textarea';
import { Table, TableBody, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import economyCar  from '@/assets/economyCar.png';
import { Label } from '@/components/ui/label';

export function CustomerMBookingSummery() {
  const { name } = useParams<{ name: string }>();
  const navigate = useNavigate();
  
  // Mock function to simulate getting booking data
  const getBookingById = (id: string) => {
    return {
      id: id,
      outstandingBalance: 594.00,
      pickupDate: '2024-01-15',
      pickupTime: '10:00 AM',
      returnDate: '2024-01-16',
      returnTime: '10:00 AM',
      vehicle: 'Toyota Camry',
      totalPrice: 532.52,
      loanVehicle: 'Yes'
    };
  };

  const invoiceItems = [
    { description: 'Eco Plus Car', rate: 'AUD 38.18 Per Day', days: '1 Day', amount: '38.18' },
    { description: 'Bond Compulsory - 500', rate: '', days: '', amount: '500.00' },
    { description: 'Insurance', rate: '', days: '', amount: '13.64' },
    { description: 'Child Seat', rate: 'AUD 36.36 Per Day', days: '1 Day', amount: '36.36' },
  ];
  
  // Fetch booking data by ID
  const booking = getBookingById(name || '1');
  
  const handlePrintAgreement = () => {
    window.open(`/customer/booking-summary/agreement/${name}`, '_blank');
  };

  const handleEmailAgreement = () => {
    // Simulate email functionality
    alert('Agreement will be sent to the customer email address.');
  };

  const handlePrintReceipt =() =>{
    window.open(`/customer/booking-summary/receipt/${name}`, '_blank');
  }

  const handleEmailReceipt = () => {
    alert('Receipt will be sent to the customer email address.');
  };
  
  if (!booking) {
    return (
      <div className="min-h-screen flex items-center justify-center px-2 xs:px-3 sm:px-4">
        <div className="text-center">
          <h1 className="text-lg xs:text-xl sm:text-2xl font-bold text-gray-900 mb-3 xs:mb-4 sm:mb-6">Booking Not Found</h1>
          <Button
            onClick={() => navigate('/booking-history')}
            className="text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
          >
            <ArrowLeft className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
            Back to Booking History
          </Button>
        </div>
      </div>
    );
  }
  
  const bookingData = {
    id: booking.id,
    outstandingBalance: `AUD${booking.outstandingBalance.toFixed(2)}`,
    pickupDate: booking.pickupDate,
    pickupTime: booking.pickupTime,
    returnDate: booking.returnDate,
    returnTime: booking.returnTime,
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    branch: 'Lion Car Rental',
    reservationType: 'Short Term',
    vehicle: {
      class: 'Eco Plus Car',
      features: ['Automatic Transmission', 'Air-conditioning', 'Fuel: 92 Petrol', 'Power Steering'],
      doors: '5 Doors',
      vehicleId: booking.vehicle,
      price: `AUD ${(booking.totalPrice / 14).toFixed(2)}`, 
      priceNote: `AUD ${(booking.totalPrice / 14).toFixed(2)} (Incl GST) Daily excluding GST 10%`
    },
    protections: [
      { name: 'Bond Compulsory - 500 (Mandatory)', price: 'AUD 500.00 One Time' },
      { name: 'Insurance-15', price: 'Yes AUD 15.00 One Time' }
    ],
    equipment: [
      { name: 'Child Seat', price: 'Yes AUD$40.00/Day' }
    ],
    customer: {
      type: 'Cooperate Customer',
      firstName: 'Pradeep',
      lastName: 'Testing',
      email: '<EMAIL>',
      phone: '0411 111 111',
      address: '2185 Hume Hwy',
      postcode: '2091',
      country: 'Australia',
      birthday: '02/01/1960'
    },
    license: {
      type: 'Class C (Car License)',
      idNumber: '12345678',
      issueDate: '20/04/2024',
      expireDate: '20/04/2028',
      country: 'Australia'
    },
    emergency: {
      name: 'Rose Perera',
      number: '0412 456 789'
    },
    summary: {
      ecoPlus: { description: 'Eco Plus Car', amount: 'AUD 38.18', days: '1 Day' },
      bond: { description: 'Bond Compulsory - 500', amount: 'AUD 500.00', days: '1 Day' },
      insurance: { description: 'Insurance Plus', amount: 'AUD 13.64', days: '1 Day' },
      childSeat: { description: 'Child Seat', amount: 'AUD 36.36', days: '1 Day' },
      subtotal: '588.18',
      gst: '8.82',
      discount: '3.00',
      total: '594.00',
      securityDeposit: '500.00',
      amountDue: '594.00'
    }
  };

  return (
    <div className="min-h-screen px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
      {/* Header */}
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-3 xs:py-4 sm:py-6">
            <div className="flex items-center mb-2 sm:mb-0">
              <Button
                className="bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base flex items-center"
                size="sm"
                onClick={() => navigate('/admin/customerMasterAdmin/customerM-customers')}
              >
                <ArrowLeft className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
                <span className="hidden sm:inline">Go Back</span>
              </Button>
            </div>
            <div className="flex flex-wrap items-center gap-1 xs:gap-2 sm:gap-3">
              <Button
                size="sm"
                className="bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
                onClick={() => navigate('/admin/customerMasterAdmin/customerM-assignVehicle')}
              >
                Assign Vehicle
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
                onClick={() => navigate('/admin/customerMasterAdmin/customerM-returnVehicle')}
              >
                Return Vehicle
              </Button>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline" 
                    size="sm"
                    className="text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2 hover:bg-[#330101] hover:text-white focus:outline-none focus:ring-0 focus:border-none border-gray-300"
                  >
                    Agreement
                    <ChevronDown className="w-3 xs:w-4 h-3 xs:h-4 text-gray-500 ml-1 xs:ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handlePrintAgreement} className="text-xs xs:text-sm sm:text-base">
                    Print Agreement
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEmailAgreement} className="text-xs xs:text-sm sm:text-base">
                    Email Agreement
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2 hover:bg-[#330101] hover:text-white focus:outline-none focus:ring-0 focus:border-none border-gray-300"
                  >
                    Receipt
                    <ChevronDown className="w-3 xs:w-4 h-3 xs:h-4 text-gray-500 ml-1 xs:ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handlePrintReceipt} className="text-xs xs:text-sm sm:text-base">
                    Print Receipt
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEmailReceipt} className="text-xs xs:text-sm sm:text-base">
                    Email Receipt
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                size="sm"
                className="bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
                onClick={() => navigate('/admin/customerMasterAdmin/customerM-viewInvoice')}
              >
                Invoice
              </Button>
            </div>
          </div>
        </div>
      </div>

      <div className="max-w-7xl mx-auto px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8 py-3 xs:py-4 sm:py-6">
        {/* Date & Time Section */}
        <div className="text-right mb-3 xs:mb-4 sm:mb-6">
          <span className="text-xs xs:text-sm sm:text-base text-gray-600">Outstanding Balance: </span>
          <span className="font-semibold text-sm xs:text-base sm:text-lg">{bookingData.outstandingBalance}</span>
        </div>

        <Card className="mb-3 xs:mb-4 sm:mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">
              <Calendar className="h-4 xs:h-5 w-4 xs:w-5 mr-1 xs:mr-2" />
              Date & Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2 xs:gap-3 sm:gap-4">
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Pickup Date</Label>
                <p className="text-xs xs:text-sm">{bookingData.pickupDate}</p>
              </div>
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Pickup Time</Label>
                <p className="text-xs xs:text-sm">{bookingData.pickupTime}</p>
              </div>
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Return Date</Label>
                <p className="text-xs xs:text-sm">{bookingData.returnDate}</p>
              </div>
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Return Time</Label>
                <p className="text-xs xs:text-sm">{bookingData.returnTime}</p>
              </div>
            </div>
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 mt-2 xs:mt-3 sm:mt-4">
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Pickup Location</Label>
                <p className="text-xs xs:text-sm">{bookingData.pickupLocation}</p>
              </div>
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Return Location</Label>
                <p className="text-xs xs:text-sm">{bookingData.returnLocation}</p>
              </div>
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Branch</Label>
                <p className="text-xs xs:text-sm">{bookingData.branch}</p>
              </div>
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Reservation Type</Label>
                <p className="text-xs xs:text-sm">{bookingData.reservationType}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Selected Vehicle Class */}
        <Card className="mb-3 xs:mb-4 sm:mb-6 relative">
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">
              <Car className="h-4 xs:h-5 w-4 xs:w-5 mr-1 xs:mr-2" />
              Selected Vehicle Class
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row md:items-start md:space-x-3 sm:space-x-4 space-y-3 xs:space-y-4 md:space-y-0">
              <div className="w-full md:w-24 xs:w-28 sm:w-32 h-auto flex items-center justify-center">
                <img
                  src={economyCar}
                  alt="Vehicle Class"
                  className="object-contain w-full h-auto max-h-16 xs:max-h-20 sm:max-h-24"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-sm xs:text-base sm:text-lg md:text-xl mb-1 xs:mb-2 sm:mb-3"> {bookingData.vehicle.class}</h3>
                <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 text-xs xs:text-sm">
                  <div>
                    {bookingData.vehicle.features.map((feature, index) => (
                      <p key={index} className="text-gray-600">{feature}</p>
                    ))}
                  </div>
                  <div>
                    <p className="text-gray-600">{bookingData.vehicle.doors}</p>
                  </div>
                </div>
              </div>
              <div className="text-right relative">
                <div
                  className={`absolute top-0 right-0 w-auto px-2 xs:px-3 sm:px-4 py-1 text-[10px] xs:text-xs sm:text-sm font-medium ${booking.loanVehicle === 'Yes' ? 'bg-green-600' : 'bg-red-600 hover:bg-red-700'} text-white`}
                >
                  {booking.loanVehicle === 'Yes' ? 'Loan Vehicle' : 'No Loan Vehicle'}
                </div>
                <div className="text-sm xs:text-base sm:text-lg md:text-xl font-bold">{bookingData.vehicle.price}</div>
                <p className="text-[10px] xs:text-xs sm:text-sm text-gray-600 max-w-32 xs:max-w-36 sm:max-w-40">{bookingData.vehicle.priceNote}</p>
                <div className="mt-1 xs:mt-2 sm:mt-3">
                  <span className="text-xs xs:text-sm sm:text-base text-gray-600">Vehicle: </span>
                  <span className="font-medium text-xs xs:text-sm sm:text-base">{bookingData.vehicle.vehicleId}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Protection and Coverages */}
        <Card className="mb-3 xs:mb-4 sm:mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">
              <Shield className="h-4 xs:h-5 w-4 xs:w-5 mr-1 xs:mr-2" />
              Protection and Coverages
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 xs:space-y-3">
              {bookingData.protections.map((protection, index) => (
                <div key={index} className="flex justify-between items-center py-1 xs:py-2 border-b last:border-b-0">
                  <span className="text-xs xs:text-sm">{protection.name}</span>
                  <span className="text-xs xs:text-sm font-medium">{protection.price}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Equipment & Services */}
        <Card className="mb-3 xs:mb-4 sm:mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">
              <Baby className="h-4 xs:h-5 w-4 xs:w-5 mr-1 xs:mr-2" />
              Equipment & Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-2 xs:space-y-3">
              {bookingData.equipment.map((item, index) => (
                <div key={index} className="flex justify-between items-center py-1 xs:py-2">
                  <span className="text-xs xs:text-sm">{item.name}</span>
                  <span className="text-xs xs:text-sm font-medium text-green-600">{item.price}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>

        {/* Customer Information */}
        <Card className="mb-3 xs:mb-4 sm:mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">
              <User className="h-4 xs:h-5 w-4 xs:w-5 mr-1 xs:mr-2" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3 xs:space-y-4">
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Cooperate Customer</Label>
                <p className="text-xs xs:text-sm">{bookingData.customer.type}</p>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                <div>
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">First Name</Label>
                  <p className="text-xs xs:text-sm">{bookingData.customer.firstName}</p>
                </div>
                <div>
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Last Name</Label>
                  <p className="text-xs xs:text-sm">{bookingData.customer.lastName}</p>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                <div>
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Email Address</Label>
                  <p className="text-xs xs:text-sm">{bookingData.customer.email}</p>
                </div>
                <div>
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Phone Number</Label>
                  <p className="text-xs xs:text-sm">{bookingData.customer.phone}</p>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                <div>
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Address</Label>
                  <p className="text-xs xs:text-sm">{bookingData.customer.address}</p>
                </div>
                <div>
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Postcode</Label>
                  <p className="text-xs xs:text-sm">{bookingData.customer.postcode}</p>
                </div>
              </div>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                <div>
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Country</Label>
                  <p className="text-xs xs:text-sm">{bookingData.customer.country}</p>
                </div>
                <div>
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Birthday</Label>
                  <p className="text-xs xs:text-sm">{bookingData.customer.birthday}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Extra Information */}
        <Card className="mb-3 xs:mb-4 sm:mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">
              <FileText className="h-4 xs:h-5 w-4 xs:w-5 mr-1 xs:mr-2" />
              Extra Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-3 xs:mb-4 sm:mb-6">
              <h4 className="font-medium text-sm xs:text-base sm:text-lg mb-1 xs:mb-2 sm:mb-3">Driving License</h4>
              <div className="p-2 xs:p-3 sm:p-4 rounded">
                <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 text-xs xs:text-sm">
                  <div>
                    <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Type</Label>
                    <p>{bookingData.license.type}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">ID Number</Label>
                    <p>{bookingData.license.idNumber}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Issue Date</Label>
                    <p>{bookingData.license.issueDate}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Expire Date</Label>
                    <p>{bookingData.license.expireDate}</p>
                  </div>
                </div>
                <div className="mt-2 xs:mt-3 sm:mt-4">
                  <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Issue Country</Label>
                  <p className="text-xs xs:text-sm">{bookingData.license.country}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Emergency Contact Details */}
        <Card className="mb-3 xs:mb-4 sm:mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">
              <Phone className="h-4 xs:h-5 w-4 xs:w-5 mr-1 xs:mr-2" />
              Emergency Contact Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Emergency Contact Person Name</Label>
                <p className="text-xs xs:text-sm">{bookingData.emergency.name}</p>
              </div>
              <div>
                <Label className="text-gray-700 text-xs xs:text-sm sm:text-base">Emergency Contact Number</Label>
                <p className="text-xs xs:text-sm">{bookingData.emergency.number}</p>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Note */}
        <Card className="mb-3 xs:mb-4 sm:mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">Note</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea        
              placeholder="..."
              className="focus:outline-none focus:ring-0 focus:border-none border-gray-500 text-xs xs:text-sm sm:text-base"
              disabled
            />
          </CardContent>
        </Card>

        {/* Summary */}
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-black text-sm xs:text-base sm:text-lg md:text-xl">
              <CreditCard className="h-4 xs:h-5 w-4 xs:w-5 mr-1 xs:mr-2" />
              Summary
            </CardTitle>
          </CardHeader>
          {/* Table */}
          <div className="p-2 xs:p-3 sm:p-4 mb-3 xs:mb-4 sm:mb-6 md:mb-8">
            <Table className="w-full">
              <TableHeader>
                <tr>
                  <th className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base font-bold py-1 xs:py-2">DESCRIPTION</th>
                  <th className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base font-bold py-1 xs:py-2 hidden sm:table-cell"></th>
                  <th className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base font-bold py-1 xs:py-2 hidden md:table-cell"></th>
                  <th className="text-right text-[10px] xs:text-xs sm:text-sm md:text-base font-bold py-1 xs:py-2">AMOUNT (AUD)</th>
                </tr>
              </TableHeader>
              <TableBody>
                {invoiceItems.map((item, index) => (
                  <TableRow key={index} className="border-b border-gray-200">
                    <td className="text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2">
                      <div>{item.description}</div>
                      {(item.rate || item.days) && (
                        <div className="text-[9px] xs:text-[10px] sm:text-xs md:hidden">
                          <span>{item.rate} {item.days}</span>
                        </div>
                      )}
                    </td>
                    <td className="text-left text-[9px] xs:text-[10px] sm:text-xs md:text-sm py-1 xs:py-2 hidden sm:table-cell">
                      {item.rate && <div>{item.rate}</div>}
                    </td>
                    <td className="text-left text-[9px] xs:text-[10px] sm:text-xs md:text-sm py-1 xs:py-2 hidden md:table-cell">
                      {item.days && <div>{item.days}</div>}
                    </td>
                    <td className="text-right text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2">{item.amount}</td>
                  </TableRow>
                ))}
                <TableRow className="hidden md:table-row">
                  <td className="py-1 xs:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 font-semibold hidden md:table-cell">Sub Total</td>
                  <td className="text-right text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 hidden md:table-cell">588.18</td>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <td className="py-1 xs:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 font-semibold hidden md:table-cell">Total GST 10%</td>
                  <td className="text-right text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 hidden md:table-cell">8.82</td>
                </TableRow>
                <TableRow className="border-b border-gray-400 hidden md:table-row">
                  <td className="py-1 xs:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 font-semibold hidden md:table-cell">Discount</td>
                  <td className="text-right text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 hidden md:table-cell">3.00</td>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <td className="py-1 xs:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 font-semibold hidden md:table-cell">Invoice Total AUD</td>
                  <td className="text-right text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 hidden md:table-cell">594.00</td>
                </TableRow>
                <TableRow className="border-b border-gray-400 hidden md:table-row">
                  <td className="py-1 xs:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 font-semibold hidden md:table-cell">Security Deposit</td>
                  <td className="text-right text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 hidden md:table-cell">100.00</td>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <td className="py-1 xs:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 font-bold hidden md:table-cell">Amount Due AUD</td>
                  <td className="text-right text-[10px] xs:text-xs sm:text-sm md:text-base py-1 xs:py-2 font-bold hidden md:table-cell">494.00</td>
                </TableRow>
                {/* Mobile totals */}
                <TableRow className="md:hidden">
                  <td className="py-1 xs:py-2" colSpan={4}>
                    <div className="text-[10px] xs:text-xs sm:text-sm font-semibold">Sub Total: 588.18</div>
                    <div className="text-[10px] xs:text-xs sm:text-sm font-semibold">Total GST 10%: 8.82</div>
                    <div className="text-[10px] xs:text-xs sm:text-sm font-semibold">Discount: 3.00</div>
                    <div className="text-[10px] xs:text-xs sm:text-sm font-semibold">Invoice Total AUD: 594.00</div>
                    <div className="text-[10px] xs:text-xs sm:text-sm font-semibold">Security Deposit: 100.00</div>
                    <div className="text-[10px] xs:text-xs sm:text-sm font-bold">Amount Due AUD: 494.00</div>
                  </td>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </Card>
      </div>
    </div>
  );
}