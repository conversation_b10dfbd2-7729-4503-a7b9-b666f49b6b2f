import { NavigateFunction } from 'react-router-dom';
import { useToast } from '@/components/ui/use-toast';
import { VehicleData, WorkshopData, ImageData, ServiceTypeOption } from '../type/teamleadertype';

export const handleVehicleDataChange = (
  field: keyof VehicleData,
  value: string,
  setVehicleData: React.Dispatch<React.SetStateAction<VehicleData>>
): void => {
  setVehicleData((prev) => ({
    ...prev,
    [field]: value,
  }));
};

export const handleWorkshopDataChange = (
  field: keyof WorkshopData,
  value: string | string[],
  setWorkshopData: React.Dispatch<React.SetStateAction<WorkshopData>>
): void => {
  setWorkshopData((prev) => ({
    ...prev,
    [field]: value,
  }));
};

export const handleMechanicChange = (
  value: string,
  workshopData: WorkshopData,
  setWorkshopData: React.Dispatch<React.SetStateAction<WorkshopData>>
): void => {
  const updatedMechanics = workshopData.mechanicAssigned.includes(value)
    ? workshopData.mechanicAssigned.filter((m) => m !== value)
    : [...workshopData.mechanicAssigned, value];
  handleWorkshopDataChange('mechanicAssigned', updatedMechanics, setWorkshopData);
};

export const handleImageUpload = (
  field: keyof ImageData,
  files: FileList | null,
  setImageData: React.Dispatch<React.SetStateAction<ImageData>>,
  toast: ReturnType<typeof useToast>['toast']
): void => {
  if (files && files.length > 0) {
    const validFiles = Array.from(files).filter(
      (file) => file.type.startsWith('image/') && file.size < 5 * 1024 * 1024
    );
    if (validFiles.length === 0) {
      toast({
        title: 'Error',
        description: 'Please upload valid images (max 5MB).',
        variant: 'destructive',
      });
      return;
    }
    const fileNames = validFiles.map((file) => file.name);
    setImageData((prev) => ({
      ...prev,
      [field]: fileNames,
    }));
  }
};

export const handleSubmit = (
  vehicleData: VehicleData,
  workshopData: WorkshopData,
  serviceType: ServiceTypeOption,
  imageData: ImageData,
  navigate: NavigateFunction,
  toast: ReturnType<typeof useToast>['toast']
): void => {
  const payload = {
    ...vehicleData,
    ...workshopData,
    serviceType,
    ...(serviceType === 'Accident' || serviceType === 'Damage' ? { imageData } : {}),
  };
  console.log('Saving:', payload);
  toast({
    title: 'Success',
    description: 'Changes saved successfully!',
  });
  navigate('/teamleader/service-accident');
};

export const handleCancel = (
  initialVehicleData: VehicleData,
  initialImageData: ImageData,
  initialWorkshopData: WorkshopData,
  setVehicleData: React.Dispatch<React.SetStateAction<VehicleData>>,
  setImageData: React.Dispatch<React.SetStateAction<ImageData>>,
  setWorkshopData: React.Dispatch<React.SetStateAction<WorkshopData>>,
  setChangeServiceType: React.Dispatch<React.SetStateAction<boolean>>,
  setServiceType: React.Dispatch<React.SetStateAction<ServiceTypeOption>>,
  navigate: NavigateFunction
): void => {
  setVehicleData(initialVehicleData);
  setImageData(initialImageData);
  setWorkshopData(initialWorkshopData);
  setChangeServiceType(false);
  setServiceType('');
  navigate('/teamleader/service-accident');
};