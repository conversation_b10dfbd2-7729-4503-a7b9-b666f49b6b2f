import { useState } from 'react';
import { JobCardBreakdownServiceAddForm } from '../type/mechanictype';
import { useNavigate } from 'react-router-dom';

export function useJobcardBreakdownServiceAdd() {
  const navigate = useNavigate();
  const [form, setForm] = useState<JobCardBreakdownServiceAddForm>({
    vehicleClass: '',
    rego: '',
    breakdownDate: '',
    repairedBy: '',
    repairCompletionDate: '',
    notes: {
      initialInspection: false,
      batteryBoost: false,
      towing: '',
      engineFault: false,
      engineRepair: false,
      checkRadiator: false,
      checkGPS: false,
      roadTest: false,
    },
  });

  const handleInputChange = (field: keyof JobCardBreakdownServiceAddForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const handleCheckboxChange = (note: keyof typeof form.notes) => {
    setForm(prev => ({
      ...prev,
      notes: { ...prev.notes, [note]: !prev.notes[note] }
    }));
  };

  const handleTowingChange = (value: string) => {
    setForm(prev => ({
      ...prev,
      notes: { ...prev.notes, towing: value }
    }));
  };

  const handleAdd = () => {
    // Save logic here (API or localStorage)
    navigate('/mechanic/jobcard-breakdown-service');
  };

  const handleCancel = () => {
    navigate('/mechanic/jobcard-breakdown-service');
  };

  return {
    form,
    handleInputChange,
    handleCheckboxChange,
    handleTowingChange,
    handleAdd,
    handleCancel
  };
}