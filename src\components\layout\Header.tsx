/* import React from 'react';
import { <PERSON>, User, Bell } from 'lucide-react';
import { Link } from 'react-router-dom';
export function Header() {
  return <header className="flex items-center justify-between bg-[#4A0000] text-white px-4 py-3 w-full">
      <div className="flex items-center">
        <Link to="/dashboard">
          <img src="/lion-logo.png" alt="Lion Car Rentals" className="h-12 object-contain" style={{
          objectPosition: 'left',
          objectFit: 'cover',
          width: '180px'
        }} />
        </Link>
      </div>
      <div className="flex items-center space-x-6">
        <button className="hover:text-gold-light transition-colors">
          <Search className="h-6 w-6" />
        </button>
        <button className="hover:text-gold-light transition-colors">
          <User className="h-6 w-6" />
        </button>
        <button className="hover:text-gold-light transition-colors">
          <Bell className="h-6 w-6" />
        </button>
      </div>
    </header>;
} */


import React, { useState } from 'react';
import { Search, User, Bell, Menu, X } from 'lucide-react';
import { Link } from 'react-router-dom';

export function Header({ onMenuToggle, isSidebarOpen }) {
  const [isSearchOpen, setIsSearchOpen] = useState(false);

  return (
    <header className="flex items-center justify-between bg-[#4A0000] text-white px-4 py-3 w-full relative z-50">
      {/* Left section with logo and menu button */}
      <div className="flex items-center">
        {/* Mobile menu button */}
        <button 
          onClick={onMenuToggle}
          className="lg:hidden mr-3 hover:text-gold-light transition-colors"
          aria-label="Toggle menu"
        >
          {isSidebarOpen ? <X className="h-6 w-6" /> : <Menu className="h-6 w-6" />}
        </button>
        
        <Link to="/dashboard">
          <img 
            src="/lion-logo.png" 
            alt="Lion Car Rentals" 
            className="h-8 sm:h-10 lg:h-12 object-contain" 
            style={{
              objectPosition: 'left',
              objectFit: 'cover',
              width: 'auto',
              maxWidth: '180px',
            }}
          />
        </Link>
      </div>

      {/* Right section with action buttons */}
      <div className="flex items-center space-x-2 sm:space-x-4 lg:space-x-6">
        {/* Search - expandable on mobile */}
        <div className="relative">
          <button 
            onClick={() => setIsSearchOpen(!isSearchOpen)}
            className="hover:text-gold-light transition-colors"
            aria-label="Search"
          >
            <Search className="h-5 w-5 sm:h-6 sm:w-6" />
          </button>
          
          {/* Expandable search input for mobile */}
          {isSearchOpen && (
            <div className="absolute right-0 top-full mt-2 w-64 sm:w-80">
              <input
                type="text"
                placeholder="Search..."
                className="w-full px-3 py-2 bg-white text-black rounded shadow-lg border focus:outline-none focus:ring-2 focus:ring-gold-light"
                autoFocus
                onBlur={() => setIsSearchOpen(false)}
              />
            </div>
          )}
        </div>

        <button 
          className="hover:text-gold-light transition-colors"
          aria-label="User profile"
        >
          <User className="h-5 w-5 sm:h-6 sm:w-6" />
        </button>
        
        <button 
          className="hover:text-gold-light transition-colors relative"
          aria-label="Notifications"
        >
          <Bell className="h-5 w-5 sm:h-6 sm:w-6" />
          {/* Optional notification dot */}
          <span className="absolute -top-1 -right-1 h-2 w-2 bg-red-500 rounded-full"></span>
        </button>
      </div>
    </header>
  );
}