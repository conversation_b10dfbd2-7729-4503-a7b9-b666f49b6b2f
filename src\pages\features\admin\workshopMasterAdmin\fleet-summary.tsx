import React, { useState } from 'react';
import { Search, CarFront } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import { Card } from '@/components/ui/card';
import { FleetSummaryData } from '../../teamleader/type/teamleadertype';

export function FleetSummaryPage() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data - replace with actual data source
  const sampleData: FleetSummaryData[] = [
    {
      id: '1',
      category: 'Passenger',
      vehicle: 'Civic Hybrid Sedan - 1PX 12R',
      lastServiceType: 'General Maintenance',
      lastOdometer: 170333,
      status: 'Pending',
      mechanicAssigned: '-'
    },
    {
      id: '2',
      category: 'Passenger',
      vehicle: 'Atom - PCR 455',
      lastServiceType: 'Breakdown',
      lastOdometer: 176394,
      status: 'In Progress',
      mechanicAssigned: 'Mike Smith'
    },
    {
      id: '3',
      category: 'Commercial',
      vehicle: 'Ford Mustang Match - 1PY 2TR',
      lastServiceType: 'Accident Repair',
      lastOdometer: 172200,
      status: 'Awaiting Parts',
      mechanicAssigned: 'Jane Peter'
    },
    {
      id: '4',
      category: 'Passenger',
      vehicle: 'Atom - ASR 321',
      lastServiceType: 'Accident Repair',
      lastOdometer: 176204,
      status: 'Rentable',
      mechanicAssigned: '-'
    },
  ];

  // Filter data based on search term and filter status
  const filteredData = sampleData.filter((fleet) =>
    (fleet.category.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fleet.lastServiceType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fleet.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      fleet.mechanicAssigned.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || fleet.status === filterStatus)
  );

  // Pagination (only for md and up)
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  const getStatusColor = (status: string) => {
    switch (status.toLowerCase()) {
      case 'in progress':
        return 'bg-blue-500 text-white';
      case 'awaiting parts':
        return 'bg-orange-500 text-white';
      case 'rentable':
        return 'bg-green-500 text-white';
      case 'pending':
        return 'bg-yellow-500 text-white';
      default:
        return 'bg-gray-500 text-white';
    }
  };

  // Responsive: show cards for <md, table for md+
  return (
    <div className="p-2 sm:p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-2 sm:gap-4 mb-3 sm:mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <CarFront className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
          <h1 className="text-lg sm:text-xl md:text-2xl font-bold text-earth-dark">Summary</h1>
        </div>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center gap-2 sm:gap-3 md:gap-4 mb-3 sm:mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-10 sm:h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="In Progress">In Progress</SelectItem>
              <SelectItem value="Awaiting Parts">Awaiting Parts</SelectItem>
              <SelectItem value="Rentable">Rentable</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1 w-full">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 h-10 sm:h-12 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap w-full sm:w-auto h-10 sm:h-12 text-sm">
          Save this search
        </Button>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="block md:hidden space-y-4 mb-4">
        {filteredData.length === 0 ? (
          <div className="text-center text-gray-500 py-8">No fleet records found.</div>
        ) : (
          filteredData.map((fleet) => (
            <Card
              key={fleet.id}
              className="bg-white rounded-lg shadow-md border border-gray-200 p-4 mb-4 hover:shadow-lg transition-shadow relative"
            >
              {/* Status badge - top right, fixed width */}
              <div className="absolute top-4 right-4 flex flex-col items-end gap-2">
                <span
                  className={`inline-flex items-center justify-center rounded px-3 py-1 text-xs font-semibold w-[110px] text-center ${getStatusColor(
                    fleet.status
                  )} whitespace-nowrap`}
                >
                  {fleet.status}
                </span>
              </div>
              {/* Card content */}
              <div className="flex items-center mb-3 pr-[120px]">
                <CarFront className="w-6 h-6 mr-2 text-gray-500 flex-shrink-0" />
                <span className="text-sm font-medium text-gray-700 break-words whitespace-pre-line">
                  {fleet.vehicle}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-4">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Category</div>
                  <div className="flex items-center text-sm">{fleet.category}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Last Service Type</div>
                  <div className="flex items-center text-sm">{fleet.lastServiceType}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Last Odometer</div>
                  <div className="flex items-center text-sm">{fleet.lastOdometer.toLocaleString()}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Mechanic Assigned</div>
                  <div className="flex items-center text-sm">
                    {fleet.mechanicAssigned && fleet.mechanicAssigned !== '-' ? (
                      <span>{fleet.mechanicAssigned}</span>
                    ) : (
                      <span className="text-gray-400">-</span>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          ))
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block rounded-md border bg-white overflow-x-auto">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 min-w-[100px]">Category</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 min-w-[200px]">Vehicle</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 min-w-[140px]">Last Service Type</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 min-w-[120px]">Last Odometer</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 min-w-[120px]">Status</TableHead>
              <TableHead className="text-xs lg:text-sm px-2 lg:px-4 min-w-[140px]">Mechanic Assigned</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((fleet) => (
              <TableRow key={fleet.id} className="hover:bg-gray-50 transition-colors">
                <TableCell className="px-2 lg:px-4 py-3">{fleet.category}</TableCell>
                <TableCell className="px-2 lg:px-4 py-3">{fleet.vehicle}</TableCell>
                <TableCell className="px-2 lg:px-4 py-3">{fleet.lastServiceType}</TableCell>
                <TableCell className="text-xs lg:text-sm px-2 lg:px-4 py-3">
                  <span className="text-xs lg:text-sm">
                    {fleet.lastOdometer.toLocaleString()}
                  </span>
                </TableCell>
                <TableCell>
                  <span
                    className={`inline-flex items-center justify-center w-[120px] h-[40px] rounded-md text-xs lg:text-sm font-sm  ${getStatusColor(
                      fleet.status
                    )} whitespace-nowrap`}
                  >
                    {fleet.status}
                  </span>
                </TableCell>
                <TableCell className="text-xs lg:text-sm px-2 lg:px-4 py-3">
                  {fleet.mechanicAssigned && fleet.mechanicAssigned !== '-' ? (
                    <span className="text-xs lg:text-sm">{fleet.mechanicAssigned}</span>
                  ) : (
                    <span className="text-gray-400">-</span>
                  )}
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination only for md and up */}
      <div className="mt-4 sm:mt-6 hidden md:block">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4"
        />
      </div>
    </div>
  );
}