import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Card } from '../../../components/ui/card';
import { Textarea } from '../../../components/ui/textarea';
import { ArrowLeft, AlertTriangle, ChevronDown } from 'lucide-react';
import { useJobcardBreakdownServiceView } from './hook/usejobcard-breakdown-service-view';
import { useNavigate, useParams } from 'react-router-dom';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useRef } from 'react';

export function JobcardBreakdownServiceView() {
  const {
    form,
    handleCancel
  } = useJobcardBreakdownServiceView();

  const navigate = useNavigate();
  const { id } = useParams();
  const pageRef = useRef<HTMLDivElement>(null);

  // Mock customer and job card details
  const customerDetails = [
    "Customer:",
    "01 Lion Car Rentals Somerton",
    "2/85 Hume Hwy",
    "Somerton, VIC 3062",
    "0390374447",
    "<EMAIL>"
  ];
  const jobCardDetails = [
    "Job Card Details",
    `Post Date: ${form.postDate ?? "25/07/2025"}`,
    `Job Card: ${form.jobCardNumber ?? "JC-654321"}`,
    `New Odometer / Hours: ${form.odo ?? "123456"} / ${form.hours ?? "1200"}`
  ];

  // Download handler using html2canvas + jsPDF
  const handleDownload = async () => {
    if (!pageRef.current) return;

    // Hide all buttons before capture
    const buttons = pageRef.current.querySelectorAll('button');
    buttons.forEach(btn => (btn.style.display = 'none'));

    // Optionally, scroll to top to ensure all content is visible
    window.scrollTo(0, 0);

    // Wait for UI to update
    await new Promise(res => setTimeout(res, 100));

    // Capture the page as an image
    const canvas = await html2canvas(pageRef.current, { scale: 2, useCORS: true });
    const imgData = canvas.toDataURL('image/png');

    // Restore buttons after capture
    buttons.forEach(btn => (btn.style.display = ''));

    // Create PDF (auto fit to A4, multi-page if needed)
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'px',
      format: 'a4'
    });

    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    const imgWidth = pageWidth;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    let position = 0;
    let remainingHeight = imgHeight;

    while (remainingHeight > 0) {
      pdf.addImage(
        imgData,
        'PNG',
        0,
        position ? 0 : 0,
        imgWidth,
        imgHeight
      );
      remainingHeight -= pageHeight;
      if (remainingHeight > 0) {
        pdf.addPage();
        position -= pageHeight;
      }
    }

    pdf.save('breakdown-jobcard-details.pdf');
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen" ref={pageRef}>
      {/* Back Button - Mobile only */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] flex items-center justify-center text-sm"
          size="sm"
          onClick={() => navigate('/mechanic/jobcard-breakdown-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
        {/* Request Parts & Services Buttons - Mobile only */}
        <div className="flex flex-col gap-2 mt-2">
          <Button
            className="w-full bg-[#330101] text-white px-4 py-2"
            onClick={() => navigate(`/mechanic/request-parts-add?breakdownId=${id}`)}
          >
            Request Parts
          </Button>
          <Button
            className="w-full bg-[#330101] text-white px-4 py-2 gap-2"
            onClick={() => navigate('/mechanic/request-service-add')}
          >
            Request Services
          </Button>
        </div>
      </div>
      {/* Header - Desktop only */}
      <div className="hidden md:flex items-center gap-2 justify-between mb-6">
        <div className="flex items-center gap-2">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={() => navigate('/mechanic/jobcard-breakdown-service')}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
        </div>
        <div className="flex gap-2">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-4 py-2 text-xs sm:text-sm"
            onClick={() => navigate(`/mechanic/request-parts-add?breakdownId=${id}`)}
          >
            Request Parts
          </Button>
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-4 py-2 text-xs sm:text-sm"
            onClick={() => navigate('/mechanic/request-service-add')}
          >
            Request Services
          </Button>
        </div>
      </div>

      {/* Customer and Job Card Details */}
      <div className="mb-8 flex flex-col md:flex-row md:justify-between md:items-start gap-8 text-sm text-gray-700">
        <div className="md:w-1/2 md:text-left">
          {customerDetails.map((line, idx) => (
            <div key={idx} className={idx === 0 ? "font-semibold" : ""}>{line}</div>
          ))}
        </div>
        <div className="md:w-1/2 md:text-right mt-4 md:mt-0">
          {jobCardDetails.map((line, idx) => (
            <div key={idx} className={idx === 0 ? "font-semibold" : ""}>{line}</div>
          ))}
        </div>
      </div>

      {/* Vehicle Details */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Vehicle Details</h2>
        {/* Mobile Card View */}
        <div className="block md:hidden space-y-4">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rego</span>
              <Input
                id="rego"
                value={form.rego}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 mt-2"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Class</span>
              <Input
                id="vehicleClass"
                value={form.vehicleClass}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 mt-2"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</span>
              <Textarea
                id="description"
                value={form.description || ''}
                readOnly
                className="text-sm min-h-[40px] resize-none border border-gray-300 bg-gray-100 w-full mt-2"
              />
            </div>
          </div>
        </div>
        {/* Desktop/Grid View (unchanged) */}
        <div className="hidden md:block">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="relative ">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Rego</span>
              <Input
                id="rego"
                value={form.rego}
                readOnly
                className="text-sm h-8 w-full bg-gray-100"
              />
            </div>
            <div className="relative ">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Class</span>
              <Input
                id="vehicleClass"
                value={form.vehicleClass}
                readOnly
                className="text-sm h-8 w-full bg-gray-100"
              />
            </div>
          </div>
          <div className="relative ">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</span>
            <Textarea
              id="description"
              value={form.description || ''}
              readOnly
              className="text-sm min-h-[40px] resize-none border border-gray-300 bg-gray-100 w-full"
            />
          </div>
        </div>
      </div>

      {/* Incident Details */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Incident Details</h2>
        {/* Mobile Card View */}
        <div className="block md:hidden space-y-4">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Parts Replaced</span>
              <Input
                id="partsReplaced"
                value={form.partsReplaced || ''}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 mt-2"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
              <Input
                id="repairTasksRequired"
                value={form.repairTasksRequired || ''}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 mt-2"
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
              <Input
                id="repairedBy"
                value={form.repairedBy}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 mt-2"
              />
            </div>
          </div>
        </div>
        {/* Desktop/Grid View (unchanged) */}
        <div className="hidden md:block">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="relative ">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Parts Replaced</span>
              <Input
                id="partsReplaced"
                value={form.partsReplaced || ''}
                readOnly
                className="text-sm h-8 w-full bg-gray-100"
              />
            </div>
            <div className="relative ">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
              <Input
                id="repairTasksRequired"
                value={form.repairTasksRequired || ''}
                readOnly
                className="text-sm h-8 w-full bg-gray-100"
              />
            </div>
          </div>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative ">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
              <Input
                id="repairedBy"
                value={form.repairedBy}
                readOnly
                className="text-sm h-8 w-full bg-gray-100"
              />
            </div>
          </div>
        </div>
      </div>

      {/* Comments */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Comments</h2>
        <div className="block md:hidden">
          <div className="bg-white rounded-lg shadow-sm p-4">
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comments</span>
              <Textarea
                id="notesComment"
                value={form.notesComment || ''}
                readOnly
                className="text-sm min-h-[40px] resize-none border border-gray-300 bg-gray-100 w-full mt-2"
              />
            </div>
          </div>
        </div>
        <div className="hidden md:block">
          <div className="relative">
            <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comments</span>
            <Textarea
              id="notesComment"
              value={form.notesComment || ''}
              readOnly
              className="text-sm min-h-[40px] resize-none border border-gray-300 bg-gray-100 w-full"
            />
          </div>
        </div>
      </div>

      {/* Job Card Details in sentences */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Job Card Details</h2>
        <div className="block md:hidden">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-2 text-sm text-gray-800">
            <div>
              {form.notes.initialInspection
                ? "Initial inspection at breakdown site was performed."
                : "Initial inspection at breakdown site was not performed."}
            </div>
            <div>
              {form.notes.batteryBoost
                ? "Battery boost/replacement was required."
                : "Battery boost/replacement was not required."}
            </div>
            <div>
              {form.notes.towing === 'Required'
                ? "Towing service was required."
                : form.notes.towing === 'Not Required'
                  ? "Towing service was not required."
                  : "Towing service requirement not specified."}
            </div>
            <div>
              {form.notes.engineFault
                ? "Engine fault was diagnosed."
                : "Engine fault diagnosis was not required."}
            </div>
            <div>
              {form.notes.engineRepair
                ? "Engine component repair/replacement was performed."
                : "Engine component repair/replacement was not performed."}
            </div>
            <div>
              {form.notes.checkRadiator
                ? "Radiator and coolant system were checked."
                : "Radiator and coolant system check was not required."}
            </div>
            <div>
              {form.notes.checkGPS
                ? "GPS was checked."
                : "GPS check was not required."}
            </div>
            <div>
              {form.notes.roadTest
                ? "Road test was conducted."
                : "Road test was not conducted."}
            </div>
          </div>
        </div>
        <div className="hidden md:block">
          <div className="text-sm text-gray-800 space-y-2">
            <div>
              {form.notes.initialInspection
                ? "Initial inspection at breakdown site was performed."
                : "Initial inspection at breakdown site was not performed."}
            </div>
            <div>
              {form.notes.batteryBoost
                ? "Battery boost/replacement was required."
                : "Battery boost/replacement was not required."}
            </div>
            <div>
              {form.notes.towing === 'Required'
                ? "Towing service was required."
                : form.notes.towing === 'Not Required'
                  ? "Towing service was not required."
                  : "Towing service requirement not specified."}
            </div>
            <div>
              {form.notes.engineFault
                ? "Engine fault was diagnosed."
                : "Engine fault diagnosis was not required."}
            </div>
            <div>
              {form.notes.engineRepair
                ? "Engine component repair/replacement was performed."
                : "Engine component repair/replacement was not performed."}
            </div>
            <div>
              {form.notes.checkRadiator
                ? "Radiator and coolant system were checked."
                : "Radiator and coolant system check was not required."}
            </div>
            <div>
              {form.notes.checkGPS
                ? "GPS was checked."
                : "GPS check was not required."}
            </div>
            <div>
              {form.notes.roadTest
                ? "Road test was conducted."
                : "Road test was not conducted."}
            </div>
          </div>
        </div>
      </div>

      {/* Download Button at the end */}
      <div className="flex justify-end mt-8">
        <Button
          className="bg-[#330101] text-white flex items-center gap-2"
          onClick={handleDownload}
        >
          Download Details
        </Button>
      </div>
    </div>
  );
}