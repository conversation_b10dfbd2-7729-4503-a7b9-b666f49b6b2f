import { useNavigate, useParams } from 'react-router-dom';
import { JobCardBreakdownServiceAddForm } from '../type/mechanictype';
import { useState } from 'react';

// Mock function to get job card data by id
function getJobCardById(id: string): JobCardBreakdownServiceAddForm {
  // Replace with actual data fetching logic
  return {
    rego: 'ABC123',
    vehicleClass: 'SUV',
    description: 'Breakdown at highway',
    breakdownDate: '2025-07-21',
    partsReplaced: 'Alternator',
    repairTasksRequired: 'Replace alternator, check battery',
    repairedBy: 'John <PERSON>e',
    repairCompletionDate: '2025-07-22',
    notes: {
      initialInspection: true,
      batteryBoost: true,
      towing: 'Required',
      engineFault: true,
      engineRepair: true,
      checkRadiator: false,
      checkGPS: true,
      roadTest: true,
    },
    notesComment: 'All tasks completed successfully.',
  };
}

export function useJobcardBreakdownServiceView() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // Fetch job card data by id
  const initialData = id ? getJobCardById(id) : {
    rego: '',
    vehicleClass: '',
    description: '',
    breakdownDate: '',
    partsReplaced: '',
    repairTasksRequired: '',
    repairedBy: '',
    repairCompletionDate: '',
    notes: {
      initialInspection: false,
      batteryBoost: false,
      towing: '',
      engineFault: false,
      engineRepair: false,
      checkRadiator: false,
      checkGPS: false,
      roadTest: false,
    },
    notesComment: '',
  };

  const [form] = useState<JobCardBreakdownServiceAddForm>(initialData);

  const handleCancel = () => {
    navigate('/mechanic/jobcard-breakdown-service');
  };

  return {
    form,
    handleCancel
  };
}