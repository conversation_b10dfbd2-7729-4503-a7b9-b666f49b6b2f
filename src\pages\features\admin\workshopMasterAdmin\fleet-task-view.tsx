import { useState, useEffect } from 'react'
import { use<PERSON><PERSON><PERSON>, useNavigate } from 'react-router-dom'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { ArrowLeft, Check, CalendarCheck } from 'lucide-react'

// Define interface for task data
interface FleetTaskData {
  id: string
  taskId: string
  rego: string
  lastService: string
  lastServiceDate: string
  status: string
}

export function FleetTaskViewPage() {
  const { taskId } = useParams<{ taskId: string }>()
  const navigate = useNavigate()
  const [isLoading, setIsLoading] = useState(true)
  const [taskDetails, setTaskDetails] = useState<FleetTaskData | null>(null)

  // Sample data - in a real app this would come from an API
  const sampleTasks: FleetTaskData[] = [
    {
      id: '1',
      taskId: 'TO001',
      rego: '1PX 12R',
      lastService: 'Oil Change',
      lastServiceDate: '2025-07-10',
      status: 'Rentable'
    },
    {
      id: '2',
      taskId: 'TO002',
      rego: 'PCR 455',
      lastService: 'Brake Service',
      lastServiceDate: '2025-07-08',
      status: 'Pending'
    },
    {
      id: '3',
      taskId: 'TO003',
      rego: '1PY 2TR',
      lastService: 'Full Service',
      lastServiceDate: '2025-07-06',
      status: 'Pending'
    },
    {
      id: '4',
      taskId: 'TO004',
      rego: 'ASR 321',
      lastService: 'Tire Rotation',
      lastServiceDate: '2025-07-09',
      status: 'Rentable'
    },
    {
      id: '5',
      taskId: 'TO005',
      rego: 'QBV 233',
      lastService: 'Engine Check',
      lastServiceDate: '2025-07-05',
      status: 'Pending'
    },
  ]

  useEffect(() => {
    // Simulate fetching data
    const foundTask = sampleTasks.find(task => task.taskId === taskId)
    
    if (foundTask) {
      setTaskDetails(foundTask)
    } else {
      // Initialize with default values if task not found
      setTaskDetails({
        id: '0',
        taskId: taskId || 'TO000',
        rego: 'Unknown',
        lastService: 'Unknown',
        lastServiceDate: 'Unknown',
        status: 'Unknown'
      })
    }
    setIsLoading(false)
  }, [taskId])

  const handleApproveConfirm = () => {
    if (!taskDetails) return

    console.log('Task approved for:', taskDetails.rego)
    
    // Update local state
    const updatedTask = { ...taskDetails, status: 'Rentable' }
    setTaskDetails(updatedTask)
    
    try {
      // Initialize localStorage if it doesn't exist
      let tasksData: FleetTaskData[] = []
      const savedData = localStorage.getItem('fleetTaskData')
      if (savedData) {
        tasksData = JSON.parse(savedData)
      } else {
        localStorage.setItem('fleetTaskData', JSON.stringify(sampleTasks))
        tasksData = sampleTasks
      }

      const updatedTasks = tasksData.map(task => 
        task.taskId === taskDetails.taskId 
          ? { ...task, status: 'Rentable' }
          : task
      )
      localStorage.setItem('fleetTaskData', JSON.stringify(updatedTasks))
    } catch (error) {
      console.error('Error updating task status:', error)
    }
    
    navigate('/admin/workshopMasterAdmin/fleet-task')
  }

  const handleRejectConfirm = () => {
    if (!taskDetails) return

    console.log('Task rejected for:', taskDetails.rego)
    
    // Update local state
    const updatedTask = { ...taskDetails, status: 'Pending' }
    setTaskDetails(updatedTask)
    
    try {
      let tasksData: FleetTaskData[] = []
      const savedData = localStorage.getItem('fleetTaskData')
      if (savedData) {
        tasksData = JSON.parse(savedData)
      } else {
        localStorage.setItem('fleetTaskData', JSON.stringify(sampleTasks))
        tasksData = sampleTasks
      }

      const updatedTasks = tasksData.map(task => 
        task.taskId === taskDetails.taskId 
          ? { ...task, status: 'Pending' }
          : task
      )
      localStorage.setItem('fleetTaskData', JSON.stringify(updatedTasks))
    } catch (error) {
      console.error('Error updating task status:', error)
    }
    
    navigate('/admin/workshopMasterAdmin/fleet-task')
  }

  const handleBack = () => {
    navigate('/admin/workshopMasterAdmin/fleet-task')
  }

  if (isLoading) {
    return <div className="min-h-screen p-6">Loading...</div>
  }

  if (!taskDetails) {
    return <div className="min-h-screen p-6">Task not found</div>
  }

  return (
    <div className="min-h-screen">
      <div className="p-4 sm:p-6">
        {/* Responsive Back Button */}
        <div className="mb-6">
          <Button
            className="bg-[#330101] text-white w-full sm:w-auto px-3 py-2 text-xs sm:text-sm md:text-base flex items-center justify-center"
            size="sm"
            onClick={handleBack}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            <span className="inline">Go Back</span>
          </Button>
        </div>
        <div className="flex items-center gap-2 mb-6 mt-4">
          <CalendarCheck className="w-6 h-6" />
          <h1 className="text-2xl font-semibold">Task Overview</h1>
        </div>

        <Card className="shadow-sm">
          <CardHeader>
            <CardTitle>Task Details</CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div className="flex flex-col">
                <span className="font-bold text-gray-700">Registration</span>
                <span className="text-gray-900 mt-1">{taskDetails.rego}</span>
              </div>
              <div className="flex flex-col">
                <span className="font-bold text-gray-700">Last Service Type</span>
                <span className="text-gray-900 mt-1">{taskDetails.lastService}</span>
              </div>
              <div className="flex flex-col">
                <span className="font-bold text-gray-700">Last Service Date</span>
                <span className="text-gray-900 mt-1">{taskDetails.lastServiceDate}</span>
              </div>
              <div className="flex justify-end mt-8 pt-6">
                <AlertDialog>
                  <AlertDialogTrigger asChild>
                    <Button className="bg-green-500 hover:bg-green-600 text-white px-3 py-1 text-lg font-medium w-full sm:w-auto">
                      <Check className="w-5 h-5 mr-2" />
                      Approve
                    </Button>
                  </AlertDialogTrigger>
                  <AlertDialogContent className="max-w-lg w-full min-h-[200px]">
                    <AlertDialogHeader className="py-6">
                      <AlertDialogTitle className="text-xl text-center">
                        Are you sure you want to approve this vehicle?
                      </AlertDialogTitle>
                    </AlertDialogHeader>
                    <AlertDialogFooter className="py-6 gap-4 flex flex-col sm:flex-row">
                      <AlertDialogCancel 
                        onClick={handleRejectConfirm}
                        className="bg-red-500 hover:bg-red-600 text-white px-8 py-3 text-lg w-full sm:w-auto"
                      >
                        No
                      </AlertDialogCancel>
                      <AlertDialogAction 
                        onClick={handleApproveConfirm}
                        className="bg-blue-500 hover:bg-blue-600 text-white px-8 py-3 text-lg w-full sm:w-auto"
                      >
                        Yes
                      </AlertDialogAction>
                    </AlertDialogFooter>
                  </AlertDialogContent>
                </AlertDialog>
              </div>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}