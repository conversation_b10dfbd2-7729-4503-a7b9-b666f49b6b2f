import { useParams, useNavigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import { JobCardAccidentRepairData } from '../type/mechanictype';

// Import images only from src/assets folder
import proof1 from '../../../assets/proof1.jpg';
import proof2 from '../../../assets/proof2.jpg';

export function useJobcardAccidentRepairView() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [formData, setFormData] = useState<JobCardAccidentRepairData>({
    id: '',
    vehicle: '',
    damageDescription: '',
    accidentDate: '',
    damageParts: '',
    repairTasks: '',
    repairedBy: '',
    repairCompletionDate: '',
  });
  const [notesComment, setNotesComment] = useState('');
  const [serviceNotes, setServiceNotes] = useState<string[]>([]);
  const [proofImages, setProofImages] = useState<string[]>([]);

  useEffect(() => {
    // Mock data for demonstration
    setFormData({
      id: id || '',
      vehicle: 'Economy - YT1 230',
      damageDescription: 'Front bumper and left headlight damaged due to a frontal collision with a pole',
      accidentDate: '25/05/2025',
      damageParts: 'Bumper, Headlight, Door',
      repairTasks: 'Part Replacement & Painting',
      repairedBy: 'John Doe',
      repairCompletionDate: '25/05/2025',
    });
    setNotesComment('');
    setServiceNotes([
      'Remove and replace damaged body panels',
      'Replace broken glass - Windscreen, Windows',
      'Repair or replace damaged lights - Headlights',
      'Replace/repair bumper - Front',
      'Replace damaged grille/fender/bonnet',
      'Road test'
    ]);
    setProofImages([proof1, proof2]);
  }, [id]);

  const handleRequestParts = () => {
    navigate('/mechanic/request-part');
  };

  const handleRequestServices = () => {
    navigate('/mechanic/request-services');
  };

  return {
    formData,
    notesComment,
    serviceNotes,
    proofImages,
    handleRequestParts,
    handleRequestServices
  };
}