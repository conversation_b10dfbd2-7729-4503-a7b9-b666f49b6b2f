import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '../../../components/ui/table'
import { Pagination } from '../../../components/layout/Pagination'
import { Input } from '../../../components/ui/input'
import { Button } from '../../../components/ui/button'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import { Search, CalendarCheck } from 'lucide-react'
import { useFleetQuality } from './hook/usefleet-quality'


export function FleetQualityPage() {
  const {
    currentPage,
    recordsPerPage,
    searchTerm,
    currentData,
    filteredTotalRecords,
    filteredTotalPages,
    setCurrentPage,
    setSearchTerm,
    handleRecordsPerPageChange,
  } = useFleetQuality()

  return (
    <div className="pt-0 px-4 pb-8 max-w-7xl mx-auto">
      {/* Title*/}
      <div className="flex items-center mb-6 sm:mb-8">
        <CalendarCheck className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
        <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Quality</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-6 mb-8 sm:mb-10">
        <div className="relative flex-1 order-3 sm:order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Start typing here..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>
          
          <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-2 sm:order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
    
    {/* Desktop Table View */}
    <div className="hidden md:block rounded-md border overflow-hidden shadow-sm">
      <div className="overflow-x-auto">
        <Table>
      <TableHeader>
        <TableRow className='bg-gray-50 uppercase'>
          <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Vehicle ID</TableHead>
          <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Rego</TableHead>
          <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Detail Clean</TableHead>
          <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Service</TableHead>
          <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Last Inspection</TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {currentData.length > 0 ? (
          currentData.map((vehicle) => (
            <TableRow key={vehicle.id}>
              <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.vehicleId}</TableCell>
              <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.rego}</TableCell>
              <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.lastDetailClean}</TableCell>
              <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.lastService}</TableCell>
              <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{vehicle.lastInspection}</TableCell>
            </TableRow>
          ))
        ) : (
          <TableRow>
            <TableCell colSpan={5} className="text-center py-8 text-gray-500">
              No fleet quality data available
            </TableCell>
          </TableRow>
        )}
      </TableBody>
    </Table>
      </div>
    </div>

    {/* Pagination */}
    <div className="mt-4 sm:mt-6">
      <Pagination
        currentPage={currentPage}
        totalPages={filteredTotalPages}
        totalRecords={filteredTotalRecords}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={handleRecordsPerPageChange}
        className="text-sm sm:text-base"
      />
    </div>
  </div>
);
}
