import { useState } from 'react';
import { initialFormData } from '../common/mockdata';
import { PasswordFormData } from '../type/customer-type';

export const useSettingHook = () => {
  const [formData, setFormData] = useState<PasswordFormData>(initialFormData);
  const [showPasswords, setShowPasswords] = useState({
    current: false,
    new: false,
    confirm: false
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const togglePasswordVisibility = (field: 'current' | 'new' | 'confirm') => {
    setShowPasswords(prev => ({
      ...prev,
      [field]: !prev[field]
    }));
  };

  const handleChangePassword = () => {
    if (formData.newPassword !== formData.confirmPassword) {
      alert('New password and confirm password do not match!');
      return;
    }
    
    if (!formData.newPassword || !formData.confirmPassword) {
      alert('Please fill in all required fields!');
      return;
    }

    console.log('Password change requested:', formData);
    alert('Password changed successfully!');
    
    // Reset form
    setFormData(initialFormData);
  };

  const handleCancel = () => {
    setFormData(initialFormData);
    setShowPasswords({
      current: false,
      new: false,
      confirm: false
    });
  };

  return {
    formData,
    setFormData,
    showPasswords,
    setShowPasswords,
    handleInputChange,
    togglePasswordVisibility,
    handleChangePassword,
    handleCancel,
  };
};