
import React, { useRef } from 'react';
import html2canvas from 'html2canvas';
import jsPDF from 'jspdf';
import { Table, TableBody, TableHeader, TableRow } from '@/components/ui/table';
import { Button } from '@/components/ui/button';
import { ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

export function ReceptionChangerequestInvoice() {
    const invoiceItems = [
        { description: 'Extension of Duration - Eco Plus Car', amount: '38.18' },
     
    ];

    const LionLogo = () => (
        <img src="/logo.png" alt="Lion Logo" className="h-12 sm:h-14 md:h-16 lg:h-16 w-auto" />
    );

    const invoiceRef = useRef(null);

    const handleDownloadInvoice = async () => {
        if (!invoiceRef.current) return;

        try {
            // Create a hidden container with fixed A4 size for PDF rendering
            const hiddenContainer = document.createElement('div');
            hiddenContainer.style.position = 'absolute';
            hiddenContainer.style.left = '-9999px';
            hiddenContainer.style.width = '794px';
            hiddenContainer.style.minHeight = '1122px';
            hiddenContainer.innerHTML = invoiceRef.current.innerHTML;

            document.body.appendChild(hiddenContainer);

            // Capture the hidden container as a canvas
            const canvas = await html2canvas(hiddenContainer, {
                scale: 2,
                useCORS: true,
            });

            const imgData = canvas.toDataURL('image/png');

            // A4 dimensions in mm
            const a4Width = 210;
            const a4Height = 297;

            // Create a new jsPDF instance
            const pdf = new jsPDF({
                orientation: 'portrait',
                unit: 'mm',
                format: 'a4',
            });

            // Add the image to the PDF, centered
            const imgWidth = canvas.width / 3.7795; // Convert pixels to mm
            const imgHeight = canvas.height / 3.7795;
            const ratio = Math.min(a4Width / imgWidth, a4Height / imgHeight);
            const scaledWidth = imgWidth * ratio;
            const scaledHeight = imgHeight * ratio;
            const xOffset = (a4Width - scaledWidth) / 2;
            const yOffset = (a4Height - scaledHeight) / 2;

            pdf.addImage(imgData, 'PNG', xOffset, yOffset, scaledWidth, scaledHeight);

            // Save the PDF
            pdf.save('Lion_invoice.pdf');

            // Clean up
            document.body.removeChild(hiddenContainer);
        } catch (error) {
            console.error('Error generating PDF:', error);
        }
    };

    const handlePrintInvoice = () => {
        window.print();
    };

    const handleSendInvoice = () => {
        // Send functionality would be implemented here
        console.log('Send invoice clicked');
    };
    const navigate = useNavigate();

    return (
        <div className="p-2 sm:p-4 md:p-6 lg:p-8 xl:p-10">
            <Button
                className=' bg-[#330101] text-white hover:bg-[#ffde5c] mb-4'
                size="sm"
                onClick={() => navigate('/reception/reception-addrequest')}>
                <ArrowLeft className="h-4 w-4 mr-1" />
                Go Back
            </Button>

            <div className="flex flex-col md:flex-row gap-2 sm:gap-4 md:gap-6 lg:gap-6 max-w-screen-xl mx-auto">
                {/* Left Side (70%) */}
                <div className="w-full md:w-[70%] flex justify-center">
                    <div
                        ref={invoiceRef}
                        className="w-full max-w-[90%] sm:max-w-[95%] md:max-w-[794px] bg-white shadow-lg"
                        style={{ aspectRatio: '210/297', minHeight: 'auto' }}
                    >
                        <div className="p-4 sm:p-6 md:p-8 border border-gray-300">
                            <div className="flex justify-between items-start mb-4 sm:mb-6 md:mb-8">
                                <div className="flex-1">
                                    <h1 className="text-[10px] sm:text-base md:text-lg lg:text-xl mb-0.5 sm:mb-1">LION RENTALS PTY LTD</h1>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">ABN: ***********</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">ACN: *********</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">2/85 Hume Hwy, Somerton, VIC, 3062</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base">03 9303 7447 | <EMAIL></p>
                                </div>
                                <div className="ml-2 sm:ml-4">
                                    <LionLogo />
                                </div>
                            </div>

                            <h2 className="text-lg sm:text-xl md:text-2xl lg:text-2xl font-bold text-center mb-4 sm:mb-6 md:mb-6">TAX INVOICE</h2>

                            <div className="flex justify-between items-start mb-4 sm:mb-6 md:mb-6">
                                <div>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base"><strong>Customer:</strong> Pradeep Testing</p>
                                </div>
                                <div className="text-right">
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1"><strong>Invoice Date:</strong> 12-06-2025</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base"><strong>Invoice No:</strong> INV-00030</p>
                                </div>
                            </div>

                            {/* Table */}
                            <div className="border-2 border-dashed border-gray-400 p-2 sm:p-4 mb-4 sm:mb-6 md:mb-8">
                                <Table className="w-full">
                                    <TableHeader>
                                        <tr className="border-b border-gray-300">
                                            <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2">DESCRIPTION</th>
                                          {/*   <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2 hidden sm:table-cell"></th>
                                            <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2 hidden md:table-cell"></th> */}
                                            <th className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2">AMOUNT (AUD)</th>
                                        </tr>
                                    </TableHeader>
                                    <TableBody>
                                        {invoiceItems.map((item, index) => (
                                            <TableRow key={index} className="border-b border-gray-200">
                                                <td className="text-[10px] sm:text-xs md:text-sm lg:text-base py-1 sm:py-2">
                                                    <div>{item.description}</div>
                                                   
                                                </td>
                                               {/*  <td className="text-left text-[9px] sm:text-[10px] md:text-xs lg:text-sm py-1 sm:py-2 hidden sm:table-cell">
                                                    {item.rate && <div>{item.rate}</div>}
                                                </td>
                                                <td className="text-left text-[9px] sm:text-[10px] md:text-xs lg:text-sm py-1 sm:py-2 hidden md:table-cell">
                                                    {item.days && <div>{item.days}</div>}
                                                </td> */}
                                                <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 sm:py-2">{item.amount}</td>
                                            </TableRow>
                                        ))}
                                        <br />
                                        <TableRow className="hidden md:table-row">
                                            <td className="py-1 sm:py-2" colSpan={2}></td>
                                            <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Sub Total</td>
                                            <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">42.00</td>
                                        </TableRow>
                                        <TableRow className="hidden md:table-row">
                                            <td className="py-1 sm:py-2" colSpan={2}></td>
                                            <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Total GST 10%</td>
                                            <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">4.20</td>
                                        </TableRow>
                                      {/*   <TableRow className="border-b border-gray-400 hidden md:table-row">
                                            <td className="py-1 sm:py-2" colSpan={2}></td>
                                            <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Discount</td>
                                            <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">3.00</td>
                                        </TableRow> */}
                                        <TableRow className="hidden md:table-row">
                                            <td className="py-1 sm:py-2" colSpan={2}></td>
                                            <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Invoice Total AUD</td>
                                            <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">46.20</td>
                                        </TableRow>
                                        <TableRow className="border-b border-gray-400 hidden md:table-row">
                                            <td className="py-1 sm:py-2" colSpan={2}></td>
                                            <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Total Net Payment AUD</td>
                                            <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">46.20</td>
                                        </TableRow>
                                        <TableRow className="hidden md:table-row">
                                            <td className="py-1 sm:py-2" colSpan={2}></td>
                                            <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-bold hidden md:table-cell">Amount Due AUD</td>
                                            <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-bold hidden md:table-cell">00.00</td>
                                        </TableRow> 
                                        <br />
                                        {/* Mobile totals */}
                                        <TableRow className="md:hidden">
                                            <td className="py-1 sm:py-2" colSpan={4}>
                                                <div className="text-[10px] sm:text-xs font-semibold">Sub Total: 42.00</div>
                                                <div className="text-[10px] sm:text-xs font-semibold">Total GST 10%: 4.20</div>
                                                <div className="text-[10px] sm:text-xs font-semibold">Discount: 3.00</div>
                                                <div className="text-[10px] sm:text-xs font-semibold">Invoice Total AUD: 46.20</div>
                                                {/* <div className="text-[10px] sm:text-xs font-semibold">Security Deposit: 100.00</div> */}
                                                <div className="text-[10px] sm:text-xs font-bold">Amount Due AUD: 494.00</div>
                                            </td>
                                        </TableRow>
                                    </TableBody>
                                </Table>
                            </div>

                            <div className="flex flex-col md:flex-row justify-between items-start mb-4 sm:mb-6 md:mb-8">
                                <div className="mb-4 md:mb-0">
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base font-bold mb-1 sm:mb-2">BANK DETAILS</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">Name: LION RENTALS PTY LTD</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">BSB: 013 511</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">Acc: 459 854 616</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1">Ref: INV-00030</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base">Due Date: 15 June 2025</p>
                                </div>
                                <div className="text-left">
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1"><strong>Customer:</strong> Pradeep Testing</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1"><strong>Invoice No:</strong> INV-00030</p>
                                    <p className="border-b border-gray-400"></p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-0.5 sm:mb-1"><strong>Amount Due:</strong> 00.00</p>
                                    <p className="text-[10px] sm:text-xs md:text-sm lg:text-base mb-1 sm:mb-3"><strong>Due Date:</strong> 15 June 2025</p>
                                    <p className="border-b border-gray-400"></p>
                                    <div className="text-[10px] sm:text-xs md:text-sm lg:text-base">
                                        <p className="mb-0.5 sm:mb-1"><strong>Amount Enclosed:</strong>
                                            <div className="border-b border-gray-400 w-24 sm:w-28 md:w-32 h-5 sm:h-6 inline-block"></div>
                                        </p>
                                        <p className="text-[9px] sm:text-[10px] md:text-xs lg:text-sm mt-0.5 sm:mt-1">Enter the amount you are paying above</p>
                                    </div>
                                </div>
                            </div>

                            
                        </div>
                    </div>
                </div>

                {/* Right Side (25%) */}
                <div className="w-full md:w-[25%] print:hidden">
                    <div className="bg-white rounded-lg p-4 sm:p-6">
                        <div className="space-y-2 sm:space-y-4 shadow-lg rounded-lg p-2 sm:p-4 text-center mt-2 sm:mt-4 min-w-[150px] sm:min-w-[200px]">
                            <p className="text-[10px] sm:text-xs md:text-sm lg:text-base font-semibold">Invoice Options</p>
                            <Button
                                onClick={handleSendInvoice}
                                className="w-full flex items-center justify-center gap-1 sm:gap-2 bg-[#EBBB4E] text-[10px] sm:text-xs md:text-sm lg:text-base text-white py-1 sm:py-2 rounded-lg hover:bg-[#EBBB44] transition duration-300"
                            >
                                Send Invoice
                            </Button>
                            <Button
                                onClick={handlePrintInvoice}
                                className="w-full bg-[#1E3A5F] flex items-center justify-center gap-1 sm:gap-2 text-[10px] sm:text-xs md:text-sm lg:text-base text-white py-1 sm:py-2 rounded-lg"
                            >
                                Print Invoice
                            </Button>
                            <Button
                                onClick={handleDownloadInvoice}
                                className="w-full bg-[#1E3A5F] flex items-center justify-center gap-1 sm:gap-2 text-[10px] sm:text-xs md:text-sm lg:text-base text-white py-1 sm:py-2 rounded-lg"
                            >
                                Download Invoice
                            </Button>
                        </div>
                    </div>
                </div>
            </div>

           
        </div>
    );
}
