import { TeamLeaderLayout } from "@/components/layout/TeamLeaderLayout"
import { FleetQualityPage } from "@/pages/features/teamleader/fleet-quality"
import { FleetSummaryPage } from "@/pages/features/teamleader/fleet-summary"
import { FleeTaskoverview } from "@/pages/features/teamleader/fleet-taskoverview"
import { FleetTaskoverviewView } from "@/pages/features/teamleader/fleet-taskoverview-view"

import { MechanicsTable } from "@/pages/features/teamleader/mechanics-table"
import { MechanicViewPage } from "@/pages/features/teamleader/mechanic-view"

import { TeamleaderDashboard } from "@/pages/features/teamleader/teamleader-dashboard"
import { Route, Routes } from "react-router-dom"


import { VendorsPage } from "@/pages/features/teamleader/vendors"
import { VendorEditPage } from "@/pages/features/teamleader/vendor-edit"
import { VendorAddPage } from "@/pages/features/teamleader/vendor-add"
import { PartsPage } from "@/pages/features/teamleader/parts"
import { PartsEditPage } from "@/pages/features/teamleader/parts-edit"
import { PartsAddPage } from "@/pages/features/teamleader/parts-add"
import { ServicesPage } from "@/pages/features/teamleader/services"
import { ServicesEditPage }  from "@/pages/features/teamleader/services-edit"
import { ServicesAddPage }  from "@/pages/features/teamleader/services-add"
import { IncidentReportingPage }  from "@/pages/features/teamleader/incidentReporting"
import IncidentReportingEditPage  from "@/pages/features/teamleader/incidentReporting-edit"
import { AccidentForm } from '@/pages/features/teamleader/accidentForm';
import { AccidentFormsecond } from '@/pages/features/teamleader/accidentForm-second';
import { AccidentPoliceDetails } from '@/pages/features/teamleader/accident-policedetails';
import { AccidentSignatureForm } from '@/pages/features/teamleader/accident-signatureForm';
import { MaintenancePage } from '@/pages/features/teamleader/maintenance';
import { MaintenanceEditPage } from '@/pages/features/teamleader/maintenance-edit';
import { MaintenanceViewPage } from '@/pages/features/teamleader/maintenance-view';
import { VehiclePage } from '@/pages/features/teamleader/vehicle';
import { ServiceManagementPage } from '@/pages/features/teamleader/service-management';
import { ServiceManagementEditPage } from '@/pages/features/teamleader/service-management-edit';
import { ServiceManagementViewPage } from '@/pages/features/teamleader/service-management-view';
import { PanelBeaterPage } from '@/pages/features/teamleader/panelBeater';
import { PanelBeaterAddPage } from '@/pages/features/teamleader/panelBeater-add';
import { PanelBeaterEditPage } from '@/pages/features/teamleader/panelBeater-edit';

import { PreventiveMaintenance } from "@/pages/features/teamleader/priventive-maintenance"
import { MaintenanceHistory } from "@/pages/features/teamleader/maintenance-history"
import { AuditTrailPage } from "@/pages/features/teamleader/audittrail"
import { ViewAuditTrail } from "@/pages/features/teamleader/view-audittrail"
import { TeamleaderNotification } from "@/pages/features/teamleader/teamleader-notification"
import { TeamleaderActivitylog } from "@/pages/features/teamleader/teamleader-activitylog"
import { ServiceAccident } from "@/pages/features/teamleader/service-accident"
import { ServiceEditAccident } from "@/pages/features/teamleader/service-editaccident"
import { ServiceViewAccident } from "@/pages/features/teamleader/service-viewaccident"
import { ServiceBreakdown } from "@/pages/features/teamleader/service-breakdown"
import { ServiceEditBreakdown } from "@/pages/features/teamleader/service-editBreakdown"
import { ServiceViewBreakdown } from "@/pages/features/teamleader/service-viewBreakdown"
import { ServiceDamage } from "@/pages/features/teamleader/service-damage"
import { ServiceEditDamage } from "@/pages/features/teamleader/service-editDamage"
import { ServiceViewDamage } from "@/pages/features/teamleader/service-viewDamage"


// Customer-specific pages (placeholders for now)
const MachanicsPage = () => <div className="p-6">Machanics Page</div>

export const TeamLeaderRoutes = () => {
  return (

    <Routes>
        <Route element={<TeamLeaderLayout />}>
        <Route path="teamleader-dashboard" element={<TeamleaderDashboard />} />
        
        <Route path="summary" element={<FleetSummaryPage />} />
        <Route path="quality" element={<FleetQualityPage />} />
        <Route path="taskoverview" element={<FleeTaskoverview />} />

        <Route path="fleet-taskoverview-view/:taskId" element={<FleetTaskoverviewView />} />
        <Route path="mechanics" element={<MechanicsTable />} />
        <Route path="mechanics/view/:id" element={<MechanicViewPage />} />

        {/* Customer-specific pages */} 
        
        <Route path="panelBeater" element={<PanelBeaterPage />} />
        <Route path="panelBeater-add" element={<PanelBeaterAddPage />} />
        <Route path="panelBeater-edit/:id" element={<PanelBeaterEditPage />} />
        <Route path="vendors" element={<VendorsPage />} />
        <Route path="vendor-edit/:id" element={<VendorEditPage />} />
        <Route path="vendor-add" element={<VendorAddPage />} />
        <Route path="parts" element={<PartsPage />} />  
        <Route path="parts-edit/:id" element={<PartsEditPage />} />
        <Route path="parts-add" element={<PartsAddPage />} />  
        <Route path="services" element={<ServicesPage />} />
        <Route path="services-edit/:id" element={<ServicesEditPage />} />
        <Route path="services-add" element={<ServicesAddPage />} />  
        <Route path="incidentReporting" element={<IncidentReportingPage />} />
        <Route path="incidentReporting-edit" element={<IncidentReportingEditPage />} />
        <Route path="accidentForm" element={<AccidentForm/>} /> 
        <Route path="accidentForm-second" element={<AccidentFormsecond/>}/>
        <Route path="accident-policedetails" element={<AccidentPoliceDetails/>} />
        <Route path="accident-signatureForm" element={< AccidentSignatureForm/>}/>
        <Route path="maintenance" element={<MaintenancePage />} />
        <Route path="maintenance-edit/:vehicleId" element={<MaintenanceEditPage />} />
        <Route path="maintenance-view" element={<MaintenanceViewPage />} />
        <Route path="vehicle" element={<VehiclePage />} />
        <Route path="service-management" element={<ServiceManagementPage />} />
        <Route path="service-management-edit/:vehicleId" element={<ServiceManagementEditPage />} />
        <Route path="service-management-view/:vehicleId" element={<ServiceManagementViewPage />} />

        <Route path="preventivemaintenance" element={<PreventiveMaintenance />} />
        <Route path="servicemanagement" element={<ServiceManagementPage />} />

        <Route path="preventive-maintenance" element={<PreventiveMaintenance />} />
        <Route path="maintenance-history/:vehicleId" element={<MaintenanceHistory/>}/>
        
        <Route path="audittrail" element={<AuditTrailPage />} />
        <Route path="view-audittrail/:mechanicId" element={<ViewAuditTrail/>}/>
        <Route path="teamleader-notification" element={<TeamleaderNotification />} />
        <Route path="teamleader-activitylog" element={<TeamleaderActivitylog />} />
        <Route path="service-accident" element={<ServiceAccident/>}/>
        <Route path="service-editaccident/:vehicle" element={<ServiceEditAccident/>}/>
        <Route path="service-viewaccident/:vehicle" element={<ServiceViewAccident/>}/>
        <Route path="service-breakdown" element={<ServiceBreakdown/>}/>
        <Route path="service-editBreakdown/:vehicle" element={<ServiceEditBreakdown/>}/>
        <Route path="service-viewBreakdown/:vehicle" element={<ServiceViewBreakdown/>}/>
        <Route path="service-damage" element={<ServiceDamage/>}/>
        <Route path="service-editDamage/:vehicle" element={<ServiceEditDamage/>}/>
        <Route path="service-viewDamage/:vehicle" element={<ServiceViewDamage/>}/>

        </Route>
    </Routes>
  )
}