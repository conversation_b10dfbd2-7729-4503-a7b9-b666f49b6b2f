import { NavigateFunction } from 'react-router-dom';
import { Parts } from '../type/teamleadertype';

export const handlePartNoClick = (partNo: string, navigate: NavigateFunction): void => {
  navigate(`/teamleader/parts-edit/${partNo}`);
};

export const filterParts = (parts: Parts[], searchTerm: string): Parts[] => {
  return parts.filter(
    (part) =>
      part.partName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      part.vendorName.toLowerCase().includes(searchTerm.toLowerCase()) ||
      part.partNo.toLowerCase().includes(searchTerm.toLowerCase())
  );
};

export const paginateParts = (
  filteredParts: Parts[],
  currentPage: number,
  entriesPerPage: number
): { paginatedParts: Parts[]; totalPages: number; startEntry: number; endEntry: number } => {
  const totalEntries = filteredParts.length;
  const totalPages = Math.ceil(totalEntries / entriesPerPage);
  const paginatedParts = filteredParts.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  );
  const startEntry = (currentPage - 1) * entriesPerPage + 1;
  const endEntry = Math.min(currentPage * entriesPerPage, totalEntries);

  return { paginatedParts, totalPages, startEntry, endEntry };
};