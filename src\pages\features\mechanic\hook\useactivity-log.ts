import { useState } from "react";
import { ActivityLogEntry } from "../type/mechanictype";

const MOCK_ACTIVITIES: ActivityLogEntry[] = [
  {
    type: "Login",
    message: "<PERSON> logged in at 9.00 am, on 29th of May 2025",
  },
  {
    type: "Request",
    message: "<PERSON> request vehicle parts from team leader at 11.00 am, on 27th of May 2025",
  },
  {
    type: "Job Card",
    message: "<PERSON> created a job card for Rego 1PX 1ZR at 4.00 pm, on 23rd of May 2025",
  },
  {
    type: "Upload",
    message: "<PERSON> uploaded images for Rego ACX 201 at 11.00 am, on 20th of May 2025",
  },
  {
    type: "Logout",
    message: "<PERSON> logged out at 9.00 am, on 13th of May 2025",
  },
];

export function useActivityLog() {
  const [search, setSearch] = useState("");
  const [filterType, setFilterType] = useState("All");

  const allTypes = ["All", ...Array.from(new Set(MOCK_ACTIVITIES.map(a => a.type)))];

  const filteredActivities = MOCK_ACTIVITIES.filter((a) => {
    const matchesType = filterType === "All" || a.type === filterType;
    const matchesSearch = a.message.toLowerCase().includes(search.toLowerCase());
    return matchesType && matchesSearch;
  });

  return {
    search,
    setSearch,
    filterType,
    setFilterType,
    allTypes,
    filteredActivities,
  };
}