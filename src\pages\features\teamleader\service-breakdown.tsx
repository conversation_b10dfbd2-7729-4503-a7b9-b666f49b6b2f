import React, { useState } from 'react';
import { Search, Eye, Edit } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';


export function ServiceBreakdown() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [activeTab, setActiveTab] = useState('Breakdowns');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data for the table with new headers
  const breakdownData = [
    {
      vehicleId: 'VID0007',
      vehicle: 'Isuzu Npr 200 - AFS 009',
      incidentDate: '21-02-2025',
      actualDate: '-',
      estimatedDate: '25-02-2025',
      mechanicAssigned: 'Mike Smith',
      status: 'InProgress'
    },
    {
      vehicleId: 'VID0008',
      vehicle: 'Toyota Camry - ABC 123',
      incidentDate: '20-02-2025',
      actualDate: '-',
      estimatedDate: '23-02-2025',
      mechanicAssigned: 'John Doe',
      status: 'Pending'
    },
    {
      vehicleId: 'VID0009',
      vehicle: 'Honda Civic - XYZ 789',
      incidentDate: '19-02-2025',
      actualDate: '20-02-2025',
      estimatedDate: '22-02-2025',
      mechanicAssigned: 'Jane Smith',
      status: 'Done'
    },
  ];

  // Filter data based on search term
  const filteredData = breakdownData.filter((item) =>
    (item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.incidentDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.actualDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.estimatedDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.mechanicAssigned.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || item.status === filterStatus)
  );

  const handleVehicleIdClick = (vehicleId: string) => {
    navigate(`/teamleader/service-editBreakdown/${vehicleId}`);
  };

  // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Done':
        return 'bg-green-500 text-white';
      case 'Pending':
        return 'bg-gray-400 text-white';
      case 'InProgress':
        return 'bg-blue-500 text-white';
    }
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Breakdowns</h1>
      </div>

      {/* Tab Bar */}
        <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
          {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
            <Button
              key={tab}
              variant={activeTab === tab ? 'default' : 'outline'}
              onClick={() => {
                      if (tab === 'All') {
                        navigate('/teamleader/service-management');
                      } else {
                        setActiveTab(tab);
                      }
                      if (tab === 'Accident') {
                        navigate('/teamleader/service-accident');
                      } else {
                        setActiveTab(tab);
                      }
                      if (tab === 'General Maintenance') {
                        navigate('/teamleader/maintenance');
                      } else {
                        setActiveTab(tab);
                      }
                      if (tab === 'Damage') {
                        navigate('/teamleader/service-damage');
                      } else {
                        setActiveTab(tab);
                      }
                    }}
                  className="text-sm md:text-base"
                >
              {tab}
            </Button>
          ))}
        </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
                </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="All">All</SelectItem>
                    <SelectItem value="Pending">Pending</SelectItem>
                    <SelectItem value="InProgress">InProgress</SelectItem>
                    <SelectItem value="Done">Done</SelectItem>
              </SelectContent>
            </Select>
          </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Table View */}
      <div className="rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>Vehicle ID</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Incident Date</TableHead>
              <TableHead>Actual Date</TableHead>
              <TableHead>Estimated Date</TableHead>
              <TableHead>Mechanic Assigned</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item, index) => (
              <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                <TableCell>{item.vehicleId}</TableCell>
                <TableCell>{item.vehicle}</TableCell>
                <TableCell>{item.incidentDate}</TableCell>
                <TableCell>{item.actualDate}</TableCell>
                <TableCell>{item.estimatedDate}</TableCell>
                <TableCell>{item.mechanicAssigned}</TableCell>
                <TableCell className="px-3 py-4">
                  <span className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </TableCell>
                <TableCell>
                  {/* <Button variant="ghost" className="text-gray-600 hover:text-gray-800 mr-2" 
                    onClick={() => navigate(`/teamleader/service-viewbreakdown/${item.vehicle.split(' - ')[1]}`)}>
                    <Eye className="w-4 h-4" />
                  </Button> */}
                  <Button variant="ghost" className="text-gray-600 hover:text-gray-800" 
                    onClick={() => handleVehicleIdClick(item.vehicleId)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination */}
      <Pagination
        currentPage={currentPage}
        totalPages={totalPages}
        totalRecords={totalEntries}
        recordsPerPage={recordsPerPage}
        onPageChange={setCurrentPage}
        onRecordsPerPageChange={(records) => {
          setRecordsPerPage(records);
          setCurrentPage(1);
        }}
        className="mt-6"
      />
    </div>
  );
}