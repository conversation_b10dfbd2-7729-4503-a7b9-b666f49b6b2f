import { useState, useMemo } from 'react'
import { MechanicsData } from '../type/teamleadertype'

const STORAGE_KEY = 'mechanicsData'

const initialData: MechanicsData[] = [
  {
    id: '1',
    fullName: '<PERSON>',
    phoneNumber: '0421 433 500',
    address: '123 Main St, Downtown',
    enabled: true
  },
  {
    id: '2',
    fullName: '<PERSON>',
    phoneNumber: '0421 343 566',
    address: '456 Oak Ave, Midtown',
    enabled: true
  },
  {
    id: '3',
    fullName: '<PERSON>',
    phoneNumber: '0421 400 982',
    address: '789 Pine Rd, Uptown',
    enabled: false
  },
  {
    id: '4',
    fullName: '<PERSON>',
    phoneNumber: '0421 550 126',
    address: '321 Elm St, Westside',
    enabled: true
  },
  {
    id: '5',
    fullName: '<PERSON>',
    phoneNumber: '0421 643 768',
    address: '654 Maple Dr, Eastside',
    enabled: true
  },
  {
    id: '6',
    fullName: '<PERSON>',
    phoneNumber: '0421 294 509',
    address: '987 Cedar Ln, Northside',
    enabled: false
  },
  {
    id: '7',
    fullName: '<PERSON>',
    phoneNumber: '0421 452 953',
    address: '147 Birch Way, Southside',
    enabled: true
  },
  {
    id: '8',
    fullName: '<PERSON>',
    phoneNumber: '0421 789 903',
    address: '258 Walnut St, Central',
    enabled: true
  }
]

export const useMechanicsTable = () => {
  const [currentPage, setCurrentPage] = useState(1)
  const [recordsPerPage, setRecordsPerPage] = useState(10)
  const [searchTerm, setSearchTerm] = useState('')
  const [filterStatus, setFilterStatus] = useState('All')
  
  // Load data from localStorage or use initial data
  const [mechanicsData, setMechanicsData] = useState<MechanicsData[]>(() => {
    const savedData = localStorage.getItem(STORAGE_KEY)
    return savedData ? JSON.parse(savedData) : initialData
  })

  // Save to localStorage when data changes
  const saveData = (data: MechanicsData[]) => {
    setMechanicsData(data)
    localStorage.setItem(STORAGE_KEY, JSON.stringify(data))
  }

  // Filter data based on search and status
  const filteredData = useMemo(() => {
    return mechanicsData.filter(mechanic => {
      const matchesSearch = 
        mechanic.fullName.toLowerCase().includes(searchTerm.toLowerCase()) ||
        mechanic.phoneNumber.toLowerCase().includes(searchTerm.toLowerCase()) ||
        mechanic.address.toLowerCase().includes(searchTerm.toLowerCase())
      
      const matchesFilter = 
        filterStatus === 'All' ||
        (filterStatus === 'Yes' && mechanic.enabled) ||
        (filterStatus === 'No' && !mechanic.enabled)
      
      return matchesSearch && matchesFilter
    })
  }, [mechanicsData, searchTerm, filterStatus])

  const filteredTotalRecords = filteredData.length
  const filteredTotalPages = Math.ceil(filteredTotalRecords / recordsPerPage)

  // Calculate current page data from filtered results
  const currentData = useMemo(() => {
    const startIndex = (currentPage - 1) * recordsPerPage
    const endIndex = startIndex + recordsPerPage
    return filteredData.slice(startIndex, endIndex)
  }, [filteredData, currentPage, recordsPerPage])

  const getStatusColor = (enabled: boolean) => {
    return enabled 
      ? 'text-xs font-medium rounded bg-blue-100 text-blue-800 px-2 py-1.5'
      : 'text-xs font-medium rounded bg-red-100 text-red-800 px-2 py-1.5'
  }

  const handleRecordsPerPageChange = (records: number) => {
    setRecordsPerPage(records)
    setCurrentPage(1) // Reset to first page when changing records per page
  }

  const toggleMechanicStatus = (id: string) => {
    const updatedData = mechanicsData.map(mechanic =>
      mechanic.id === id ? { ...mechanic, enabled: !mechanic.enabled } : mechanic
    )
    saveData(updatedData)
  }

  return {
    // State
    currentPage,
    recordsPerPage,
    searchTerm,
    filterStatus,
    
    // Computed values
    currentData,
    filteredTotalRecords,
    filteredTotalPages,
    
    // Functions
    setCurrentPage,
    setSearchTerm,
    setFilterStatus,
    getStatusColor,
    handleRecordsPerPageChange,
    toggleMechanicStatus
  }
}
