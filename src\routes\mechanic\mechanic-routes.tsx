import { MechanicLayout } from "@/components/layout/MechanicLayout";
import { MechanicDashboard } from "@/pages/features/mechanic/mechanic-dashboard";
import { JobcardRegularMaintainance } from "@/pages/features/mechanic/jobcard-regular-maintainance";
import { JobcardRegularMaintainanceAdd } from "@/pages/features/mechanic/jobcard-regular-maintainance-add";
import JobcardRegularMaintainanceEdit from "@/pages/features/mechanic/jobcard-regular-maintainance-edit";
import { JobcardRegularMaintainanceView } from "@/pages/features/mechanic/jobcard-regular-maintainance-view";
import { RequestPartsAdd } from "@/pages/features/mechanic/request-parts-add";
import { RequestServiceAdd } from "@/pages/features/mechanic/request-service-add";
import { JobcardAccidentRepair } from "@/pages/features/mechanic/jobcard-accident-repair";
import { JobcardAccidentRepairAdd } from "@/pages/features/mechanic/jobcard-accident-repair-add";
import { JobcardAccidentRepairEdit } from "@/pages/features/mechanic/jobcard-accident-repair-edit";
import { JobcardAccidentRepairView } from "@/pages/features/mechanic/jobcard-accident-repair-view";
import { JobcardBreakdownService } from "@/pages/features/mechanic/jobcard-breakdown-service";
import { JobcardBreakdownServiceAdd } from "@/pages/features/mechanic/jobcard-breakdown-service-add";
import { JobcardBreakdownServiceEdit } from "@/pages/features/mechanic/jobcard-breakdown-service-edit";
import { JobcardBreakdownServiceView } from "@/pages/features/mechanic/jobcard-breakdown-service-view";
import { AuditTrail } from "@/pages/features/mechanic/audit&trail";
import { AuditTrailAdd } from "@/pages/features/mechanic/audit&trail-add";
import { AuditTrailEdit } from "@/pages/features/mechanic/audit&trail-edit";
import { RequestPartsMain } from "@/pages/features/mechanic/request-parts-main";
import RequestPartsMainEdit from "@/pages/features/mechanic/request-parts-main-edit";
import { RequestServicesMain } from "@/pages/features/mechanic/request-services-main";
import RequestServicesMainEdit from "@/pages/features/mechanic/request-services-main-edit";
import { Route, Routes } from "react-router-dom";
import { JobHistory } from "@/pages/features/mechanic/jobhistory";
import { NotificationCenter } from "@/pages/features/mechanic/notification-center";
import { ActivityLog } from "@/pages/features/mechanic/activity-log";
import { ServiceManagementAllPage } from "@/pages/features/mechanic/service-management-all";
import { MaintainanceServicePage } from "@/pages/features/mechanic/maintainance-service";
import { AccidentServicePage } from "@/pages/features/mechanic/accident-service";
import { BreakdownServicePage } from "@/pages/features/mechanic/breakdown-service";
import { ServiceManagementAllViewPage } from "@/pages/features/mechanic/service-managemant-all-view";
import { MaintainanceServiceEditPage } from "@/pages/features/mechanic/maintainance-service-edit";
import { MaintainanceServiceViewPage } from "@/pages/features/mechanic/maintainance-service-view";
import { AccidentServiceEditPage } from "@/pages/features/mechanic/accident-service-edit";
import { AccidentServiceViewPage } from "@/pages/features/mechanic/accident-service-view";
import { BreakdownServiceEditPage } from "@/pages/features/mechanic/breakdown-service-edit";
import { BreakdownServiceViewPage } from "@/pages/features/mechanic/breakdown-service-view";
import { DamageServicePage } from "@/pages/features/mechanic/damage-service";
import { DamageServiceEditPage } from "@/pages/features/mechanic/damage-service-edit";
import { DamageServiceViewPage } from "@/pages/features/mechanic/damage-service-view";
import { JobcardDamage } from "@/pages/features/mechanic/jobcard-damage";
import { JobcardDamageAdd } from "@/pages/features/mechanic/jobcard-damage-add";
import { JobcardDamageEdit } from "@/pages/features/mechanic/jobcard-damage-edit";
import { JobcardDamageView } from "@/pages/features/mechanic/jobcard-damage-view";

const ServiceManagement = () => <div className="p-6">Service Management</div>;

export const MechanicRoutes = () => {
  return (
    <Routes>
      <Route element={<MechanicLayout />}>
        <Route path="mechanic-dashboard" element={<MechanicDashboard />} />

        <Route path="services" element={<JobcardRegularMaintainance />} />
        <Route path="jobcard-regular-maintainance" element={<JobcardRegularMaintainance />} />
        <Route path="jobcard-regular-maintainance-add" element={<JobcardRegularMaintainanceAdd />} />
        <Route path="jobcard-regular-maintainance-edit/:id" element={<JobcardRegularMaintainanceEdit />} />
        <Route path="jobcard-regular-maintainance-view/:id" element={<JobcardRegularMaintainanceView />} />
        <Route path="request-part" element={<RequestPartsMain />} />
        <Route path="request-parts-add" element={<RequestPartsAdd />} />
        <Route path="request-parts-main-edit/:id" element={<RequestPartsMainEdit />} />
        <Route path="request-services-main" element={<RequestServicesMain />} />
        <Route path="request-services" element={<RequestServicesMain />} />
        <Route path="request-service-add" element={<RequestServiceAdd />} />
        <Route path="jobcard-accident-repair" element={<JobcardAccidentRepair />} />
        <Route path="jobcard-accident-repair-add" element={<JobcardAccidentRepairAdd />} />
        <Route path="jobcard-accident-repair-edit/:id" element={<JobcardAccidentRepairEdit />} />
        <Route path="jobcard-accident-repair-view/:id" element={<JobcardAccidentRepairView />} />
        <Route path="jobcard-breakdown-service" element={<JobcardBreakdownService />} />
        <Route path="jobcard-breakdown-service-add" element={<JobcardBreakdownServiceAdd />} />
        <Route path="jobcard-breakdown-service-edit/:id" element={<JobcardBreakdownServiceEdit />} />
        <Route path="jobcard-breakdown-service-view/:id" element={<JobcardBreakdownServiceView />} />
        <Route path="request-services-main-edit/:id" element={<RequestServicesMainEdit />} />
        <Route path="service-management" element={<ServiceManagement />} />
        <Route path="audit-trail" element={<AuditTrail />} />
        <Route path="audit-trail-add" element={<AuditTrailAdd />} />
        <Route path="audit-trail-edit/:id" element={<AuditTrailEdit />} />
        <Route path="audit-trail-add" element={<AuditTrailAdd />} />
        <Route path="audit-trail-edit/:id" element={<AuditTrailEdit />} />
        <Route path="job-history" element={<JobHistory />} />
        <Route path="notification-center" element={<NotificationCenter />} />
        <Route path="activity-log" element={<ActivityLog />} />
        <Route path="/mechanic/service-management-all" element={<ServiceManagementAllPage />} />
        <Route path="service-management-all" element={<ServiceManagementAllPage />} />
        <Route path="service-management-all-view/:vehicleId" element={<ServiceManagementAllViewPage />} />
        <Route path="maintainance-service" element={<MaintainanceServicePage />} />
        <Route path="maintainance-service-edit/:vehicleId" element={<MaintainanceServiceEditPage />} />
        <Route path="maintainance-service-view" element={<MaintainanceServiceViewPage />} />
        <Route path="accident-service" element={<AccidentServicePage />} />
        <Route path="breakdown-service" element={<BreakdownServicePage />} />
        
        <Route path="accident-service-edit/:vehicleId" element={<AccidentServiceEditPage />} />
        <Route path="accident-service-view" element={<AccidentServiceViewPage />} />
        <Route path="breakdown-service-edit/:vehicleId" element={<BreakdownServiceEditPage />} />
        <Route path="breakdown-service-view" element={<BreakdownServiceViewPage />} />
        <Route path="damage-service" element={<DamageServicePage />} />
        <Route path="damage-service-edit/:vehicleId" element={<DamageServiceEditPage />} />
        <Route path="damage-service-view" element={<DamageServiceViewPage />} />
        <Route path="jobcard-damage" element={<JobcardDamage />} />
        <Route path="jobcard-damage-add" element={<JobcardDamageAdd />} />
        <Route path="jobcard-damage-edit/:id" element={<JobcardDamageEdit />} />
        <Route path="jobcard-damage-view/:id" element={<JobcardDamageView />} />
      </Route>
    </Routes>
  );
};

<Route path="/mechanic/*" element={<MechanicRoutes />} />