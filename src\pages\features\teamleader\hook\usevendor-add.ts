import { NavigateFunction } from 'react-router-dom';
import { VendorFormData } from '../type/teamleadertype';

export const handleInputChange = (
  field: keyof VendorFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<VendorFormData>>
): void => {
  setFormData(prev => ({
    ...prev,
    [field]: value
  }));
};

export const handleSave = (navigate: NavigateFunction): void => {
  navigate('/teamleader/vendors');
};