import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import { <PERSON>, EyeOff, User, Lock } from 'lucide-react';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Checkbox } from '../components/ui/checkbox';
import { Logo } from '../components/auth/logo';
import Cookies from 'js-cookie';
import useFetch from '../hooks/useFetch';


interface LoginResponse {
  token: string;
  user: {
    id: number;
    email: string;
    role: string;
    clientProfile?: Record<string, any>;
  };
}


export function LoginPage() {
  const navigate = useNavigate();
  const [showPassword, setShowPassword] = useState(false);
  const [email, setEmail] = useState('');
  const [password, setPassword] = useState('');
  const [rememberMe, setRememberMe] = useState(false);

  // useFetch hook initialize 
  const {  loading, error, fetchData } = useFetch<LoginResponse>('/api/auth/login', {
    method: 'POST',
    silent: false,
    successMessage: 'Login successful!',
  });


  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    // API call 
    const response = await fetchData({
      data: { email, password },
    });

    if (response?.success && response.token) {
      // Token saved in cookies ( handle over useFetch)
      Cookies.set('accessToken', response.token, {
        expires: rememberMe ? 7 : undefined, // "Remember me" selected means it expires in 7 days.
        secure: true,
        sameSite: 'Strict',
      });
      navigate('/dashboard'); // Success, then redirect to the dashboard.
    }
  };

  const handleForgotPassword = () => {
    navigate('/forgot-password');
  };
  const handleSignup = () => {
    navigate('/signup');
  };
  return <div className="auth-container">
    <div className="auth-form">
      <Logo />
      <h1 className="auth-heading">Let's Get Started</h1>
      <h2 className="auth-subheading">Welcome Back</h2>
      <form onSubmit={handleSubmit} className="space-y-4">
        <div className="space-y-4">
          <div className="relative">
            <Input
              type="email"
              id="email"
              placeholder="ex: <EMAIL>"
              className="pl-10"
              value={email}
              onChange={(e) => setEmail(e.target.value)}
              required
            />
            <User className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
          </div>
          <div className="relative">
            <Input
              type={showPassword ? 'text' : 'password'}
              id="password"
              placeholder="••••••••••••••"
              className="pl-10"
              value={password}
              onChange={(e) => setPassword(e.target.value)}
              required
            />
            <Lock className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
            <button
              type="button"
              onClick={() => setShowPassword(!showPassword)}
              className="absolute right-3 top-3.5"
              aria-label={showPassword ? 'Hide password' : 'Show password'}
            >
              {showPassword ? (
                <EyeOff className="h-5 w-5 text-gray-400" />
              ) : (
                <Eye className="h-5 w-5 text-gray-400" />
              )}
            </button>
          </div>
          <div className="flex items-center justify-between">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="remember"
                checked={rememberMe}
                onCheckedChange={(checked: boolean) => setRememberMe(!!checked)}/>
              <label htmlFor="remember" className="text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70">
                Remember me
              </label>
            </div>
            <button onClick={handleForgotPassword} className="auth-link text-sm">
              Forgot password?
            </button>
          </div>
        </div>
        {error && <p className="text-red-500 text-sm">{error}</p>}
          <Button type="submit" className="auth-button" disabled={loading}>
            {loading ? 'Logging in...' : 'Login'}
          </Button>
      </form>
      <div className="text-center text-sm">
        Don't have an account?{' '}
        <button onClick={handleSignup} className="auth-link">
          Signup
        </button>
      </div>
      <div className="text-center text-xs text-gray-500 mt-8">
        DEVELOPED BY:{' '}
        <span className="text-gold-dark">WEBS R US PTY LTD</span>
      </div>
    </div>
  </div>;
}
//login git test