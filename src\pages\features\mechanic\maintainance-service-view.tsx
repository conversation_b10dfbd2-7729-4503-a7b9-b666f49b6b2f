import React from "react";
import { useNavigate } from "react-router-dom";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { ArrowLeft } from "lucide-react";
import { useMaintainanceServiceView } from "./hook/usemaintainance-service-view";

export function MaintainanceServiceViewPage() {
  const navigate = useNavigate();
  const { formData } = useMaintainanceServiceView();

  return (
    <div className="min-h-screen p-4">
      {/* Back Button */}
      <Button
        className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center mb-4"
        size="sm"
        onClick={() => navigate('/mechanic/maintainance-service')}
      >
        <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
        <span className="hidden md:inline">Go Back</span>
      </Button>
      <div className="bg-white rounded-lg shadow-sm p-4">
        <h2 className="text-xl font-semibold mb-4">Vehicle Details</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</Label>
            <Input value={formData.model} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Reg No</Label>
            <Input value={formData.regNo} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Maintenance Type</Label>
            <Input value={formData.maintenanceType} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Maintenance Type Interval</Label>
            <Input value={formData.maintenanceTypeInterval} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Odometer at Maintenance Due Date</Label>
            <Input value={formData.odometerAtDue} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Current Odometer</Label>
            <Input value={formData.currentOdometer} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Current Renter</Label>
            <Input value={formData.vehicleCurrentRenter} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Current Location</Label>
            <Input value={formData.vehicleCurrentLocation} readOnly className="bg-gray-200"/>
          </div>
          <div className="col-span-2 relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</Label>
            <Input value={formData.description} readOnly className="bg-gray-200"/>
          </div>
        </div>

        {/* Workshop Details */}
        <h2 className="text-xl font-semibold mb-4 mt-6">Workshop Details</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Branch</Label>
            <Input value={formData.branch} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Mechanic Assigned</Label>
            <Input value={formData.mechanicAssigned.join(", ")} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Estimated End Date</Label>
            <Input type="date" value={formData.estimatedEndDate} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Actual End Date</Label>
            <Input type="date" value={formData.actualEndDate} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
            <Input value={formData.status} readOnly className="bg-gray-200"/>
          </div>
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Status</Label>
            <Input value={formData.vehicleStatus} readOnly className="bg-gray-200" />
          </div>
          <div className="col-span-2 relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
            <Input value={formData.comment} readOnly className="bg-gray-200" />
          </div>
        </div>

        {/* Mechanic Note */}
        <h2 className="text-xl font-semibold mb-4 mt-6">Mechanic Note</h2>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
            <Input value={formData.mechanicNoteStatus} readOnly className="bg-gray-200"/>
          </div>
          <div className="col-span-2 relative">
            <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
            <Input value={formData.mechanicNoteComment} readOnly className="bg-gray-200" />
          </div>
        </div>
      </div>
    </div>
  );
}