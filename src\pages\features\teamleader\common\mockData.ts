import { <PERSON><PERSON><PERSON><PERSON>, PanelBeaterFormD<PERSON>, PanelBeaterEditFormData } from '../type/teamleadertype';
import { Vendor, VendorFormData, VendorEditFormData } from '../type/teamleadertype';
import { Parts, PartsFormData, PartsEditFormData } from '../type/teamleadertype';
import { Service,ServicesFormData, ServicesEditFormData } from '../type/teamleadertype';
import { Booking } from '../type/teamleadertype';
import { Management, VehicleFormData, MaintenanceRecord, VehicleData, WorkshopData, ImageData, ServiceTypeOption, StatusOfVehicleOption, BranchOption, MechanicOption, InsuranceStatusOption, AccidentRecord } from '../type/teamleadertype';
import { PreventiveRecord, MaintenanceHistoryRecord } from '../type/teamleadertype';
import { ViewAuditTrailRecord } from '../type/teamleadertype';
import { Notifications, Activities } from '../type/teamleadertype';


export const panelBeaterData: PanelBeater[] = [
  {
    id: 'PB001',
    name: '<PERSON><PERSON>',
    phone: '0421 458 431',
    address: '15 Horne Street, Somerton VIC 3062'
  },
  {
    id: 'PB002',
    name: '<PERSON><PERSON> Alvis',
    phone: '0421 871 031',
    address: '18 Cooper Street, Somerton VIC 3062'
  },
  {
    id: 'PB003',
    name: 'Tehan Perera',
    phone: '0421 300 530',
    address: '5 Parkside Drive, Somerton VIC 3062'
  }
];

export const initialPanelBeaterFormData: PanelBeaterFormData = {
  id: 'PB001',
  name: '',
  phone: '',
  address: ''
};

export const initialPanelBeaterEditFormData: PanelBeaterEditFormData = {
  id: 'PB001',
  name: 'Hashan Dias',
  phone: '0421 458 431',
  address: '15 Horne Street, Somerton VIC 3062'
};

export const vendorData: Vendor[] = [
  {
    id: 'VID001',
    vendorName: 'ARB Somerton',
    phone: '0421 458 431',
    email: '<EMAIL>',
    address: '26 Somerton Road, Somerton VIC 3062'
  },
  {
    id: 'VID002',
    vendorName: 'RPM Somerton',
    phone: '0421 871 031',
    email: '<EMAIL>',
    address: '26/34-36 Somerton Road, Somerton VIC 3062'
  },
  {
    id: 'VID003',
    vendorName: 'Melbourne Car Removals',
    phone: '0421 300 530',
    email: '<EMAIL>',
    address: '1-6 Somerton Park Drive, Somerton VIC 3062'
  }
];

export const initialVendorFormData: VendorFormData = {
  id: 'VID001',
  vendorName: '',
  phone: '',
  email: '',
  address: ''
};

export const initialVendorEditFormData: VendorEditFormData = {
  id: 'VID001',
  vendorName: 'ARB Somerton',
  phone: '0421 458 431',
  email: '<EMAIL>',
  address: '26 Somerton Road, Somerton VIC 3062'
};

export const partsData: Parts[] = [
  {
    partNo: 'PN001',
    partName: 'Tyre 205/50-17',
    quantity: '06',
    vendorName: 'ARB Somerton',
    mechanicName: 'John Doe'
  },
  {
    partNo: 'PN002',
    partName: 'Air Filter',
    quantity: '04',
    vendorName: 'RPM Somerton',
    mechanicName: 'Peter Smith'
  },
  {
    partNo: 'PN003',
    partName: 'Wiper Plates',
    quantity: '02',
    vendorName: 'Melbourne Car Removals',
    mechanicName: ''
  }
];

export const initialPartsFormData: PartsFormData = {
  partNo: 'PN001',
  vendorName: '',
  parts: [{ id: Date.now(), partName: '', quantity: '', comment: '' }]
};

export const vendorOptions: string[] = ['', 'ARB Somerton', 'RPM Somerton', 'Melbourne Car Removals'];

export const initialPartsEditFormData: PartsEditFormData = {
  partNo: 'PN001',
  vendorName: 'ARB Somerton',
  parts: [{ id: Date.now(), partName: 'Tyre 205/50-17', quantity: '06', comment: 'Urgent order needed' }]
};

export const servicesData: Service[] = [
  {
    serviceCode: 'SC001',
    vehicle: 'Isuzu Npr 200  - AFS 009',
    serviceName: 'Wheel Alignments',
    vendorName: 'ARB Somerton',
    mechanicName: 'John Doe'
  },
  {
    serviceCode: 'SC002',
    vehicle: 'Atom - BBA 600',
    serviceName: 'Painting',
    vendorName: 'RPM Somerton',
    mechanicName: 'Peter Smith'
  },
  {
    serviceCode: 'SC003',
    vehicle: 'Ford Mustang Match - QW5 P91',
    serviceName: 'Wheel Alignments',
    vendorName: 'Melbourne Car Removals',
    mechanicName: ''
  }
];

export const initialServicesFormData: ServicesFormData = {
  serviceCode: 'SC001',
  vehicle: '',
  vendorName: '',
  services: [{ id: Date.now(), serviceName: '', description: '' }]
};

export const vehicleOptions: string[] = ['', 'Isuzu Npr 200  - AFS 009', 'Atom - BBA 600', 'Ford Mustang Match - QW5 P91'];

export const initialServicesEditFormData: ServicesEditFormData = {
  serviceCode: 'SC001',
  vehicle: 'Isuzu Npr 200  - AFS 009',
  vendorName: 'ARB Somerton',
  services: [{ id: Date.now(), serviceName: 'Wheel Alignments', description: 'There were no matching the model code 12345678.' }]
};

export const bookingsData: Booking[] = [
  {
    id: '#0036',
    vehicle: 'PCR 455',
    pickupDate: '09-07-2025',
    pickupTime: '10:30',
    returnDate: '10-07-2025',
    returnTime: '11:30',
    incidentDate: '10-07-2025',
    reportType: 'Breakdown',
  },
  {
    id: '#0034',
    vehicle: 'XBB 211',
    pickupDate: '08-07-2025',
    pickupTime: '12:30',
    returnDate: '10-07-2025',
    returnTime: '10:00',
    incidentDate: '10-07-2025',
    reportType: 'Accident',
  },
  {
    id: '#0033',
    vehicle: 'PPR 255',
    pickupDate: '07-07-2025',
    pickupTime: '09:00',
    returnDate: '09-07-2025',
    returnTime: '12:00',
    incidentDate: '09-07-2025',
    reportType: 'General Maintenance',
  },
  {
    id: '#0032',
    vehicle: 'NAA 455',
    pickupDate: '07-07-2025',
    pickupTime: '10:30',
    returnDate: '08-07-2025',
    returnTime: '12:30',
    incidentDate: '08-07-2025',
    reportType: 'Damage',
  },
  {
    id: '#0031',
    vehicle: 'PCR 455',
    pickupDate: '07-07-2025',
    pickupTime: '10:00',
    returnDate: '07-07-2025',
    returnTime: '16:00',
    incidentDate: '07-07-2025',
    reportType: 'Accident',
  },
];

export const managementData: Management[] = [
  {
    vehicleId: 'VID0001',
    vehicle: 'Civic Hybrid Sedan - 1PX 1ZR',
    serviceTypes: 'General Maintenance',
    totalInParts: 'AUD 0.00',
    totalInLabor: 'AUD 0.00',
    status: 'Pending',
    dateIn: '2025/07/28',
    dateOut: '2025/08/02',
  },
  {
    vehicleId: 'VID0002',
    vehicle: 'Atom - 1PX 5ZP',
    serviceTypes: 'Accident',
    totalInParts: 'AUD 60.00',
    totalInLabor: 'AUD 0.00',
    status: 'Pending',
    dateIn: '2025/07/29',
    dateOut: '2025/08/01',
  },
  {
    vehicleId: 'VID0003',
    vehicle: 'BMW 330 d Coupe - 191',
    serviceTypes: 'Breakdown',
    totalInParts: 'AUD 120.00',
    totalInLabor: 'AUD 100.0',
    status: 'InProgress',
    dateIn: '2025/08/04',
    dateOut: '2025/08/06',
  },
  {
    vehicleId: 'VID0004',
    vehicle: 'Holden CTS H2 Automatic - 181',
    serviceTypes: 'Damage',
    totalInParts: 'AUD 80.00',
    totalInLabor: 'AUD 140.00',
    status: 'Done',
    dateIn: '2025/07/29',
    dateOut: '2025/07/31',
  },
];

export const initialVehicleFormData: VehicleFormData = {
  vehicleId: 'VID0001',
  vehicleModel: 'Nuzzi N200',
  regoNumber: '7Q9 4H9',
  serviceType: 'General Maintenance',
  totalParts: '00:00',
  totalLabor: '00:00',
  dateIn: '00:00',
  damages: '',
  notes: '',
  dateOut: '2025-02-08',
  timeOut: '13:43',
  timeIn: '13:43',
  status: 'Pending',
  comments: '',
  reservation: '',
  fuelIn: '',
  fuelOut: '',
  odometerIn: '',
  odometerOut: ''
};

export const maintenanceData: MaintenanceRecord[] = [
  {
    vehicleId: 'VID0001',
    vehicle: 'Civic Hybrid Sedan - 1PX 1ZR',
    maintenanceType: 'General Maintenance Every 5000km',
    estimatedEndDate: '26-05-2025',
    actualEndDate: '-',
    status: 'Pending',
    statusOfVehicle: 'Dirty',
    vehicleCurrentRenter: 'Not Assigned',
    mechanic: '-'
  },
  {
    vehicleId: 'VID0002',
    vehicle: 'BMW 330 d Coupe - 191',
    maintenanceType: 'General Maintenance Every 10000km',
    estimatedEndDate: '26-05-2025',
    actualEndDate: '-',
    status: 'InProgress',
    statusOfVehicle: 'Dirty',
    vehicleCurrentRenter: 'Not Assigned',
    mechanic: 'Peter Smith'
  },
  {
    vehicleId: 'VID0003',
    vehicle: 'Holden CTS H2 Automatic - 181',
    maintenanceType: 'General Maintenance Every 5000km',
    estimatedEndDate: '26-05-2025',
    actualEndDate: '-',
    status: 'InProgress',
    statusOfVehicle: 'Dirty',
    vehicleCurrentRenter: 'John Doe',
    mechanic: 'Anne Johnson'
  },
  {
    vehicleId: 'VID0004',
    vehicle: 'Atom - 1PX 5ZP',
    maintenanceType: 'General Maintenance Every 7000km',
    estimatedEndDate: '26-05-2025',
    actualEndDate: '31-05-2025',
    status: 'Done',
    statusOfVehicle: 'Cleaned',
    vehicleCurrentRenter: 'Not Assigned',
    mechanic: '-'
  }
];

export const initialVehicleData: VehicleData = {
  vehicleId: 'VID0001',
  model: 'Civic Hybrid Sedan',
  regNo: '1PX 1ZR',
  maintenanceType: 'General Maintenance Every 5000km',
  typeInterval: 'Every 5000km',
  estimatedEndDate: '2025-05-26',
  actualEndDate: '2025-05-31',
  odometerAtDueDate: '5000',
  currentOdometer: '4500',
  status: 'Pending',
  statusOfVehicle: 'Dirty',
  vehicleCurrentRenter: 'Not Assigned',
  vehicleCurrentLocation: 'Office',
  description: '',
  mechanic: '',
  branch: 'Somerton',
  comment: '',
  insuranceStatus: '',
  insuranceClaimNumber: '',
  incidentDate: '',
};

export const initialWorkshopData: WorkshopData = {
  branch: 'Somerton',
  mechanicAssigned: ['John Doe', 'Mike Smith'],
  comment: 'Type here...',
};

export const initialImageData: ImageData = {
  interiorImages: [],
  exteriorImages: [],
  leftSideDoors: [],
  rightSideDoors: [],
  frontSideImages: [],
  backSideImages: [],
  sideMirrors: [],
  other: [],
};

export const statusOfVehicleOptions: StatusOfVehicleOption[] = ['', 'Dirty', 'Cleaned'];
export const statusOptions: string[] = ['', 'Pending', 'InProgress', 'Done'];
export const branchOptions: BranchOption[] = ['', 'Somerton'];
export const mechanicOptions: MechanicOption[] = ['', 'John Doe', 'Mike Smith', 'Robert Brown'];
export const serviceTypeOptions: ServiceTypeOption[] = ['', 'General Maintenance', 'Breakdowns', 'Accident', 'Damage'];
export const insuranceStatusOptions: InsuranceStatusOption[] = ['', 'Initial Review', 'Accepted', 'Declined'];

export const accidentData: AccidentRecord[] = [
  {
    vehicleId: 'VID0004',
    vehicle: 'Isuzu - AFS 009',
    insuranceClaimNumber: '-',
    actualDate: '22/07/2025',
    estimatedDate: '21/07/2025',
    insuranceStatus: 'Initial Review',
    mechanicAssigned: 'Anne Katrina, Jane Doe',
    status: 'Pending'
  },
  {
    vehicleId: 'VID0005',
    vehicle: 'Atom - 1PX 5ZP',
    insuranceClaimNumber: '1234569',
    actualDate: '10/06/2025',
    estimatedDate: '12/06/2025',
    insuranceStatus: 'Accepted',
    mechanicAssigned: 'Mike Smith',
    status: 'InProgress'
  },
  {
    vehicleId: 'VID0006',
    vehicle: 'Toyota - BCS 903',
    insuranceClaimNumber: '-',
    actualDate: '08/06/2025',
    estimatedDate: '08/06/2025',
    insuranceStatus: 'Declined',
    mechanicAssigned: '-',
    status: 'Done'
  },
];

export const preventiveData: PreventiveRecord[] = [
  {
    vehicleId: 'VID0001',
    vehicle: 'Civic Hybrid Sedan - 1PX 1ZR',
    nextServiceAt: '10000 km',
    serviceTypes: 'General Maintenance every 5000 km',
    alertStatus: 'Overdue',
  },
  {
    vehicleId: 'VID0002',
    vehicle: 'Atom - 1PX 5ZP',
    nextServiceAt: '12000 km',
    serviceTypes: 'General Maintenance every 10000 km',
    alertStatus: 'Upcoming',
  },
  {
    vehicleId: 'VID0003',
    vehicle: 'Atom - 200 500',
    nextServiceAt: '12000 km',
    serviceTypes: 'General Maintenance every 5000 km',
    alertStatus: 'Delay',
  },
];

export const maintenanceHistoryData: MaintenanceHistoryRecord[] = [
  {
    serviceType: 'General Maintenance',
    lastServicedate: '12/07/2025',
    lastServiceat: '5000 km',
    nextSrviceat: '10000 km',
  },
  {
    serviceType: 'Accident',
    lastServicedate: '05/06/2025',
    lastServiceat: '-',
    nextSrviceat: '-',
  },
  {
    serviceType: 'Damage',
    lastServicedate: '12/05/2025',
    lastServiceat: '-',
    nextSrviceat: '-',
  },
];

export const viewAuditTrailData: ViewAuditTrailRecord[] = [
  {
    mechanicId: 'MC001',
    mechanicName: 'John Doe',
    vehicle: 'Atom - 1PX 1ZR',
    serviceType: 'Accident Repair',
    dateTime: '21-02-2025 10:00 am',
    description: 'Set brakes and repair the doors in front of the vehicle',
  },
  {
    mechanicId: 'MC002',
    mechanicName: 'Mike Smith',
    vehicle: 'Isuzu Npr 200 - AFS 009',
    serviceType: 'General Maintenance',
    dateTime: '21-02-2025 11:00 am',
    description: 'Performed routine maintenance including oil change and tire rotation',
  },
];

export const notificationData: Notifications[] = [
  {
    id: 1,
    type: 'info',
    message: 'Parts awaiting request from mechanic Anne Peter at 11.00 am, on 27th of July 2025 ',
    date: '2025-07-27'
  },
  {
    id: 2,
    type: 'info',
    message: 'Rego AVS 456 added to accident repair vehicle by reception at 10.30 am, on 27th of July 2025 .',
    date: '2025-07-20'
  },
  {
    id: 3,
    type: 'info',
    message: 'Rego QLC 780 replaced to Rego ABC 002 for an accident at 12.30 pm, on 24th of June 2025',
    date: '2025-06-22'
  },
  {
    id: 4,
    type: 'info',
    message: 'Parts awaiting request from mechanic Jane Katine at 02.00 pm, on 20th of June 2025 ',
    date: '2025-06-10'
  },
    {
    id: 4,
    type: 'info',
    message: 'Rego AXO 300 replaced to Rego NWD 602 for an accident at 10.50 am, on 18th of May 2025',
    date: '2025-05-18'
  }
];

export const activitiesData: Activities[] = [
  {
    id: 1,
    type: 'info',
    message: 'John Smith logged in at 9.00 am, on 29th of May 2025',
    date: '2025-05-29'
  },
  {
    id: 2,
    type: 'info',
    message: 'John Smith request vehicle parts from Filters Direct suppliers at 11.00 am, on 27th of May 2025 ',
    date: '2025-05-27'
  },
  {
    id: 3,
    type: 'info',
    message: 'John Smith changed the service type from breakdown service to General Maintenance at 2.30 pm, on 21st of May 2025',
    date: '2025-05-21'
  },
  {
    id: 4,
    type: 'info',
    message: 'John Smith assigned mechanic Anne Peter to Rego 1PX 1ZR at 4.00 pm, on 15th of May 2025 ',
    date: '2025-05-15'
  },
  {
    id: 5,
    type: 'info',
    message: 'John Smith logged out at 9.00 am, on 13th of May 2025',
    date: '2025-05-13'
  }
];


