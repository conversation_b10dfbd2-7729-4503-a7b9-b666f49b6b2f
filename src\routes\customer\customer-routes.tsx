import { Routes, Route } from 'react-router-dom'
import { CustomerLayout } from '../../components/layout/CustomerLayout'
import { DashboardPage } from '../../pages/features/customerDashboard/dashboard'
import { Profile } from '@/pages/features/customerDashboard/profile'
import { Editprofile } from '@/pages/features/customerDashboard/editprofile'
import { Addnewlicense } from '@/pages/features/customerDashboard/addnewlicense'
import { IncidentReportForm } from '@/pages/features/customerDashboard/incident-report-form'
import { AccidentForm } from '@/pages/features/customerDashboard/accident-form'
import { AccidentFormsecond } from '@/pages/features/customerDashboard/accident-formsecond'
import { AccidentPoliceDetails } from '@/pages/features/customerDashboard/accident-policedetails'
import { AccidentSignatureForm } from '@/pages/features/customerDashboard/accident-signatureform'
import { SettingPage } from '@/pages/features/customerDashboard/Customer-setting'


import { BookingHistoryPage } from '../../pages/features/customerDashboard/bookingHistory';
import BookingSummaryPage from '../../pages/features/customerDashboard/bookingSummary';
import { AgreementPage } from '../../pages/agreement';
import { ChangeRequestPage } from '../../pages/features/customerDashboard/changeRequest';
import { AddRequestPage } from '../../pages/features/customerDashboard/addRequest';
import { EditRequestPage } from '../../pages/features/customerDashboard/editRequest';
import { InvoicesPage } from '../../pages/features/customerDashboard/invoiceTabel';
import { IncidentReportingPage } from '@/pages/features/customerDashboard/incidentReporting';
import { FinesPage } from '@/pages/features/customerDashboard/fines';
import { InvoicePage } from '@/pages/features/customerDashboard/invoice'
import { NotificationsPage } from '@/pages/features/customerDashboard/notifications'
import { ActivityLogPage } from '@/pages/features/customerDashboard/activityLog'
import { EditFinesPage } from '@/pages/features/customerDashboard/editFines'
import { AddRentalPage } from '@/pages/features/customerDashboard/addRental'
import CustomerReceipt from '@/pages/customer-receipt'

// Customer-specific pages (placeholders for now)
//const ProfilePage = () => <div className="p-6">Customer Profile Page</div>


export const CustomerRoutes = () => {
  return (
    <Routes>
      <Route element={<CustomerLayout />}>
        <Route path="dashboard" element={<DashboardPage />} />
        <Route path="profile" element={<Profile />} />
        <Route path="add-rental" element={<AddRentalPage />} />
        <Route path="booking-history" element={<BookingHistoryPage />} />
        <Route path="incident-reporting" element={<IncidentReportingPage />} />
        <Route path="change-requests" element={<ChangeRequestPage />} />
        <Route path="invoices" element={<InvoicesPage />} />
        <Route path="fines" element={<FinesPage />} />
        <Route path="editFines" element={<EditFinesPage />} />
        <Route path="notifications" element={<NotificationsPage />} />
        <Route path="activity-log" element={<ActivityLogPage />} />
        <Route path="settings" element={<SettingPage />} />
        <Route path="editprofile" element={<Editprofile/>}/>
        <Route path="addnewlicense" element={<Addnewlicense/>} />
        <Route path="incident-report-form" element={<IncidentReportForm/>}/>  
        <Route path="accident-form" element={<AccidentForm/>} /> 
        <Route path="accident-formsecond" element={<AccidentFormsecond/>}/>
        <Route path="accident-policedetails" element={<AccidentPoliceDetails/>} />
        <Route path="accident-signatureform" element={< AccidentSignatureForm/>}/>
        

        <Route path="/booking-summary/:bookingId" element={< BookingSummaryPage/>} />
        <Route path="/booking-summary/agreement/:bookingId" element={<AgreementPage />} />
        <Route path="/booking-summary/customer-receipt/:bookingId" element={<CustomerReceipt />} />

        <Route path="/change-requests/add-request" element={<AddRequestPage />} />
        <Route path="/change-requests/edit-request/:id" element={<EditRequestPage />} />
        <Route path="/invoices/:rentalId" element={<InvoicePage />} />
      
      </Route>
    </Routes>
  )
}
