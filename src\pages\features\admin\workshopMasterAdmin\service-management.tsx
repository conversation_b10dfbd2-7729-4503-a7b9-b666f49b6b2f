import React, { useState } from 'react';
import { <PERSON>, AlertTriangle, Eye } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Card } from '@/components/ui/card';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';

export function ServiceManagement() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [activeTab, setActiveTab] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data for the table with dateIn and dateOut
  const maintenanceData = [
    {
      vehicleId: 'VID0001',
      vehicle: 'Civic Hybrid Sedan - 1PX 1ZR',
      serviceTypes: 'General Maintenance',
      totalInParts: 'AUD 0.00',
      totalInLabor: 'AUD 0.00',
      status: 'Pending',
      dateIn: '2025/07/28',
      dateOut: '2025/08/02',
    },
    {
      vehicleId: 'VID0002',
      vehicle: 'Atom - 1PX 5ZP',
      serviceTypes: 'Accident',
      totalInParts: 'AUD 60.00',
      totalInLabor: 'AUD 0.00',
      status: 'Pending',
      dateIn: '2025/07/29',
      dateOut: '2025/08/01',
    },
    {
      vehicleId: 'VID0003',
      vehicle: 'BMW 330 d Coupe - 191',
      serviceTypes: 'Breakdown',
      totalInParts: 'AUD 120.00',
      totalInLabor: 'AUD 100.0',
      status: 'InProgress',
      dateIn: '2025/08/04',
      dateOut: '2025/08/06',
    },
    {
      vehicleId: 'VID0004',
      vehicle: 'Holden CTS H2 Automatic - 181',
      serviceTypes: 'Damage',
      totalInParts: 'AUD 80.00',
      totalInLabor: 'AUD 140.00',
      status: 'Done',
      dateIn: '2025/07/29',
      dateOut: '2025/07/31',
    },
  ];

  // Get current date and calculate this week's range (Monday to Sunday)
  const today = new Date('2025-07-29T11:25:00+0530'); // Current date and time: July 29, 2025, 11:25 AM IST
  const startOfWeek = new Date(today);
  startOfWeek.setDate(today.getDate() - today.getDay() + (today.getDay() === 0 ? -6 : 1)); // Start of week (Monday)
  const endOfWeek = new Date(startOfWeek);
  endOfWeek.setDate(startOfWeek.getDate() + 6); // End of week (Sunday)

  // Filter data based on search term, filter status, and due this week
  const filteredData = maintenanceData.filter((item) =>
    (item.vehicleId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.status.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || item.status === filterStatus) &&
    (activeTab === 'Due This Week'
      ? (new Date(item.dateIn) >= startOfWeek && new Date(item.dateIn) <= endOfWeek) ||
        (new Date(item.dateOut) >= startOfWeek && new Date(item.dateOut) <= endOfWeek)
      : true)
  );

  const handleVehicleIdClick = (vehicleId: string) => {
    navigate(`/admin/workshopMasterAdmin/service-management-view/${vehicleId}`);
  };

  const handleVehicleClick = (vehicle: string) => {
    navigate(`/admin/workshopMasterAdmin/${vehicle}`);
  };

  const handleEditVehicleIdClick = (vehicleId: string) => {
    navigate(`/admin/workshopMasterAdmin/service-management-edit/${vehicleId}`);
  };

  // Pagination for desktop only
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Done':
        return 'bg-green-500 text-white';
      case 'Pending':
        return 'bg-gray-400 text-white';
      case 'InProgress':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-300 text-gray-700';
    }
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <AlertTriangle className="w-6 h-6 mr-3 text-earth-dark" />
          <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Service Management</h1>
        </div>
      </div>

      {/* Tab Bar */}
      <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
        {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
          <Button
            key={tab}
            variant={activeTab === tab ? 'default' : 'outline'}
            onClick={() => {
              if (tab === 'Accident') {
                navigate('/admin/workshopMasterAdmin/service-accident');
              } else if (tab === 'General Maintenance') {
                navigate('/admin/workshopMasterAdmin/service-maintenance');
              } else if (tab === 'Breakdowns') {
                navigate('/admin/workshopMasterAdmin/service-breakdown');
              } else if (tab === 'Damage') {
                navigate('/admin/workshopMasterAdmin/service-damage');
              } else {
                setActiveTab(tab);
              }
            }}
            className="text-sm md:text-base"
          >
            {tab}
          </Button>
        ))}
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        <div className="relative w-full sm:w-auto order-1">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="InProgress">InProgress</SelectItem>
              <SelectItem value="Done">Done</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1 order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Responsive Card View for xs, sm, md (below lg) */}
      <div className="lg:hidden space-y-4 mb-4">
        {filteredData.length === 0 && (
          <div className="text-center text-gray-500 py-8">No records found.</div>
        )}
        {filteredData.map((item) => (
          <Card key={item.vehicleId} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
            {/* Status badge - top right */}
            <div className="absolute top-4 right-4 flex flex-col items-end gap-2">
              <span
                className={`inline-flex items-center justify-center rounded px-3 py-1 text-xs font-semibold w-[90px] text-center ${getStatusColor(
                  item.status
                )} whitespace-nowrap`}
              >
                {item.status}
              </span>
            </div>
            {/* Card content */}
            <div className="mb-3 pr-[100px]">
              <span
                className="text-blue-600 hover:text-blue-800 font-semibold cursor-pointer text-base"
                onClick={() => handleEditVehicleIdClick(item.vehicleId)}
              >
                {item.vehicleId}
              </span>
            </div>
            <div className="mb-2">
              <span
                className="text-gray-900 font-medium cursor-pointer"
                onClick={() => handleVehicleClick(item.vehicle)}
              >
                {item.vehicle}
              </span>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-2">
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Service Types</div>
                <div className="text-sm">{item.serviceTypes}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Total in Parts</div>
                <div className="text-sm">{item.totalInParts}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Total in Labor</div>
                <div className="text-sm">{item.totalInLabor}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Date In</div>
                <div className="text-sm">{item.dateIn}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Date Out</div>
                <div className="text-sm">{item.dateOut}</div>
              </div>
            </div>
            {/* Actions at the bottom */}
            <div className="flex justify-end gap-2 mt-4">
              <Button
                onClick={() => handleVehicleIdClick(item.vehicleId)}
                variant="ghost"
                size="sm"
                className="text-gray-600 hover:text-gray-800 p-1"
              >
                <Eye className="w-5 h-5" />
                <span className="ml-2 text-xs">View</span>
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Table View for lg and up */}
      <div className="hidden lg:block rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>ID</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Service Types</TableHead>
              <TableHead>Total in Parts</TableHead>
              <TableHead>Total in Labor</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item) => (
              <TableRow key={item.vehicleId} className="hover:bg-gray-50 transition-colors">
                <TableCell>
                  <span
                    className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                    onClick={() => handleEditVehicleIdClick(item.vehicleId)}
                  >
                    {item.vehicleId}
                  </span>
                </TableCell>
                <TableCell>
                  <span
                    className="text-blue-600 hover:text-blue-800 font-medium cursor-pointer"
                    onClick={() => handleVehicleClick(item.vehicle)}
                  >
                    {item.vehicle}
                  </span>
                </TableCell>
                <TableCell>{item.serviceTypes}</TableCell>
                <TableCell>{item.totalInParts}</TableCell>
                <TableCell>{item.totalInLabor}</TableCell>
                <TableCell>
                  <span className={`inline-flex px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </TableCell>
                <TableCell className="px-3 py-4">
                  <Button
                    onClick={() => handleVehicleIdClick(item.vehicleId)}
                    variant="ghost"
                    size="sm"
                    className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination only for lg and up */}
      <div className="mt-4 sm:mt-6 hidden lg:block">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4"
        />
      </div>
    </div>
  );
}