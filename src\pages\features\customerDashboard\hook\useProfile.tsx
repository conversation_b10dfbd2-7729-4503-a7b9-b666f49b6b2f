import { useState } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { mockCustomerProfile } from '../common/mockdata';

export const useProfile = (initialData: any = mockCustomerProfile) => {
  const [profilePhoto, setProfilePhoto] = useState<File | null>(null);
  const [idPhoto, setIdPhoto] = useState<File | null>(null);
  const [isCorporate, setIsCorporate] = useState(initialData.isCorporate);

  const handleProfilePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setProfilePhoto(event.target.files[0]);
    }
  };

  const handleIdPhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setIdPhoto(event.target.files[0]);
    }
  };

  const handleBack = (navigate: NavigateFunction) => {
    navigate(-1);
  };

  const handleSubmit = () => {
    console.log('Profile submitted');
  };

  return {
    profilePhoto,
    idPhoto,
    isCorporate,
    setIsCorporate,
    handleProfilePhotoChange,
    handleIdPhotoChange,
    handleBack,
    handleSubmit,
  };
};