import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Button } from '../components/ui/button';
import { Logo } from '../components/auth/logo';
import { BackButton } from '../components/auth/back-button';
export function ForgotPasswordPage() {
  const navigate = useNavigate();
  const handleSmsReset = () => {
    navigate('/forgot-password/sms');
  };
  const handleEmailReset = () => {
    navigate('/forgot-password/email');
  };
  return <div className="auth-container">
      <BackButton />
      <div className="auth-form">
        <Logo />
        <h1 className="auth-heading">Forgot Your Password?</h1>
        <div className="space-y-4">
          <button onClick={handleSmsReset} className="w-full p-4 border-2 border-gold-light rounded-md text-left hover:bg-gold-lighter transition-colors">
            Via SMS: •• •• 011
          </button>
          <button onClick={handleEmailReset} className="w-full p-4 border-2 border-gold-light rounded-md text-left hover:bg-gold-lighter transition-colors">
            Via email: ••••@gmail.com
          </button>
        </div>
      </div>
    </div>;
}