import React, { useState, useEffect } from 'react';
import { <PERSON><PERSON><PERSON>rian<PERSON>, ArrowLeft } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Card, CardContent } from '@/components/ui/card';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';

// Examples
const mockRentals = [
  {
    id: 'RENT001',
    customerName: 'Suresh Perera',
    phoneNumber: '0421 475 120',
    vehicle: 'Atom - PCR 455',
    rego: 'PCR 455',
    vehicleClass: 'Minibus 12 Seats',
    pickupDateTime: '2025-08-04 10:00',
    returnDateTime: '2025-08-06 12:00',
  },
  {
    id: 'RENT002',
    customerName: '<PERSON> <PERSON>',
    phoneNumber: '0711 234 567',
    vehicle: 'Honda - ABC 123',
    rego: 'ABC 123',
    vehicleClass: 'Sedan',
    pickupDateTime: '2025-08-04 14:00',
    returnDateTime: '2025-08-05 16:00',
  },
  {
    id: 'RENT003',
    customerName: 'Nimal Fernando',
    phoneNumber: '0777 890 123',
    vehicle: 'Toyota - XYZ 789',
    rego: 'XYZ 789',
    vehicleClass: 'SUV',
    pickupDateTime: '2025-08-04 09:00',
    returnDateTime: '2025-08-07 11:00',
  },
];

interface RentalData {
  id: string;
  customerName: string;
  phoneNumber: string;
  vehicle: string;
  rego: string;
  vehicleClass: string;
  pickupDateTime: string;
  returnDateTime: string;
}

export function CustomerMAddfine() {
  const [rentalId, setRentalId] = useState('');
  const [customerName, setCustomerName] = useState('');
  const [phoneNumber, setPhoneNumber] = useState('');
  const [rego, setRego] = useState('');
  const [vehicleClass, setVehicleClass] = useState('');
  const [pickupDateTime, setPickupDateTime] = useState('');
  const [returnDateTime, setReturnDateTime] = useState('');
  const [fineNumber, setFineNumber] = useState('');
  const [fineType, setFineType] = useState('');
  const [penaltyAmount, setPenaltyAmount] = useState('');
  const [obligationNumber, setObligationNumber] = useState('');
  const [location, setLocation] = useState('');
  const [offenseDateTime, setOffenseDateTime] = useState('');
  const [note, setNote] = useState('');
  const [uploadedFile, setUploadedFile] = useState<File | null>(null);
  
  const [suggestions, setSuggestions] = useState<RentalData[]>([]);
  const [showSuggestions, setShowSuggestions] = useState(false);
  const [selectedRental, setSelectedRental] = useState<RentalData | null>(null);

  const navigate = useNavigate();
  const navigationLocation = useLocation();
  const state = navigationLocation.state as { rentalId?: string; rego?: string; customerName?: string; phoneNumber?: string; pickupDateTime?: string; returnDateTime?: string } | undefined;

  useEffect(() => {
    if (state) {
      setRentalId(state.rentalId || '');
      setRego(state.rego || '');
      setCustomerName(state.customerName || '');
      setPhoneNumber(state.phoneNumber || '');
      setPickupDateTime(state.pickupDateTime || '');
      setReturnDateTime(state.returnDateTime || '');
    }
  }, [state]);

  const handleRentalIdChange = (value: string) => {
    setRentalId(value);
    
    if (value.length > 0) {
      const filtered = mockRentals.filter(rental => 
        rental.id.toLowerCase().includes(value.toLowerCase())
      );
      setSuggestions(filtered);
      setShowSuggestions(true);
    } else {
      setSuggestions([]);
      setShowSuggestions(false);
      clearCustomerData();
    }
  };

  const handleSuggestionSelect = (rental: RentalData) => {
    setRentalId(rental.id);
    setCustomerName(rental.customerName);
    setPhoneNumber(rental.phoneNumber);
    setRego(rental.rego);
    setVehicleClass(rental.vehicleClass);
    setPickupDateTime(rental.pickupDateTime);
    setReturnDateTime(rental.returnDateTime);
    setSelectedRental(rental);
    setShowSuggestions(false);
  };

  const clearCustomerData = () => {
    setCustomerName('');
    setPhoneNumber('');
    setRego('');
    setVehicleClass('');
    setPickupDateTime('');
    setReturnDateTime('');
    setSelectedRental(null);
  };

  const handleFileUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      setUploadedFile(file);
    }
  };

  const handleSubmit = () => {
    console.log('Form submitted with data:', {
      rentalId,
      customerName,
      phoneNumber,
      rego,
      vehicleClass,
      pickupDateTime,
      returnDateTime,
      fineNumber,
      fineType,
      penaltyAmount,
      obligationNumber,
      location,
      offenseDateTime,
      note,
      uploadedFile,
    });
    setRentalId('');
    setCustomerName('');
    setPhoneNumber('');
    setRego('');
    setVehicleClass('');
    setPickupDateTime('');
    setReturnDateTime('');
    setFineNumber('');
    setFineType('');
    setPenaltyAmount('');
    setObligationNumber('');
    setLocation('');
    setOffenseDateTime('');
    setNote('');
    setUploadedFile(null);
    navigate('/reception/reception-fines/');
  };

  const handleCancel = () => {
    setRentalId('');
    setCustomerName('');
    setPhoneNumber('');
    setRego('');
    setVehicleClass('');
    setPickupDateTime('');
    setReturnDateTime('');
    setFineNumber('');
    setFineType('');
    setPenaltyAmount('');
    setObligationNumber('');
    setLocation('');
    setOffenseDateTime('');
    setNote('');
    setUploadedFile(null);
    navigate('/reception/reception-fines/');
  };

  return (
    <div className="p-2 xs:p-3 sm:p-4 md:p-6 lg:p-8">
      <Button
        className="bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base flex items-center"
        size="sm"
        onClick={() => navigate('/admin/customerMasterAdmin/customerM-fines/')}
      >
        <ArrowLeft className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
        <span className="hidden sm:inline">Go Back</span>
      </Button>

      <div className="flex items-center justify-between gap-1 xs:gap-2 sm:gap-3 mb-4 xs:mb-5 sm:mb-6 mt-3 xs:mt-4 sm:mt-5">
        <div className="flex items-center gap-1 xs:gap-2 sm:gap-3">
          <AlertTriangle className="w-5 xs:w-6 sm:w-7 md:w-8 h-5 xs:h-6 sm:h-7 md:h-8" />
          <h1 className="text-lg xs:text-xl sm:text-2xl md:text-3xl font-semibold">Fines - Add</h1>
        </div>
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 py-1 xs:py-2 text-xs xs:text-sm sm:text-base"
          onClick={() => navigate('/admin/customerMasterAdmin/customerM-checkVehicle/')}
        >
          Check Vehicle
        </Button>
      </div>

      <div className="space-y-3 xs:space-y-4 sm:space-y-5">
        <h3 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 mt-3 xs:mt-4 sm:mt-5 pb-1 xs:pb-2">Vehicle Details</h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="rentalId" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Rental ID</Label>
            <Input
              id="rentalId"
              value={rentalId}
              onChange={(e) => handleRentalIdChange(e.target.value)}
              placeholder="eg: RENT001"
              className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
            />
            {showSuggestions && suggestions.length > 0 && (
              <div className="absolute z-10 w-full mt-1 bg-white border border-gray-300 rounded-md shadow-lg max-h-40 xs:max-h-48 sm:max-h-60 overflow-y-auto">
                {suggestions.map((rental) => (
                  <div
                    key={rental.id}
                    className="px-2 xs:px-3 sm:px-4 py-1 xs:py-2 cursor-pointer hover:bg-gray-100"
                    onClick={() => handleSuggestionSelect(rental)}
                  >
                    <div className="font-medium text-xs xs:text-sm sm:text-base">{rental.id}</div>
                    <div className="text-[10px] xs:text-xs sm:text-sm text-gray-600">{rental.customerName}</div>
                  </div>
                ))}
              </div>
            )}
          </div>
          <div className="relative">
            <Label htmlFor="rego" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Rego</Label>
            <Input
              id="rego"
              value={rego}
              onChange={(e) => setRego(e.target.value)}
              readOnly={selectedRental !== null || state?.rego !== undefined}
              className={`mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 ${selectedRental || state?.rego ? 'bg-gray-100' : ''}`}
              placeholder="eg: PCR 455"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="vehicleClass" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Vehicle Class</Label>
            <Input
              id="vehicleClass"
              value={vehicleClass}
              onChange={(e) => setVehicleClass(e.target.value)}
              readOnly={selectedRental !== null || state?.rego !== undefined}
              className={`mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 ${selectedRental || state?.rego ? 'bg-gray-100' : ''}`}
              placeholder="eg: Minibus 12 Seats"
            />
          </div>
          <div className="relative">
            <Label htmlFor="customerName" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Customer Name</Label>
            <Input
              id="customerName"
              value={customerName}
              onChange={(e) => setCustomerName(e.target.value)}
              readOnly={selectedRental !== null || state?.customerName !== undefined}
              className={`mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 ${selectedRental || state?.customerName ? 'bg-gray-100' : ''}`}
              placeholder="eg: Suresh Perera"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="phoneNumber" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Phone Number</Label>
            <Input
              id="phoneNumber"
              value={phoneNumber}
              onChange={(e) => setPhoneNumber(e.target.value)}
              readOnly={selectedRental !== null || state?.phoneNumber !== undefined}
              className={`mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 ${selectedRental || state?.phoneNumber ? 'bg-gray-100' : ''}`}
              placeholder="eg: 0421 475 120"
            />
          </div>
          <div className="relative">
            <Label htmlFor="pickupDateTime" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Pickup Date & Time</Label>
            <Input
              id="pickupDateTime"
              type="datetime-local"
              value={pickupDateTime}
              onChange={(e) => setPickupDateTime(e.target.value)}
              readOnly={selectedRental !== null || state?.pickupDateTime !== undefined}
              className={`mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 ${selectedRental || state?.pickupDateTime ? 'bg-gray-100' : ''}`}
              placeholder="eg: 2025-08-04 10:00"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="returnDateTime" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Return Date & Time</Label>
            <Input
              id="returnDateTime"
              type="datetime-local"
              value={returnDateTime}
              onChange={(e) => setReturnDateTime(e.target.value)}
              readOnly={selectedRental !== null || state?.returnDateTime !== undefined}
              className={`mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 ${selectedRental || state?.returnDateTime ? 'bg-gray-100' : ''}`}
              placeholder="eg: 2025-08-06 12:00"
            />
          </div>
        </div>

        <h3 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 mt-3 xs:mt-4 sm:mt-5 pb-1 xs:pb-2">Registration</h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="fineNumber" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Fine Number</Label>
            <Input
              id="fineNumber"
              value={fineNumber}
              onChange={(e) => setFineNumber(e.target.value)}
              placeholder="eg: 234567891023"
              className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
            />
          </div>
          <div className="relative">
            <Label htmlFor="fineType" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Fine Type</Label>
            <Select
              value={fineType}
              onValueChange={setFineType}
              className="mt-1">
              <SelectTrigger className="w-full focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3">
                <SelectValue placeholder="Select Fine Type" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="Speeding">Speeding</SelectItem>
                <SelectItem value="Parking">Parking</SelectItem>
                <SelectItem value="Other">Other</SelectItem>
              </SelectContent>
            </Select>
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="penaltyAmount" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Penalty Amount</Label>
            <Input
              id="penaltyAmount"
              value={penaltyAmount}
              onChange={(e) => setPenaltyAmount(e.target.value)}
              placeholder="eg: 100.00"
              className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
            />
          </div>
          <div className="relative">
            <Label htmlFor="obligationNumber" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Obligation Number</Label>
            <Input
              id="obligationNumber"
              value={obligationNumber}
              onChange={(e) => setObligationNumber(e.target.value)}
              placeholder="eg: 234567891023"
              className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
            />
          </div>
        </div>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="location" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Location</Label>
            <Input
              id="location"
              value={location}
              onChange={(e) => setLocation(e.target.value)}
              placeholder="eg: Main Street"
              className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
            />
          </div>
        </div>

        <h3 className="text-base xs:text-lg sm:text-xl md:text-2xl font-semibold text-gray-900 mt-3 xs:mt-4 sm:mt-5 pb-1 xs:pb-2">Traffic Offense</h3>
        
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="offenseDateTime" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Offense Date & Time</Label>
            <Input
              id="offenseDateTime"
              type="datetime-local"
              value={offenseDateTime}
              onChange={(e) => setOffenseDateTime(e.target.value)}
              className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
              placeholder="eg: 2025-08-04 10:00"
            />
          </div>
          <div className="relative">
            <Label htmlFor="note" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Note</Label>
            <Input
              id="note"
              value={note}
              onChange={(e) => setNote(e.target.value)}
              placeholder="eg: Speeding incident"
              className="mt-1 focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
            />
          </div>
        </div>

        <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
          <div className="relative">
            <Label htmlFor="fileUpload" className="text-[10px] xs:text-xs sm:text-sm md:text-base font-medium text-gray-700">Update File</Label>
            <div className="mt-1 xs:mt-2 sm:mt-3 flex items-center gap-1 xs:gap-2 sm:gap-3">
              <Button type="button" variant="outline" onClick={() => document.getElementById('fileUpload')?.click()} className="px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base">
                Choose File
              </Button>
              <span className="text-[10px] xs:text-xs sm:text-sm text-gray-600">
                {uploadedFile ? uploadedFile.name : 'infringement.pdf'}
              </span>
              <input
                id="fileUpload"
                type="file"
                className="hidden"
                onChange={handleFileUpload}
                accept=".pdf,.jpg,.jpeg,.png"
              />
            </div>
          </div>
        </div>
      
        <div className="flex justify-end space-x-2 xs:space-x-3 sm:space-x-4 mt-6 xs:mt-7 sm:mt-8">
          <Button
            variant="outline"
            onClick={handleCancel}
            className="px-4 xs:px-5 sm:px-6 py-1 xs:py-2 text-xs xs:text-sm sm:text-base"
          >
            Cancel
          </Button>
          <Button
            onClick={handleSubmit}
            className="px-4 xs:px-5 sm:px-6 py-1 xs:py-2 bg-[#330101] text-white hover:bg-[#660404] text-xs xs:text-sm sm:text-base"
          >
            Submit
          </Button>
        </div>
      </div>
    </div>
  );
}