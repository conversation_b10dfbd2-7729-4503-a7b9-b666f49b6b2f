import { NavigateFunction } from 'react-router-dom';
import { AccidentRecord } from '../type/teamleadertype';
;

export const handleVehicleIdClick = (
  vehicleId: string,
  navigate: NavigateFunction
): void => {
  navigate(`/teamleader/service-editaccident/${vehicleId}`);
};

export const getStatusColor = (status: string): string => {
  switch (status.toLowerCase()) {
    case 'initial review':
      return 'bg-yellow-500 text-white';
    case 'accepted':
      return 'bg-green-500 text-white';
    case 'declined':
      return 'bg-red-500 text-white';
    case 'done':
      return 'bg-green-500 text-white';
    case 'pending':
      return 'bg-gray-500 text-white';
    case 'inprogress':
      return 'bg-blue-500 text-white';
    default:
      return 'bg-gray-500 text-white';
  }
};

export const handlePageChange = (
  page: number,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>
): void => {
  setCurrentPage(page);
};

export const handleRecordsPerPageChange = (
  records: number,
  setRecordsPerPage: React.Dispatch<React.SetStateAction<number>>,
  setCurrentPage: React.Dispatch<React.SetStateAction<number>>
): void => {
  setRecordsPerPage(records);
  setCurrentPage(1);
};