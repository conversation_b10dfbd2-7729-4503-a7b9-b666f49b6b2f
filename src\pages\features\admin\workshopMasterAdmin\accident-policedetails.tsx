import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { ArrowLeft } from 'lucide-react';
import React, { useState } from 'react';
import { useNavigate } from 'react-router-dom';

interface AccidentPoliceDetailsData {
  policeNotified: 'yes' | 'no' | '';
  policeInformation: string;
  policeAction: string;
  officerBatchNo: string;
  policeStation: string;
  stationPhoneNumber: string;
  reportNumber: string;
}

export const AccidentPoliceDetails: React.FC = () => {
  const navigate = useNavigate();
  const [formData, setFormData] = useState<AccidentPoliceDetailsData>({
    policeNotified: '',
    policeInformation: '',
    policeAction: '',
    officerBatchNo: '',
    policeStation: '',
    stationPhoneNumber: '',
    reportNumber: ''
  });

  const handleInputChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement>) => {
    const { name, value } = e.target;
    setFormData(prev => ({
      ...prev,
      [name]: value
    }));
  };

  const handleRadioChange = (value: 'yes' | 'no') => {
    setFormData(prev => ({
      ...prev,
      policeNotified: value
    }));
  };

  const handleGoBack = () => {
    navigate('/admin/workshopMasterAdmin/accident-form-second');
  };

  const handleNext = () => {
    navigate('/admin/workshopMasterAdmin/accident-signatureForm'); 
  };

  return (
    <div className="min-h-screen">
      {/* Go Back Button */}
      <div className="flex items-center mb-4 sm:mb-6">
        <Button
          className='bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2'
          size="sm"
          onClick={() => navigate('/admin/workshopMasterAdmin/accident-form-second')}
          >
          <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
           Go Back
        </Button>
      </div>

      {/* Main Content */}
      <div className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8 p-2 xs:p-3 sm:p-4 md:p-6">
        <h1 className="text-base xs:text-lg sm:text-xl md:text-xl lg:text-3xl font-bold text-gray-800 mb-4 xs:mb-5 sm:mb-6 md:mb-8">Police Details</h1>

        <form className="space-y-4 xs:space-y-5 sm:space-y-6 md:space-y-8">
          {/* Police Notified Section */}
          <div>
            <div className="mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <p className="text-[10px] xs:text-xs sm:text-sm md:text-base text-gray-600 mb-2 xs:mb-3 sm:mb-4">Police Notified :</p>
              <div className="flex gap-4 xs:gap-5 sm:gap-6 md:gap-8">
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="policeNotified"
                    value="yes"
                    checked={formData.policeNotified === 'yes'}
                    onChange={() => handleRadioChange('yes')}
                    className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500"
                  />
                  <span className="ml-1 xs:ml-2 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-700">Yes</span>
                </label>
                <label className="flex items-center">
                  <input
                    type="radio"
                    name="policeNotified"
                    value="no"
                    checked={formData.policeNotified === 'no'}
                    onChange={() => handleRadioChange('no')}
                    className="w-3 xs:w-4 sm:w-5 h-3 xs:h-4 sm:h-5 text-red-600 bg-gray-100 border-gray-300 focus:ring-red-500"
                  />
                  <span className="ml-1 xs:ml-2 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-700">No</span>
                </label>
              </div>
            </div>

            {/* Officer Batch No and Police Station */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <div className="relative">
                <Input
                  type="text"
                  id="officerBatchNo"
                  name="officerBatchNo"
                  value={formData.officerBatchNo}
                  onChange={handleInputChange}
                  placeholder="Enter"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="officerBatchNo" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Officer Batch No
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="policeStation"
                  name="policeStation"
                  value={formData.policeStation}
                  onChange={handleInputChange}
                  placeholder="Enter"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="policeStation" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Police Station
                </Label>
              </div>
            </div>

            {/* Station Phone Number and Report Number */}
            <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4 md:gap-6 mb-4 xs:mb-5 sm:mb-6 md:mb-8">
              <div className="relative">
                <Input
                  type="tel"
                  id="stationPhoneNumber"
                  name="stationPhoneNumber"
                  value={formData.stationPhoneNumber}
                  onChange={handleInputChange}
                  placeholder="Enter"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="stationPhoneNumber" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Station Phone Number
                </Label>
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="reportNumber"
                  name="reportNumber"
                  value={formData.reportNumber}
                  onChange={handleInputChange}
                  placeholder="Enter"
                  className="w-full h-9 xs:h-10 sm:h-11 md:h-12 border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent"
                />
                <Label htmlFor="reportNumber" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
                  Report Number
                </Label>
              </div>
            </div>
          </div>

          {/* Police Information */}
          <div className="relative mb-4 xs:mb-5 sm:mb-6 md:mb-8">
            <textarea
              id="policeInformation"
              name="policeInformation"
              value={formData.policeInformation}
              onChange={handleInputChange}
              placeholder="Type here..."
              rows={4}
              className="w-full min-h-[80px] xs:min-h-[100px] sm:min-h-[120px] border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-2 sm:py-3 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent resize-none"
            />
            <Label htmlFor="policeInformation" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
              Police Information
            </Label>
          </div>

          {/* Police Action */}
          <div className="relative mb-4 xs:mb-5 sm:mb-6 md:mb-8">
            <textarea
              id="policeAction"
              name="policeAction"
              value={formData.policeAction}
              onChange={handleInputChange}
              placeholder="Type here ....."
              rows={6}
              className="w-full min-h-[100px] xs:min-h-[120px] sm:min-h-[140px] border border-gray-500 rounded px-2 xs:px-3 sm:px-4 py-1 xs:py-2 sm:py-3 text-xs xs:text-sm sm:text-sm md:text-sm focus:outline-none focus:ring-2 focus:ring-[#FF6F3D] focus:border-transparent resize-none"
            />
            <Label htmlFor="policeAction" className="absolute left-1 xs:left-2 top-[-7px] xs:top-[-8px] bg-white px-0.5 xs:px-1 text-[10px] xs:text-xs sm:text-sm md:text-sm text-gray-600">
              Police Action
            </Label>
          </div>

          {/* Next Button */}
          <div className="flex justify-end mt-4 xs:mt-5 sm:mt-6 md:mt-8">
            <Button
              type="button"
              onClick={handleNext}
              className="bg-[#330101] text-white px-4 xs:px-6 sm:px-8 md:px-10 py-1 xs:py-1.5 sm:py-2 text-xs xs:text-sm sm:text-base md:text-sm rounded transition-colors"
            >
              Next
            </Button>
          </div>
        </form>
      </div>
    </div>
  );
};