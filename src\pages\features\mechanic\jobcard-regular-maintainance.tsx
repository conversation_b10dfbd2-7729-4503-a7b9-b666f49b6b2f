import { useState } from 'react'
import { useNavigate } from 'react-router-dom'
import { Button } from '../../../components/ui/button'
import { Card, CardContent } from '../../../components/ui/card'
import { Input } from '../../../components/ui/input'
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '../../../components/ui/select'
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '../../../components/ui/table'
import { Badge } from '../../../components/ui/badge'
import { CalendarCheck, Search, Edit, Eye, ArrowLeft } from 'lucide-react'
import { useJobcardRegularMaintainance } from './hook/usejobcard-regular-maintainance'
import { Pagination } from '../../../components/layout/Pagination'


export function JobcardRegularMaintainance() {
  const navigate = useNavigate();
  const {
    jobCards,
    customerInfo,
    filters,
    updateFilters,
    getStatusColor
  } = useJobcardRegularMaintainance();

  // Pagination logic
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const totalRecords = jobCards.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentData = jobCards.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const handleAddClick = () => {
    navigate('/mechanic/jobcard-regular-maintainance-add')
  }

  // Responsive Card for Mobile/Tablet
  const JobCard = ({ card }: { card: any }) => (
    <Card className="relative mb-4 p-4 shadow-md border border-gray-200 rounded-lg">
      {/* Status badge right aligned */}
      <div className="flex justify-between  mb-2">
        <div className="font-medium text-base">{card.registration}</div>
        <div className="flex items-center justify-center min-w-[90px]">
          <Badge
            className={`
              ${getStatusColor(card.status)}
              text-xs px-0 py-0
              rounded-sm
              w-[80px] h-[28px]
              flex items-center justify-center
              font-semibold
              uppercase
              
              text-center
            `}
            style={{ minWidth: '90px', height: '28px', lineHeight: '26px' }}
          >
            {card.status}
          </Badge>
        </div>
      </div>
      <div className="grid grid-cols-2 gap-2 text-xs mb-2">
        <div><span className="font-medium">Make:</span> {card.make}</div>
        <div><span className="font-medium">Model:</span> {card.model}</div>
        <div><span className="font-medium">Series:</span> {card.modelSeries}</div>
        <div><span className="font-medium">Colour:</span> {card.colour}</div>
      </div>
      <div className="text-xs mb-2"><span className="font-medium">Next Service:</span> {card.nextService}</div>
      <div className="text-xs mb-2"><span className="font-medium">Prod Date:</span> {card.prodDate}</div>
      <div className="text-xs mb-2"><span className="font-medium">Rego Due:</span> {card.regoDue}</div>
      <div className="text-xs mb-2"><span className="font-medium">Build Date:</span> {card.buildDate}</div>
      {/* Actions at the bottom */}
      <div className="flex justify-end gap-2 pt-3 border-t border-gray-100">
        <Button
          onClick={() => navigate(`/mechanic/jobcard-regular-maintainance-view/${card.id}`)}
          variant="ghost"
          size="sm"
          className="text-gray-600 hover:text-gray-800"
        >
          <Eye className="w-4 h-4 mr-1" />
          View
        </Button>
        <Button
          onClick={() => navigate(`/mechanic/jobcard-regular-maintainance-edit/${card.id}`)}
          variant="ghost"
          size="sm"
          className="text-gray-600 hover:text-gray-800"
        >
          <Edit className="w-4 h-4 mr-1" />
          Edit
        </Button>
      </div>
    </Card>
  );

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Mobile Back Button */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/mechanic/maintainance-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>
      {/* Desktop Back Button */}
      <Button
        className="hidden md:flex bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base items-center mb-4"
        size="sm"
        onClick={() => navigate('/mechanic/maintainance-service')}
      >
        <ArrowLeft className="h-4 w-4 mr-1" />
        <span className="hidden md:inline">Go Back</span>
      </Button>
      {/* Header */}
      <div className="flex flex-col sm:flex-row items-start sm:items-center justify-between gap-4 mb-6">
        <div className="flex items-center">
          <CalendarCheck className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
          <h1 className="text-xl sm:text-2xl font-bold text-gray-800">Job Card-General Maintainance</h1>
        </div>
        <Button
          onClick={handleAddClick}
          className="w-full md:w-32 bg-[#330101] hover:bg-[#ffde5c] text-white px-4 py-2 md:px-8 md:py-2 mb-4 md:mb-0"
        >
          Add
        </Button>
      </div>

      {/* Search and Filter */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 sm:mb-6">
        {/* Filter Dropdown */}
        <div className="w-full sm:w-auto">
          <Select
            value={filters.status}
            onValueChange={(value) => updateFilters({ status: value })}
          >
            <SelectTrigger className="h-12 w-full border border-gray-200 text-sm focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All" className="text-sm">All</SelectItem>
              <SelectItem value="Completed" className="text-sm">Completed</SelectItem>
              <SelectItem value="In Progress" className="text-sm">In Progress</SelectItem>
              <SelectItem value="Pending" className="text-sm">Pending</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Search Bar */}
        <div className="relative w-full sm:flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={filters.searchTerm}
            onChange={(e) => updateFilters({ searchTerm: e.target.value })}
            className="w-full pl-10 pr-4 py-2 h-12 text-sm focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        {/* Save Button */}
        <Button
          variant="outline"
          size="sm"
          className="w-full sm:w-auto h-12 text-sm bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap"
        >
          Save this search
        </Button>
      </div>

      {/* Responsive Card View for xs, sm, md */}
      <div className="block lg:hidden">
        {jobCards.length > 0 ? (
          jobCards.map((card) => (
            <JobCard key={card.id} card={card} />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            No job card data available
          </div>
        )}
      </div>

      {/* Desktop Table View for lg and up */}
      <div className="hidden lg:block">
        <div className="rounded-md border overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50">
                <TableHead className="text-sm font-medium px-3 py-2">Registration</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Prod Date</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Next Service</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Rego Due</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Build Date</TableHead>
                <TableHead className="text-sm font-medium px-3 py-2">Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.length > 0 ? (
                currentData.map((card) => (
                  <TableRow key={card.id}>
                    <TableCell className="text-sm px-3 py-2">{card.registration}</TableCell>
                    <TableCell className="text-sm px-3 py-2">{card.prodDate}</TableCell>
                    <TableCell className="text-sm px-3 py-2 max-w-32 truncate">{card.nextService}</TableCell>
                    <TableCell className="text-sm px-3 py-2">{card.regoDue}</TableCell>
                    <TableCell className="text-sm px-3 py-2">{card.buildDate}</TableCell>
                    <TableCell className="text-sm px-3 py-2">
                      <div className="flex gap-1">
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-1 h-6 w-6 text-gray-600 hover:text-gray-800 transition-colors"
                          onClick={() => navigate(`/mechanic/jobcard-regular-maintainance-view/${card.id}`)}
                          title="View"
                        >
                          <Eye className="w-4 h-4" />
                        </Button>
                        <Button
                          variant="ghost"
                          size="sm"
                          className="p-1 h-6 w-6 text-gray-600 hover:text-gray-800 transition-colors"
                          onClick={() => navigate(`/mechanic/jobcard-regular-maintainance-edit/${card.id}`)}
                          title="Edit"
                        >
                          <Edit className="w-4 h-4" />
                        </Button>
                      </div>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={13} className="text-center py-8 text-gray-500">
                    No job card data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* Pagination only for desktop */}
        <div className="mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={recordsPerPage}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setRecordsPerPage(records);
              setCurrentPage(1);
            }}
            className="text-sm sm:text-base"
          />
        </div>
      </div>
    </div>
  )
}