import { useState } from "react";
import { MaintainanceServiceEditData } from "../type/mechanictype";

const MOCK_DATA: MaintainanceServiceEditData = {
  model: "Civic Hybrid Sedan",
  regNo: "1PX 1ZR",
  maintenanceType: "General Maintenance Every 5000km",
  maintenanceTypeInterval: "Every 5000km",
  odometerAtDue: "",
  currentOdometer: "4500",
  vehicleCurrentRenter: "Not Assigned",
  vehicleCurrentLocation: "Office",
  description: "",
  branch: "Somerton",
  mechanicAssigned: ["<PERSON>", "<PERSON>"],
  estimatedEndDate: "2025-05-26",
  actualEndDate: "2025-05-31",
  status: "Pending",
  vehicleStatus: "Dirty",
  comment: "",
  mechanicNoteStatus: "Pending",
  mechanicNoteComment: "",
};

export function useMaintainanceServiceView() {
  const [formData] = useState<MaintainanceServiceEditData>(MOCK_DATA);
  return { formData };
}