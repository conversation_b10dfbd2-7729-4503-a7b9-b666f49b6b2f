import React, { useState } from 'react';
import { Trash2} from 'lucide-react';
import { Button } from '@/components/ui/button';
import { useNavigate, useParams } from 'react-router-dom';
import { Pagination } from '@/components/layout/Pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

interface RelocationRecord {
  from: string;
  to: string;
  date: string;
  reason: string;
  oldOdometer: string;
  newOdometer: string;
  oldFuelLevel: string;
  newFuelLevel: string;
  vehicleRelocated: string;
}

export function VehicleRelocationPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);

  const relocationData: RelocationRecord[] = [
    {
      from: 'Footscray',
      to: 'Somerton',
      date: '16-12-2021 09:17',
      reason: '',
      oldOdometer: '196205',
      newOdometer: '196205',
      oldFuelLevel: '8',
      newFuelLevel: '8',
      vehicleRelocated: 'Yes'
    },
    {
      from: 'Footscray',
      to: 'Footscray',
      date: '14-12-2021 16:11',
      reason: '',
      oldOdometer: '196205',
      newOdometer: '196205',
      oldFuelLevel: '8',
      newFuelLevel: '8',
      vehicleRelocated: 'Yes'
    },
    {
      from: 'Somerton',
      to: 'Footscray',
      date: '12-11-2021 13:53',
      reason: '',
      oldOdometer: '195311',
      newOdometer: '195311',
      oldFuelLevel: '8',
      newFuelLevel: '8',
      vehicleRelocated: 'Yes'
    },
    {
      from: 'Footscray',
      to: 'Somerton',
      date: '19-10-2021 09:58',
      reason: '',
      oldOdometer: '194838',
      newOdometer: '194838',
      oldFuelLevel: '8',
      newFuelLevel: '8',
      vehicleRelocated: 'Yes'
    },
    {
      from: 'Footscray',
      to: 'Somerton',
      date: '01-10-2021 12:48',
      reason: '',
      oldOdometer: '192466',
      newOdometer: '192466',
      oldFuelLevel: '8',
      newFuelLevel: '8',
      vehicleRelocated: 'Yes'
    },
    {
      from: 'Somerton',
      to: 'Footscray',
      date: '17-09-2021 11:55',
      reason: '',
      oldOdometer: '192411',
      newOdometer: '192411',
      oldFuelLevel: '8',
      newFuelLevel: '8',
      vehicleRelocated: 'Yes'
    },
    {
      from: 'Footscray',
      to: 'Somerton',
      date: '19-05-2021 14:41',
      reason: '',
      oldOdometer: '184414',
      newOdometer: '184414',
      oldFuelLevel: '6',
      newFuelLevel: '6',
      vehicleRelocated: 'Yes'
    },
    {
      from: 'Somerton',
      to: 'Footscray',
      date: '24-03-2021 07:08',
      reason: '',
      oldOdometer: '181869',
      newOdometer: '181869',
      oldFuelLevel: '8',
      newFuelLevel: '8',
      vehicleRelocated: 'Yes'
    }
  ];

  const totalEntries = relocationData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);

  const handleTabClick = (tab: string) => {
    if (!id) {
      navigate('/receptionMasterAdmin/fleet/vehicles');
      return;
    }

    const tabRoutes: { [key: string]: string } = {
      'View': `/receptionMasterAdmin/fleet/vehicles/${id}`,
      'Edit': `/admin/receptionMasterAdmin/fleet/vehicles/edit-vehicle/${id}`,
      'Reservations': `/admin/receptionMasterAdmin/fleet/vehicles/reservations/${id}`,
      'Damages': `/admin/receptionMasterAdmin/fleet/vehicles/damages/${id}`,
      'Blocked Periods': `/admin/receptionMasterAdmin/fleet/vehicles/blocked-periods/${id}`,
      'Expenses': `/admin/receptionMasterAdmin/fleet/vehicles/expenses/${id}`,
      'Relocations': `/admin/receptionMasterAdmin/fleet/vehicles/relocations/${id}`,
      'Repair Orders': `/admin/receptionMasterAdmin/fleet/vehicles/repair-orders/${id}`,
      'Files': `/admin/receptionMasterAdmin/fleet/vehicles/files/${id}`,
      'Check List': `/admin/receptionMasterAdmin/fleet/vehicles/check-list/${id}`
    };

    navigate(tabRoutes[tab]);
  };

  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-semibold text-gray-900">
                Mercedes-Benz E 280 CDI Classic - {id || 'Unknown'}
              </h1>
              <span className="bg-[#330101] text-white px-3 py-1 rounded text-sm font-medium">
                Rental
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button className="bg-[#330101] hover:bg-[#660404] text-white">
                Relocate Vehicle
              </Button>
            </div>
            </div>

          {/* Tabs */}
          <div className="flex space-x-1">
            {['View', 'Edit', 'Reservations', 'Damages', 'Blocked Periods', 'Expenses', 'Relocations', 'Repair Orders', 'Files', 'Check List'].map((tab) => (
              <Button
                key={tab}
                onClick={() => handleTabClick(tab)}
                className={`px-4 py-2 text-sm font-medium border border-gray-300 transition-colors hover:bg-gray-100 ${
                  tab === 'Relocations'
                    ? 'bg-white text-gray-900'
                    : 'bg-gray-100 text-gray-600'
                }`}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        {/* Table */}
        <div className="hidden md:block rounded-md border overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className='bg-gray-50 uppercase'>
                  <TableHead>From</TableHead>
                  <TableHead>To</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Reason</TableHead>
                  <TableHead>Old Odometer</TableHead>
                  <TableHead>New Odometer</TableHead>
                  <TableHead>Old Fuel Level</TableHead>
                  <TableHead>New Fuel Level</TableHead>
                  <TableHead>Vehicle Relocated?</TableHead>
                  <TableHead>Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {relocationData.slice(
                  (currentPage - 1) * recordsPerPage,
                  currentPage * recordsPerPage
                ).map((record, index) => (
                  <TableRow key={index} className="hover:bg-gray-50">
                    <TableCell>{record.from}</TableCell>
                    <TableCell>{record.to}</TableCell>
                    <TableCell>{record.date}</TableCell>
                    <TableCell>{record.reason}</TableCell>
                    <TableCell>{record.oldOdometer}</TableCell>
                    <TableCell>{record.newOdometer}</TableCell>
                    <TableCell>{record.oldFuelLevel}</TableCell>
                    <TableCell>{record.newFuelLevel}</TableCell>
                    <TableCell>{record.vehicleRelocated}</TableCell>
                    <TableCell>
                      <Button
                        variant="ghost"
                        size="sm"
                        className="text-red-600 hover:text-red-800 hover:bg-red-50 p-1"
                      >
                        <Trash2 className="h-4 w-4" />
                      </Button>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(value) => {
            setRecordsPerPage(value);
            setCurrentPage(1);
          }}
          recordsPerPageOptions={[5, 10, 25, 50]}
          className="mt-4"
        />
      </div>
    </div>
  );
}