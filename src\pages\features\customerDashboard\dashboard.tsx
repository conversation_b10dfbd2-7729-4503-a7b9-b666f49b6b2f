import React from 'react';
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from '../../../components/ui/card';
import { DoorOpen, Music, Snowflake } from 'lucide-react';
import UnderConstructionPage from '../underConstruction';
export function DashboardPage() {
  return <div className="space-y-6">
   <UnderConstructionPage />

   {/*  <h1 className="text-3xl font-bold">Pickup Vehicle Details</h1>
      <Card>
        <CardContent className="pt-6">
          <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-gold mr-2"></div>
              <span>Vehicle Class : Economy Plus</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-gold mr-2"></div>
              <span>Return Date : 15/06/2025</span>
            </div>
            <div className="flex items-center">
              <div className="w-3 h-3 rounded-full bg-gold mr-2"></div>
              <span>Return Time : 10.30 AM</span>
            </div>
          </div>
        </CardContent>
      </Card>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        <div className="md:col-span-2">
          <div className="grid grid-cols-2 sm:grid-cols-3 gap-4">
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <div className="h-10 w-10 text-gold mb-2" />
                <p className="text-center">Automatic Transmission</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <DoorOpen className="h-10 w-10 text-gold mb-2" />
                <p className="text-center">Doors</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <div className="h-10 w-10 text-gold mb-2" />
                <p className="text-center">Power Steering</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <Music className="h-10 w-10 text-gold mb-2" />
                <p className="text-center">Radio / CD Player</p>
              </CardContent>
            </Card>
            <Card>
              <CardContent className="flex flex-col items-center justify-center p-6">
                <Snowflake className="h-10 w-10 text-gold mb-2" />
                <p className="text-center">Airconditioning</p>
              </CardContent>
            </Card>
          </div>
        </div>
        <div>
          <img src="/22-Customer_Dashboard.jpg" alt="Nissan Versa" className="w-full h-auto rounded-lg object-contain" />
        </div>
        
      </div>
      */} 
    </div>;
     
}