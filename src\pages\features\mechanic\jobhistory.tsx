import React from "react";
import { useJobHistory } from "./hook/usejobhistory";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table";
import { Pagination } from "@/components/layout/Pagination";
import { Input } from "@/components/ui/input";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import { Search, CalendarClock } from "lucide-react";
import { Card } from "@/components/ui/card";

export function JobHistory() {
  const {
    search,
    setSearch,
    filterType,
    setFilterType,
    allServiceTypes,
    pageSize,
    setPageSize,
    currentPage,
    setCurrentPage,
    totalPages,
    totalRecords,
    paginated,
    filtered,
  } = useJobHistory();

  // Card for mobile/tablet view
  const JobHistoryCard = ({ row }: { row: any }) => (
    <Card className="relative mb-4 p-3 sm:p-4 shadow-sm border border-gray-200 rounded-lg hover:shadow-md transition-shadow">
      {/* Status top right */}
      <div className="absolute right-4 top-4">
        <span className="bg-green-200 text-green-700 rounded px-3 py-1 text-xs font-semibold">
          {row.status}
        </span>
      </div>
      {/* Main content */}
      <div className="mb-2">
        <span className="text-base font-semibold text-blue-700">{row.rego}</span>
      </div>
      <div className="mb-1">
        <span className="text-xs text-gray-500 uppercase font-medium">Service Type</span>
        <div className="text-sm">{row.serviceType}</div>
      </div>
      <div className="mb-1">
        <span className="text-xs text-gray-500 uppercase font-medium">Service Date</span>
        <div className="text-sm">{row.serviceDate}</div>
      </div>
    </Card>
  );

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title */}
      <div className="flex items-center mb-4 sm:mb-6">
        <CalendarClock className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3" style={{ color: "#330101" }} />
        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
          Job History
        </h1>
      </div>

      {/* Filter, then Search, then Save */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        {/* Filter */}
        <div className="relative w-full sm:w-auto">
          <Select
            value={filterType}
            onValueChange={(value) => {
              setFilterType(value);
              setCurrentPage(1);
            }}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              {allServiceTypes
                .filter((type) => type !== "All")
                .map((type) => (
                  <SelectItem key={type} value={type}>
                    {type}
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
        </div>
        {/* Search */}
        <div className="relative w-full sm:flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing a name...."
            value={search}
            onChange={(e) => {
              setSearch(e.target.value);
              setCurrentPage(1);
            }}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        {/* Save */}
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap w-full sm:w-auto">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="block md:hidden">
        {(filtered ?? paginated ?? []).length > 0 ? (
          (filtered ?? paginated ?? []).map((row: any, idx: number) => (
            <JobHistoryCard key={idx} row={row} />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            No job history data available
          </div>
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block">
        <div className="w-full rounded-md border overflow-x-auto shadow-sm flex justify-center">
          <Table className="w-full">
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left w-1/4">
                  Rego
                </TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left w-1/4">
                  Service Type
                </TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left w-1/4">
                  Service Date
                </TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4 text-left w-1/4">
                  Status
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginated.length > 0 ? (
                paginated.map((row, idx) => (
                  <TableRow key={idx}>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left w-1/4">
                      {row.rego}
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left w-1/4">
                      {row.serviceType}
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left w-1/4">
                      {row.serviceDate}
                    </TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4 text-left w-1/4">
                      <span className="bg-green-200 text-green-700 rounded px-3 py-1 text-xs font-semibold">
                        {row.status}
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell
                    colSpan={4}
                    className="text-center py-8 text-gray-500"
                  >
                    No job history data available
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
        {/* Pagination for desktop only */}
        <div className="mt-4 sm:mt-6">
          <Pagination
            currentPage={currentPage}
            totalPages={totalPages}
            totalRecords={totalRecords}
            recordsPerPage={pageSize}
            onPageChange={setCurrentPage}
            onRecordsPerPageChange={(records) => {
              setPageSize(records);
              setCurrentPage(1);
            }}
            className="text-sm sm:text-base"
          />
        </div>
      </div>
    </div>
  );
}