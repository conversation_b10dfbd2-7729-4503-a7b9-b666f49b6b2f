import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Textarea } from '../../../components/ui/textarea';
import { ArrowLeft, AlertTriangle, ChevronDown, Camera } from 'lucide-react';
import { useJobcardDamageAdd } from './hook/usejobcard-damage-add';
import { useState } from 'react';

export function JobcardDamageAdd() {
  const {
    formData,
    isSubmitting,
    handleInputChange,
    handleSubmit,
    handleCancel
  } = useJobcardDamageAdd();

  // Example vehicle options with rego, engineNumber, and accidentDate
  const vehicleOptions = [
    { rego: 'ACV 003', engineNumber: '#457815', accidentDate: '2024-07-01' },
    { rego: 'XYZ 789', engineNumber: '#123456', accidentDate: '2024-07-05' },
    { rego: 'DEF 456', engineNumber: '#987654', accidentDate: '2024-07-10' },
    { rego: 'GHI 321', engineNumber: '#654321', accidentDate: '2024-07-15' }
  ];

  const [showVehicleDropdown, setShowVehicleDropdown] = useState(false);

  const handleVehicleSelect = (vehicle: { rego: string; engineNumber: string; accidentDate: string }) => {
    handleInputChange('vehicle', `${vehicle.rego} (${vehicle.engineNumber})`);
    handleInputChange('accidentDate', vehicle.accidentDate);
    setShowVehicleDropdown(false);
  };

  // Service checkboxes state
  const [serviceChecks, setServiceChecks] = useState({
    damagedPanels: false,
    realignFrame: false,
    windscreen: false,
    windows: false,
    mirrors: false,
    headlights: false,
    tailLights: false,
    repaint: false,
    bumperFront: false,
    bumperRear: false,
    grilleFenderBonnet: false,
    airbags: false,
    wheelsSuspension: false,
    wiring: false,
    numberPlate: false,
    bodyQuality: false,
    cleanVehicle: false,
    gps: false,
    roadTest: false,
  });
  const [notesComment, setNotesComment] = useState('');

  const handleServiceCheck = (key: string) => {
    setServiceChecks(prev => ({ ...prev, [key]: !prev[key] }));
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Back Button - Mobile only */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
        
      </div>
      {/* Header - Desktop only */}
      <div className="hidden md:flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
        
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Vehicle Details */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Vehicle Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4">
              {/* Vehicle Rego - keep as simple label above input */}
              <div className="mb-3">
                <span className="block text-xs text-gray-600 mb-1">Vehicle Rego</span>
                <div className="relative flex">
                  <Input
                    id="vehicle"
                    placeholder="Select"
                    value={formData.vehicle}
                    readOnly
                    onClick={() => setShowVehicleDropdown((prev) => !prev)}
                    className="bg-gray-100 text-sm h-8 w-full cursor-pointer pr-8 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                    required
                  />
                  <ChevronDown
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"
                  />
                  {showVehicleDropdown && (
                    <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow left-0 max-h-56 overflow-y-auto">
                      {vehicleOptions.map(option => (
                        <div
                          key={option.rego}
                          className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                          onClick={() => handleVehicleSelect(option)}
                        >
                          {option.rego} ({option.engineNumber})
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Floating label for Damage Description */}
              <div className="relative mb-3">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Description</span>
                <Textarea
                  id="damageDescription"
                  value={formData.damageDescription}
                  onChange={(e) => handleInputChange('damageDescription', e.target.value)}
                  className="bg-gray-100 text-sm min-h-8 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  required
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:block">
            <div className="space-y-6">
              {/* Vehicle Rego */}
              <div className="relative mt-6 pt-3">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Rego</span>
                <div className="relative flex ">
                  <Input
                    id="vehicle"
                    placeholder="Select"
                    value={formData.vehicle}
                    readOnly
                    onClick={() => setShowVehicleDropdown((prev) => !prev)}
                    className="text-sm h-8 w-full cursor-pointer pr-8"
                    required
                  />
                  <ChevronDown
                    className="absolute right-2 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400 pointer-events-none"
                  />
                  {showVehicleDropdown && (
                    <div className="absolute z-10 mt-1 w-full bg-white border rounded shadow left-0 max-h-56 overflow-y-auto">
                      {vehicleOptions.map(option => (
                        <div
                          key={option.rego}
                          className="px-3 py-2 hover:bg-gray-100 cursor-pointer text-sm"
                          onClick={() => handleVehicleSelect(option)}
                        >
                          {option.rego} ({option.engineNumber})
                        </div>
                      ))}
                    </div>
                  )}
                </div>
              </div>
              {/* Damage Description */}
              <div className="space-y-2 relative ">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Description</span>
                <Textarea
                  id="damageDescription"
                  value={formData.damageDescription}
                  onChange={(e) => handleInputChange('damageDescription', e.target.value)}
                  className="text-sm min-h-8"
                  required
                />
              </div>
            </div>
          </div>
        </div>

        {/* Incident Details */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Incident Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-3">
              {/* Damage Parts Identified - floating label style */}
              <div className="relative mb-3">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Parts Identified</span>
                <Input
                  id="damageParts"
                  placeholder=""
                  value={formData.damageParts}
                  onChange={(e) => handleInputChange('damageParts', e.target.value)}
                  className="bg-gray-100 text-sm h-8 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                />
              </div>
              {/* Repair Tasks Required - floating label style */}
              <div className="relative mb-3">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
                <Input
                  id="repairTasks"
                  placeholder=""
                  value={formData.repairTasks}
                  onChange={(e) => handleInputChange('repairTasks', e.target.value)}
                  className="bg-gray-100 text-sm h-8 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                />
              </div>
              {/* Repaired By - floating label style */}
              <div className="relative mb-3">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
                <Input
                  id="repairedBy"
                  placeholder="Select"
                  value={formData.repairedBy}
                  onChange={(e) => handleInputChange('repairedBy', e.target.value)}
                  className="bg-gray-100 text-sm h-8 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent"
                  required
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:block">
            <div className="space-y-6">
              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                <div className="space-y-2 relative">
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Parts Identified</span>
                  <Input
                    id="damageParts"
                    placeholder=""
                    value={formData.damageParts}
                    onChange={(e) => handleInputChange('damageParts', e.target.value)}
                    className="text-sm h-8"
                  />
                </div>
                <div className="space-y-2 relative">
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
                  <Input
                    id="repairTasks"
                    placeholder=""
                    value={formData.repairTasks}
                    onChange={(e) => handleInputChange('repairTasks', e.target.value)}
                    className="text-sm h-8"
                  />
                </div>
              </div>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                <div className="space-y-2 relative">
                  <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
                  <Input
                    id="repairedBy"
                    placeholder="Select"
                    value={formData.repairedBy}
                    onChange={(e) => handleInputChange('repairedBy', e.target.value)}
                    className="text-sm h-8"
                    required
                  />
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Upload Images */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Upload Images</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            {[
              { id: "interiorImages", label: "Interior Images" },
              { id: "leftSideDoors", label: "Left Side Doors" },
              { id: "frontSideImages", label: "Front Side Images" },
              { id: "sideMirrors", label: "Side Mirrors" },
              { id: "exteriorImages", label: "Exterior Images" },
              { id: "rightSideDoors", label: "Right Side Doors" },
              { id: "backSideImages", label: "Back Side Images" },
              { id: "otherImages", label: "Other" }
            ].map(img => (
              <div className="space-y-2 relative" key={img.id}>
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">{img.label}</span>
                <div className="border border-gray-500 rounded-md px-4 py-3 flex items-center gap-2">
                  <Input
                    type="file"
                    id={img.id}
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor={img.id} className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
            ))}
          </div>
          {/* Desktop/Grid View */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Left Column */}
            <div className="space-y-6">
              <div className="space-y-2 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Interior Images</span>
                <div className="border rounded px-4 py-3 flex items-center">
                  <Input
                    type="file"
                    id="interiorImages"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor="interiorImages" className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
              <div className="space-y-2 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Left Side Doors</span>
                <div className="border rounded px-4 py-3 flex items-center">
                  <Input
                    type="file"
                    id="leftSideDoors"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor="leftSideDoors" className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
              <div className="space-y-2 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Front Side Images</span>
                <div className="border rounded px-4 py-3 flex items-center">
                  <Input
                    type="file"
                    id="frontSideImages"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor="frontSideImages" className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
              <div className="space-y-2 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Side Mirrors</span>
                <div className="border rounded px-4 py-3 flex items-center">
                  <Input
                    type="file"
                    id="sideMirrors"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor="sideMirrors" className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
            </div>
            {/* Right Column */}
            <div className="space-y-6">
              <div className="space-y-2 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Exterior Images</span>
                <div className="border rounded px-4 py-3 flex items-center">
                  <Input
                    type="file"
                    id="exteriorImages"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor="exteriorImages" className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
              <div className="space-y-2 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Right Side Doors</span>
                <div className="border rounded px-4 py-3 flex items-center">
                  <Input
                    type="file"
                    id="rightSideDoors"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor="rightSideDoors" className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
              <div className="space-y-2 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Back Side Images</span>
                <div className="border rounded px-4 py-3 flex items-center">
                  <Input
                    type="file"
                    id="backSideImages"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor="backSideImages" className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
              <div className="space-y-2 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Other</span>
                <div className="border rounded px-4 py-3 flex items-center">
                  <Input
                    type="file"
                    id="otherImages"
                    accept="image/*"
                    capture="environment"
                    className="hidden"
                    onChange={(e) => {
                      // handle file upload logic here
                    }}
                  />
                  <label htmlFor="otherImages" className="cursor-pointer text-gray-700 text-base w-full flex items-center gap-2">
                    <Camera className="w-5 h-5 text-gray-500 mr-1 flex-shrink-0" />
                    <span className="flex-1 text-left">Add Photo</span>
                  </label>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Job Card Notes */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Job Card Notes</h2>
          <div className="font-semibold mb-2">The Service Requires;</div>
          <div className="space-y-3 mb-4">
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Service Checklist</span>
              <div className="pt-3 space-y-3">
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.damagedPanels} onChange={() => handleServiceCheck('damagedPanels')} />
                  Remove and replace damaged body panels
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.realignFrame} onChange={() => handleServiceCheck('realignFrame')} />
                  Remove dents and realign frame
                </label>
                <div className="flex flex-wrap items-center gap-2">
                  Replace broken glass -
                  <label className="flex items-center gap-1">
                    <input type="checkbox" checked={serviceChecks.windscreen} onChange={() => handleServiceCheck('windscreen')} />
                    Windscreen
                  </label>
                  <label className="flex items-center gap-1">
                    <input type="checkbox" checked={serviceChecks.windows} onChange={() => handleServiceCheck('windows')} />
                    Windows
                  </label>
                  <label className="flex items-center gap-1">
                    <input type="checkbox" checked={serviceChecks.mirrors} onChange={() => handleServiceCheck('mirrors')} />
                    Mirrors
                  </label>
                </div>
                <div className="flex flex-wrap items-center gap-2">
                  Repair or replace damaged lights -
                  <label className="flex items-center gap-1">
                    <input type="checkbox" checked={serviceChecks.headlights} onChange={() => handleServiceCheck('headlights')} />
                    Headlights
                  </label>
                  <label className="flex items-center gap-1">
                    <input type="checkbox" checked={serviceChecks.tailLights} onChange={() => handleServiceCheck('tailLights')} />
                    Tail lights
                  </label>
                </div>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.repaint} onChange={() => handleServiceCheck('repaint')} />
                  Repaint affected areas
                </label>
                <div className="flex flex-wrap items-center gap-2">
                  Replace/repair bumper -
                  <label className="flex items-center gap-1">
                    <input type="checkbox" checked={serviceChecks.bumperFront} onChange={() => handleServiceCheck('bumperFront')} />
                    Front
                  </label>
                  <label className="flex items-center gap-1">
                    <input type="checkbox" checked={serviceChecks.bumperRear} onChange={() => handleServiceCheck('bumperRear')} />
                    Rear
                  </label>
                </div>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.grilleFenderBonnet} onChange={() => handleServiceCheck('grilleFenderBonnet')} />
                  Replace damaged grille/fender/bonnet
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.airbags} onChange={() => handleServiceCheck('airbags')} />
                  Inspect airbags and safety systems
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.wheelsSuspension} onChange={() => handleServiceCheck('wheelsSuspension')} />
                  Realign wheels and suspension
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.wiring} onChange={() => handleServiceCheck('wiring')} />
                  Electrical wiring check and repair
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.numberPlate} onChange={() => handleServiceCheck('numberPlate')} />
                  Reattach number plate
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.bodyQuality} onChange={() => handleServiceCheck('bodyQuality')} />
                  Perform final body quality check
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.cleanVehicle} onChange={() => handleServiceCheck('cleanVehicle')} />
                  Clean vehicle exterior/interior post-repair
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.gps} onChange={() => handleServiceCheck('gps')} />
                  Check GPS
                </label>
                <label className="flex items-center gap-2">
                  <input type="checkbox" checked={serviceChecks.roadTest} onChange={() => handleServiceCheck('roadTest')} />
                  Road test
                </label>
              </div>
            </div>
            <div className="mt-4 relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</span>
              <Textarea
                id="notesComment"
                placeholder=""
                value={notesComment}
                onChange={e => setNotesComment(e.target.value)}
                className="text-sm min-h-8"
              />
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex flex-row flex-wrap justify-end gap-2 pt-4">
          <Button
            type="submit"
            className="bg-[#330110] hover:bg-gray-800 text-white px-8 py-2  sm:w-32 text-sm"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Adding...' : 'Add'}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="px-8 py-2  sm:w-32 text-sm"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}