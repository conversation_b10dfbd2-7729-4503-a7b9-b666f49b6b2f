import React, { useState } from 'react';
import { Button } from '@/components/ui/button';
import { Checkbox } from '@/components/ui/checkbox';
import { useNavigate, useParams } from 'react-router-dom';
import { Pagination } from '@/components/layout/Pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';

export function VehicleExpensesPage() {
  const { id } = useParams<{ id: string }>();
  const navigate = useNavigate();
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(10);

  const expensesData: any[] = []; // Empty array as per original code
  const totalEntries = expensesData.length;
  const totalPages = Math.max(1, Math.ceil(totalEntries / recordsPerPage)); 
  const handleTabClick = (tab: string) => {
    if (!id) {
      console.error('Vehicle ID is undefined for tab navigation');
      navigate('/receptionMasterAdmin/fleet/vehicles');
      return;
    }

    const tabRoutes: { [key: string]: string } = {
      'Edit': `/admin/receptionMasterAdmin/fleet/vehicles/edit-vehicle/${id}`,
      'Reservations': `/admin/receptionMasterAdmin/fleet/vehicles/reservations/${id}`,
      'Damages': `/admin/receptionMasterAdmin/fleet/vehicles/damages/${id}`,
      'Blocked Periods': `/admin/receptionMasterAdmin/fleet/vehicles/blocked-periods/${id}`,
      'Expenses': `/admin/receptionMasterAdmin/fleet/vehicles/expenses/${id}`,
      'Relocations': `/admin/receptionMasterAdmin/fleet/vehicles/relocations/${id}`,
      'Repair Orders': `/admin/receptionMasterAdmin/fleet/vehicles/repair-orders/${id}`,
      'Files': `/admin/receptionMasterAdmin/fleet/vehicles/files/${id}`,
      'Check List': `/admin/receptionMasterAdmin/fleet/vehicles/check-list/${id}`
    };

    navigate(tabRoutes[tab]);
  };


  return (
    <div className="min-h-screen p-6">
      <div className="max-w-7xl mx-auto">
        {/* Header */}
        <div className="mb-6">
          
          <div className="flex justify-between items-center">
            <div className="flex items-center space-x-2">
              <h1 className="text-2xl font-semibold text-gray-900">
                Mercedes-Benz E 280 CDI Classic - {id || 'Unknown'}
              </h1>
              <span className="bg-[#330101] text-white px-3 py-1 rounded text-sm font-medium">
                Rental
              </span>
            </div>
            <div className="flex items-center space-x-2">
              <Button className="bg-[#330101] hover:bg-[#660404] text-white">
                Add Expense
              </Button>
            {/*   <Button variant="outline" size="sm">
                <MoreHorizontal className="h-4 w-4" />
              </Button> */}
            </div>
          </div>

          {/* Tabs */}
          <div className="flex space-x-1">
            {['Edit', 'Reservations', 'Damages', 'Blocked Periods', 'Expenses', 'Relocations', 'Repair Orders', 'Files', 'Check List'].map((tab) => (
              <Button
                key={tab}
                onClick={() => handleTabClick(tab)}
                className={`px-4 py-2 text-sm font-medium border border-gray-300 transition-colors hover:bg-gray-100 ${
                  tab === 'Expenses'
                    ? 'bg-white text-gray-900'
                    : 'bg-gray-100 text-gray-600'
                }`}
              >
                {tab}
              </Button>
            ))}
          </div>
        </div>

        {/* Table */}
        <div className="hidden md:block rounded-md border overflow-hidden">
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow className='bg-gray-50 uppercase'>
                 {/* <TableHead className="w-12 p-4">
                    <Checkbox />
                  </TableHead> */} 
                  <TableHead>#</TableHead>
                  <TableHead>Vehicle</TableHead>
                  <TableHead>Label</TableHead>
                  <TableHead>Expense Type</TableHead>
                  <TableHead>Amount</TableHead>
                  <TableHead>Date</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {expensesData.length === 0 ? (
                  <TableRow>
                    <TableCell colSpan={7} className="p-8 text-center text-gray-500">
                      No data available in table
                    </TableCell>
                  </TableRow>
                ) : (
                  expensesData.slice(
                    (currentPage - 1) * recordsPerPage,
                    currentPage * recordsPerPage
                  ).map((record, index) => (
                    <TableRow key={index} className="hover:bg-gray-50">
                     {/*  <TableCell className="p-4">
                        <Checkbox />
                      </TableCell>*/}
                      <TableCell>{record.id}</TableCell>
                      <TableCell>{record.vehicle}</TableCell>
                      <TableCell>{record.label}</TableCell>
                      <TableCell>{record.expenseType}</TableCell>
                      <TableCell>{record.amount}</TableCell>
                      <TableCell>{record.date}</TableCell>
                    </TableRow>
                  ))
                )}
              </TableBody>
            </Table>
          </div>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(value) => {
            setRecordsPerPage(value);
            setCurrentPage(1);
          }}
          recordsPerPageOptions={[5, 10, 25, 50]}
          className="mt-4"
        />
      </div>
    </div>
  );
}