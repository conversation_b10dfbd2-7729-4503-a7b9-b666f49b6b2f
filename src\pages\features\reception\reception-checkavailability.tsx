
import React, { useState, useEffect } from 'react';
import { Button } from '@/components/ui/button';
import { Label } from '@/components/ui/label';
import { Input } from '@/components/ui/input';
import { Search, Eye, ArrowLeft } from 'lucide-react';
import { useNavigate } from 'react-router-dom';

interface VehicleFeatures {
  transmission: boolean;
  doors?: number;
  seats?: number;
  powerSteering: boolean;
  radioCd: boolean;
  airConditioning: boolean;
  hydraulicLift?: boolean;
  liftCapacity?: string;
}

interface VehicleOption {
  id: string;
  name: string;
  price: number;
  totalPrice: number;
  image: string;
  features: VehicleFeatures;
  available: boolean;
  recommended?: boolean;
  limitedAvailability?: boolean;
  vehiclesAvailable: number;
  availabilityPercentage?: string;
}

interface RentalDetails {
  pickupDate: Date;
  pickupTime: string;
  returnDate: Date;
  returnTime: string;
  walkInCustomer: boolean;
}

const initialVehicles: VehicleOption[] = [
  {
    id: 'eco-plus',
    name: 'Eco Plus Car',
    price: 42.00,
    totalPrice: 42.00,
    image: '/Eco Plus Car 1.png',
    features: { transmission: true, doors: 4, powerSteering: true, radioCd: true, airConditioning: true },
    available: true,
    vehiclesAvailable: 2,
    availabilityPercentage: '33.3%',
  },
  {
    id: 'standard',
    name: 'Standard Car',
    price: 62.00,
    totalPrice: 62.00,
    image: '/Standard Car.png',
    features: { transmission: true, doors: 4, powerSteering: true, radioCd: true, airConditioning: true },
    available: true,
    recommended: true,
    vehiclesAvailable: 13,
    availabilityPercentage: '72.2%',
  },
  {
    id: 'minibus',
    name: 'Minibus 7 Seats',
    price: 107.00,
    totalPrice: 107.00,
    image: '/Minibus.png',
    features: { transmission: true, powerSteering: true, radioCd: true, airConditioning: true },
    available: false,
    vehiclesAvailable: 0,
    availabilityPercentage: '0%',
  },
  {
    id: 'delivery-van',
    name: 'Delivery Van 1.25 Tonne - With the Hydraulic Lift',
    price: 89.00,
    totalPrice: 89.00,
    image: '/Delivery Van.png',
    features: { transmission: true, seats: 3, powerSteering: true, radioCd: true, airConditioning: true, hydraulicLift: true, liftCapacity: '250kg' },
    available: false,
    limitedAvailability: true,
    vehiclesAvailable: 0,
    availabilityPercentage: '0%',
  },
];

const FeatureIcon: React.FC<{ feature: string }> = ({ feature }) => {
  const iconColor = 'text-[#EBBB4E]';
  const iconMap: Record<string, JSX.Element> = {
    transmission: <Search className={`w-4 h-4 ${iconColor}`} />,
    doors: <Eye className={`w-4 h-4 ${iconColor}`} />,
    seats: <Eye className={`w-4 h-4 ${iconColor}`} />,
    powerSteering: <Search className={`w-4 h-4 ${iconColor}`} />,
    radioCd: <Eye className={`w-4 h-4 ${iconColor}`} />,
    airConditioning: <Search className={`w-4 h-4 ${iconColor}`} />,
    hydraulicLift: <Eye className={`w-4 h-4 ${iconColor}`} />,
  };
  return iconMap[feature] || null;
};

export function ReceptionCheckAvailability() {
  const navigate = useNavigate();

  // Initialize dates
  const today = new Date();
  const tomorrow = new Date(today);
  tomorrow.setDate(today.getDate() + 1);

  const [rentalDetails, setRentalDetails] = useState<RentalDetails>({
    pickupDate: today,
    pickupTime: '',
    returnDate: tomorrow,
    returnTime: '',
    walkInCustomer: false,
  });
  const [vehicles, setVehicles] = useState<VehicleOption[]>(initialVehicles);
  const [showAvailability, setShowAvailability] = useState(false);
  const [currentDateTime, setCurrentDateTime] = useState(new Date());

  // Format current date and time for display
 const formattedDateTime = currentDateTime.toLocaleString('en-US', {
    weekday: 'long',
    day: '2-digit',
    month: '2-digit',
    year: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true,
    timeZone: 'Asia/Kolkata',
  }).replace(/(\d+)\/(\d+)\/(\d+),/, '$2/$1/$3,');

  // Format dates for input type="date" (YYYY-MM-DD)
  const formatDateForInput = (date: Date) => {
    return date.toISOString().split('T')[0];
  };

  // Validate and update return date when pickup date changes
useEffect(() => {
    const interval = setInterval(() => {
      setCurrentDateTime(new Date());
    }, 1000); // Update every second
    return () => clearInterval(interval); // Cleanup on unmount
  }, []);

  const handleCheckAvailability = () => {
    // Simple logic to simulate availability check based on dates and walk-in status
    const updatedVehicles = initialVehicles.map(vehicle => {
      const isAvailable = vehicle.available && Math.random() > 0.3; // Randomly adjust availability
      return {
        ...vehicle,
        available: isAvailable,
        vehiclesAvailable: isAvailable ? Math.floor(Math.random() * 5) + 1 : 0,
        availabilityPercentage: isAvailable ? `${(Math.random() * 100).toFixed(1)}%` : '0%',
      };
    });
    setVehicles(updatedVehicles);
    setShowAvailability(true);
  };

  const handleRentVehicle = (id: string) => {
    navigate('/search/extras', { state: { vehicleId: id, rentalDetails } });
  };

  // Calculate min date for return date (pickup date + 1 day)
  const minReturnDate = new Date(rentalDetails.pickupDate);
  minReturnDate.setDate(rentalDetails.pickupDate.getDate() + 1);
  const minReturnDateStr = formatDateForInput(minReturnDate);

  return (
    <>
      <div>
        <div className="max-w-7xl mx-auto px-4 py-4">
          <div className="mb-2 xs:mb-6 sm:mb-4 md:mb-4 lg:mb-4 xl:mb-4 text-left">
            <div className="mb-5 flex items-center space-x-4">
              <Button
              size="sm"
               className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
                onClick={() => navigate('/reception/reception-reservantionmanagement')}>
                <ArrowLeft className="w-4 h-4" />
                <span>Go Back</span>
              </Button>
            </div>
            <h1 className="mb-5 text-xl sm:text-2xl md:text-xl lg:text-xl xl:text-xl font-semibold text-gray-800">
              Availability Check - {formattedDateTime}
            </h1>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-4 gap-4">
            <div className="lg:col-span-3">
              <div className="max-w-full xs:max-w-2xl sm:max-w-3xl md:max-w-6xl lg:max-w-5xl xl:max-w-6xl mx-auto">
                <div className="mb-2 xs:mb-2 sm:mb-2 md:mb-2 lg:mb-4 xl:mb-4 mt-1">
                  <h2 className="text-lg xs:text-lg sm:text-xl md:text-xl lg:text-xl xl:text-xl font-semibold mb-2">
                    Date and Time
                  </h2>
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10">Pickup Date</Label>
                      <Input
                        type="date"
                        value={formatDateForInput(rentalDetails.pickupDate)}
                        onChange={(e) => {
                          const newDate = new Date(e.target.value);
                          if (!isNaN(newDate.getTime())) {
                            setRentalDetails({ ...rentalDetails, pickupDate: newDate });
                          }
                        }}
                        min={formatDateForInput(today)}
                        className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                      />
                    </div>
                    <div>
                      <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10">Pickup Time</Label>
                      <Input
                        type="text"
                        value={rentalDetails.pickupTime}
                        onChange={(e) => setRentalDetails({ ...rentalDetails, pickupTime: e.target.value })}
                        placeholder="HH:MM"
                        className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                      />
                    </div>
                  </div>
                </div>

                <div className="mb-6 xs:mb-6 sm:mb-8 md:mb-8 lg:mb-10 xl:mb-10">
                  <div className="grid grid-cols-2 gap-4">
                    <div>
                      <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10">Return Date</Label>
                      <Input
                        type="date"
                        value={formatDateForInput(rentalDetails.returnDate)}
                        onChange={(e) => {
                          const newDate = new Date(e.target.value);
                          if (!isNaN(newDate.getTime())) {
                            setRentalDetails({ ...rentalDetails, returnDate: newDate });
                          }
                        }}
                        min={minReturnDateStr}
                        className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                      />
                    </div>
                    <div>
                      <Label className="absolute bg-white px-2 text-xs text-gray-600 z-10">Return Time</Label>
                      <Input
                        type="text"
                        value={rentalDetails.returnTime}
                        onChange={(e) => setRentalDetails({ ...rentalDetails, returnTime: e.target.value })}
                        placeholder="HH:MM"
                        className="mt-1 focus:outline-none focus:ring-0 focus:border-none"
                      />
                    </div>
                  </div>
                </div>

                <div className="mb-6 xs:mb-6 sm:mb-8 md:mb-8 lg:mb-10 xl:mb-6">
                  <h2 className="text-sm xs:text-sm sm:text-sm md:text-sm lg:text-lg xl:text-base font-semibold">
                    Walk In Customer?
                  </h2>
                  <div className="flex space-x-6">
                    <label className="flex items-center space-x-2">
                      <Input
                        type="radio"
                        name="walkInCustomer"
                        checked={rentalDetails.walkInCustomer}
                        onChange={() => setRentalDetails({ ...rentalDetails, walkInCustomer: true })}
                        className="form-radio"
                      />
                      <span>Yes</span>
                    </label>
                    <label className="flex items-center space-x-2">
                      <Input
                        type="radio"
                        name="walkInCustomer"
                        checked={!rentalDetails.walkInCustomer}
                        onChange={() => setRentalDetails({ ...rentalDetails, walkInCustomer: false })}
                        className="form-radio"
                      />
                      <span>No</span>
                    </label>
                  </div>
                </div>

                <div className="mb-6 xs:mb-6 sm:mb-8 md:mb-8 lg:mb-10 xl:mb-10">
                  <Button
                    onClick={handleCheckAvailability}
                    className="bg-[#330101] text-white px-4 py-2 rounded"
                  >
                    Check Availability
                  </Button>
                </div>

                {showAvailability && (
                  <div>
                    <div className="space-y-6 xs:space-y-6 sm:space-y-8 md:space-y-8 lg:space-y-10 xl:space-y-10">
                      {vehicles.filter(vehicle => vehicle.available).map((vehicle) => (
                        <div
                          key={vehicle.id}
                          className="bg-white rounded-lg border border-gray-200 p-4 xs:p-4 sm:p-6 md:p-6 lg:p-8 xl:p-8 relative"
                        >
                          {vehicle.recommended && (
                            <div className="absolute top-0 right-0 bg-green-500 text-white px-2 py-1 text-xs font-medium">
                              Recommended
                            </div>
                          )}
                          <div className="flex flex-col sm:flex-row gap-4">
                            <div className="w-full sm:w-1/3">
                              <img
                                src={vehicle.image}
                                alt={vehicle.name}
                                className="w-full h-40 object-cover rounded-lg bg-gray-100"
                              />
                            </div>
                            <div className="w-full sm:w-1/3 space-y-2">
                              <h3 className="text-base sm:text-lg font-semibold">{vehicle.name}</h3>
                              <div className="grid grid-cols-2 gap-2 text-sm">
                                {Object.entries(vehicle.features).map(([key, value]) =>
                                  value && (
                                    <div key={key} className="flex items-center space-x-2">
                                      <FeatureIcon feature={key} />
                                      <span>
                                        {key === 'doors'
                                          ? `${value}-doors`
                                          : key === 'seats'
                                          ? `${value} Seats`
                                          : key === 'hydraulicLift'
                                          ? `Hydraulic Lift (${vehicle.features.liftCapacity})`
                                          : key.split(/(?=[A-Z])/).join(' ').replace('Cd', 'CD')}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                            <div className="w-full sm:w-1/3 text-right space-y-2">
                              <div>
                                <div className="text-lg sm:text-xl font-medium">
                                  AUD {vehicle.price.toFixed(2)}<span className="text-sm text-gray-500 ml-1">/ day</span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  AUD {vehicle.totalPrice.toFixed(2)} total for 1 Day
                                </div>
                                <div className="text-sm text-gray-600">Including GST</div>
                                <div className="text-sm text-blue-600">
                                  Vehicle Available: {vehicle.vehiclesAvailable} ({vehicle.availabilityPercentage})
                                </div>
                              </div>
                              <Button
                                onClick={() => handleRentVehicle(vehicle.id)}
                                className="w-full border border-[#EBBB4E] text-[#EBBB4E] hover:bg-[#EBBB4E] hover:text-white py-2"
                                disabled={!vehicle.available}
                              >
                                Rent this vehicle
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>

                    <div className="space-y-6 xs:space-y-6 sm:space-y-8 md:space-y-8 lg:space-y-10 xl:space-y-10">
                      {vehicles.filter(vehicle => !vehicle.available).map((vehicle) => (
                        <div
                          key={vehicle.id}
                          className="bg-white rounded-lg border border-gray-200 p-4 xs:p-4 sm:p-6 md:p-6 lg:p-8 xl:p-8 relative"
                        >
                          {vehicle.limitedAvailability && (
                            <div className="absolute top-0 right-0 bg-blue-400 text-white px-2 py-1 text-xs font-medium">
                              Limited Availability
                            </div>
                          )}
                          <div className="flex flex-col sm:flex-row gap-4">
                            <div className="w-full sm:w-1/3">
                              <img
                                src={vehicle.image}
                                alt={vehicle.name}
                                className="w-60 h-40 object-cover rounded-lg bg-gray-100"
                              />
                            </div>
                            <div className="w-full sm:w-1/3 space-y-2">
                              <h3 className="text-base sm:text-lg font-semibold">{vehicle.name}</h3>
                              <div className="grid grid-cols-2 gap-2 text-sm">
                                {Object.entries(vehicle.features).map(([key, value]) =>
                                  value && (
                                    <div key={key} className="flex items-center space-x-2">
                                      <FeatureIcon feature={key} />
                                      <span>
                                        {key === 'doors'
                                          ? `${value}-doors`
                                          : key === 'seats'
                                          ? `${value} Seats`
                                          : key === 'hydraulicLift'
                                          ? `Hydraulic Lift (${vehicle.features.liftCapacity})`
                                          : key.split(/(?=[A-Z])/).join(' ').replace('Cd', 'CD')}
                                      </span>
                                    </div>
                                  )
                                )}
                              </div>
                            </div>
                            <div className="w-full sm:w-1/3 text-right space-y-2">
                              <div>
                                <div className="text-lg sm:text-xl font-medium">
                                  AUD {vehicle.price.toFixed(2)}<span className="text-sm text-gray-500 ml-1">/ day</span>
                                </div>
                                <div className="text-sm text-gray-600">
                                  AUD {vehicle.totalPrice.toFixed(2)} total for 1 Day
                                </div>
                                <div className="text-sm text-gray-600">Including GST</div>
                                <div className="text-sm text-red-600">
                                  Vehicle Available: {vehicle.vehiclesAvailable} ({vehicle.availabilityPercentage})
                                </div>
                              </div>
                              <Button
                                disabled
                                className="w-full bg-red-600 text-white py-2"
                              >
                                Not Available
                              </Button>
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  </div>
                )}
              </div>
            </div>
            <div className="lg:col-span-1">
              <div className="p-4 sm:p-6 rounded-lg shadow-sm sticky top-6">
                {/* Placeholder for summary or additional info */}
              </div>
            </div>
          </div>
        </div>
      </div>
    </>
  );
}
