import { NavigateFunction } from 'react-router-dom';
import { VehicleFormData } from '../type/teamleadertype';

export const handleInputChange = (
  field: keyof VehicleFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<VehicleFormData>>
): void => {
  setFormData(prev => ({ ...prev, [field]: value }));
};

export const handleGoBack = (navigate: NavigateFunction): void => {
  navigate('/teamleader/service-management');
};