import { ArrowLeft, Download } from 'lucide-react';
import { useJobcardDamageView } from './hook/usejobcard-damage-view';
import { Button } from '../../../components/ui/button';
import carImg from '../../../assets/car.png';
import { useNavigate } from 'react-router-dom';
import jsPDF from 'jspdf';
import html2canvas from 'html2canvas';
import { useRef } from 'react';

export function JobcardDamageView() {
  const navigate = useNavigate();
  const pageRef = useRef<HTMLDivElement>(null);

  // Use mock data for job card details and proof images
  const formData = {
    vehicle: 'Economy - YT1 230',
    damageDescription: 'Front bumper and left headlight damaged due to a frontal collision with a pole',
    accidentDate: '25/05/2025',
    damageParts: 'Bumper, Headlight, Door',
    repairTasks: 'Part Replacement & Painting',
    repairedBy: '<PERSON>',
    repairCompletionDate: '25/05/2025',
    customer: [
      "Customer:",
      "01 Lion Car Rentals Somerton",
      "2/85 Hume Hwy",
      "Somerton, VIC 3062",
      "0393037447",
      "<EMAIL>"
    ],
    repairOrder: [
      "Repair Order / Job Card",
      "Post Date: 25/05/2025",
      "Job Card: 123456",
      "New Odometer / Hours: 123456"
    ]
  };
  const proofImages = [carImg, carImg];
  const notesComment = 'Vehicle inspected and ready for repair.';
  const serviceNotes = [
    'Remove and replace damaged body panels',
    'Replace broken glass - Windscreen, Windows',
    'Repair or replace damaged lights - Headlights',
    'Replace/repair bumper - Front',
    'Replace damaged grille/fender/bonnet',
    'Road test'
  ];

  // Download handler using html2canvas + jsPDF
  const handleDownload = async () => {
    if (!pageRef.current) return;

    // Hide all buttons before capture
    const buttons = pageRef.current.querySelectorAll('button');
    buttons.forEach(btn => (btn.style.display = 'none'));

    // Optionally, scroll to top to ensure all content is visible
    window.scrollTo(0, 0);

    // Wait for UI to update
    await new Promise(res => setTimeout(res, 100));

    // Capture the page as an image
    const canvas = await html2canvas(pageRef.current, { scale: 2, useCORS: true });
    const imgData = canvas.toDataURL('image/png');

    // Restore buttons after capture
    buttons.forEach(btn => (btn.style.display = ''));

    // Create PDF (auto fit to A4, multi-page if needed)
    const pdf = new jsPDF({
      orientation: 'portrait',
      unit: 'px',
      format: 'a4'
    });

    const pageWidth = pdf.internal.pageSize.getWidth();
    const pageHeight = pdf.internal.pageSize.getHeight();

    // Calculate image dimensions to fit A4
    const imgWidth = pageWidth;
    const imgHeight = (canvas.height * imgWidth) / canvas.width;

    let position = 0;
    let remainingHeight = imgHeight;

    // If content is longer than one page, add pages
    while (remainingHeight > 0) {
      pdf.addImage(
        imgData,
        'PNG',
        0,
        position ? 0 : 0,
        imgWidth,
        imgHeight
      );
      remainingHeight -= pageHeight;
      if (remainingHeight > 0) {
        pdf.addPage();
        position -= pageHeight;
      }
    }

    pdf.save('damage-details.pdf');
  };

  const handleRequestParts = () => {
    navigate('/mechanic/request-parts-add?service=damage');
  };
  const handleRequestServices = () => {
    navigate('/mechanic/request-service-add?service=damage');
  };

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen" ref={pageRef}>
      {/* Back Button - Mobile only */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] flex items-center justify-center text-sm"
          size="sm"
          onClick={() => navigate('/mechanic/jobcard-damage')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
        {/* Request Parts & Services Buttons - Mobile only */}
        <div className="flex flex-col gap-2 mt-2">
          <Button
            className="w-full bg-[#330101] text-white px-4 py-2"
            onClick={handleRequestParts}
          >
            Request parts
          </Button>
          <Button
            className="w-full bg-[#330101] text-white px-4 py-2"
            onClick={handleRequestServices}
          >
            Request Services
          </Button>
        </div>
      </div>
      {/* Header - Desktop only */}
      <div className="hidden md:flex justify-between items-center mb-6 no-print">
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
          size="sm"
          onClick={() => navigate('/mechanic/jobcard-damage')}
        >
          <ArrowLeft className="h-4 w-4 mr-1" />
          Go Back
        </Button>
        <div className="flex gap-2">
          <Button className="bg-[#330101] text-white px-4 py-2" onClick={handleRequestParts}>
            Request parts
          </Button>
          <Button className="bg-[#330101] text-white px-4 py-2" onClick={handleRequestServices}>
            Request Services
          </Button>
        </div>
      </div>

      {/* Vehicle Details */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Vehicle Details</h2>
        {/* Mobile Card View */}
        <div className="block md:hidden space-y-4">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle</span>
              <input
                type="text"
                value={formData.vehicle}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 rounded border px-2 mt-2"
                style={{ minHeight: 32, display: 'flex', alignItems: 'center' }}
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Description</span>
              <input
                type="text"
                value={formData.damageDescription}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 rounded border px-2 mt-2"
                style={{ minHeight: 32, display: 'flex', alignItems: 'center' }}
              />
            </div>
          </div>
        </div>
        {/* Desktop/Grid View */}
        <div className="hidden md:block">
          <div className="space-y-6">
            <div className="relative w-1/3">
              <input
                type="text"
                value={formData.vehicle}
                readOnly
                disabled
                className="w-full border border-gray-300 rounded px-2 py-2 text-black text-sm bg-gray-100 focus:outline-none"
              />
              <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Vehicle
              </label>
            </div>
            <div className="relative">
              <input
                type="text"
                value={formData.damageDescription}
                readOnly
                disabled
                className="w-full border border-gray-300 rounded px-2 py-2 text-black text-sm bg-gray-100 focus:outline-none"
              />
              <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Damage Description
              </label>
            </div>
          </div>
        </div>
      </div>

      {/* Incident Details */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Incident Details</h2>
        {/* Mobile Card View */}
        <div className="block md:hidden space-y-4">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Damage Parts Identified</span>
              <input
                type="text"
                value={formData.damageParts}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 rounded border px-2 mt-2"
                style={{ minHeight: 32, display: 'flex', alignItems: 'center' }}
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
              <input
                type="text"
                value={formData.repairTasks}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 rounded border px-2 mt-2"
                style={{ minHeight: 32, display: 'flex', alignItems: 'center' }}
              />
            </div>
            <div className="relative">
              <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
              <input
                type="text"
                value={formData.repairedBy}
                readOnly
                className="text-sm h-8 w-full bg-gray-100 rounded border px-2 mt-2"
                style={{ minHeight: 32, display: 'flex', alignItems: 'center' }}
              />
            </div>
          </div>
        </div>
        {/* Desktop/Grid View */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-6">
          <div className="relative">
            <input
              type="text"
              value={formData.damageParts}
              readOnly
              disabled
              className="w-full border border-gray-300 rounded px-2 py-2 text-black text-sm bg-gray-100 focus:outline-none"
            />
            <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Damage Parts Identified
            </label>
          </div>
          <div className="relative">
            <input
              type="text"
              value={formData.repairTasks}
              readOnly
              disabled
              className="w-full border border-gray-300 rounded px-2 py-2 text-black text-sm bg-gray-100 focus:outline-none"
            />
            <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Repair Tasks Required
            </label>
          </div>
          <div className="relative">
            <input
              type="text"
              value={formData.repairedBy}
              readOnly
              disabled
              className="w-full border border-gray-300 rounded px-2 py-2 text-black text-sm bg-gray-100 focus:outline-none"
            />
            <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Repaired By
            </label>
          </div>
        </div>
      </div>

      {/* Upload Images */}
      <div className="mb-8">
        <h2 className="text-lg font-bold mb-4">Upload Images</h2>
        {/* Mobile Card View */}
        <div className="block md:hidden space-y-4">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
            {/*
              { id: "interiorImages", label: "Interior Images" },
              { id: "leftSideDoors", label: "Left Side Doors" },
              { id: "frontSideImages", label: "Front Side Images" },
              { id: "sideMirrors", label: "Side Mirrors" },
              { id: "exteriorImages", label: "Exterior Images" },
              { id: "rightSideDoors", label: "Right Side Doors" },
              { id: "backSideImages", label: "Back Side Images" },
              { id: "otherImages", label: "Other" },
            */}
            {['Interior Images', 'Left Side Doors', 'Front Side Images', 'Side Mirrors', 'Exterior Images', 'Right Side Doors', 'Back Side Images', 'Other'].map((label, idx) => (
              <div className="relative" key={idx}>
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">{label}</span>
                <div className="border rounded px-4 py-3 flex items-center mt-2">
                  <img src={carImg} alt={label} className="w-16 h-16 object-cover rounded border" />
                  <span className="ml-4 text-gray-700 text-sm">{label}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
        {/* Desktop/Grid View */}
        <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-6">
          <div className="space-y-6">
            {['Interior Images', 'Left Side Doors', 'Front Side Images', 'Side Mirrors'].map((label, idx) => (
              <div className="relative" key={idx}>
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">{label}</span>
                <div className="border rounded px-4 py-3 flex items-center mt-2">
                  <img src={carImg} alt={label} className="w-16 h-16 object-cover rounded border" />
                  <span className="ml-4 text-gray-700 text-sm">{label}</span>
                </div>
              </div>
            ))}
          </div>
          <div className="space-y-6">
            {['Exterior Images', 'Right Side Doors', 'Back Side Images', 'Other'].map((label, idx) => (
              <div className="relative" key={idx}>
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">{label}</span>
                <div className="border rounded px-4 py-3 flex items-center mt-2">
                  <img src={carImg} alt={label} className="w-16 h-16 object-cover rounded border" />
                  <span className="ml-4 text-gray-700 text-sm">{label}</span>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>

      {/* Comments */}
      <div className="mb-4">
        <div className="font-semibold mb-1">Comments:</div>
        <div className="bg-gray-100 rounded px-2 py-2 text-sm">{notesComment || 'No comments.'}</div>
      </div>

      {/* Job Card Notes */}
      <div>
        <h2 className="text-lg font-bold mb-2">Job Card Notes</h2>
        <div className="font-semibold mb-2">The Service Requires;</div>
        {/* Mobile Card View */}
        <div className="block md:hidden">
          <div className="bg-white rounded-lg shadow-sm p-4 space-y-2">
            <ul className="list-disc ml-6 text-sm">
              {serviceNotes.length > 0 ? (
                serviceNotes.map((note, idx) => <li key={idx}>{note}</li>)
              ) : (
                <li>No service notes.</li>
              )}
            </ul>
          </div>
        </div>
        {/* Desktop View */}
        <div className="hidden md:block">
          <ul className="list-disc ml-6 text-sm">
            {serviceNotes.length > 0 ? (
              serviceNotes.map((note, idx) => <li key={idx}>{note}</li>)
            ) : (
              <li>No service notes.</li>
            )}
          </ul>
        </div>
      </div>

      {/* Download Button at the end */}
      <div className="flex justify-end mt-8 no-print">
        <Button
          className="bg-[#330101] text-white hover:bg-[#ffde5c] flex items-center gap-2"
          onClick={handleDownload}
        >
          <Download className="w-4 h-4" />
          Download Details
        </Button>
      </div>
    </div>
  );
}

