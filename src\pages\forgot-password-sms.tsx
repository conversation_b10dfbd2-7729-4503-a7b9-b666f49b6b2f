import React from 'react';
import { useNavigate } from 'react-router-dom';
import { Phone } from 'lucide-react';
import { But<PERSON> } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Logo } from '../components/auth/logo';
import { BackButton } from '../components/auth/back-button';
export function ForgotPasswordSmsPage() {
  const navigate = useNavigate();
  const handleSend = () => {
    navigate('/verify-otp');
  };
  return <div className="auth-container">
      <BackButton />
      <div className="auth-form">
        <Logo />
        <h1 className="auth-heading">
          Enter your phone number to change password
        </h1>
        <div className="space-y-6">
          <div className="relative">
            <Input type="tel" id="phone" placeholder="ex: 0421 234 567" className="pl-10" />
            <Phone className="absolute left-3 top-3.5 h-5 w-5 text-gray-400" />
          </div>
          <p className="text-center text-sm">
            We will send a 4-digit code to your Phone Number
          </p>
          <Button onClick={handleSend} className="auth-button">
            Send
          </Button>
        </div>
      </div>
    </div>;
}