import React from 'react';
import { <PERSON>, Eye, Edit } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { useNavigate } from 'react-router-dom';
import { useDamageService } from './hook/usedamage-service';
import { Card } from '@/components/ui/card';

export function DamageServicePage() {
  const navigate = useNavigate();
  const {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    currentData,
    totalPages,
    totalEntries,
  } = useDamageService();

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Done':
        return 'bg-green-500 text-white';
      case 'Pending':
        return 'bg-gray-400 text-white';
      case 'InProgress':
        return 'bg-blue-500 text-white';
      default:
        return '';
    }
  };

  const getInsuranceColor = (status: string) => {
    switch (status) {
      case 'Initial Review':
        return 'bg-yellow-500 text-white';
      case 'Accepted':
        return 'bg-green-500 text-white';
      case 'Declined':
        return 'bg-red-500 text-white';
      default:
        return '';
    }
  };
  // Tab navigation handler
  const handleTabClick = (tab: string) => {
    if (tab === "All" || tab === "Due This Week") {
      navigate("/mechanic/service-management-all", { state: { tab } });
    } else if (tab === "General Maintenance") {
      navigate("/mechanic/maintainance-service");
    } else if (tab === "Accident") {
      navigate("/mechanic/accident-service");
    } else if (tab === "Breakdowns") {
      navigate("/mechanic/breakdown-service");
    } else if (tab === "Damage") {
      navigate("/mechanic/damage-service");
    }
  };

  // Responsive Card for Mobile/Tablet
  const DamageCard = ({ item }) => (
    <Card className="relative mb-4 p-4 shadow-md border border-gray-200 rounded-lg">
      {/* Header: Vehicle ID and Status */}
      <div className="flex justify-between items-start mb-4">
        <span className="text-lg font-semibold text-blue-600">{item.vehicleId}</span>
        <div className="flex flex-col gap-2 items-end">
          <span
            className={`px-3 py-1 rounded text-sm font-medium w-28 flex justify-center items-center ${getStatusColor(item.status)}`}
          >
            {item.status}
          </span>
          <span
            className={`px-3 py-1 rounded text-sm font-medium w-28 flex justify-center items-center ${getInsuranceColor(item.insuranceStatus)}`}
          >
            Insurance: {item.insuranceStatus}
          </span>
        </div>
      </div>
      {/* Vehicle Name - now clickable */}
      <div className="flex items-center mb-3">
        <span
          className="text-sm font-medium text-blue-600 cursor-pointer hover:text-blue-800"
          onClick={() => navigate('/mechanic/jobcard-damage')}
          title="View Job Card"
        >
          {item.vehicle}
        </span>
      </div>
      {/* Insurance Claim Number */}
      <div className="mb-2">
        <span className="text-xs text-gray-500 uppercase font-medium">Claim No.</span>
        <div className="text-sm">{item.insuranceClaimNumber || '-'}</div>
      </div>
      {/* Estimated/Actual End Date */}
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium mb-1">Estimated End</div>
          <div className="text-sm">{item.estimatedEndDate || '-'}</div>
        </div>
        <div>
          <div className="text-xs text-gray-500 uppercase font-medium mb-1">Actual End</div>
          <div className="text-sm">{item.actualEndDate || '-'}</div>
        </div>
      </div>
      {/* Actions at the bottom */}
      <div className="flex justify-end gap-2 pt-3 border-t border-gray-100">
        <Button
          onClick={() => navigate(`/mechanic/damage-service-edit/${item.vehicleId}`)}
          variant="ghost"
          size="sm"
          className="text-gray-600 hover:text-gray-800"
        >
          <Edit className="w-4 h-4 mr-1" />
          Edit
        </Button>
      </div>
    </Card>
  );

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex items-center mb-6">
        <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Damage</h1>
      </div>
       {/* Tabs */}
            <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
              {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
                <Button
                  key={tab}
                  variant={tab === "Damage" ? 'default' : 'outline'}
                  onClick={() => handleTabClick(tab)}
                  className="text-sm md:text-base"
                >
                  {tab}
                </Button>
              ))}
            </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={setFilterStatus}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="InProgress">InProgress</SelectItem>
              <SelectItem value="Done">Done</SelectItem>
            </SelectContent>
          </Select>
        </div>
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="block md:hidden">
        {currentData && currentData.length > 0 ? (
          currentData.map((item, idx) => (
            <DamageCard key={idx} item={item} />
          ))
        ) : (
          <div className="text-center py-8 text-gray-500">
            No records found matching your search criteria.
          </div>
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block">
        {/* Table View */}
        <div className="rounded-md border bg-white">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead>Vehicle ID</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Insurance Claim Number</TableHead>
                <TableHead>Estimated End Date</TableHead>
                <TableHead>Actual End Date</TableHead>
                <TableHead>Insurance Status</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {currentData.map((item, idx) => (
                <TableRow key={idx} className="hover:bg-gray-50 transition-colors">
                  <TableCell>{item.vehicleId}</TableCell>
                  <TableCell>
                    <span
                      className="text-blue-600 cursor-pointer hover:text-blue-800"
                      onClick={() => navigate('/mechanic/jobcard-damage')}
                      title="View Job Card"
                    >
                      {item.vehicle}
                    </span>
                  </TableCell>
                  <TableCell>{item.insuranceClaimNumber}</TableCell>
                  <TableCell>{item.estimatedEndDate}</TableCell>
                  <TableCell>{item.actualEndDate}</TableCell>
                  <TableCell>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getInsuranceColor(item.insuranceStatus)}`}>
                      {item.insuranceStatus}
                    </span>
                  </TableCell>
                  <TableCell>
                    <span className={`inline-flex px-2 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.status)}`}>
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell className="px-3 py-4">
                    <Button
                      onClick={() => navigate(`/mechanic/damage-service-edit/${item.vehicleId}`)}
                      variant="ghost"
                      size="sm"
                      className="text-gray-600 hover:text-gray-800 p-1 sm:p-2"
                    >
                      <Edit className="w-4 h-4" />
                    </Button>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-6"
        />
      </div>
    </div>
  );
}