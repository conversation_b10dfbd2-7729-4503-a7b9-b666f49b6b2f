import React from "react";
import { useNavigate, useParams } from "react-router-dom";
import { useAuditTrailEdit } from "./hook/useaudit&trail-edit";
import { Button } from "../../../components/ui/button";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "../../../components/ui/select";
import { Input } from "../../../components/ui/input";
import { ArrowLeft } from "lucide-react";

export function AuditTrailEdit() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const {
    rego,
    setRego,
    serviceType,
    timestamp,
    description,
    setDescription,
    regoOptions,
    handleUpdate,
    handleCancel,
  } = useAuditTrailEdit({ navigate, id });

  return (
    <div className="w-full min-h-screen bg-white flex flex-col items-start pt-10 px-4">
      {/* Back Button */}
      <Button
        className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center mb-6"
        size="sm"
        onClick={() => navigate('/mechanic/audit-trail')}
      >
        <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
        <span className="hidden md:inline">Go Back</span>
      </Button>
      <form
        className="w-full max-w-2xl"
        onSubmit={e => {
          e.preventDefault();
          handleUpdate();
        }}
      >
        {/* Rego */}
        <div className="relative mb-8">
          <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
            Rego
          </label>
          <Select value={rego} onValueChange={setRego}>
            <SelectTrigger className="w-full h-12 border rounded px-4 text-base">
              <SelectValue placeholder="Select" />
            </SelectTrigger>
            <SelectContent>
              {regoOptions.map(opt => (
                <SelectItem key={opt} value={opt}>
                  {opt}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Service Type & Timestamp (horizontal) */}
        <div className="flex gap-4 mb-8 w-full">
          {/* Service Type */}
          <div className="relative flex-1">
            <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Service Type
            </label>
            <Input
              className="w-full h-12 border rounded px-4 text-base"
              value={serviceType}
              placeholder="Service type"
              readOnly
            />
          </div>
          {/* Timestamp */}
          <div className="relative flex-1">
            <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Timestamp
            </label>
            <Input
              className="w-full h-12 border rounded px-4 text-base"
              value={timestamp}
              placeholder="Timestamp"
              readOnly
            />
          </div>
        </div>

        {/* Description */}
        <div className="relative mb-12">
          <label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
            Description
          </label>
          <Input
            as="textarea"
            className="w-full min-h-[70px] h-24 border rounded px-4 py-3 text-base bg-white border-gray-300 focus:border-[#ffde5c] focus:ring-2 focus:ring-[#ffde5c] transition-colors resize-none"
            placeholder="Type here ......"
            value={description}
            onChange={e => setDescription(e.target.value)}
          />
        </div>

        {/* Buttons */}
        <div className="flex justify-end gap-3">
          <Button
            type="submit"
            className="bg-[#330101] hover:bg-[#220000] text-white px-8 py-2 w-28"
          >
            Update
          </Button>
          <Button
            type="button"
            className="bg-gray-400 hover:bg-gray-500 text-white px-8 py-2 w-28"
            onClick={handleCancel}
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}
