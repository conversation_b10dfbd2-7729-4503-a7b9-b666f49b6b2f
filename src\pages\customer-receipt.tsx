

import React, { useRef, useState } from 'react';
import { Download, Printer, ZoomIn, ZoomOut, RotateCcw } from 'lucide-react';

interface RentalItem {
  date: string;
  description: string;
  days: number;
  rate: string;
  amount: string;
}

const CustomerReceipt: React.FC = () => {
  const [zoom, setZoom] = useState(73);
  const [pageNumber, setPageNumber] = useState(1);
  const totalPages = 1;

  const invoiceNumber = "27068";
  const date = "16/06/2025";
  const billTo = {
    name: "Test 123 123",
    companyName: "Company Name:",
    address: "Address:",
    city: "Australia",
    postcode: "04123456"
  };
  const rentalDetails = {
    vehicle: "VYL580 Toyota Hiace",
    from: "16/06/2025 12:00 PM",
    to: "17/06/2025 12:00 PM"
  };
  const items: RentalItem[] = [
    {
      date: "16/06/2025",
      description: "Vehicle Rental - from 16/06/2025 to 17/06/2025 (1 days)",
      days: 1,
      rate: "AUD81.82",
      amount: "AUD81.82"
    },
    {
      date: "16/06/2025",
      description: "Bond Compulsory - 500",
      days: 0,
      rate: "AUD500.00 Per Day",
      amount: "AUD500.00"
    }
  ];
  const totals = {
    gst: "AUD8.18",
    invoiceTotal: "AUD590.00",
    payments: "",
    balanceDue: "AUD590.00"
  };

  const handleZoomIn = () => {
    setZoom(Math.min(zoom + 10, 200));
  };

  const handleZoomOut = () => {
    setZoom(Math.max(zoom - 10, 25));
  };

  const handleDownload = () => {
    window.print();
  };
 

  const handlePrint = () => {
    window.print();
  }; 



  const handleReset = () => {
    setZoom(73);
  };

  const invoiceRef = useRef<HTMLDivElement>(null);


  return (
    <div className="flex flex-col h-screen bg-gray-800 print:bg-white print:h-auto">

      {/* PDF Viewer Header */}
      <div className="flex items-center justify-between bg-gray-700 text-white px-4 py-2 text-sm print:hidden">
        <div className="flex items-center space-x-4">
          <button className="p-1 hover:bg-gray-600 rounded">
            <div className="w-5 h-5 flex flex-col justify-center">
              <div className="h-0.5 bg-white mb-1"></div>
              <div className="h-0.5 bg-white mb-1"></div>
              <div className="h-0.5 bg-white"></div>
            </div>
          </button>
          <span className="font-medium">customer-receipt.pdf</span>
        </div>
        
        <div className="flex items-center space-x-4">
          <div className="flex items-center space-x-2">
            <span>{pageNumber}</span>
            <span>/</span>
            <span>{totalPages}</span>
          </div>
          
          <div className="flex items-center space-x-2">
            <button 
              onClick={handleZoomOut}
              className="p-1 hover:bg-gray-600 rounded"
              title="Zoom Out"
            >
              <ZoomOut size={16} />
            </button>
            <span className="min-w-12 text-center">{zoom}%</span>
            <button 
              onClick={handleZoomIn}
              className="p-1 hover:bg-gray-600 rounded"
              title="Zoom In"
            >
              <ZoomIn size={16} />
            </button>
          </div>

          <button 
            onClick={handleReset}
            className="p-1 hover:bg-gray-600 rounded"
            title="Reset"
          >
            <RotateCcw size={16} />
          </button>

          <button 
            onClick={handlePrint}
            className="p-1 hover:bg-gray-600 rounded"
            title="Print"
          >
            <Printer size={16} />
          </button>
          
          <button 
            onClick={handleDownload}
            className="p-1 hover:bg-gray-600 rounded"
            title="Download"
          >
            <Download size={16} />
          </button>
          
          <button className="p-1 hover:bg-gray-600 rounded">
            <div className="w-4 h-4 flex flex-col justify-center">
              <div className="w-1 h-1 bg-white rounded-full mb-1"></div>
              <div className="w-1 h-1 bg-white rounded-full mb-1"></div>
              <div className="w-1 h-1 bg-white rounded-full"></div>
            </div>
          </button>
        </div>
      </div>

      {/* PDF Content Area */}
      <div className="flex-1 bg-gray-600 overflow-auto p-4 print:block">
        <div className="flex justify-center">
          <div 
            ref={invoiceRef}
            className="bg-white shadow-lg border border-gray-300 transition-all duration-200"
            style={{ 
              transform: `scale(${zoom / 100})`,
              transformOrigin: 'top center',
              width: '794px', // A4 width
              minHeight: '1123px' // A4 height
            }}
          >
            {/* Invoice Content */}
            <div className="p-8">
              {/* Header Section */}
              <div className="flex justify-between items-start mb-8">
                <div className="flex items-center">
                  <div className="max-w-full sm:max-w-3xl md:max-w-4xl lg:max-w-5xl xl:max-w-7xl px-2 sm:px-4 md:px-6 lg:px-8 xl:px-10 flex items-center justify-start">
                    <img src="/logo.png" alt="Lion Logo" className="h-10 sm:h-12 md:h-14 lg:h-16 xl:h-20 w-auto" />
                  </div>
                </div>
                <div className="text-center">
                  <h2 className="text-2xl font-bold text-gray-800 mb-2">Lion Car Rentals</h2>
                  <p className="text-sm text-gray-600">Somerton, VIC 3062, AUSTRALIA</p>
                  <p className="text-sm text-gray-600">Phone: (03) 93037447</p>
                  <p className="text-sm text-gray-600">Email: <EMAIL></p>
                  <div className="mt-2 text-sm text-gray-600">
                    <p>A.B.N. ************** &nbsp;&nbsp; A.C.N. ***********</p>
                  </div>
                </div>
                <div className="text-right">
                  <p className="text-xl font-bold text-gray-800">INVOICE #{invoiceNumber}</p>
                  <p className="text-sm text-gray-600">Date: {date}</p>
                </div>
              </div>

              {/* Bill To and Rental Details Section */}
              <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
                <div>
                  <h3 className="text-lg font-bold text-gray-800 mb-4">Bill To:</h3>
                  <div className="text-sm text-gray-700 space-y-1">
                    <p>{billTo.name}</p>
                    <p>{billTo.companyName}</p>
                    <p>{billTo.address}</p>
                    <p>{billTo.city}</p>
                    <p>{billTo.postcode}</p>
                  </div>
                </div>
                <div className="flex justify-end">
                  <div className="flex flex-col">
                    <h3 className="text-lg font-bold text-gray-800 mb-4">RENTAL DETAILS:</h3>
                    <div className="text-sm text-gray-700 space-y-1">
                      <div className="flex">
                        <span className="font-medium w-16">Vehicle:</span>
                        <span>{rentalDetails.vehicle}</span>
                      </div>
                      <div className="flex">
                        <span className="font-medium w-16">From:</span>
                        <span>{rentalDetails.from}</span>
                      </div>
                      <div className="flex">
                        <span className="font-medium w-16">To:</span>
                        <span>{rentalDetails.to}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Invoice Table */}
              <div className="mb-8">
                <div className="relative w-full">
                  <table className="w-full border-collapse border border-gray-400 table-auto">
                    <thead>
                      <tr className="bg-gray-50">
                        <th className="border border-gray-400 px-4 py-2 text-left font-medium text-gray-800">Date</th>
                        <th className="border border-gray-400 px-4 py-2 text-left font-medium text-gray-800">Description</th>
                        <th className="border border-gray-400 px-4 py-2 text-center font-medium text-gray-800">Days</th>
                        <th className="border border-gray-400 px-4 py-2 text-center font-medium text-gray-800">Rate</th>
                        <th className="border border-gray-400 px-4 py-2 text-right font-medium text-gray-800">Amount</th>
                      </tr>
                    </thead>
                    <tbody>
                      {items.map((item, index) => (
                        <tr key={index}>
                          <td className="border border-gray-400 px-4 py-2 text-sm">{item.date}</td>
                          <td className="border border-gray-400 px-4 py-2 text-sm">{item.description}</td>
                          <td className="border border-gray-400 px-4 py-2 text-center text-sm">
                            {item.days > 0 ? item.days : ''}
                          </td>
                          <td className="border border-gray-400 px-4 py-2 text-center text-sm">{item.rate}</td>
                          <td className="border border-gray-400 px-4 py-2 text-right text-sm">{item.amount}</td>
                        </tr>
                      ))}
                      <tr>
                        <td className="border border-gray-400 px-4 py-8" colSpan={5}></td>
                      </tr>
                    </tbody>
                  </table>

                  {/* Totals Section */}
                  <div className="w-full flex justify-end">
                    <div className="w-full md:w-2/5 lg:w-1/3 border border-gray-400 border-t-0 mt-[-1px]">
                      <div className="flex justify-between items-center py-2 px-4 border-b border-gray-400">
                        <span className="text-sm font-medium">GST (10%)</span>
                        <span className="text-sm">{totals.gst}</span>
                      </div>
                      <div className="flex justify-between items-center py-2 px-4 border-b border-gray-400">
                        <span className="text-sm font-medium">Invoice Total</span>
                        <span className="text-sm font-bold">{totals.invoiceTotal}</span>
                      </div>
                      <div className="flex justify-between items-center py-2 px-4 border-b border-gray-400">
                        <span className="text-sm font-medium">Payments</span>
                        <span className="text-sm">{totals.payments}</span>
                      </div>
                      <div className="flex justify-between items-center py-2 px-4 bg-gray-200">
                        <span className="text-sm font-bold">Balance Due</span>
                        <span className="text-sm font-bold">{totals.balanceDue}</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Details Section */}
              <div className="space-y-6 text-sm text-gray-700">
                {/* RACV Section */}
                <div className="border-t border-b border-gray-400 py-4">
                  <div className="flex items-center mb-2">
                    <div className="bg-gray-800 text-white px-2 py-1 text-xs font-bold mr-3">RACV</div>
                    <span className="font-bold">24 x 7 ROADSIDE ASSISTANCE - CALL 1800 686 464</span>
                  </div>
                  <p className="text-xs">
                    We have partnered with RACV for all your roadside assistance needs Australia wide. In case of any 
                    vehicle breakdowns or emergencies, please contact <span className="font-bold">RACV on 1800 686 464</span>.
                  </p>
                </div>

                {/* Insurance Section */}
                <div className="border-b border-gray-400 pb-4">
                  <h4 className="font-bold mb-2">Insurance</h4>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Default: $5000 damage liability in case of an accident could be reduced to $1000 by purchasing Damage Liability Reduction option (per day rate depending on Vehicle Class).</li>
                    <li>The damage liability reduction fee is compulsory if the driver is aged between 21-25 years, and the excess amount will be $2000 in case of an accident.</li>
                    <li>Damage liability amount will be charged accordingly per incident (in case of 2 or more accidents)</li>
                    <li>Standard insurance cover WILL NOT cover overhead or under-body vehicle damages.</li>
                  </ul>
                </div>

                {/* Mileage Section */}
                <div className="border-b border-gray-400 pb-4">
                  <h4 className="font-bold mb-2">Passenger Vehicles 250 Kms per Day | Commercial Vehicles 100 Kms per Day</h4>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Your rental contract includes 100 kms (Unless the unlimited mileage option is selected)</li>
                  </ul>
                  <p className="text-xs mt-2">
                    If allowed number of kilometers are exceeded, the excess mileage charges will apply at the rate of 20 cents per additional kilometer
                  </p>
                  <ul className="list-disc list-inside space-y-1 text-xs mt-2">
                    <li>Unlimited km (Optional - an additional cost depending on Vehicle Class)</li>
                  </ul>
                </div>

                {/* Vehicle Condition Sections */}
                <div className="border-b border-gray-400 pb-4">
                  <h4 className="font-bold mb-2">Vehicle condition</h4>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Vehicle must be returned in the same condition, including fuel level and cleanliness.</li>
                  </ul>
                </div>

                <div className="border-b border-gray-400 pb-4">
                  <h4 className="font-bold mb-2">Vehicle condition</h4>
                  <ul className="list-disc list-inside space-y-1 text-xs">
                    <li>Any fines, tickets, toll way or any nominations relating to vehicles usage will attract $5 or $15 administration fee.</li>
                    <li>Please note that the deposit will be non-refundable in case of any Early Termination. Visit the Terms & Conditions page for more details.</li>
                  </ul>
                </div>

                {/* Footer */}
                <div className="text-center text-xs text-gray-600 pt-4">
                  <p>Please Refer to full Terms & Conditions available on our Website or on the Rental Contract to be signed.</p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default CustomerReceipt;