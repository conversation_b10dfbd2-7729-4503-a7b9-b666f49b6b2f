import { User, Lock, MapPin, IdCard, Calendar, Eye } from 'lucide-react';
import { BackButton } from '../components/auth/back-button';
import { Button } from '../components/ui/button';
import { Input } from '../components/ui/input';
import { Logo } from '../components/auth/logo';
import { Calendar as CalendarComponent } from '@/components/ui/calendar';
import { Popover, PopoverContent, PopoverTrigger } from '@/components/ui/popover';
import { useSignUp } from './hooks/useSignUp';
import PhoneInput from 'react-phone-input-2';
import 'react-phone-input-2/lib/style.css';


export function Signup() {
  const {
    formData,
    birthday,
    setBirthday,
    issueDate,
    setIssueDate,
    expireDate,
    setExpireDate,
    showPassword,
    setShowPassword,
    showConfirmPassword,
    setShowConfirmPassword,
    errors,
    handleInputChange,
    handlePhoneChange,
    handlePaste,
    handleSubmit,
    handleLogin,
    formatDate,
    loading,
    error,
    data,
  } = useSignUp();



  return (
    <form onSubmit={(e) => { e.preventDefault(); handleSubmit(); }}>
      <div className="auth-container">
        <BackButton />
        <div className="auth-form">
          <Logo />
          <h1 className="auth-heading">Sign Up</h1>
          <div className="space-y-6">
            {/* customer info */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="firstName"
                  placeholder="ex: John"
                  value={formData.firstName}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="firstName" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  First Name*
                </label>
                <User className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                {errors.firstName && <p className="text-red-500 text-xs">{errors.firstName}</p>}
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="lastName"
                  placeholder="ex: Peter"
                  value={formData.lastName}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="lastName" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Last Name*
                </label>
                <User className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                {errors.lastName && <p className="text-red-500 text-xs">{errors.lastName}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="address"
                  placeholder="ex: 2/85 Hume Hwy, Somerton VIC 3062"
                  value={formData.address}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="address" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Address*
                </label>
                <MapPin className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                {errors.address && <p className="text-red-500 text-xs">{errors.address}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="postCode"
                  placeholder="ex: 3062"
                  value={formData.postCode}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="postCode" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Post Code*
                </label>
                {errors.postCode && <p className="text-red-500 text-xs">{errors.postCode}</p>}
              </div>
              <div className="relative">
                <Input
                  type="text"
                  id="country"
                  placeholder="ex: Australia"
                  value={formData.country}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="country" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Country*
                </label>
                {errors.country && <p className="text-red-500 text-xs">{errors.country}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Input
                  type="email"
                  id="email"
                  placeholder="ex: <EMAIL>"
                  value={formData.email}
                  onChange={handleInputChange}
                  onPaste={handlePaste} // Add paste handler
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="email" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Email*
                </label>
                {errors.email && <p className="text-red-500 text-xs">{errors.email}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Input
                  type="email"
                  id="confirmEmail"
                  placeholder="ex: <EMAIL>"
                  value={formData.confirmEmail}
                  onChange={handleInputChange}
                  onPaste={handlePaste} // Add paste handler
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="confirmEmail" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Confirm Email*
                </label>
                {errors.confirmEmail && <p className="text-red-500 text-xs">{errors.confirmEmail}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <PhoneInput
                  country={'au'}
                  value={formData.phone}
                  onChange={(value) => handlePhoneChange(value, 'phone')}
                  inputProps={{
                    name: 'phone',
                    required: true,
                  }}
                  inputStyle={{
                    width: '100%',
                    border: '1px solid #6b7280',
                    borderRadius: '0.25rem',
                    padding: '0.5rem 0.75rem',
                    fontSize: '0.875rem',
                    lineHeight: '1.25rem',
                    color: '#111827',
                    paddingLeft: '50px',
                  }}
                  buttonStyle={{
                    border: '1px solid #6b7280',
                    borderRadius: '0.25rem 0 0 0.25rem',
                    background: '#f9fafb',
                  }}
                />
                <label htmlFor="phone" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                  Phone Number*
                </label>
                {errors.phone && <p className="text-red-500 text-xs">{errors.phone}</p>}
              </div>
            </div>
            {/* Birthday */}
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Popover>
                  <PopoverTrigger asChild>
                    <div className="relative">
                      <Input
                        type="text"
                        id="birthday"
                        value={formatDate(birthday)}
                        readOnly
                        className="w-[450px] h-[50px] border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                        required
                      />
                      <label htmlFor="birthday" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                        Birthday*
                      </label>
                      <button
                        type="button"
                        className="absolute right-3 top-3.5"
                        aria-label="Open date picker"
                      >
                        <Calendar className="h-5 w-5 text-gray-400" />
                      </button>
                    </div>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={birthday || undefined}
                      onSelect={(date: Date | undefined) => setBirthday(date || null)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.birthday && <p className="text-red-500 text-xs">{errors.birthday}</p>}
              </div>
            </div>

            {/* Company Name */}
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="companyName"
                  placeholder="ex: Lion Car Rentals"
                  value={formData.companyName}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="companyName" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Company Name*
                </label>
                {errors.companyName && <p className="text-red-500 text-xs">{errors.companyName}</p>}
              </div>
            </div>
            {/* Driver License */}
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="driverLicense"
                  placeholder="ex: *********"
                  value={formData.driverLicense}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="driverLicense" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Driver License Number*
                </label>
                <IdCard className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                {errors.dlNumber && <p className="text-red-500 text-xs">{errors.dlNumber}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Popover>
                  <PopoverTrigger asChild>
                    <div className="relative">
                      <Input
                        type="text"
                        id="issueDate"
                        value={formatDate(issueDate)}
                        readOnly
                        className="w-[450px] h-[50px] border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                        required
                      />
                      <label htmlFor="issueDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                        Issue Date*
                      </label>
                      <button
                        type="button"
                        className="absolute right-3 top-3.5"
                        aria-label="Open date picker"
                      >
                        <Calendar className="h-5 w-5 text-gray-400" />
                      </button>
                    </div>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={issueDate || undefined}
                      onSelect={(date: Date | undefined) => setIssueDate(date || null)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.issueDate && <p className="text-red-500 text-xs">{errors.issueDate}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Popover>
                  <PopoverTrigger asChild>
                    <div className="relative">
                      <Input
                        type="text"
                        id="expireDate"
                        value={formatDate(expireDate)}
                        readOnly
                        className="w-[450px] h-[50px] border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                        required
                      />
                      <label htmlFor="expireDate" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                        Expiry Date*
                      </label>
                      <button
                        type="button"
                        className="absolute right-3 top-3.5"
                        aria-label="Open date picker"
                      >
                        <Calendar className="h-5 w-5 text-gray-400" />
                      </button>
                    </div>
                  </PopoverTrigger>
                  <PopoverContent className="w-auto p-0" align="start">
                    <CalendarComponent
                      mode="single"
                      selected={expireDate || undefined}
                      onSelect={(date: Date | undefined) => setExpireDate(date || null)}
                      initialFocus
                    />
                  </PopoverContent>
                </Popover>
                {errors.expiryDate && <p className="text-red-500 text-xs">{errors.expiryDate}</p>}
              </div>
            </div>
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="issueCountry"
                  placeholder="ex: Victoria"
                  value={formData.issueCountry}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="issueCountry" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Issue Country*
                </label>
                {errors.issueCountry && <p className="text-red-500 text-xs">{errors.issueCountry}</p>}
              </div>
            </div>


          

            {/* Emergency Contact */}
            <div className="grid grid-cols-1 gap-4">
              <div className="relative">
                <Input
                  type="text"
                  id="emergencyContactName"
                  placeholder="Name"
                  value={formData.emergencyContactName}
                  onChange={handleInputChange}
                  className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                  required
                />
                <label htmlFor="emergencyContactName" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                  Emergency Contact Person*
                </label>
                {errors.emergencyContactPersonName && <p className="text-red-500 text-xs">{errors.emergencyContactPersonName}</p>}

              {/* Emergency Contact */}
              <div className="grid grid-cols-1 gap-4">
                <div className="relative">
                  <Input
                    type="text"
                    id="emergencyContactName"
                    placeholder="Name"
                    value={formData.emergencyContactName}
                    onChange={handleInputChange}
                    className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                    required
                  />
                  <label htmlFor="emergencyContactName" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                    Emergency Contact Person*
                  </label>
                  {errors.emergencyContactPersonName && <p className="text-red-500 text-xs">{errors.emergencyContactPersonName}</p>}
                </div>

              </div>

              <div className="grid grid-cols-1 gap-4">
                <div className="relative">
                  <PhoneInput
                    country={'au'}
                    value={formData.emergencyContactNumber}
                    onChange={(value) => handlePhoneChange(value, 'emergencyContactNumber')}
                    inputProps={{
                      name: 'emergencyContactNumber',
                      required: true,
                    }}
                    inputStyle={{
                      width: '100%',
                      border: '1px solid #6b7280',
                      borderRadius: '0.25rem',
                      padding: '0.5rem 0.75rem',
                      fontSize: '0.875rem',
                      lineHeight: '1.25rem',
                      color: '#111827',
                      paddingLeft: '50px',
                    }}
                    buttonStyle={{
                      border: '1px solid #6b7280',
                      borderRadius: '0.25rem 0 0 0.25rem',
                      background: '#f9fafb',
                    }}
                  />
                  <label htmlFor="emergencyContactNumber" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600">
                    Emergency Contact Person Number*
                  </label>
                  {errors.emergencyContactPhoneNumber && <p className="text-red-500 text-xs">{errors.emergencyContactPhoneNumber}</p>}
                </div>
              </div>
              {/* Password */}
              <div className="grid grid-cols-1 gap-4">
                <div className="relative">
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    id="password"
                    placeholder="••••••••••••••"
                    value={formData.password}
                    onChange={handleInputChange}
                    onPaste={handlePaste}
                    className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                    required
                    aria-describedby="password-error"
                  />
                  <label htmlFor="password" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                    Password*
                  </label>
                  <Lock className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                  <button
                    type="button"
                    onClick={() => setShowPassword(!showPassword)}
                    className="absolute right-12 top-3.5"
                    aria-label={showPassword ? 'Hide password' : 'Show password'}
                  >
                    <Eye className="h-5 w-5 text-gray-400" />
                  </button>
                  {errors.password && <p id="password-error" className="text-red-500 text-xs">{errors.password}</p>}
                </div>
              </div>
              <div className="grid grid-cols-1 gap-4">
                <div className="relative">
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    id="confirmPassword"
                    placeholder="••••••••••••••"
                    value={formData.confirmPassword}
                    onChange={handleInputChange}
                    onPaste={handlePaste}
                    className="w-full border border-gray-500 rounded px-3 py-2 text-gray-900 text-sm focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent pr-10"
                    required
                    aria-describedby="confirmPassword-error"
                  />
                  <label htmlFor="confirmPassword" className="absolute left-2 top-[-8px] bg-white px-1 text-xs text-gray-600 peer-focus:text-blue-500">
                    Confirm Password*
                  </label>
                  <Lock className="absolute right-3 top-3.5 h-5 w-5 text-gray-400" />
                  <button
                    type="button"
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className="absolute right-12 top-3.5"
                    aria-label={showConfirmPassword ? 'Hide password' : 'Show password'}
                  >
                    <Eye className="h-5 w-5 text-gray-400" />
                  </button>
                  {errors.confirmPassword && <p id="confirmPassword-error" className="text-red-500 text-xs">{errors.confirmPassword}</p>}
                </div>
              </div>
            </div>
            <p className="text-xs text-gray-500 mb-4">*All Fields Are Required</p>
            <Button
              type='submit'
              disabled={loading}
              className="auth-button bg-yellow-500 text-white">
              {loading ? 'Registering...' : 'Sign Up'}
            </Button>
            <div className="text-center text-sm mt-4">
              Already have an account?{' '}
              <button onClick={handleLogin} className="auth-link">
                Login
              </button>
            </div>
            <div className="text-center text-xs text-gray-600 mt-8">
              DEVELOPED BY: <span className="text-yellow-700">WEBS R US PTY LTD</span>
            </div>
            {error && <p className="text-center text-sm mt-4 text-red-600">{error}</p>}
            {data && data.status && <p className="text-center text-sm mt-4 text-green-600">{data.message}</p>}
            </div>

          </div>
        </div>
      </div>
    </form>
  );
}
