import React, { useState } from 'react';
import { Search, Eye, FileText, ChevronDown } from 'lucide-react';
import { useNavigate } from 'react-router-dom';
import { Pagination } from '@/components/layout/Pagination';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';


interface Invoice {
  id: string;
  rentalId: string;
  customerName:string;
    type: string;
  totalAmount: number;
  receivedAmount: number;
  dueAmount: number;
  paymentType: string;
  status: 'Completed' | 'Pending Payment';
}

// Sample data
const invoicesData: Invoice[] = [
  {
    id: '1',
    rentalId: '#0026',
    customerName:'Rose Markes',
    type: 'Stranded long-term Rental Invoice',
    totalAmount: 592.00,
    receivedAmount: 592.00,
    dueAmount: 0,
    paymentType: 'Credit Card',
    status: 'Completed'
  },
  {
    id: '2',
    rentalId: '#0001',
    customerName:'Ishark',
    type: 'Stranded long-term Rental Invoice',
    totalAmount: 540.00,
    receivedAmount: 400.00,
    dueAmount: 140.00,
    paymentType: 'Walking - Credit Card',
    status: 'Pending Payment'
  },
  {
    id: '3',
    rentalId: '#0007',
    customerName:'Meena',
    type: 'Short Term rental Invoice with Insurance Excess',
    totalAmount: 740.00,
    receivedAmount: 740.00,
    dueAmount: 0,
    paymentType: 'Walking- Cash',
    status: 'Completed'
  },
  {
    id: '4',
    rentalId: '#0014',
    customerName:'Shen Reyo',
    type: 'Late Return Penalties or Fines',
    totalAmount: 470.00,
    receivedAmount: 470.00,
    dueAmount: 0,
    paymentType: 'Credit Card',
    status: 'Completed'
  },
  {
    id: '5',
    rentalId: '#0103',
    customerName:'Fathima Safiya',
    type: 'Early termination or Customer Requested Mid-Rental',
    totalAmount: 865.00,
    receivedAmount: 865.00,
    dueAmount: 0,
    paymentType: 'Phone Authorization',
    status: 'Completed'
  },
  {
    id: '6',
    rentalId: '#0105',
    customerName:'Jane Polle',
    type: 'Standard Rental Invoice',
    totalAmount: 320.00,
    receivedAmount: 320.00,
    dueAmount: 0,
    paymentType: 'Credit Card',
    status: 'Completed'
  },
  {
    id: '7',
    rentalId: '#0106',
    customerName:'Maya',
    type: 'Insurance Claim Invoice',
    totalAmount: 1200.00,
    receivedAmount: 800.00,
    dueAmount: 400.00,
    paymentType: 'Bank Transfer',
    status: 'Pending Payment'
  },
  {
    id: '8',
    rentalId: '#0107',
    customerName:'Vikesh Anand',
    type: 'Extended Rental Invoice',
    totalAmount: 680.00,
    receivedAmount: 680.00,
    dueAmount: 0,
    paymentType: 'Cash',
    status: 'Completed'
  }
];

export function CustomerMInvoices() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState(''); 
  const [filterStatus, setFilterStatus] = useState('All');
  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(5);

  // Filtering
  const filteredInvoices = invoicesData.filter(invoice => {
    const matchesSearch = invoice.type.toLowerCase().includes(searchTerm.toLowerCase()) || 
      invoice.paymentType.toLowerCase().includes(searchTerm.toLowerCase()) ||
      invoice.rentalId.toLowerCase().includes(searchTerm.toLowerCase());
    const matchesFilter = filterStatus === 'All' || invoice.status === filterStatus;

    return matchesSearch && matchesFilter;
  });

  // Pagination
  const totalRecords = filteredInvoices.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentInvoices = filteredInvoices.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  // For mobile view - show all filtered requests (no pagination)
  const mobileRequests = filteredInvoices;

  const handleViewInvoice = (rentalId: string) => {
    console.log('View invoice:', rentalId);
    const cleanRentalId = rentalId.replace('#', '');
    navigate(`/admin/customerMasterAdmin/customerM-viewInvoice/${cleanRentalId}`);
  };

  const getStatusBadgeStyle = (status: string) => {
    switch (status) {
      case 'Completed':
        return 'bg-blue-500 text-white px-3 py-1 rounded text-sm font-medium';
      case 'Pending Payment':
        return 'bg-green-600 text-white px-3 py-1 rounded text-sm font-medium';
      default:
        return 'bg-gray-500 text-white px-3 py-1 rounded text-sm font-medium';
    }
  };

  return (
    <div>
      {/* Header */}
      <div className="flex items-center mb-6">
        <FileText className="w-6 h-6 mr-3 text-gray-800" />
        <h1 className="text-2xl font-bold text-gray-800">Invoices</h1>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-4 mb-6">
        <div className="relative">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending Payment">Payment Pending</SelectItem>
              <SelectItem value="Completed">Completed</SelectItem>
            </SelectContent>
          </Select>
           </div>
              
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <input
            type="text"
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
          />
        </div>
              
        <button className="bg-gray-100 text-gray-700 hover:bg-gray-200 px-4 py-2 rounded-md transition-colors">
          Save this search
        </button>
      </div>

      {/* Desktop Table View */}
      <div className="hidden lg:block bg-white rounded-lg shadow overflow-hidden">
        <div className="overflow-x-auto">
          <table className="min-w-full divide-y divide-gray-200">
            <thead className="bg-gray-50">
              <tr>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Rental ID
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Customer Name
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Invoice Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Total Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Received Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Due Amount
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Payment Type
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Status
                </th>
                <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                  Action
                </th>
              </tr>
            </thead>
            <tbody className="bg-white divide-y divide-gray-200">
              {currentInvoices.map((invoice) => (
                <tr key={invoice.id} className="hover:bg-gray-50">
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.rentalId}
                  </td>
                    <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.customerName}
                  </td>
                  <td className="px-6 py-4 text-sm text-gray-900">
                    {invoice.type}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${invoice.totalAmount.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    ${invoice.receivedAmount.toFixed(2)}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.dueAmount === 0 ? '-' : `$${invoice.dueAmount.toFixed(2)}`}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap text-sm text-gray-900">
                    {invoice.paymentType}
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <span className={getStatusBadgeStyle(invoice.status)}>
                      {invoice.status}
                    </span>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <button
                      onClick={() => handleViewInvoice(invoice.rentalId)}
                      className="text-gray-600 hover:text-gray-900 p-1 rounded hover:bg-gray-100"
                    >
                      <Eye className="w-4 h-4" />
                    </button>
                  </td>
                </tr>
              ))}
            </tbody>
          </table>
        </div>
      </div>

      {/* Mobile/Tablet Card View */}
      <div className="lg:hidden space-y-4">
        {mobileRequests.map((invoice) => (
          <div key={invoice.id} className="bg-white shadow p-4 relative">
            {/* Status Badge - Top Right */}
            <div className="absolute top-4 right-4">
              <span className={getStatusBadgeStyle(invoice.status)}>
                {invoice.status}
              </span>
            </div>
            
            {/* Invoice ID */}
            <div className="mb-3">
              <span className="text-sm font-medium text-gray-500">ID: </span>
              <span className="text-base font-semibold text-gray-900">{invoice.rentalId}</span>
            </div>
            
            {/* Invoice Type */}
            <div className="mb-3">
              <span className="text-sm font-medium text-gray-500">Invoice Type: </span>
              <span className="text-sm text-gray-900">{invoice.type}</span>
            </div>
            
            {/* Amount Details */}
            <div className="grid grid-cols-2 gap-4 mb-3">
              <div>
                <span className="text-sm font-medium text-gray-500">Total Amount: </span>
                <span className="text-sm font-semibold text-gray-900">${invoice.totalAmount.toFixed(2)}</span>
              </div>
              <div>
                <span className="text-sm font-medium text-gray-500">Received Amount: </span>
                <span className="text-sm font-semibold text-gray-900">${invoice.receivedAmount.toFixed(2)}</span>
              </div>
            </div>
            
            {/* Due Amount */}
            <div className="mb-3">
              <span className="text-sm font-medium text-gray-500">Due Amount: </span>
              <span className="text-sm font-semibold text-gray-900">
                {invoice.dueAmount === 0 ? '-' : `$${invoice.dueAmount.toFixed(2)}`}
              </span>
            </div>
            
            {/* Payment Type */}
            <div className="mb-4">
              <span className="text-sm font-medium text-gray-500">Payment Type: </span>
              <span className="text-sm text-gray-900">{invoice.paymentType}</span>
            </div>
            
            {/* Action Button */}
            <div className="flex justify-end">
              <button
                onClick={() => handleViewInvoice(invoice.rentalId)}
                className="text-black p-2 rounded-full"
              >
                <Eye className="w-5 h-5" />
              </button>
            </div>
          </div>
        ))}
      </div>

      {/* Pagination */}
      <div className="hidden lg:block">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-6"
        />
      </div>
    </div>
  );
}    