import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Eye } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { useDamageServiceView } from "./hook/usedamage-service-view";

export function DamageServiceViewPage() {
  const navigate = useNavigate();
  const { formData } = useDamageServiceView();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  return (
    <div className="min-h-screen">
      <Button
        className="bg-[#330101] text-white text-sm sm:text-base px-3 sm:px-4 py-2 mb-4"
        size="sm"
        onClick={() => navigate('/mechanic/damage-service')}
      >
        <ArrowLeft className="w-3 h-3 sm:w-4 sm:h-4 mr-1" />
        Go Back
      </Button>
      <div className="bg-white rounded-lg mb-6 p-4">
        <h1 className="text-xl sm:text-2xl font-medium mb-4">Vehicle Details</h1>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input value={formData.model} readOnly className="bg-gray-200" />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Model</Label>
          </div>
          <div className="relative">
            <Input value={formData.regNo} readOnly className="bg-gray-200" />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Reg No</Label>
          </div>
        </div>
        <div className="relative mb-4">
          <Input value={formData.description} readOnly className="bg-gray-200" />
          <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Description</Label>
        </div>
      </div>
      <div className="bg-white rounded-lg mb-6 p-4">
        <h2 className="text-xl font-medium mb-4">Uploaded Images</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
          {Object.entries(formData.images).map(([key, files]) => (
            <div className="relative" key={key}>
              <div className="flex items-center justify-between w-full p-2 border border-gray-400 rounded-md">
                <span className="text-gray-600">{files.join(", ")}</span>
                {files[0] && (
                  <Button
                    variant="ghost"
                    className="text-gray-600 hover:text-gray-800"
                    onClick={() => setSelectedImage(files[0])}
                  >
                    <Eye className="w-4 h-4" />
                  </Button>
                )}
              </div>
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">
                {key.replace(/([A-Z])/g, " $1")}
              </Label>
            </div>
          ))}
        </div>
      </div>
      <div className="bg-white rounded-lg mb-6 p-4">
        <h2 className="text-xl font-medium mb-4">Workshop Details</h2>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input value={formData.branch} readOnly className="bg-gray-200" />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Branch</Label>
          </div>
          <div className="relative">
            <Input value={formData.mechanicAssigned.join(", ")} readOnly className="bg-gray-200" />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Mechanic Assigned</Label>
          </div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input value={formData.insuranceStatus} readOnly className="bg-gray-200" />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Insurance Status</Label>
          </div>
          <div className="relative">
            <Input value={formData.insuranceClaimNumber} readOnly className="bg-gray-200" />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Insurance Claim Number</Label>
          </div>
        </div>
        <div className="grid grid-cols-2 gap-4 mb-4">
          <div className="relative">
            <Input type="date" value={formData.estimatedEndDate} readOnly className="bg-gray-200" />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Estimated End Date</Label>
          </div>
          <div className="relative">
            <Input type="date" value={formData.actualEndDate} readOnly className="bg-gray-200" />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Actual End Date</Label>
          </div>
        </div>
        <div className="relative mb-4">
          <Input value={formData.status} readOnly className="bg-gray-200" />
          <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Status</Label>
        </div>
        <div className="relative mb-4">
          <Input value={formData.comment} readOnly className="bg-gray-200" />
          <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Comment</Label>
        </div>
      </div>
      {/* Mechanic Note Section */}
      <h2 className="text-xl font-medium mb-4 mt-6">Mechanic Note</h2>
      <div className="grid grid-cols-2 gap-4 mb-4">
        <div className="relative">
          <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Status</Label>
          <Input value={formData.mechanicNoteStatus} readOnly className="bg-gray-200" />
        </div>
        <div className="col-span-2 relative">
          <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Comment</Label>
          <Input value={formData.mechanicNoteComment} readOnly className="bg-gray-200" />
        </div>
      </div>
      {/* Image Preview Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg">
            <img src={selectedImage} alt="Preview" className="max-w-full max-h-96" />
            <Button
              variant="outline"
              className="mt-4 w-full"
              onClick={() => setSelectedImage(null)}
            >
              Close
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}