import { useNavigate, useParams } from "react-router-dom";
import { useState, useEffect } from "react";
import { JobCardData } from "../type/mechanictype";

// Mock job card data
const jobCards: JobCardData[] = [
  {
    id: "1",
    registration: "ACV 003",
    make: "Kawasaki",
    model: "Z800",
    modelSeries: "1234",
    colour: "Dark Blue",
    prodDate: "26-05-2025",
    nextService: "General Maintenance every 5000 km",
    regoDue: "Rego Due",
    engineNumber: "#457815",
    vin: "JQQDTDMFNBYZ0NZTJ",
    buildDate: "26-05-2025",
    litres: "40 L",
    trans: "Trans",
    air: "Air",
    cyl: "Cyl",
    body: "Body",
    odo: "176,394",
    hours: "5 h",
    repairedBy: "John Doe",
    drainRefillEngineOil: true,
    engineOilLitres: "5",
    airFilterReplaced: true,
    checkAdjustTyrePressure: true,
    checkTopUpAllFluids: true,
    sparkPlugsChecked: true,
    comments: "All main services completed.",
  },
  // Add more mock cards if needed
];

export function useJobcardRegularMaintainanceView() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();
  const [selectedJobCard, setSelectedJobCard] = useState<JobCardData | null>(null);

  useEffect(() => {
    const found = jobCards.find(card => card.id === id);
    setSelectedJobCard(found || null);
  }, [id]);

  function handleBack() {
    navigate(-1);
  }

  return {
    selectedJobCard,
    handleBack,
    currentPage: 1,
    totalPages: 1,
    goToPage: () => {},
  };
}