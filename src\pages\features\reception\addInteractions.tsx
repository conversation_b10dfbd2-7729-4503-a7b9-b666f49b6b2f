import React, { useState } from 'react';
import { ArrowLeft, MessageCircle } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { useNavigate } from 'react-router-dom';
import { InteractionData } from './type/reception-type';
import {
  interactionModes,
  purposes,
  statuses,
  staffMembers
} from './common/mockData';
import {
  handleInputChange,
  handleSubmit,
  handleCancel,
  renderConditionalDropdown
} from './hook/useAddInteraction';
import { Label } from '@/components/ui/label';

export function AddInteractionPage() {
  const [interactionData, setInteractionData] = useState<InteractionData>({
    interactionId: 'IM00001',
    customerName: '',
    phoneNumber: '',
    email: '',
    interactionMode: '',
    purpose: '',
    status: '',
    serviceType: '',
    breakdownDate: '',
    location: '',
    insuranceClaimed: false,
    staffMember: '',
    staffRole: '', // Default empty, can be set based on user role
    additionalNotes: ''
  });

  const navigate = useNavigate();

  return (
    <div className="p-6 min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <Button
          variant="outline"
          className="mb-4 bg-red-900 text-white hover:bg-red-800"
          onClick={() => navigate('/reception/interaction-management')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          Go Back
        </Button>
        
        <div className="flex items-center gap-2">
          <MessageCircle className="h-6 w-6 text-gray-600" />
          <h1 className="text-2xl font-semibold text-gray-900">
            Interaction Management - <span className="font-normal">Add</span>
          </h1>
        </div>
      </div>

      {/* Form */}
      <div className="bg-white rounded-lg p-6">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          {/* Row 1 */}
          <div className="relative">
            <Label htmlFor="interactionId" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Interaction ID
            </Label>
            <Input
              value={interactionData.interactionId}
              onChange={(e) => handleInputChange('interactionId', e.target.value, setInteractionData)}
              className="w-full bg-gray-100"
              readOnly
            />
          </div>

          <div className="relative">
            <Label htmlFor="customerName" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Customer Name
            </Label>
            <Input
              placeholder="eg. Emma Sophia"
              value={interactionData.customerName}
              onChange={(e) => handleInputChange('customerName', e.target.value, setInteractionData)}
              className="w-full"
            />
          </div>

          {/* Row 2 */}
          <div className="relative">
            <Label htmlFor="phoneNumber" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Phone Number
            </Label>
            <Input
              placeholder="eg. 0427 899 650"
              value={interactionData.phoneNumber}
              onChange={(e) => handleInputChange('phoneNumber', e.target.value, setInteractionData)}
              className="w-full"
            />
          </div>

          <div className="relative">
            <Label htmlFor="interactionMode" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Interaction Mode
            </Label>
            <Select
              value={interactionData.interactionMode}
              onValueChange={(value) => handleInputChange('interactionMode', value, setInteractionData)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {interactionModes.map((mode) => (
                  <SelectItem key={mode} value={mode}>
                    {mode}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Conditional Email Field */}
          {interactionData.interactionMode === 'Email' && (
            <div>
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Customer Email
              </label>
              <Input
                placeholder="eg. <EMAIL>"
                value={interactionData.email}
                onChange={(e) => handleInputChange('email', e.target.value, setInteractionData)}
                className="w-full"
              />
            </div>
          )}

          {/* Row 3 */}
          <div>
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Purpose
            </label>
            <Select 
              value={interactionData.purpose} 
              onValueChange={(value) => handleInputChange('purpose', value, setInteractionData)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {purposes.map((purpose) => (
                  <SelectItem key={purpose} value={purpose}>
                    {purpose}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Conditional Dropdown */}
          {renderConditionalDropdown(interactionData, (field, value) => handleInputChange(field, value, setInteractionData))}

          {/* Status Field */}
          <div className="md:col-span-1">
            <label className="block text-sm font-medium text-gray-700 mb-2">
              Status
            </label>
            <Select 
              value={interactionData.status} 
              onValueChange={(value) => handleInputChange('status', value, setInteractionData)}
            >
              <SelectTrigger className="w-full">
                <SelectValue placeholder="Select" />
              </SelectTrigger>
              <SelectContent>
                {statuses.map((status) => (
                  <SelectItem key={status} value={status}>
                    {status}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Staff Member Field (Conditional) */}
          {interactionData.status === 'Escalated' && (
            <div className="md:col-span-1">
              <label className="block text-sm font-medium text-gray-700 mb-2">
                Staff Member
              </label>
              <Select 
                value={interactionData.staffMember || ''} 
                onValueChange={(value) => handleInputChange('staffMember', value, setInteractionData)}
              >
                <SelectTrigger className="w-full">
                  <SelectValue placeholder="Select Staff Member" />
                </SelectTrigger>
                <SelectContent>
                  {staffMembers.map((member) => (
                    <SelectItem key={member} value={member}>
                      {member}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>
          )}
        </div>

        {/* Action Buttons */}
        <div className="flex justify-end gap-4 mt-8">
          <Button 
            variant="outline" 
            onClick={() => handleCancel()}
            className="px-8"
          >
            Cancel
          </Button>
          <Button 
            onClick={() => handleSubmit(interactionData)}
            className="bg-red-900 hover:bg-red-800 text-white px-8"
          >
            Add
          </Button>
        </div>
      </div>
    </div>
  );
}