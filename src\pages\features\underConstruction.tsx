import React, { useState, useEffect } from 'react';
import { <PERSON>, <PERSON><PERSON>, <PERSON>, Settings} from 'lucide-react';

export default function UnderConstructionPage() {
  const [carPosition, setCarPosition] = useState(0);

  useEffect(() => {
    const interval = setInterval(() => {
      setCarPosition((prev) => (prev + 1) % 100);
    }, 50);
    return () => clearInterval(interval);
  }, []);

  return (
    <div className="relative flex flex-col items-center justify-center min-h-screen bg-gradient-to-br from-gray-100 via-white to-gray-100 px-4 text-gray-900 overflow-hidden">
      {/* Animated Car with Road Effect and Tool Icons */}
      <div className="relative mb-8">
        <div className="relative flex items-center justify-center">
          {/* Car Icon - Center */}
          <div className="relative  ">
            <Car
              className="w-32 h-32 text-yellow-400"
            />
            {/* Animated Wrench */}
            <div className="absolute -top-4 -right-8">
              <div className="animate-spin-slow animate-bounce">
                <Wrench className="h-12 w-12 text-blue-600 transform rotate-45" />
              </div>
            </div>
            {/* Animated Hammer */}
            <div className="absolute -bottom-6 -left-6">
              <div className="animate-pulse animate-wiggle">
                <Hammer className="h-10 w-10 text-orange-600 transform -rotate-12" />
              </div>
            </div>
            {/* Animated Settings Gear */}
            <div className="absolute top-12 -left-8">
              <div className="animate-spin-slow">
                <Settings className="h-8 w-8 text-gray-600" />
              </div>
            </div>
            {/* Sparks Effect */}
            <div className="absolute top-8 right-4">
                  <div className="animate-ping">
                    <div className="h-2 w-2 bg-yellow-400 rounded-full"></div>
                  </div>
            </div>
            <div className="absolute bottom-8 left-8">
                <div className="animate-ping delay-300">
                <div className="h-1.5 w-1.5 bg-orange-400 rounded-full"></div>
                </div>
            </div>
            <div className="absolute top-16 right-12">
                <div className="animate-ping delay-700">
                <div className="h-1 w-1 bg-red-400 rounded-full"></div>
                </div>
            </div>
          </div>
        </div>
      </div>

      {/* Title with Gradient and Pulse Effect */}
      <h1 className="text-4xl md:text-6xl font-extrabold bg-clip-text text-transparent bg-gradient-to-r from-gray-500 to-blue-600 mb-4 animate-pulse">
        Site Under Construction
      </h1>

      {/* Description with Subtle Fade-In */}
      <p className="text-lg md:text-xl text-gray-700 text-center mb-2 animate-fade-in max-w-2xl">
        We're revving up your rental experience with a brand-new page!
      </p>

      {/* Background Decorative Elements */}
      <div className="absolute inset-0 -z-10 overflow-hidden">
        <div className="absolute top-0 left-0 w-full h-full bg-[radial-gradient(circle_at_center,_rgba(107,114,128,0.1)_0%,_transparent_70%)]" />
        <div className="absolute top-20 left-20 w-40 h-40 bg-gray-300/10 rounded-full animate-pulse-slow" />
        <div className="absolute bottom-20 right-20 w-60 h-60 bg-blue-300/10 rounded-full animate-pulse-slow delay-500" />
        <div className="absolute top-1/2 left-0 w-20 h-20 bg-gray-200/10 rounded-full animate-ping delay-1000" />
      </div>
    </div>
  );
}