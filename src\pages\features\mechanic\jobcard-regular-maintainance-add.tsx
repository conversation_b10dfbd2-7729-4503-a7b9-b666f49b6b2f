import { Button } from '../../../components/ui/button'
import { Input } from '../../../components/ui/input'
import { Label } from '../../../components/ui/label'
import { Checkbox } from '../../../components/ui/checkbox'
import { Textarea } from '../../../components/ui/textarea'
import { ArrowLeft, Wrench } from 'lucide-react'
import { useJobcardRegularMaintainanceAdd } from './hook/usejobcard-regular-maintainance-add'

export function JobcardRegularMaintainanceAdd() {
  const {
    formData,
    isSubmitting,
    handleInputChange,
    handleCheckboxChange,
    handleSubmit,
    handleCancel
  } = useJobcardRegularMaintainanceAdd()

  return (
    <div className="p-0 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Mobile Back Button */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>
      {/* Desktop Back Button and Header */}
      <div className="hidden md:flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
        </div>
      </div>

      <form onSubmit={handleSubmit}>
        {/* Vehicle Details Section */}
        <div className="bg-white rounded-lg mb-6 p-4">
          <h2 className="text-lg font-bold text-gray-800 mb-4">Vehicle Details</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-4">
            {/* Registration */}
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Registration</Label>
              <Input
                id="registration"
                placeholder="ASV 003"
                value={formData.registration}
                onChange={(e) => handleInputChange('registration', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                required
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Make</Label>
              <Input
                id="make"
                placeholder="Kawasaki"
                value={formData.make}
                onChange={(e) => handleInputChange('make', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                required
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Model</Label>
              <Input
                id="model"
                placeholder="Z800"
                value={formData.model}
                onChange={(e) => handleInputChange('model', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
                required
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Model Series</Label>
              <Input
                id="modelSeries"
                placeholder="1234"
                value={formData.modelSeries}
                onChange={(e) => handleInputChange('modelSeries', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Colour</Label>
              <Input
                id="colour"
                placeholder="Dark Blue"
                value={formData.colour}
                onChange={(e) => handleInputChange('colour', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Prod Date</Label>
              <Input
                id="prodDate"
                type="date"
                value={formData.prodDate}
                onChange={(e) => handleInputChange('prodDate', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Next Service</Label>
              <Input
                id="nextService"
                placeholder="General Maintenance every 5000 km"
                value={formData.nextService}
                onChange={(e) => handleInputChange('nextService', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Rego Due</Label>
              <Input
                id="regoDue"
                type="date"
                value={formData.regoDue}
                onChange={(e) => handleInputChange('regoDue', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Engine #</Label>
              <Input
                id="engineNumber"
                placeholder="#457815"
                value={formData.engineNumber}
                onChange={(e) => handleInputChange('engineNumber', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">VIN</Label>
              <Input
                id="vin"
                placeholder="3QGOTDMJRYBZ0NZF1"
                value={formData.vin}
                onChange={(e) => handleInputChange('vin', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Build Date</Label>
              <Input
                id="buildDate"
                type="date"
                value={formData.buildDate}
                onChange={(e) => handleInputChange('buildDate', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Litres</Label>
              <Input
                id="engineOilLitres"
                placeholder="5"
                value={formData.engineOilLitres}
                onChange={(e) => handleInputChange('engineOilLitres', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Trans</Label>
              <Input
                id="trans"
                placeholder="Trans"
                value={formData.trans}
                onChange={(e) => handleInputChange('trans', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Air</Label>
              <Input
                id="air"
                placeholder="Air"
                value={formData.air}
                onChange={(e) => handleInputChange('air', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Cyl</Label>
              <Input
                id="cyl"
                placeholder="Cyl"
                value={formData.cyl}
                onChange={(e) => handleInputChange('cyl', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Body</Label>
              <Input
                id="body"
                placeholder="Body"
                value={formData.body}
                onChange={(e) => handleInputChange('body', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Odo</Label>
              <Input
                id="odo"
                placeholder="176,394"
                value={formData.odo}
                onChange={(e) => handleInputChange('odo', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Hours</Label>
              <Input
                id="hours"
                placeholder="5 H"
                value={formData.hours}
                onChange={(e) => handleInputChange('hours', e.target.value)}
                className="bg-white w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        {/* Job Card Notes Section */}
        <div className="bg-white rounded-lg mb-6 p-4">
          <h2 className="text-lg font-bold text-gray-800 mb-4">Job Card Notes</h2>
          <h3 className="text-sm font-semibold text-gray-700 mb-6">The Service Requires;</h3>
          <div className="space-y-6">
            {/* Drain & Refilled Engine Oil */}
            <div className="flex flex-col gap-2">
              <div className="flex items-center gap-2">
                <Checkbox
                  id="drainRefillEngineOil"
                  checked={formData.drainRefillEngineOil}
                  onCheckedChange={(checked) => handleCheckboxChange('drainRefillEngineOil', checked as boolean)}
                />
                <Label htmlFor="drainRefillEngineOil" className="text-sm">Drain & Refilled Engine Oil</Label>
                <Input
                  placeholder="........................."
                  value={formData.engineOilLitres}
                  onChange={(e) => handleInputChange('engineOilLitres', e.target.value)}
                  className="w-32 h-6 text-sm mx-2 bg-transparent border-b border-dotted border-gray-400 rounded-none focus:outline-none focus:ring-0 focus:border-b"
                  style={{ minWidth: 100 }}
                />
                <span className="text-sm">L</span>
              </div>
            </div>

            {/* Replace Oil Filter and Replace Fuel Filter */}
            <div className="flex flex-col gap-2 md:flex-row md:items-center md:gap-4">
              <div className="flex items-center gap-2">
                <Checkbox
                  id="replaceOilFilter"
                  checked={formData.replaceOilFilter}
                  onCheckedChange={(checked) => handleCheckboxChange('replaceOilFilter', checked as boolean)}
                />
                <Label htmlFor="replaceOilFilter" className="text-sm">Replace Oil Filter</Label>
                <Input
                  placeholder="........................."
                  value={formData.replaceOilFilterLitres}
                  onChange={(e) => handleInputChange('replaceOilFilterLitres', e.target.value)}
                  className="w-32 h-6 text-sm mx-2 bg-transparent border-b border-dotted border-gray-400 rounded-none focus:outline-none focus:ring-0 focus:border-b"
                  style={{ minWidth: 100 }}
                />
                <span className="text-sm">L</span>
              </div>
              <div className="flex items-center gap-2 mt-2 md:mt-0">
                <Checkbox
                  id="replaceFuelFilter"
                  checked={formData.replaceFuelFilter}
                  onCheckedChange={(checked) => handleCheckboxChange('replaceFuelFilter', checked as boolean)}
                />
                <Label htmlFor="replaceFuelFilter" className="text-sm">Replace Fuel Filter</Label>
              </div>
            </div>

            {/* Air Filter */}
            <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-2 md:space-y-0">
              <span className="text-sm">Air Filter -</span>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="airFilterChecked"
                  checked={formData.airFilterChecked}
                  onCheckedChange={(checked) => handleCheckboxChange('airFilterChecked', checked as boolean)}
                />
                <Label htmlFor="airFilterChecked" className="text-sm">Checked</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="airFilterReplaced"
                  checked={formData.airFilterReplaced}
                  onCheckedChange={(checked) => handleCheckboxChange('airFilterReplaced', checked as boolean)}
                />
                <Label htmlFor="airFilterReplaced" className="text-sm">Replaced</Label>
              </div>
            </div>

            {/* Cabin Filter */}
            <div className="flex flex-col md:flex-row md:items-center md:space-x-8 space-y-2 md:space-y-0">
              <span className="text-sm">Cabin Filter -</span>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="cabinFilterChecked"
                  checked={formData.cabinFilterChecked}
                  onCheckedChange={(checked) => handleCheckboxChange('cabinFilterChecked', checked as boolean)}
                />
                <Label htmlFor="cabinFilterChecked" className="text-sm">Checked</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="cabinFilterReplaced"
                  checked={formData.cabinFilterReplaced}
                  onCheckedChange={(checked) => handleCheckboxChange('cabinFilterReplaced', checked as boolean)}
                />
                <Label htmlFor="cabinFilterReplaced" className="text-sm">Replaced</Label>
              </div>
            </div>

            {/* Check and Adjust Tyre Air Pressure */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="checkAdjustTyrePressure"
                checked={formData.checkAdjustTyrePressure}
                onCheckedChange={(checked) => handleCheckboxChange('checkAdjustTyrePressure', checked as boolean)}
              />
              <Label htmlFor="checkAdjustTyrePressure" className="text-sm">Check and Adjust Tyre Air Pressure</Label>
            </div>

            {/* Check & Top Up all fluids */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="checkTopUpAllFluids"
                checked={formData.checkTopUpAllFluids}
                onCheckedChange={(checked) => handleCheckboxChange('checkTopUpAllFluids', checked as boolean)}
              />
              <Label htmlFor="checkTopUpAllFluids" className="text-sm">Check & Top Up all fluids</Label>
            </div>

            {/* Check, Clean & Adjust Break (two instances) */}
            <div className="flex items-center space-x-8">
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkCleanAdjustBreak"
                  checked={formData.checkCleanAdjustBreak}
                  onCheckedChange={(checked) => handleCheckboxChange('checkCleanAdjustBreak', checked as boolean)}
                />
                <Label htmlFor="checkCleanAdjustBreak" className="text-sm">Check, Clean & Adjust Break</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="checkCleanAdjustBreak2"
                  checked={formData.checkCleanAdjustBreak2}
                  onCheckedChange={(checked) => handleCheckboxChange('checkCleanAdjustBreak2', checked as boolean)}
                />
                <Label htmlFor="checkCleanAdjustBreak2" className="text-sm">Check, Clean & Adjust Break</Label>
              </div>
            </div>

            {/* Check Wiper Blades */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="checkWiperBlades"
                checked={formData.checkWiperBlades}
                onCheckedChange={(checked) => handleCheckboxChange('checkWiperBlades', checked as boolean)}
              />
              <Label htmlFor="checkWiperBlades" className="text-sm">Check Wiper Blades</Label>
            </div>

            {/* Check Lights */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="checkLights"
                checked={formData.checkLights}
                onCheckedChange={(checked) => handleCheckboxChange('checkLights', checked as boolean)}
              />
              <Label htmlFor="checkLights" className="text-sm">Check Lights</Label>
            </div>

            {/* Spark Plugs */}
            <div className="flex items-center space-x-4">
              <span className="text-sm">Spark Plugs -</span>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sparkPlugsChecked"
                  checked={formData.sparkPlugsChecked}
                  onCheckedChange={(checked) => handleCheckboxChange('sparkPlugsChecked', checked as boolean)}
                />
                <Label htmlFor="sparkPlugsChecked" className="text-sm">Checked</Label>
              </div>
              <div className="flex items-center space-x-2">
                <Checkbox
                  id="sparkPlugsReplaced"
                  checked={formData.sparkPlugsReplaced}
                  onCheckedChange={(checked) => handleCheckboxChange('sparkPlugsReplaced', checked as boolean)}
                />
                <Label htmlFor="sparkPlugsReplaced" className="text-sm">Replaced</Label>
              </div>
            </div>

            {/* Check for correct operation */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="checkCorrectOperation"
                checked={formData.checkCorrectOperation}
                onCheckedChange={(checked) => handleCheckboxChange('checkCorrectOperation', checked as boolean)}
              />
              <Label htmlFor="checkCorrectOperation" className="text-sm">Check for correct operation. Safety Check & Report</Label>
            </div>

            {/* Tyre Conditions */}
            <div className="text-sm mt-4">
              <span>Tyre Conditions : RF </span>
              <Input
                placeholder="..................."
                value={formData.tyreConditionRF}
                onChange={(e) => handleInputChange('tyreConditionRF', e.target.value)}
                className="inline-block w-16 h-6 text-sm mx-1"
              />
              <span>% LF </span>
              <Input
                placeholder="..................."
                value={formData.tyreConditionLF}
                onChange={(e) => handleInputChange('tyreConditionLF', e.target.value)}
                className="inline-block w-16 h-6 text-sm mx-1"
              />
              <span>% RR </span>
              <Input
                placeholder="..................."
                value={formData.tyreConditionRR}
                onChange={(e) => handleInputChange('tyreConditionRR', e.target.value)}
                className="inline-block w-16 h-6 text-sm mx-1"
              />
              <span>% LR </span>
              <Input
                placeholder="..................."
                value={formData.tyreConditionLR}
                onChange={(e) => handleInputChange('tyreConditionLR', e.target.value)}
                className="inline-block w-16 h-6 text-sm mx-1"
              />
              <span>%</span>
            </div>

            {/* Break Condition */}
            <div className="text-sm">
              <div className="mb-2">
                <span>Break Condition : Front </span>
                <Input
                  placeholder="..................."
                  value={formData.breakConditionFront}
                  onChange={(e) => handleInputChange('breakConditionFront', e.target.value)}
                  className="inline-block w-16 h-6 text-sm mx-1"
                />
                <span>% Discs </span>
                <Input
                  placeholder="................"
                  value={formData.breakDiscsFront}
                  onChange={(e) => handleInputChange('breakDiscsFront', e.target.value)}
                  className="inline-block w-16 h-6 text-sm mx-1"
                />
                <span>.......... MM</span>
              </div>
              <div className="ml-24">
                <span>Rear </span>
                <Input
                  placeholder="..................."
                  value={formData.breakConditionRear}
                  onChange={(e) => handleInputChange('breakConditionRear', e.target.value)}
                  className="inline-block w-16 h-6 text-sm mx-1"
                />
                <span>% Discs </span>
                <Input
                  placeholder="................"
                  value={formData.breakDiscsRear}
                  onChange={(e) => handleInputChange('breakDiscsRear', e.target.value)}
                  className="inline-block w-16 h-6 text-sm mx-1"
                />
                <span>.......... MM</span>
              </div>
            </div>

            {/* Check GPS */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="checkGPS"
                checked={formData.checkGPS}
                onCheckedChange={(checked) => handleCheckboxChange('checkGPS', checked as boolean)}
              />
              <Label htmlFor="checkGPS" className="text-sm">Check GPS</Label>
            </div>

            {/* Road test */}
            <div className="flex items-center space-x-2">
              <Checkbox
                id="roadTest"
                checked={formData.roadTest}
                onCheckedChange={(checked) => handleCheckboxChange('roadTest', checked as boolean)}
              />
              <Label htmlFor="roadTest" className="text-sm">Road test</Label>
            </div>

            {/* Comments */}
            <div className="mt-6">
              <div className="border rounded-md p-3">
                <div className="text-sm text-gray-600 mb-2">Comment</div>
                <Textarea
                  placeholder="Type Here ---"
                  value={formData.comments}
                  onChange={(e) => handleInputChange('comments', e.target.value)}
                  className="text-sm min-h-[80px] resize-none border-none p-0"
                />
              </div>
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex flex-row justify-end gap-2  pt-4 px-4">
          <Button
            type="submit"
            className="bg-[#330101] hover:bg-gray-800 text-white px-8 py-2 w-full sm:w-32 text-sm"
            disabled={isSubmitting}
          >
            {isSubmitting ? 'Adding...' : 'Add'}
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="px-8 py-2 w-full sm:w-32 text-sm"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  )
}