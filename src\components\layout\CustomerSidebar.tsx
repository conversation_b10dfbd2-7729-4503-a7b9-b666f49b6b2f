/* import { NavLink } from 'react-router-dom';
import { Home, UserCircle, CalendarPlus, History, FileWarning, FileEdit, FileText, Bell, ClipboardList, Settings, AlertTriangle } from 'lucide-react';

export function CustomerSidebar() {
  const navItems = [
    {
      path: '/customer/dashboard',
      label: 'Dashboard Overview',
      icon: <Home className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/profile',
      label: 'Profile',
      icon: <UserCircle className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/add-rental',
      label: 'Add a Rental',
      icon: <CalendarPlus className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/booking-history',
      label: 'Booking History',
      icon: <History className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/incident-reporting',
      label: 'Incident Reporting',
      icon: <FileWarning className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/change-requests',
      label: 'Change Requests',
      icon: <FileEdit className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/invoices',
      label: 'Invoices',
      icon: <FileText className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/fines',
      label: 'Fines',
      icon: <AlertTriangle className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/notifications',
      label: 'Notifications Centre',
      icon: <Bell className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/activity-log',
      label: 'Activity Log',
      icon: <ClipboardList className="w-5 h-5 mr-3" />
    },
    {
      path: '/customer/settings',
      label: 'Settings',
      icon: <Settings className="w-5 h-5 mr-3" />
    }
  ];

  return (
    <aside className="bg-earth-cream w-64 min-h-screen flex flex-col">
      <nav className="flex-1">
        <div className="p-4 border-b border-gold-lighter">
          <h2 className="text-lg font-semibold text-earth-dark">Customer Portal</h2>
        </div>
        <ul className="space-y-1 pt-4">
          {navItems.map(item => (
            <li key={item.path}>
              <NavLink
                to={item.path}
                className={({ isActive }) =>
                  `flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors ${
                    isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                  }`
                }
              >
                {item.icon}
                <span>{item.label}</span>
              </NavLink>
            </li>
          ))}
        </ul>
      </nav>
    </aside>
  );
}
 */


import React from 'react';
import { NavLink } from 'react-router-dom';
import { 
  Home, 
  UserCircle, 
  CalendarPlus, 
  History, 
  FileWarning, 
  FileEdit, 
  FileText, 
  Bell, 
  ClipboardList, 
  Settings, 
  AlertTriangle 
} from 'lucide-react';

export function CustomerSidebar({ isOpen, onClose }) {
  const navItems = [
    {
      path: '/customer/dashboard',
      label: 'Dashboard Overview',
      icon: <Home className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/profile',
      label: 'Profile',
      icon: <UserCircle className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/add-rental',
      label: 'Add a Rental',
      icon: <CalendarPlus className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/booking-history',
      label: 'Booking History',
      icon: <History className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/incident-reporting',
      label: 'Incident Reporting',
      icon: <FileWarning className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/change-requests',
      label: 'Change Requests',
      icon: <FileEdit className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/invoices',
      label: 'Invoices',
      icon: <FileText className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/fines',
      label: 'Fines',
      icon: <AlertTriangle className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/notifications',
      label: 'Notifications Centre',
      icon: <Bell className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/activity-log',
      label: 'Activity Log',
      icon: <ClipboardList className="w-5 h-5 mr-3 flex-shrink-0" />
    },
    {
      path: '/customer/settings',
      label: 'Settings',
      icon: <Settings className="w-5 h-5 mr-3 flex-shrink-0" />
    }
  ];

  return (
    <>
      {/* Mobile overlay */}
      {isOpen && (
        <div 
          className="fixed inset-0 bg-black bg-opacity-50 z-40 lg:hidden"
          onClick={onClose}
        />
      )}
      
      {/* Sidebar */}
      <aside className={`
        bg-earth-cream flex flex-col
        fixed lg:static inset-y-0 left-0 z-50
        w-64 lg:w-64
        transform transition-transform duration-300 ease-in-out
        ${isOpen ? 'translate-x-0' : '-translate-x-full lg:translate-x-0'}
      `}>
        <nav className="flex-1 overflow-y-auto">
          <div className="p-4 border-b border-gold-lighter">
            <h2 className="text-lg font-semibold text-earth-dark">Customer Portal</h2>
          </div>
          <ul className="space-y-1 pt-4 pb-4">
            {navItems.map(item => (
              <li key={item.path}>
                <NavLink
                  to={item.path}
                  onClick={() => {
                    // Close sidebar on mobile when nav item is clicked
                    if (window.innerWidth < 1024) {
                      onClose();
                    }
                  }}
                  className={({ isActive }) =>
                    `flex items-center px-4 py-3 text-earth-dark hover:bg-gold-lighter transition-colors ${
                      isActive ? 'bg-gold-lighter border-l-4 border-gold-dark' : ''
                    }`
                  }
                >
                  {item.icon}
                  <span className="text-sm lg:text-base truncate">{item.label}</span>
                </NavLink>
              </li>
            ))}
          </ul>
        </nav>
      </aside>
    </>
  );
}