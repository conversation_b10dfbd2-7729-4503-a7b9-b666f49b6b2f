import { NavigateFunction } from 'react-router-dom';
import { VehicleFormData } from '../type/teamleadertype';

export const handleInputChange = (
  field: keyof VehicleFormData,
  value: string,
  setFormData: React.Dispatch<React.SetStateAction<VehicleFormData>>
): void => {
  setFormData(prev => ({ ...prev, [field]: value }));
};

export const handleImageUpload = (
  field: keyof VehicleFormData,
  value: string,
  setImageData: React.Dispatch<React.SetStateAction<VehicleFormData>>
): void => {
  setImageData(prev => ({ ...prev, [field]: value }));
};

export const handleSave = (navigate: NavigateFunction): void => {
  navigate('/teamleader/service-management');
};