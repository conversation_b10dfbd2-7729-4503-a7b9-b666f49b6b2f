import { useState, useMemo } from "react";
import { BreakdownService } from "../type/mechanictype";

const MOCK_DATA: BreakdownService[] = [
  {
    vehicleId: "VID0001",
    vehicle: "Isuzu Npr 200 - AFS 009",
    incidentDate: "21-02-2025",
    actualDate: "22-02-2025",
    estimatedDate: "25-02-2025",
    mechanicAssigned: "<PERSON>",
    status: "InProgress",
  },
  {
    vehicleId: "VID0002",
    vehicle: "Toyota Camry - ABC 123",
    incidentDate: "20-02-2025",
    actualDate: "21-02-2025",
    estimatedDate: "23-02-2025",
    mechanicAssigned: "<PERSON>",
    status: "Pending",
  },
  {
    vehicleId: "VID0003",
    vehicle: "Honda Civic - XYZ 789",
    incidentDate: "19-02-2025",
    actualDate: "20-02-2025",
    estimatedDate: "22-02-2025",
    mechanicAssigned: "<PERSON>",
    status: "Done",
  },
];

export function useBreakdownService() {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const filteredData = useMemo(() => {
    let data = MOCK_DATA;

    if (filterStatus !== "All") {
      data = data.filter((item) => item.status === filterStatus);
    }

    if (searchTerm.trim()) {
      data = data.filter(
        (item) =>
          item.vehicle.toLowerCase().includes(searchTerm.toLowerCase())
      );
    }

    return data;
  }, [searchTerm, filterStatus]);

  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);

  const currentData = useMemo(() => {
    const start = (currentPage - 1) * recordsPerPage;
    return filteredData.slice(start, start + recordsPerPage);
  }, [filteredData, currentPage, recordsPerPage]);

  return {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    recordsPerPage,
    setRecordsPerPage,
    currentPage,
    setCurrentPage,
    currentData,
    totalPages,
    totalEntries,
  };
}