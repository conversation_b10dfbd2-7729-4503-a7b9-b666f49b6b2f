import React, { useState, useEffect } from 'react';
import { ArrowLeft, Eye, Upload, ChevronDown, Trash, NotebookPen, Mail, Logs } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Checkbox } from '@/components/ui/checkbox';
import { RadioGroup, RadioGroupItem } from '@/components/ui/radio-group';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useNavigate } from 'react-router-dom';
import { DropdownMenu, DropdownMenuTrigger, DropdownMenuContent, DropdownMenuItem } from '@/components/ui/dropdown-menu';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageTitle: string;
}

const ImageModal: React.FC<ImageModalProps> = ({ isOpen, onClose, imageSrc, imageTitle }) => {
  const [newImageSrc, setNewImageSrc] = useState(imageSrc);

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const reader = new FileReader();
      reader.onload = (e) => {
        setNewImageSrc(e.target?.result as string);
      };
      reader.readAsDataURL(file);
    }
  };

  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:w-[80%] md:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-sm xs:text-base sm:text-lg md:text-xl">{imageTitle}</DialogTitle>
        </DialogHeader>
        <div className="space-y-3 xs:space-y-4 sm:space-y-6">
          <div className="flex justify-center p-2 xs:p-3 sm:p-4">
            <img 
              src={newImageSrc} 
              alt={imageTitle} 
              className="max-w-full max-h-64 xs:max-h-80 sm:max-h-96 object-contain"
            />
          </div>
          <div className="flex justify-center">
            <Button
              variant="outline"
              size="sm"
              className="text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
              onClick={() => {
                const input = document.createElement('input');
                input.type = 'file';
                input.accept = 'image/*';
                input.onchange = handleImageUpload;
                input.click();
              }}
            >
              <Upload className="w-3 xs:w-4 h-3 xs:h-4 mr-1 xs:mr-2" />
              Upload New Image
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  );
};

interface FormData {
  rentalId: string;
  vehicle: string;
  vehicleClass: string;
  pickupLocation: string;
  returnLocation: string;
  odometerAtPickup: string;
  odometerAtReturn: string;
  actualDistance: string;
  includedDistance: string;
  fuelLevelAtPickup: string;
  fuelLevelAtReturn: string;
  isFuelLevelSame: string;
  reportType: string;
  comment: string;
  incidentDate: string;
  numberOfDaysLeft: string;
  workshopProceed: string;
  feeIfApplicable: string;
  paymentDue: string;
  excessCoverObtained: string;
  vehicleReplaceable: string;
}

export function CustomerMReturnVehicle() {
  const navigate = useNavigate();
  const [selectedImage, setSelectedImage] = useState<{ src: string; title: string } | null>(null);
  const [formData, setFormData] = useState<FormData>({
    rentalId: '90001',
    vehicle: 'Nissan Micra - 1AF1AW',
    vehicleClass: 'Economy Plus',
    pickupLocation: 'Somerton',
    returnLocation: 'Somerton',
    odometerAtPickup: '12345',
    odometerAtReturn: '12695',
    actualDistance: '350',
    includedDistance: '250',
    fuelLevelAtPickup: '8',
    fuelLevelAtReturn: '6',
    isFuelLevelSame: 'no',
    reportType: '',
    comment: '',
    incidentDate: '',
    numberOfDaysLeft: '',
    workshopProceed: 'no',
    feeIfApplicable: '50.00',
    paymentDue: '',
    excessCoverObtained: 'yes',
    vehicleReplaceable: 'yes'
  });

  const handleImageView = (imageSrc: string, title: string) => {
    setSelectedImage({ src: imageSrc, title });
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const handleGoBack = (rentalId: string): void => {
    const cleanId = rentalId.replace('#', '');
    navigate(`/admin/customerMasterAdmin/customerM-bookingSummery/${cleanId}`);
  };

  const handleCancel = (rentalId: string): void => {
    const cleanId = rentalId.replace('#', '');
    navigate(`/admin/customerMasterAdmin/customerM-bookingSummery/${cleanId}`);
  };

  const handleSubmit = (rentalId: string): void => {
    const cleanId = rentalId.replace('#', '');
    navigate(`/admin/customerMasterAdmin/customerM-bookingSummery/${cleanId}`);
  };

  const handleAddBooking = () => {
    navigate('');
  };

  // Calculate actual distance when odometer values change
  useEffect(() => {
    const pickup = parseInt(formData.odometerAtPickup) || 0;
    const returnValue = parseInt(formData.odometerAtReturn) || 0;
    const distance = returnValue - pickup;
    
    setFormData(prev => ({
      ...prev,
      actualDistance: distance > 0 ? distance.toString() : '0'
    }));
  }, [formData.odometerAtPickup, formData.odometerAtReturn]);

  const showActualDistance = formData.odometerAtPickup !== formData.odometerAtReturn;
  const showFuelLevelAtReturn = formData.isFuelLevelSame === 'no';
  const showIncidentFields = formData.reportType !== 'General Maintenance' && formData.reportType !== '';
  const showProceedToInvoice = 
    formData.reportType !== '' &&
    formData.reportType !== 'General Maintenance' &&
    parseFloat(formData.feeIfApplicable) > 0;
  const showAddBooking = formData.vehicleReplaceable === 'yes';

  const imageCategories = [
    { title: 'Interior Images', items: ['image.jpg'] },
    { title: 'Exterior Images', items: ['image.jpg'] },
    { title: 'Left Side Doors', items: ['image.jpg'] },
    { title: 'Right Side Doors', items: ['image.jpg'] },
    { title: 'Front Side Images', items: ['image.jpg'] },
    { title: 'Back Side Images', items: ['image.jpg'] },
    { title: 'Side Mirrors', items: ['sideMirror.jpg'] },
    { title: 'Other', items: ['image.jpg'] }
  ];

  return (
    <div className="min-h-screen px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
      {/* Main Content */}
      <div className="flex-1 py-3 xs:py-4 sm:py-6">
        {/* Top Navigation */}
        <div className="max-w-7xl mx-auto px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-3 xs:py-4 sm:py-6">
            <div className="flex items-center mb-2 sm:mb-0">
              <Button
                className="bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base flex items-center"
                size="sm"
                onClick={() => handleGoBack(formData.rentalId)}
              >
                <ArrowLeft className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
                <span className="hidden sm:inline">Go Back</span>
              </Button>
            </div>
            <div className="flex flex-wrap items-center gap-1 xs:gap-2 sm:gap-3">
              <Button
                size="sm"
                className="bg-[#330101] text-white hover:text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
                onClick={() => navigate('/admin/customerMasterAdmin/customerM-assignVehicle')}
              >
                Assign Vehicle
              </Button>
              <Button
                size="sm"
                className="text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
              >
                Return Vehicle
              </Button>
              {/* Agreement Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" className="bg-[#330101] text-white flex items-center text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2">
                    Agreement
                    <ChevronDown className="h-3 xs:h-4 w-3 xs:w-4 ml-1 xs:ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => console.log('Print Agreement')} className="text-xs xs:text-sm sm:text-base">
                    Print Agreement
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => console.log('Send Agreement')} className="text-xs xs:text-sm sm:text-base">
                    Send Agreement
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {/* Receipt Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" className="bg-[#330101] text-white flex items-center text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2">
                    Receipt
                    <ChevronDown className="h-3 xs:h-4 w-3 xs:w-4 ml-1 xs:ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => console.log('Print Receipt')} className="text-xs xs:text-sm sm:text-base">
                    Print Receipt
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => console.log('Send Receipt')} className="text-xs xs:text-sm sm:text-base">
                    Send Receipt
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              {/* Dropdown */}
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button size="sm" className="bg-[#330101] text-white flex items-center text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2">
                    <ChevronDown className="h-3 xs:h-4 w-3 xs:w-4" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent>
                  <DropdownMenuItem onClick={() => console.log('Delete')} className="text-xs xs:text-sm sm:text-base">
                    <Trash className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
                    Delete
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => console.log('Send Receipt')} className="text-xs xs:text-sm sm:text-base">
                    <NotebookPen className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
                    Rental Agreement Revisions
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => console.log('Send Receipt')} className="text-xs xs:text-sm sm:text-base">
                    <Mail className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
                    Emails
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={() => console.log('Send Receipt')} className="text-xs xs:text-sm sm:text-base">
                    <Logs className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
                    Audit Logs
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="max-w-7xl mx-auto space-y-4 xs:space-y-5 sm:space-y-6 mt-4 xs:mt-5 sm:mt-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="rentalId" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Rental ID</Label>
              <Input
                id="rentalId"
                value={formData.rentalId}
                onChange={(e) => setFormData({...formData, rentalId: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
            <div className="relative">
              <Label htmlFor="vehicle" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Vehicle</Label>
              <Input 
                id="vehicle"
                value={formData.vehicle}
                onChange={(e) => setFormData({...formData, vehicle: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
            <div className="relative">
              <Label htmlFor="vehicleClass" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Vehicle Class</Label>
              <Input
                id="vehicleClass"
                value={formData.vehicleClass}
                onChange={(e) => setFormData({...formData, vehicleClass: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="pickupLocation" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Pickup Location</Label>
              <Input
                id="pickupLocation"
                value={formData.pickupLocation}
                onChange={(e) => setFormData({...formData, pickupLocation: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
            <div className="relative">
              <Label htmlFor="pickupDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Pickup Date</Label>
              <div className="flex space-x-1 xs:space-x-2 sm:space-x-3">
                <Input
                  placeholder="DD"
                  value="28"
                  className="w-12 xs:w-14 sm:w-16 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="MM"
                  value="05"
                  className="w-12 xs:w-14 sm:w-16 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="YYYY"
                  value="2025"
                  className="w-16 xs:w-20 sm:w-24 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="Time"
                  value="9:16 AM"
                  className="flex-1 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
              </div>
            </div>
            <div className="relative">
              <Label htmlFor="returnLocation" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Return Location</Label>
              <Input
                id="returnLocation"
                value={formData.returnLocation}
                onChange={(e) => setFormData({...formData, returnLocation: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="returnDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Return Date</Label>
              <div className="flex space-x-1 xs:space-x-2 sm:space-x-3">
                <Input
                  placeholder="DD"
                  value="29"
                  className="w-12 xs:w-14 sm:w-16 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="MM"
                  value="05"
                  className="w-12 xs:w-14 sm:w-16 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="YYYY"
                  value="2025"
                  className="w-16 xs:w-20 sm:w-24 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="Time"
                  value="9:16 AM"
                  className="flex-1 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
              </div>
            </div>
            <div className="relative">
              <Label htmlFor="odometerAtPickup" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Odometer at Pickup</Label>
              <Input
                id="odometerAtPickup"
                value={formData.odometerAtPickup}
                onChange={(e) => setFormData({...formData, odometerAtPickup: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
            <div className="relative">
              <Label htmlFor="odometerAtReturn" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Odometer at Return</Label>
              <Input
                id="odometerAtReturn"
                value={formData.odometerAtReturn}
                onChange={(e) => setFormData({...formData, odometerAtReturn: e.target.value})}
                className="focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="includedDistance" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Included Distance</Label>
              <Input
                id="includedDistance"
                value={`${formData.includedDistance}km`}
                readOnly
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
              />
            </div>
            {showActualDistance && (
              <div className="relative">
                <Label htmlFor="actualDistance" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Actual Distance Travelled</Label>
                <div className="flex items-center gap-1 xs:gap-2 sm:gap-3">
                  <Input
                    id="actualDistance"
                    value={`${formData.actualDistance}km`}
                    className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  />
                  <Checkbox checked={true} />
                </div>
              </div>
            )}
            <div className="relative">
              <Label htmlFor="fuelLevelAtPickup" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Fuel Level at Pickup</Label>
              <Input
                id="fuelLevelAtPickup"
                value={formData.fuelLevelAtPickup}
                onChange={(e) => setFormData({...formData, fuelLevelAtPickup: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
          </div>

          {/* Fuel Level Question */}
          <div className="space-y-3 xs:space-y-4 sm:space-y-5">
            <div>
              <Label className="text-[10px] xs:text-xs sm:text-sm md:text-base font-medium">Is the fuel level the same as it was in the pickup?</Label>
              <RadioGroup
                value={formData.isFuelLevelSame}
                onValueChange={(value) => setFormData({...formData, isFuelLevelSame: value})}
                className="flex gap-3 xs:gap-4 sm:gap-6 mt-1 xs:mt-2 sm:mt-3"
              >
                <div className="flex items-center space-x-1 xs:space-x-2">
                  <RadioGroupItem value="yes" id="fuel-yes" />
                  <Label htmlFor="fuel-yes" className="text-xs xs:text-sm sm:text-base">Yes</Label>
                </div>
                <div className="flex items-center space-x-1 xs:space-x-2">
                  <RadioGroupItem value="no" id="fuel-no" />
                  <Label htmlFor="fuel-no" className="text-xs xs:text-sm sm:text-base">No</Label>
                </div>
              </RadioGroup>
            </div>
            {showFuelLevelAtReturn && (
              <div className="relative w-full sm:w-1/2 md:w-1/3">
                <Label htmlFor="fuelLevelAtReturn" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Fuel Level at Return</Label>
                <div className="flex items-center gap-1 xs:gap-2 sm:gap-3">
                  <Input
                    id="fuelLevelAtReturn"
                    value={formData.fuelLevelAtReturn}
                    onChange={(e) => setFormData({...formData, fuelLevelAtReturn: e.target.value})}
                    className="focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  />
                  <Checkbox checked={true} />
                </div>
              </div>
            )}
          </div>

          {/* Proof Images */}
          <div>
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Proof Images</h3>
              <div className="flex space-x-2 xs:space-x-3 sm:space-x-4">
                <div className="w-20 xs:w-24 sm:w-28 md:w-32 h-20 xs:h-24 sm:h-28 md:h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  <img src="/src/assets/odometre.png" alt="Speedometer" className="w-full h-full object-cover rounded" />
                </div>
                <div className="w-20 xs:w-24 sm:w-28 md:w-32 h-20 xs:h-24 sm:h-28 md:h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  <div
                    className="text-center cursor-pointer"
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = 'image/*';
                      input.onchange = (event: Event) => {
                        const target = event.target as HTMLInputElement;
                        const file = target.files?.[0];
                        if (file) {
                          console.log('File selected:', file);
                        }
                      };
                      input.click();
                    }}
                  >
                    <Upload className="h-6 xs:h-7 sm:h-8 w-6 xs:w-7 sm:w-8 mx-auto mb-1 xs:mb-2 text-gray-400" />
                    <span className="text-[10px] xs:text-xs sm:text-sm text-gray-500">Upload Image</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Images of the Vehicle */}
          <div>
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Images of the Vehicle</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
                {imageCategories.map((category, index) => (
                  <div key={index} className="relative">
                    <Label className="text-[10px] xs:text-xs sm:text-sm md:text-base font-medium text-gray-700 absolute left-2 xs:left-3 -top-1 xs:-top-1.5 sm:-top-2 bg-white px-1">{category.title}</Label>
                    <div className="mt-4 xs:mt-5 sm:mt-6 space-y-1 xs:space-y-2">
                      {category.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex items-center justify-between p-1 xs:p-2 sm:p-3 border rounded">
                          <span className="text-[10px] xs:text-xs sm:text-sm">{item}</span>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleImageView('/api/placeholder/400/300', `${category.title} - ${item}`)}
                          >
                            <Eye className="h-3 xs:h-4 w-3 xs:w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Vehicle Conditions */}
          <div>
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Vehicle Conditions</h3>
              <div className="relative mb-3 xs:mb-4 sm:mb-6 w-full sm:w-1/2 md:w-1/3">
                <Label htmlFor="reportType" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Report Type</Label>
                <Select value={formData.reportType} onValueChange={(value) => setFormData({...formData, reportType: value})}>
                  <SelectTrigger className="h-9 xs:h-10 sm:h-12 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 focus:outline-none focus:ring-0 focus:border-none">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="General Maintenance" className="text-xs xs:text-sm sm:text-base">General Maintenance</SelectItem>
                    <SelectItem value="Accident" className="text-xs xs:text-sm sm:text-base">Accident</SelectItem>
                    <SelectItem value="Breakdown" className="text-xs xs:text-sm sm:text-base">Breakdown</SelectItem>
                    <SelectItem value="Damage" className="text-xs xs:text-sm sm:text-base">Damage</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="relative mt-4 xs:mt-5 sm:mt-6">
                <Label htmlFor="comment" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Comment</Label>
                <Textarea
                  id="comment"
                  value={formData.comment}
                  onChange={(e) => setFormData({...formData, comment: e.target.value})}
                  rows={4}
                  className="focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                />
              </div>
              {showIncidentFields && (
                <div className="space-y-3 xs:space-y-4 sm:space-y-5 mt-4 xs:mt-5 sm:mt-6">
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                    <div className="relative">
                      <Label htmlFor="incidentDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Incident Date</Label>
                      <Input
                        id="incidentDate"
                        value={formData.incidentDate}
                        onChange={(e) => setFormData({...formData, incidentDate: e.target.value})}
                        placeholder="Set Date"
                        className="text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                      />
                    </div>
                    <div className="relative">
                      <Label htmlFor="numberOfDaysLeft" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">No of Days Left</Label>
                      <Input
                        id="numberOfDaysLeft"
                        value={formData.numberOfDaysLeft}
                        onChange={(e) => setFormData({...formData, numberOfDaysLeft: e.target.value})}
                        placeholder="Enter"
                        className="text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                      />
                    </div>
                  </div>
                  <div>
                    <Label className="text-[10px] xs:text-xs sm:text-sm md:text-base font-medium">Does this proceed to the workshop dashboard?</Label>
                    <RadioGroup
                      value={formData.workshopProceed}
                      onValueChange={(value) => setFormData({...formData, workshopProceed: value})}
                      className="flex gap-3 xs:gap-4 sm:gap-6 mt-1 xs:mt-2 sm:mt-3"
                    >
                      <div className="flex items-center space-x-1 xs:space-x-2">
                        <RadioGroupItem value="yes" id="workshop-yes" />
                        <Label htmlFor="workshop-yes" className="text-xs xs:text-sm sm:text-base">Yes</Label>
                      </div>
                      <div className="flex items-center space-x-1 xs:space-x-2">
                        <RadioGroupItem value="no" id="workshop-no" />
                        <Label htmlFor="workshop-no" className="text-xs xs:text-sm sm:text-base">No</Label>
                      </div>
                    </RadioGroup>
                  </div>
                  <div className="relative w-full sm:w-1/2 md:w-1/3">
                    <Label htmlFor="feeIfApplicable" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Fee if applicable</Label>
                    <div className="flex items-center gap-1 xs:gap-2 sm:gap-3">
                      <span className="text-xs xs:text-sm sm:text-base">AUD</span>
                      <Input
                        id="feeIfApplicable"
                        value={formData.feeIfApplicable}
                        onChange={(e) => setFormData({...formData, feeIfApplicable: e.target.value})}
                        placeholder="0.00"
                        className="text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                      />
                    </div>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Payment & Insurance */}
          <div>
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Payment & Insurance</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                <div className="relative">
                  <Label htmlFor="paymentDue" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Payment Due</Label>
                  <Input
                    id="paymentDue"
                    value={formData.paymentDue}
                    onChange={(e) => setFormData({...formData, paymentDue: e.target.value})}
                    className="focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  />
                </div>
                <div className="relative">
                  <Label htmlFor="excessCover" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Excess Cover Obtained?</Label>
                  <Input
                    id="excessCover"
                    value={formData.excessCoverObtained}
                    onChange={(e) => setFormData({...formData, excessCoverObtained: e.target.value})}
                    className="focus:outline-none focus:ring-0 focus:border-none text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  />
                </div>
              </div>
              <div className="mt-3 xs:mt-4 sm:mt-6 flex flex-col sm:flex-row sm:items-center sm:justify-start gap-3 xs:gap-4 sm:gap-6">
                <div className="flex items-center gap-3 xs:gap-4 sm:gap-6">
                  <Label className="text-[10px] xs:text-xs sm:text-sm md:text-base font-medium">Vehicle Replaceable?</Label>
                  <RadioGroup
                    value={formData.vehicleReplaceable}
                    onValueChange={(value) => setFormData({...formData, vehicleReplaceable: value})}
                    className="flex gap-3 xs:gap-4 sm:gap-6"
                  >
                    <div className="flex items-center space-x-1 xs:space-x-2">
                      <RadioGroupItem value="yes" id="replaceable-yes" />
                      <Label htmlFor="replaceable-yes" className="text-xs xs:text-sm sm:text-base">Yes</Label>
                    </div>
                    <div className="flex items-center space-x-1 xs:space-x-2">
                      <RadioGroupItem value="no" id="replaceable-no" />
                      <Label htmlFor="replaceable-no" className="text-xs xs:text-sm sm:text-base">No</Label>
                    </div>
                  </RadioGroup>
                </div>
                {showAddBooking && (
                  <Button 
                    onClick={handleAddBooking}
                    className="px-4 xs:px-5 sm:px-6 py-1 xs:py-2 bg-[#4a1a1a] text-white hover:bg-[#5a2424] rounded-md font-medium text-xs xs:text-sm sm:text-base"
                  >
                    Add Booking
                  </Button>
                )}
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 xs:gap-3 sm:gap-4 mt-4 xs:mt-5 sm:mt-6">
            <Button
              variant="outline"
              onClick={() => handleCancel(formData.rentalId)}
              className="px-4 xs:px-6 sm:px-8 py-1 xs:py-2 text-xs xs:text-sm sm:text-base"
            >
              Cancel
            </Button>
            <Button
              onClick={() => handleSubmit(formData.rentalId)}
              className="px-4 xs:px-5 sm:px-6 py-1 xs:py-2 bg-[#330101] text-white hover:bg-[#660404] text-xs xs:text-sm sm:text-base"
            >
              Submit
            </Button>
            {showProceedToInvoice && (
              <Button
                className="px-4 xs:px-5 sm:px-6 py-1 xs:py-2 bg-[#330101] text-white hover:bg-[#660404] text-xs xs:text-sm sm:text-base"
              >
                Proceed to Invoice
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          isOpen={!!selectedImage}
          onClose={closeModal}
          imageSrc={selectedImage.src}
          imageTitle={selectedImage.title}
        />
      )}
    </div>
  );
}