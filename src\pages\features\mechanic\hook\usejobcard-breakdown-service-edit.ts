import { useState } from 'react';
import { JobCardBreakdownServiceAddForm } from '../type/mechanictype';
import { useNavigate, useParams } from 'react-router-dom';

// Mock function to get job card data by id
function getJobCardById(id: string): JobCardBreakdownServiceAddForm {
  // Replace with actual data fetching logic
  return {
    rego: 'ABC123',
    vehicleClass: 'SUV',
    description: 'Breakdown at highway',
    breakdownDate: '2025-07-21',
    partsReplaced: '',
    repairTasksRequired: '',
    repairedBy: '',
    repairCompletionDate: '',
    notes: {
      initialInspection: false,
      batteryBoost: false,
      towing: '',
      engineFault: false,
      engineRepair: false,
      checkRadiator: false,
      checkGPS: false,
      roadTest: false,
    },
    notesComment: '',
  };
}

export function useJobcardBreakdownServiceEdit() {
  const navigate = useNavigate();
  const { id } = useParams<{ id: string }>();

  // Fetch job card data by id
  const initialData = id ? getJobCardById(id) : {
    rego: '',
    vehicleClass: '',
    description: '',
    breakdownDate: '',
    partsReplaced: '',
    repairTasksRequired: '',
    repairedBy: '',
    repairCompletionDate: '',
    notes: {
      initialInspection: false,
      batteryBoost: false,
      towing: '',
      engineFault: false,
      engineRepair: false,
      checkRadiator: false,
      checkGPS: false,
      roadTest: false,
    },
    notesComment: '',
  };

  const [form, setForm] = useState<JobCardBreakdownServiceAddForm>(initialData);

  const handleInputChange = (field: keyof JobCardBreakdownServiceAddForm, value: string) => {
    setForm(prev => ({ ...prev, [field]: value }));
  };

  const handleCheckboxChange = (note: keyof typeof form.notes) => {
    setForm(prev => ({
      ...prev,
      notes: { ...prev.notes, [note]: !prev.notes[note] }
    }));
  };

  const handleTowingChange = (value: string) => {
    setForm(prev => ({
      ...prev,
      notes: { ...prev.notes, towing: value }
    }));
  };

  const handleUpdate = (notesComment: string) => {
    // Save logic here (API or localStorage)
    setForm(prev => ({ ...prev, notesComment }));
    navigate('/mechanic/jobcard-breakdown-service');
  };

  const handleCancel = () => {
    navigate('/mechanic/jobcard-breakdown-service');
  };

  return {
    form,
    handleInputChange,
    handleCheckboxChange,
    handleTowingChange,
    handleUpdate,
    handleCancel
  };
}