import React from 'react';
import { Search, Edit, ChevronDown, ClipboardPenLineIcon, Car, Calendar, Clock, FileText, AlertCircle } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { useNavigate } from 'react-router-dom';
import { useChangeRequestHook } from './hook/useChangeRequestHook';
import { ChangeRequest, ChangeRequestPageProps } from './type/customer-type';

// Card Component for Mobile view
const ChangeRequestCard = ({ request, getStatusBadge, handleEditRequest }: { request: ChangeRequest; getStatusBadge: (status: string) => string; handleEditRequest: (request: ChangeRequest) => void }) => (
  <div className="border border-gray-200 p-4 mb-4 hover:shadow-lg transition-shadow">
    {/* Header with ID and Status */}
    <div className="flex justify-between items-start mb-4">
      <div>
        <span className="text-lg font-semibold text-blue-600">
          {request.id}
        </span>
      </div>
      <div className="flex flex-col gap-2">
        <span className={getStatusBadge(request.status)}>
          {request.status}
        </span>
      </div>
    </div>

    {/* Vehicle Information */}
    <div className="flex items-center mb-3">
      <div className="flex items-center mb-3">
        <span className="text-sm font-medium text-gray-700">{request.vehicle}</span>
      </div>
      <div className="text-xs text-gray-500 ml-24">
        Class: {request.vehicleClass}
      </div>
    </div>

    {/* Request Type */}
    <div className="mb-4">
      <div className="text-xs text-gray-500 uppercase font-medium mb-1">Request Type</div>
      <div className="flex items-center text-sm">
        <span>{request.requestType}</span>
      </div>
    </div>

    {/* Date and Time Information */}
    <div className="grid grid-cols-2 gap-4 mb-4">
      <div>
        <div className="text-xs text-gray-500 uppercase font-medium mb-1">Pickup</div>
        <div className="flex items-center text-sm">
          <span>{request.pickupDate}</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <span>{request.pickupTime}</span>
        </div>
      </div>
      <div>
        <div className="text-xs text-gray-500 uppercase font-medium mb-1">Return</div>
        <div className="flex items-center text-sm">
          <span>{request.returnDate}</span>
        </div>
        <div className="flex items-center text-sm text-gray-600">
          <span>{request.returnTime}</span>
        </div>
      </div>
    </div>

    {/* Note if exists */}
    {request.note && (
      <div className="mb-4 p-3 ">
        <div className="text-xs text-gray-500 uppercase font-medium mb-1">Note</div>
        <div className="text-sm text-gray-700 text-justify">{request.note}</div>
      </div>
    )}

    {/* Edit Button */}
    <div className="flex justify-end pt-3 border-t border-gray-100">
      <Button
        variant="ghost"
        size="sm"
        onClick={() => handleEditRequest(request)}
        className="text-gray-600 hover:text-gray-800"
      >
        <Edit className="w-4 h-4 mr-1" />
      </Button>
    </div>
  </div>
);

export function ChangeRequestPage({}: ChangeRequestPageProps) {
  const navigate = useNavigate();
  const {
    searchTerm,
    setSearchTerm,
    filterStatus,
    setFilterStatus,
    currentPage,
    setCurrentPage,
    recordsPerPage,
    setRecordsPerPage,
    mobileRequests,
    currentRequests,
    totalPages,
    getStatusBadge,
    handleEditRequest,
  } = useChangeRequestHook();

  // Define totalRecords explicitly since it wasn't passed from the hook
  const totalRecords = mobileRequests.length;

  return (
    <div className="p-4 md:p-6 min-h-screen ">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <div className="flex items-center space-x-2">
          <ClipboardPenLineIcon className="w-6 h-6 mr-3 text-gray-600" />
          <h1 className="text-xl md:text-2xl font-bold text-gray-600">Change Requests</h1>
        </div>
        <Button
          onClick={() => navigate('add-request')}
          className="px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors w-full sm:w-auto"
        >
          Add Request
        </Button>
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="In-Progress">In-Progress</SelectItem>
              <SelectItem value="Approved">Approved</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>

        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Mobile Card View (xs to sm) */}
      <div className="block md:hidden">
        {mobileRequests.length > 0 ? (
          <div className="space-y-4">
            {mobileRequests.map((request) => (
              <ChangeRequestCard
                key={request.id}
                request={request}
                getStatusBadge={getStatusBadge}
                handleEditRequest={handleEditRequest}
              />
            ))}
          </div>
        ) : (
          <div className="text-center py-8 text-gray-500">
            No change requests found matching your search criteria.
          </div>
        )}
      </div>

      {/* Desktop Table View (md and up) */}
      <div className="hidden md:block">
        <div className="rounded-md border bg-white">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead>Rental ID</TableHead>
                <TableHead>Vehicle</TableHead>
                <TableHead>Vehicle Class</TableHead>
                <TableHead>Pickup Date</TableHead>
                <TableHead>Return Date</TableHead>
                <TableHead>Request Type</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Action</TableHead>
              </TableRow>
            </TableHeader>

            <TableBody>
              {currentRequests.map((request) => (
                <TableRow key={request.id}>
                  <TableCell>{request.id}</TableCell>
                  <TableCell>{request.vehicle}</TableCell>
                  <TableCell>{request.vehicleClass}</TableCell>
                  <TableCell>
                    <div>
                      <div>{request.pickupDate}</div>
                      <div className="text-gray-500">{request.pickupTime}</div>
                    </div>
                  </TableCell>
                  <TableCell>
                    <div>
                      <div>{request.returnDate}</div>
                      <div className="text-gray-500">{request.returnTime}</div>
                    </div>
                  </TableCell>
                  <TableCell>{request.requestType}</TableCell>
                  <TableCell>
                    <span className={getStatusBadge(request.status)}>
                      {request.status}
                    </span>
                  </TableCell>
                  <TableCell>
                    <div className="flex items-center">
                      <Button
                        variant="ghost"
                        onClick={() => handleEditRequest(request)}
                        className="text-gray-600 hover:text-gray-800 transition-colors"
                        title="Edit Request"
                      >
                        <Edit className="w-4 h-4" />
                      </Button>
                    </div>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination - Only for desktop */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-6"
        />
      </div>
    </div>
  );
}