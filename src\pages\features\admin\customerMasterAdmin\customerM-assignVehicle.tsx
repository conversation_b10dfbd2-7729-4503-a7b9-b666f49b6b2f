import React, { useState } from 'react';
import { 
  ArrowL<PERSON>t, Eye, Upload, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { Label } from '@/components/ui/label';
import { Textarea } from '@/components/ui/textarea';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Dialog, DialogContent, DialogHeader, DialogTitle } from '@/components/ui/dialog';
import { useNavigate } from 'react-router-dom';

interface ImageModalProps {
  isOpen: boolean;
  onClose: () => void;
  imageSrc: string;
  imageTitle: string;
}

const ImageModal: React.FC<ImageModalProps> = ({ isOpen, onClose, imageSrc, imageTitle }) => {
  return (
    <Dialog open={isOpen} onOpenChange={onClose}>
      <DialogContent className="w-[90%] sm:w-[80%] md:max-w-2xl">
        <DialogHeader>
          <DialogTitle className="text-sm xs:text-base sm:text-lg md:text-xl">{imageTitle}</DialogTitle>
        </DialogHeader>
        <div className="flex justify-center p-2 xs:p-3 sm:p-4">
          <img 
            src={imageSrc} 
            alt={imageTitle} 
            className="max-w-full max-h-64 xs:max-h-80 sm:max-h-96 object-contain"
          />
        </div>
      </DialogContent>
    </Dialog>
  );
};

export function CustomerMAssignVehicle() {
  const navigate = useNavigate();
  const [selectedImage, setSelectedImage] = useState<{ src: string; title: string } | null>(null);
  const [formData, setFormData] = useState({
    rentalId: '90001',
    vehicle: 'Nissan Micra - IAF1AW',
    vehicleClass: 'Economy Plus',
    pickupLocation: 'Somerton',
    pickupDate: '28/05/2025',
    pickupTime: '9:16 AM',
    returnDate: '29/05/2025',
    returnTime: '9:16 AM',
    odometerAtPickup: '12345',
    includedDistance: '250km',
    fuelLevelAtPickup: '8',
    fuelSameAsPickup: 'yes',
    fuelLevelAtReturn: '',
    comment: 'There is a two-inch scratch on the bumper area of the vehicle.',
    paymentDue: '',
    excessCoverObtained: 'yes'
  });

  const handleImageView = (imageSrc: string, title: string) => {
    setSelectedImage({ src: imageSrc, title });
  };

  const closeModal = () => {
    setSelectedImage(null);
  };

  const imageCategories = [
    { title: 'Interior Images', items: ['image.jpg'] },
    { title: 'Exterior Images', items: ['image.jpg'] },
    { title: 'Left Side Doors', items: ['image.jpg'] },
    { title: 'Right Side Doors', items: ['image.jpg'] },
    { title: 'Front Side Images', items: ['image.jpg'] },
    { title: 'Back Side Images', items: ['image.jpg'] },
    { title: 'Side Mirrors', items: ['image.jpg'] },
    { title: 'Other', items: ['image.jpg'] }
  ];

  const handleGoBack = (rentalId: string): void => {
    const cleanId = rentalId.replace('#', '');
    navigate(`/admin/customerMasterAdmin/customerM-bookingSummery/${cleanId}`);
  };

  const handleCancel = (rentalId: string): void => {
    const cleanId = rentalId.replace('#', '');
    navigate(`/admin/customerMasterAdmin/customerM-bookingSummery/${cleanId}`);
  };

  const handleSubmit = (rentalId: string): void => {
    const cleanId = rentalId.replace('#', '');
    navigate(`/admin/customerMasterAdmin/customerM-bookingSummery/${cleanId}`);
  };

  return (
    <div className="min-h-screen px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
      {/* Main Content */}
      <div className="flex-1 py-3 xs:py-4 sm:py-6">
        {/* Top Navigation */}
        <div className="max-w-7xl mx-auto px-2 xs:px-3 sm:px-4 md:px-6 lg:px-8">
          <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between py-3 xs:py-4 sm:py-6">
            <div className="flex items-center mb-2 sm:mb-0">
              <Button
                className="bg-[#330101] text-white hover:bg-[#ffde5c] px-2 xs:px-3 sm:px-4 py-1 xs:py-2 text-xs xs:text-sm sm:text-base flex items-center"
                size="sm"
                onClick={() => handleGoBack(formData.rentalId)}
              >
                <ArrowLeft className="h-3 xs:h-4 w-3 xs:w-4 mr-1 xs:mr-2" />
                <span className="hidden sm:inline">Go Back</span>
              </Button>
            </div>
            <div className="flex flex-wrap items-center gap-1 xs:gap-2 sm:gap-3">
              <Button
                size="sm"
                className="text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
              >
                Assign Vehicle
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white hover:text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
                onClick={() => navigate('/admin/customerMasterAdmin/customerM-returnVehicle')}
              >
                Return Vehicle
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
              >
                Agreement
                <ChevronDown className="h-3 xs:h-4 w-3 xs:w-4 ml-1 xs:ml-2" />
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
              >
                Receipt
                <ChevronDown className="h-3 xs:h-4 w-3 xs:w-4 ml-1 xs:ml-2" />
              </Button>
              <Button
                size="sm"
                className="bg-[#330101] text-white text-xs xs:text-sm sm:text-base px-2 xs:px-3 sm:px-4 py-1 xs:py-2"
              >
                <ChevronDown className="h-3 xs:h-4 w-3 xs:w-4" />
              </Button>
            </div>
          </div>
        </div>

        {/* Form Content */}
        <div className="max-w-7xl mx-auto space-y-4 xs:space-y-5 sm:space-y-6 mt-4 xs:mt-5 sm:mt-6">
          {/* Basic Information */}
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="rentalId" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Rental ID</Label>
              <Input
                id="rentalId"
                value={formData.rentalId}
                onChange={(e) => setFormData({...formData, rentalId: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
            <div className="relative">
              <Label htmlFor="vehicle" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Vehicle</Label>
              <Select value={formData.vehicle}>
                <SelectTrigger className="h-9 xs:h-10 sm:h-12 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3 focus:outline-none focus:ring-0 focus:border-none">
                  <SelectValue placeholder="Select vehicle" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Nissan Micra - IAF1AW" className="text-xs xs:text-sm sm:text-base">Nissan Micra - IAF1AW</SelectItem>
                  <SelectItem value="PCR 455" className="text-xs xs:text-sm sm:text-base">PCR 455</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label htmlFor="vehicleClass" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Vehicle Class</Label>
              <Input
                id="vehicleClass"
                value={formData.vehicleClass}
                onChange={(e) => setFormData({...formData, vehicleClass: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="pickupLocation" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Pickup Location</Label>
              <Input
                id="pickupLocation"
                value={formData.pickupLocation}
                onChange={(e) => setFormData({...formData, pickupLocation: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
            <div className="relative">
              <Label htmlFor="pickupDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Pickup Date</Label>
              <div className="flex space-x-1 xs:space-x-2 sm:space-x-3">
                <Input
                  placeholder="DD"
                  value="28"
                  className="w-12 xs:w-14 sm:w-16 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="MM"
                  value="05"
                  className="w-12 xs:w-14 sm:w-16 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="YYYY"
                  value="2025"
                  className="w-16 xs:w-20 sm:w-24 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="Time"
                  value="9:16 AM"
                  className="flex-1 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                />
              </div>
            </div>
            <div className="relative">
              <Label htmlFor="pickupLocation" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Return Location</Label>
              <Input
                id="pickupLocation"
                value={formData.pickupLocation}
                onChange={(e) => setFormData({...formData, pickupLocation: e.target.value})}
                className="bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                readOnly
              />
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 gap-2 xs:gap-3 sm:gap-4">
            <div className="relative">
              <Label htmlFor="returnDate" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Return Date</Label>
              <div className="flex space-x-1 xs:space-x-2 sm:space-x-3">
                <Input
                  placeholder="DD"
                  value="29"
                  className="w-12 xs:w-14 sm:w-16 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="MM"
                  value="05"
                  className="w-12 xs:w-14 sm:w-16 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="YYYY"
                  value="2025"
                  className="w-16 xs:w-20 sm:w-24 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
                <Input
                  placeholder="Time"
                  value="9:16 AM"
                  className="flex-1 bg-gray-100 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  readOnly
                />
              </div>
            </div>
            <div className="relative">
              <Label htmlFor="odometerAtPickup" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Odometer at Pickup</Label>
              <Input
                id="odometerAtPickup"
                value={formData.odometerAtPickup}
                onChange={(e) => setFormData({...formData, odometerAtPickup: e.target.value})}
                className="text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
              />
            </div>
            <div className="relative">
              <Label htmlFor="fuelLevelAtPickup" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Fuel Level at Pickup</Label>
              <Input
                id="fuelLevelAtPickup"
                value={formData.fuelLevelAtPickup}
                onChange={(e) => setFormData({...formData, fuelLevelAtPickup: e.target.value})}
                className="text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
              />
            </div>
          </div>

          {/* Proof Images */}
          <div>
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Proof Images</h3>
              <div className="flex space-x-2 xs:space-x-3 sm:space-x-4">
                <div className="w-20 xs:w-24 sm:w-28 md:w-32 h-20 xs:h-24 sm:h-28 md:h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  <img src="/src/assets/odometre.png" alt="Speedometer" className="w-full h-full object-cover rounded" />
                </div>
                <div className="w-20 xs:w-24 sm:w-28 md:w-32 h-20 xs:h-24 sm:h-28 md:h-32 border-2 border-dashed border-gray-300 rounded-lg flex items-center justify-center">
                  <div
                    className="text-center cursor-pointer"
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.accept = 'image/*';
                      input.onchange = (event: Event) => {
                        const target = event.target as HTMLInputElement;
                        const file = target.files?.[0];
                        if (file) {
                          console.log('File selected:', file);
                        }
                      };
                      input.click();
                    }}
                  >
                    <Upload className="h-6 xs:h-7 sm:h-8 w-6 xs:w-7 sm:w-8 mx-auto mb-1 xs:mb-2 text-gray-400" />
                    <span className="text-[10px] xs:text-xs sm:text-sm text-gray-500">Upload Image</span>
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Images of the Vehicle */}
          <div>
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Images of the Vehicle</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-2 xs:gap-3 sm:gap-4 md:gap-6">
                {imageCategories.map((category, index) => (
                  <div key={index} className="relative">
                    <Label className="text-[10px] xs:text-xs sm:text-sm md:text-base font-medium text-gray-700 absolute left-2 xs:left-3 -top-1 xs:-top-1.5 sm:-top-2 bg-white px-1">{category.title}</Label>
                    <div className="mt-4 xs:mt-5 sm:mt-6 space-y-1 xs:space-y-2">
                      {category.items.map((item, itemIndex) => (
                        <div key={itemIndex} className="flex items-center justify-between p-1 xs:p-2 sm:p-3 border rounded">
                          <span className="text-[10px] xs:text-xs sm:text-sm">{item}</span>
                          <Button 
                            variant="ghost" 
                            size="sm"
                            onClick={() => handleImageView('/api/placeholder/400/300', `${category.title} - ${item}`)}
                          >
                            <Eye className="h-3 xs:h-4 w-3 xs:w-4" />
                          </Button>
                        </div>
                      ))}
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Vehicle Conditions */}
          <div>
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Vehicle Conditions</h3>
              <div className="relative mb-3 xs:mb-4 sm:mb-6 w-full sm:w-1/2 md:w-1/3">
                <Label htmlFor="reportType" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Report Type</Label>
                <Select>
                  <SelectTrigger className="h-9 xs:h-10 sm:h-12 text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3">
                    <SelectValue placeholder="Select" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="damage" className="text-xs xs:text-sm sm:text-base">Damage Report</SelectItem>
                    <SelectItem value="maintenance" className="text-xs xs:text-sm sm:text-base">Maintenance Report</SelectItem>
                  </SelectContent>
                </Select>
              </div>
              <div className="relative mt-4 xs:mt-5 sm:mt-6">
                <Label htmlFor="comment" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Comment</Label>
                <Textarea
                  id="comment"
                  value={formData.comment}
                  onChange={(e) => setFormData({...formData, comment: e.target.value})}
                  rows={4}
                  className="text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                />
              </div>
            </div>
          </div>

          {/* Payment & Insurance */}
          <div>
            <div className="p-2 xs:p-3 sm:p-4 md:p-6">
              <h3 className="text-sm xs:text-base sm:text-lg md:text-xl font-semibold mb-2 xs:mb-3 sm:mb-4">Payment & Insurance</h3>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-2 xs:gap-3 sm:gap-4">
                <div className="relative">
                  <Label htmlFor="paymentDue" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Payment Due</Label>
                  <Input
                    id="paymentDue"
                    value={formData.paymentDue}
                    onChange={(e) => setFormData({...formData, paymentDue: e.target.value})}
                    className="text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  />
                </div>
                <div className="relative">
                  <Label htmlFor="excessCover" className="absolute left-2 xs:left-3 -top-2 xs:-top-2.5 bg-white px-1 text-[10px] xs:text-xs sm:text-sm text-gray-600">Excess Cover Obtained?</Label>
                  <Input
                    id="excessCover"
                    value={formData.excessCoverObtained}
                    onChange={(e) => setFormData({...formData, excessCoverObtained: e.target.value})}
                    className="text-xs xs:text-sm sm:text-base p-1 xs:p-2 sm:p-3"
                  />
                </div>
              </div>
            </div>
          </div>

          {/* Action Buttons */}
          <div className="flex justify-end gap-2 xs:gap-3 sm:gap-4 mt-4 xs:mt-5 sm:mt-6">
            <Button
              variant="outline"
              onClick={() => handleCancel(formData.rentalId)}
              className="px-4 xs:px-6 sm:px-8 py-1 xs:py-2 text-xs xs:text-sm sm:text-base"
            >
              Cancel
            </Button>
            <Button
              onClick={() => handleSubmit(formData.rentalId)}
              className="px-4 xs:px-5 sm:px-6 py-1 xs:py-2 bg-[#330101] text-white hover:bg-[#660404] text-xs xs:text-sm sm:text-base"
            >
              Submit
            </Button>
          </div>
        </div>
      </div>

      {/* Image Modal */}
      {selectedImage && (
        <ImageModal
          isOpen={!!selectedImage}
          onClose={closeModal}
          imageSrc={selectedImage.src}
          imageTitle={selectedImage.title}
        />
      )}
    </div>
  );
}