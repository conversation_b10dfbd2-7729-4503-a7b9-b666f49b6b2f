import React from 'react';
import { ArrowLeft, Car, Shield, Baby, FileText, Phone, Calendar, MapPin, CreditCard, Clock, User, ChevronDown } from 'lucide-react';
import { Button } from '@/components/ui/button';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { useParams, useNavigate } from 'react-router-dom';
import { Textarea } from '@/components/ui/textarea';
import { Label } from '@/components/ui/label';
import { Table, TableBody, TableHeader, TableRow } from '@/components/ui/table';
import { DropdownMenu, DropdownMenuContent, DropdownMenuTrigger, DropdownMenuItem } from '@/components/ui/dropdown-menu';
import economyCar from '@/assets/economyCar.png';
import { useBookingSummary } from './hook/useBookingSummery';
import { BookingSummaryProps } from './type/customer-type';

export default function BookingSummaryPage({}: BookingSummaryProps) {
  const { bookingId } = useParams<{ bookingId: string }>();
  const {
    navigate,
    booking,
    invoiceItems,
    handlePrintAgreement,
    handleEmailAgreement,
    handlePrintReceipt,
    handleEmailReceipt,
    bookingData,
  } = useBookingSummary();

  if (!booking) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold text-gray-900 mb-4">Booking Not Found</h1>
          <Button
            onClick={() => navigate('/booking-history')}
          >
            <ArrowLeft className="h-4 w-4 mr-2" />
            Back to Booking History
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen">
      <div className="bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Button
                className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
                size="sm"
                onClick={() => navigate('/booking-history')}
              >
                <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
                <span className="hidden md:inline">Go Back</span>
              </Button>
            </div>
            <div className="flex items-center space-x-2">
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="hover:bg-[#330101] hover:text-white focus:outline-none focus:ring-0 focus:border-none"
                  >
                    Agreement
                    <ChevronDown className="w-3 h-3 text-gray-500 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handlePrintAgreement}>
                    Print Agreement
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEmailAgreement}>
                    Email Agreement
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <Button
                    variant="outline"
                    size="sm"
                    className="hover:bg-[#330101] hover:text-white focus:outline-none focus:ring-0 focus:border-none"
                  >
                    Receipt
                    <ChevronDown className="w-3 h-3 text-gray-500 ml-2" />
                  </Button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align="end">
                  <DropdownMenuItem onClick={handlePrintReceipt}>
                    Print Receipt
                  </DropdownMenuItem>
                  <DropdownMenuItem onClick={handleEmailReceipt}>
                    Email Receipt
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
              <Button
                size="sm"
                className="hover:bg-[#330101] hover:text-white"
                onClick={() => navigate('/search/receipt-invoice')}
              >
                Invoice
              </Button>
            </div>
          </div>
        </div>
      </div>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6">
        <div className="text-right mb-4">
          <span className="text-base text-gray-600">Outstanding Balance: </span>
          <span className="font-semibold text-lg">{bookingData.outstandingBalance}</span>
        </div>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <Calendar className="h-5 w-5 mr-2" />
              Date & Time
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-4 gap-4 mb-4">
              <div>
                <Label className="text-gray-700">Pickup Date</Label>
                <p className="text-sm">{bookingData.pickupDate}</p>
              </div>
              <div>
                <Label className="text-gray-700">Pickup Time</Label>
                <p className="text-sm">{bookingData.pickupTime}</p>
              </div>
              <div>
                <Label className="text-gray-700">Return Date</Label>
                <p className="text-sm">{bookingData.returnDate}</p>
              </div>
              <div>
                <Label className="text-gray-700">Return Time</Label>
                <p className="text-sm">{bookingData.returnTime}</p>
              </div>
            </div>
            <div className="grid grid-cols-4 gap-4">
              <div>
                <Label className="text-gray-700">Pickup Location</Label>
                <p className="text-sm">{bookingData.pickupLocation}</p>
              </div>
              <div>
                <Label className="text-gray-700">Return Location</Label>
                <p className="text-sm">{bookingData.returnLocation}</p>
              </div>
              <div>
                <Label className="text-gray-700">Branch</Label>
                <p className="text-sm">{bookingData.branch}</p>
              </div>
              <div>
                <Label className="text-gray-700">Reservation Type</Label>
                <p className="text-sm">{bookingData.reservationType}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="mb-6 relative">
          <CardHeader className="relative">
            <CardTitle className="flex items-center text-black">
              <Car className="h-5 w-5 mr-2" />
              Selected Vehicle Class
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="flex flex-col md:flex-row md:items-start md:space-x-4 space-y-4 md:space-y-0">
              <div className="w-full md:w-32 h-auto md:h-24 flex items-center justify-center">
                <img
                  src={economyCar}
                  alt="Vehicle Class"
                  className="object-contain w-full h-auto max-h-24"
                />
              </div>
              <div className="flex-1">
                <h3 className="font-semibold text-lg mb-2">{bookingData.vehicle.class}</h3>
                <div className="grid grid-cols-2 gap-4 text-sm">
                  <div>
                    {bookingData.vehicle.features.map((feature, index) => (
                      <p key={index} className="text-gray-600">{feature}</p>
                    ))}
                  </div>
                  <div>
                    <p className="text-gray-600">{bookingData.vehicle.doors}</p>
                  </div>
                </div>
              </div>
              <div className="text-right">
                <div
                  className={`absolute top-0 right-0 w-auto px-2 xs:px-2 sm:px-3 md:px-3 lg:px-4 xl:px-4 py-1 text-xs xs:text-xs sm:text-sm md:text-sm lg:text-base xl:text-base font-medium ${
                    booking.loanVehicle === 'Yes' ? 'bg-green-600' : 'bg-red-600 hover:bg-red-700'
                  } text-white`}
                >
                  {booking.loanVehicle === 'Yes' ? 'Loan Vehicle' : 'No Loan Vehicle'}
                </div>
                <div className="text-lg font-bold">{bookingData.vehicle.price}</div>
                <p className="text-xs text-gray-600 max-w-40">{bookingData.vehicle.priceNote}</p>
                <div className="mt-2">
                  <span className="text-sm text-gray-600">Vehicle: </span>
                  <span className="font-medium">{bookingData.vehicle.vehicleId}</span>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <Shield className="h-5 w-5 mr-2" />
              Protection and Coverages
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {bookingData.protections.map((protection, index) => (
                <div key={index} className="flex justify-between items-center py-2 border-b last:border-b-0">
                  <span className="text-sm">{protection.name}</span>
                  <span className="text-sm font-medium">{protection.price}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <Baby className="h-5 w-5 mr-2" />
              Equipment & Services
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-3">
              {bookingData.equipment.map((item, index) => (
                <div key={index} className="flex justify-between items-center py-2">
                  <span className="text-sm">{item.name}</span>
                  <span className="text-sm font-medium text-green-600">{item.price}</span>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <User className="h-5 w-5 mr-2" />
              Customer Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="space-y-4">
              <div>
                <Label className="text-gray-700">Cooperate Customer</Label>
                <p className="text-sm">Yes</p>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-gray-700">First Name</Label>
                  <p className="text-sm">{bookingData.customer.firstName}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Last Name</Label>
                  <p className="text-sm">{bookingData.customer.lastName}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-gray-700">Email Address</Label>
                  <p className="text-sm">{bookingData.customer.email}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Phone Number</Label>
                  <p className="text-sm">{bookingData.customer.phone}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-gray-700">Address</Label>
                  <p className="text-sm">{bookingData.customer.address}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Postcode</Label>
                  <p className="text-sm">{bookingData.customer.postcode}</p>
                </div>
              </div>
              <div className="grid grid-cols-2 gap-4">
                <div>
                  <Label className="text-gray-700">Country</Label>
                  <p className="text-sm">{bookingData.customer.country}</p>
                </div>
                <div>
                  <Label className="text-gray-700">Birthday</Label>
                  <p className="text-sm">{bookingData.customer.birthday}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <FileText className="h-5 w-5 mr-2" />
              Extra Information
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="mb-4">
              <h4 className="font-medium mb-2">Driving License</h4>
              <div className="p-3 rounded">
                <div className="grid grid-cols-4 gap-4 text-sm">
                  <div>
                    <Label className="text-gray-700">Type</Label>
                    <p>{bookingData.license.type}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700">ID Number</Label>
                    <p>{bookingData.license.idNumber}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700">Issue Date</Label>
                    <p>{bookingData.license.issueDate}</p>
                  </div>
                  <div>
                    <Label className="text-gray-700">Expire Date</Label>
                    <p>{bookingData.license.expireDate}</p>
                  </div>
                </div>
                <div className="mt-2">
                  <Label className="text-gray-700">Issue Country</Label>
                  <p className="text-sm">{bookingData.license.country}</p>
                </div>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <Phone className="h-5 w-5 mr-2" />
              Emergency Contact Details
            </CardTitle>
          </CardHeader>
          <CardContent>
            <div className="grid grid-cols-2 gap-4">
              <div>
                <Label className="text-gray-700">Emergency Contact Person Name</Label>
                <p className="text-sm">{bookingData.emergency.name}</p>
              </div>
              <div>
                <Label className="text-gray-700">Emergency Contact Number</Label>
                <p className="text-sm">{bookingData.emergency.number}</p>
              </div>
            </div>
          </CardContent>
        </Card>
        <Card className="mb-6">
          <CardHeader>
            <CardTitle className="flex items-center text-black">Note</CardTitle>
          </CardHeader>
          <CardContent>
            <Textarea
              placeholder="..."
              className="focus:outline-none focus:ring-0 focus:border-none border-gray-500"
              disabled
            />
          </CardContent>
        </Card>
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center text-black">
              <CreditCard className="h-5 w-5 mr-2" />
              Summary
            </CardTitle>
          </CardHeader>
          <div className="p-2 sm:p-4 mb-4 sm:mb-6 md:mb-8">
            <Table className="w-full">
              <TableHeader>
                <tr>
                  <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2">DESCRIPTION</th>
                  <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2 hidden sm:table-cell"></th>
                  <th className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2 hidden md:table-cell"></th>
                  <th className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base font-bold py-1 sm:py-2">AMOUNT (AUD)</th>
                </tr>
              </TableHeader>
              <TableBody>
                {invoiceItems.map((item, index) => (
                  <TableRow key={index} className="border-b border-gray-200">
                    <td className="text-[10px] sm:text-xs md:text-sm lg:text-base py-1 sm:py-2">
                      <div>{item.description}</div>
                      {(item.rate || item.days) && (
                        <div className="text-[9px] sm:text-[10px] md:hidden">
                          <span>{item.rate} {item.days}</span>
                        </div>
                      )}
                    </td>
                    <td className="text-left text-[9px] sm:text-[10px] md:text-xs lg:text-sm py-1 sm:py-2 hidden sm:table-cell">
                      {item.rate && <div>{item.rate}</div>}
                    </td>
                    <td className="text-left text-[9px] sm:text-[10px] md:text-xs lg:text-sm py-1 sm:py-2 hidden md:table-cell">
                      {item.days && <div>{item.days}</div>}
                    </td>
                    <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 sm:py-2">{item.amount}</td>
                  </TableRow>
                ))}
                <br />
                <TableRow className="hidden md:table-row">
                  <td className="py-1 sm:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Sub Total</td>
                  <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">588.18</td>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <td className="py-1 sm:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Total GST 10%</td>
                  <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">8.82</td>
                </TableRow>
                <TableRow className="border-b border-gray-400 hidden md:table-row">
                  <td className="py-1 sm:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Discount</td>
                  <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">3.00</td>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <td className="py-1 sm:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Invoice Total AUD</td>
                  <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">594.00</td>
                </TableRow>
                <TableRow className="border-b border-gray-400 hidden md:table-row">
                  <td className="py-1 sm:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-semibold hidden md:table-cell">Security Deposit</td>
                  <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 hidden md:table-cell">100.00</td>
                </TableRow>
                <TableRow className="hidden md:table-row">
                  <td className="py-1 sm:py-2" colSpan={2}></td>
                  <td className="text-left text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-bold hidden md:table-cell">Amount Due AUD</td>
                  <td className="text-right text-[10px] sm:text-xs md:text-sm lg:text-base py-1 font-bold hidden md:table-cell">494.00</td>
                </TableRow>
                <br />
                <TableRow className="md:hidden">
                  <td className="py-1 sm:py-2" colSpan={4}>
                    <div className="text-[10px] sm:text-xs font-semibold">Sub Total: 588.18</div>
                    <div className="text-[10px] sm:text-xs font-semibold">Total GST 10%: 8.82</div>
                    <div className="text-[10px] sm:text-xs font-semibold">Discount: 3.00</div>
                    <div className="text-[10px] sm:text-xs font-semibold">Invoice Total AUD: 594.00</div>
                    <div className="text-[10px] sm:text-xs font-semibold">Security Deposit: 100.00</div>
                    <div className="text-[10px] sm:text-xs font-bold">Amount Due AUD: 494.00</div>
                  </td>
                </TableRow>
              </TableBody>
            </Table>
          </div>
        </Card>
      </div>
    </div>
  );
}