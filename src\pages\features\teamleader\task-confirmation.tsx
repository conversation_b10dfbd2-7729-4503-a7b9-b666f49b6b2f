import { useNavigate } from 'react-router-dom'
import { But<PERSON> } from '../../../components/ui/button'
import { Card, CardContent, CardHeader, CardTitle } from '../../../components/ui/card'
import { CheckCircle, ArrowLeft } from 'lucide-react'

export function TaskConfirmationPage() {
  const navigate = useNavigate()

  const handleBackToTasks = () => {
    navigate('/teamleader/taskoverview')
  }

  const handleBackToDashboard = () => {
    navigate('/teamleader/teamleader-dashboard')
  }

  return (
    <div className="min-h-screen">
      <div className="p-6">
        <div className="flex justify-between items-center mb-6">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={handleBackToTasks}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Back to Tasks</span>
          </Button>
        </div>

        <div className="flex flex-col items-center justify-center min-h-[60vh]">
          <Card className="shadow-sm max-w-md w-full">
            <CardHeader className="text-center">
              <div className="flex justify-center mb-4">
                <CheckCircle className="w-16 h-16 text-green-500" />
              </div>
              <CardTitle className="text-2xl font-semibold text-gray-900">
                Vehicle Approved Successfully!
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-gray-600 mb-8">
                The vehicle has been approved and marked as rentable successfully. 
                All necessary notifications have been sent.
              </p>
              
              <div className="space-y-4">
                <Button
                  onClick={handleBackToTasks}
                  className="w-full bg-[#330101] hover:bg-[#ffde5c] text-white px-6 py-3 text-lg font-medium"
                >
                  Back to Task Overview
                </Button>
                
                <Button
                  onClick={handleBackToDashboard}
                  variant="outline"
                  className="w-full px-6 py-3 text-lg font-medium"
                >
                  Go to Dashboard
                </Button>
              </div>
            </CardContent>
          </Card>
        </div>
      </div>
    </div>
  )
}
