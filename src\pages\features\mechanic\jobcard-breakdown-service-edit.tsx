import { Button } from '../../../components/ui/button';
import { Input } from '../../../components/ui/input';
import { Label } from '../../../components/ui/label';
import { Card } from '../../../components/ui/card';
import { Textarea } from '../../../components/ui/textarea';
import { ArrowLeft, AlertTriangle, ChevronDown } from 'lucide-react';
import { useJobcardBreakdownServiceEdit } from './hook/usejobcard-breakdown-service-edit';
import { useState } from 'react';
import { useNavigate } from 'react-router-dom';

// Example vehicle data (replace with your actual data source)
const vehicles = [
  { rego: 'ABC123', vehicleClass: 'Truck' },
  { rego: 'XYZ789', vehicleClass: 'Van' },
  { rego: 'LMN456', vehicleClass: 'SUV' },
];

export function JobcardBreakdownServiceEdit() {
  const {
    form,
    handleInputChange,
    handleCheckboxChange,
    handleTowingChange,
    handleUpdate,
    handleCancel
  } = useJobcardBreakdownServiceEdit();
  const navigate = useNavigate();

  const [notesComment, setNotesComment] = useState(form.notesComment || '');

  // Find the selected vehicle object
  const selectedVehicle = vehicles.find(v => v.rego === form.rego);

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Back Button - Mobile only */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] flex items-center justify-center"
          size="sm"
          onClick={handleCancel}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>
      {/* Header - Desktop only */}
      <div className="hidden md:flex items-center justify-between mb-6">
        <div className="flex items-center">
          <Button
            className="bg-[#330101] text-white hover:bg-[#ffde5c] px-3 py-2 text-xs sm:text-sm md:text-base flex items-center"
            size="sm"
            onClick={handleCancel}
          >
            <ArrowLeft className="h-4 w-4 mr-0 sm:mr-1" />
            <span className="hidden md:inline">Go Back</span>
          </Button>
        </div>
      </div>

      <form onSubmit={e => { e.preventDefault(); handleUpdate(notesComment); }}>
        {/* Vehicle Details */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Vehicle Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
              {/* Vehicle Dropdown */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle</span>
                <select
                  id="rego"
                  value={form.rego}
                  onChange={e => {
                    const rego = e.target.value;
                    const vehicle = vehicles.find(v => v.rego === rego);
                    handleInputChange('rego', rego);
                    handleInputChange('vehicleClass', vehicle ? vehicle.vehicleClass : '');
                  }}
                  className="w-full border border-gray-300 rounded px-2 py-2 pr-8 text-black text-sm bg-white focus:outline-none appearance-none mt-2"
                  required
                >
                  <option value="">Select Vehicle</option>
                  {vehicles.map(vehicle => (
                    <option key={vehicle.rego} value={vehicle.rego}>
                      {vehicle.rego}
                    </option>
                  ))}
                </select>
                <ChevronDown className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              </div>
              {/* Vehicle Class */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle Class</span>
                <Input
                  id="vehicleClass"
                  value={form.vehicleClass}
                  readOnly
                  className="text-sm h-8 bg-gray-100 mt-2"
                />
              </div>
              {/* Description */}
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</span>
                <Textarea
                  id="description"
                  value={form.description || ''}
                  className="text-sm min-h-[40px] resize-none border border-gray-300 mt-2"
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View (unchanged) */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            {/* Vehicle Dropdown */}
            <div className="relative">
              <select
                id="rego"
                value={form.rego}
                onChange={e => {
                  const rego = e.target.value;
                  const vehicle = vehicles.find(v => v.rego === rego);
                  handleInputChange('rego', rego);
                  handleInputChange('vehicleClass', vehicle ? vehicle.vehicleClass : '');
                }}
                className="w-full border border-gray-300 rounded px-2 py-2 pr-8 text-black text-sm bg-white focus:outline-none appearance-none"
                required
              >
                <option value="">Select Vehicle</option>
                {vehicles.map(vehicle => (
                  <option key={vehicle.rego} value={vehicle.rego}>
                    {vehicle.rego}
                  </option>
                ))}
              </select>
              <ChevronDown className="pointer-events-none absolute right-2 top-1/2 -translate-y-1/2 w-4 h-4 text-gray-400" />
              <Label htmlFor="rego" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Vehicle
              </Label>
            </div>
            {/* Vehicle Class (auto-selected, read-only) */}
            <div className="relative">
              <Input
                id="vehicleClass"
                value={form.vehicleClass}
                readOnly
                className="text-sm h-8 bg-gray-100"
              />
              <Label htmlFor="vehicleClass" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Vehicle Class
              </Label>
            </div>
          </div>
          <div className="hidden md:block relative">
            <Textarea
              id="description"
              value={form.description || ''}
            
              className="text-sm min-h-[40px] resize-none border border-gray-300 "
            />
            <Label htmlFor="description" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
              Description
            </Label>
          </div>
        </div>

        {/* Incident Details */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Incident Details</h2>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-4">
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Parts Replaced</span>
                <Input
                  id="partsReplaced"
                  value={form.partsReplaced || ''}
                  onChange={e => handleInputChange('partsReplaced', e.target.value)}
                  className="text-sm h-8 w-full mt-2"
                />
              </div>
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repair Tasks Required</span>
                <Input
                  id="repairTasksRequired"
                  value={form.repairTasksRequired || ''}
                  onChange={e => handleInputChange('repairTasksRequired', e.target.value)}
                  className="text-sm h-8 w-full mt-2"
                />
              </div>
              <div className="relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Repaired By</span>
                <Input
                  id="repairedBy"
                  value={form.repairedBy}
                  onChange={e => handleInputChange('repairedBy', e.target.value)}
                  className="text-sm h-8 w-full mt-2"
                  required
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View (unchanged) */}
          <div className="hidden md:grid grid-cols-1 md:grid-cols-3 gap-4 mb-4">
            <div className="relative">
              <Input
                id="partsReplaced"
                value={form.partsReplaced || ''}
                onChange={e => handleInputChange('partsReplaced', e.target.value)}
                className="text-sm h-8 w-full"
              />
              <Label htmlFor="partsReplaced" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Parts Replaced
              </Label>
            </div>
            <div className="relative">
              <Input
                id="repairTasksRequired"
                value={form.repairTasksRequired || ''}
                onChange={e => handleInputChange('repairTasksRequired', e.target.value)}
                className="text-sm h-8 w-full"
              />
              <Label htmlFor="repairTasksRequired" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Repair Tasks Required
              </Label>
            </div>
          </div>
          <div className="hidden md:grid grid-cols-1 md:grid-cols-2 gap-4">
            <div className="relative">
              <Input
                id="repairedBy"
                value={form.repairedBy}
                onChange={e => handleInputChange('repairedBy', e.target.value)}
                className="text-sm h-8 w-full"
                required
              />
              <Label htmlFor="repairedBy" className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Repaired By
              </Label>
            </div>
          </div>
        </div>

        {/* Job Card Notes */}
        <div className="mb-8">
          <h2 className="text-lg font-bold mb-4">Job Card Notes</h2>
          <div className="font-semibold mb-2">The Service Requires;</div>
          {/* Mobile Card View */}
          <div className="block md:hidden space-y-4">
            <div className="bg-white rounded-lg shadow-sm p-4 space-y-3">
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.initialInspection} onChange={() => handleCheckboxChange('initialInspection')} />
                Initial inspection at breakdown site
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.batteryBoost} onChange={() => handleCheckboxChange('batteryBoost')} />
                Battery boost/replacement
              </label>
              <div className="flex flex-wrap items-center gap-2">
                Towing service -
                <label className="flex items-center gap-1">
                  <input type="checkbox" name="towing" checked={form.notes.towing === 'Required'} onChange={() => handleTowingChange('Required')} />
                  Required
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" name="towing" checked={form.notes.towing === 'Not Required'} onChange={() => handleTowingChange('Not Required')} />
                  Not Required
                </label>
              </div>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.engineFault} onChange={() => handleCheckboxChange('engineFault')} />
                Diagnose engine fault
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.engineRepair} onChange={() => handleCheckboxChange('engineRepair')} />
                Engine component repair/replacement
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.checkRadiator} onChange={() => handleCheckboxChange('checkRadiator')} />
                Check radiator and coolant system
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.checkGPS} onChange={() => handleCheckboxChange('checkGPS')} />
                Check GPS
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.roadTest} onChange={() => handleCheckboxChange('roadTest')} />
                Road test
              </label>
              <div className="mt-4 relative">
                <span className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</span>
                <Textarea
                  id="notesComment"
                  placeholder="Type Here ---"
                  value={notesComment}
                  onChange={e => setNotesComment(e.target.value)}
                  className="text-sm min-h-8"
                />
              </div>
            </div>
          </div>
          {/* Desktop/Grid View (unchanged) */}
          <div className="hidden md:block">
            <div className="space-y-3 mb-4">
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.initialInspection} onChange={() => handleCheckboxChange('initialInspection')} />
                Initial inspection at breakdown site
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.batteryBoost} onChange={() => handleCheckboxChange('batteryBoost')} />
                Battery boost/replacement
              </label>
              <div className="flex flex-wrap items-center gap-2">
                Towing service -
                <label className="flex items-center gap-1">
                  <input type="checkbox" name="towing" checked={form.notes.towing === 'Required'} onChange={() => handleTowingChange('Required')} />
                  Required
                </label>
                <label className="flex items-center gap-1">
                  <input type="checkbox" name="towing" checked={form.notes.towing === 'Not Required'} onChange={() => handleTowingChange('Not Required')} />
                  Not Required
                </label>
              </div>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.engineFault} onChange={() => handleCheckboxChange('engineFault')} />
                Diagnose engine fault
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.engineRepair} onChange={() => handleCheckboxChange('engineRepair')} />
                Engine component repair/replacement
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.checkRadiator} onChange={() => handleCheckboxChange('checkRadiator')} />
                Check radiator and coolant system
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.checkGPS} onChange={() => handleCheckboxChange('checkGPS')} />
                Check GPS
              </label>
              <label className="flex items-center gap-2">
                <input type="checkbox" checked={form.notes.roadTest} onChange={() => handleCheckboxChange('roadTest')} />
                Road test
              </label>
            </div>
            <div className="mt-4">
              <Label htmlFor="notesComment" className="text-sm text-gray-600">Comment</Label>
              <Textarea
                id="notesComment"
                placeholder="Type Here ---"
                value={notesComment}
                onChange={e => setNotesComment(e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Submit Buttons */}
        <div className="flex justify-end space-x-4 pt-4">
          <Button
            type="submit"
            className="bg-[#330101] hover:bg-gray-800 text-white px-8 py-2 w-32 text-sm"
          >
            Update
          </Button>
          <Button
            type="button"
            variant="outline"
            onClick={handleCancel}
            className="px-8 py-2 w-32 text-sm"
          >
            Cancel
          </Button>
        </div>
      </form>
    </div>
  );
}