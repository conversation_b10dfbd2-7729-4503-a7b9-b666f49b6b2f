import React, { useState } from "react";
import { useNavigate } from "react-router-dom";
import { ArrowLeft, Eye, Plus } from "lucide-react";
import { Label } from "@/components/ui/label";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Select, SelectTrigger, SelectValue, SelectContent, SelectItem } from "@/components/ui/select";
import { useAccidentServiceEdit } from "./hook/useaccident-service-edit";

export function AccidentServiceEditPage() {
  const navigate = useNavigate();
  const { formData, handleInputChange, handleImageChange } = useAccidentServiceEdit();
  const [selectedImage, setSelectedImage] = useState<string | null>(null);

  // Status badge helpers
  const getStatusBadge = (status: string) => {
    switch (status) {
      case "Pending":
        return "bg-gray-400 text-white";
      case "In Progress":
        return "bg-blue-500 text-white";
      case "Done":
        return "bg-green-500 text-white";
      default:
        return "bg-gray-200 text-gray-700";
    }
  };
  const getInsuranceColor = (status: string) => {
    switch (status) {
      case "Initial Review":
        return "bg-yellow-500 text-white";
      case "Declined":
        return "bg-red-500 text-white";
      case "Accepted":
        return "bg-green-500 text-white";
      default:
        return "bg-gray-200 text-gray-700";
    }
  };

  return (
    <div className="min-h-screen p-0 md:p-0">
      {/* Back Button - Desktop only */}
      <div className="hidden md:flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <Button
          className="w-full sm:w-auto px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center"
          size="sm"
          onClick={() => navigate('/mechanic/accident-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
      </div>

      {/* Mobile/Tablet Card View */}
      <div className="block md:hidden">
        <Button
          className="w-full mb-4 px-4 py-2 bg-[#330101] text-white rounded hover:bg-[#660404] transition-colors flex items-center justify-center"
          size="sm"
          onClick={() => navigate('/mechanic/accident-service')}
        >
          <ArrowLeft className="h-4 w-4 mr-2" />
          <span>Go Back</span>
        </Button>
        {/* Vehicle Details */}
        <div className="bg-white rounded-lg mb-6 p-4">
          <h1 className="text-xl font-medium mb-2">Vehicle Details</h1>
          <div className="space-y-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Vehicle ID</Label>
              <Input
                value={formData.vehicleId}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Model</Label>
              <Input
                value={formData.model}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Reg No</Label>
              <Input
                value={formData.regNo}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Description</Label>
              <Input
                value={formData.description}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        {/* Upload Images */}
        <div className="bg-white rounded-lg mb-6 p-4">
          <h2 className="text-xl font-medium mb-4">Upload Images</h2>
          <div className="space-y-4">
            {Object.entries(formData.images).map(([key, files]) => (
              <div className="relative" key={key}>
                <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  {key.replace(/([A-Z])/g, " $1")}
                </Label>
                <div className="flex items-center gap-2 mt-2">
                  <div className="flex items-center p-2 border border-gray-400 rounded-md w-2/3 min-w-[180px] max-w-[500px]">
                    <span className="text-gray-600 flex-1 truncate">
                      {files.length > 0 ? files.join(", ") : "No images uploaded"}
                    </span>
                    {files[0] && (
                      <Button
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800"
                        onClick={() => setSelectedImage(files[0])}
                        title="View Image"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="border-gray-400"
                    title="Add Image"
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.multiple = true;
                      input.accept = 'image/*';
                      input.onchange = (event: Event) => {
                        const target = event.target as HTMLInputElement;
                        handleImageChange(key as keyof typeof formData.images, target.files);
                      };
                      input.click();
                    }}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>

        {/* Workshop Details */}
        <div className="bg-white rounded-lg mb-6 p-4">
          <div className="flex justify-between items-start mb-2">
            <h2 className="text-xl font-medium">Workshop Details</h2>
            <div className="flex flex-col gap-2 items-center">
              <span className={`px-3 py-1 rounded text-sm font-medium w-28 flex justify-center items-center ${getStatusBadge(formData.status)}`}>
                {formData.status}
              </span>
              <span
                className={`px-3 py-1 rounded text-sm font-medium w-28 flex justify-center items-center ${getInsuranceColor(formData.insuranceStatus)}`}
              >
                Insurance: {formData.insuranceStatus}
              </span>
            </div>
          </div>
          <div className="space-y-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Branch</Label>
              <Input
                value={formData.branch}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Mechanic Assigned</Label>
              <Input
                value={formData.mechanicAssigned.join(", ")}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Insurance Status</Label>
              <Input
                value={formData.insuranceStatus}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Insurance Claim Number</Label>
              <Input
                value={formData.insuranceClaimNumber}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Estimated End Date</Label>
              <Input
                type="date"
                value={formData.estimatedEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Actual End Date</Label>
              <Input
                type="date"
                value={formData.actualEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
              <Input
                value={formData.status}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input
                value={formData.comment}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
            </div>
          </div>
        </div>

        {/* Mechanic Note */}
        <div className="bg-white rounded-lg mb-6 p-4">
          <h2 className="text-xl font-medium mb-4">Mechanic Note</h2>
          <div className="space-y-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Status</Label>
              <Select
                value={formData.mechanicNoteStatus}
                onValueChange={value => handleInputChange("mechanicNoteStatus", value)}
              >
                <SelectTrigger className="w-full p-2 border rounded focus:border-transparent text-sm mt-2">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Done">Done</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">Comment</Label>
              <Input
                value={formData.mechanicNoteComment}
                onChange={e => handleInputChange("mechanicNoteComment", e.target.value)}
              />
            </div>
          </div>
        </div>

        {/* Buttons */}
        <div className="flex flex-row gap-4 mt-6 px-4">
          <Button className="bg-[#330101] text-white w-full" onClick={() => {/* handle update */}}>Update</Button>
          <Button className="w-full" variant="outline" onClick={() => navigate("/mechanic/accident-service")}>Cancel</Button>
        </div>
      </div>

      {/* Desktop View (md and up) */}
      <div className="hidden md:block">
        <div className="bg-white rounded-lg mb-6 p-4">
          <h1 className="text-xl sm:text-2xl font-medium mb-4">Vehicle Details</h1>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Input
                value={formData.vehicleId}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Vehicle ID</Label>
            </div>
            <div className="relative">
              <Input
                value={formData.model}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Model</Label>
            </div>
            <div className="relative">
              <Input
                value={formData.regNo}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Reg No</Label>
            </div>
          </div>
          <div className="relative mb-4">
            <Input
              value={formData.description}
              readOnly
              className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
            />
            <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Description</Label>
          </div>
        </div>
        <div className="bg-white rounded-lg mb-6 p-4">
          <h2 className="text-xl font-medium mb-4">Upload Images</h2>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
            {Object.entries(formData.images).map(([key, files]) => (
              <div className="relative" key={key}>
                <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                  {key.replace(/([A-Z])/g, " $1")}
                </Label>
                <div className="flex items-center gap-2">
                  <div className="flex items-center p-2 border border-gray-400 rounded-md w-2/3 min-w-[180px] max-w-[500px]">
                    <span className="text-gray-600 flex-1 truncate">
                      {files.length > 0 ? files.join(", ") : "No images uploaded"}
                    </span>
                    {files[0] && (
                      <Button
                        variant="ghost"
                        className="text-gray-600 hover:text-gray-800"
                        onClick={() => setSelectedImage(files[0])}
                        title="View Image"
                      >
                        <Eye className="w-4 h-4" />
                      </Button>
                    )}
                  </div>
                  <Button
                    type="button"
                    variant="outline"
                    size="icon"
                    className="border-gray-400"
                    title="Add Image"
                    onClick={() => {
                      const input = document.createElement('input');
                      input.type = 'file';
                      input.multiple = true;
                      input.accept = 'image/*';
                      input.onchange = (event: Event) => {
                        const target = event.target as HTMLInputElement;
                        handleImageChange(key as keyof typeof formData.images, target.files);
                      };
                      input.click();
                    }}
                  >
                    <Plus className="w-4 h-4" />
                  </Button>
                </div>
              </div>
            ))}
          </div>
        </div>
        <div className="bg-white rounded-lg mb-6 p-4">
          <div className="flex justify-between items-start mb-4">
            <h2 className="text-xl font-medium">Workshop Details</h2>
            
          </div>
          <div className="grid grid-cols-1 sm:grid-cols-2 gap-4 mb-4">
            <div className="relative">
              <Input
                value={formData.branch}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Branch</Label>
            </div>
            <div className="relative">
              <Input
                value={formData.mechanicAssigned.join(", ")}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Mechanic Assigned</Label>
            </div>
            <div className="relative">
              <Input
                value={formData.insuranceStatus}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Insurance Status</Label>
            </div>
            <div className="relative">
              <Input
                value={formData.insuranceClaimNumber}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Insurance Claim Number</Label>
            </div>
            <div className="relative">
              <Input
                type="date"
                value={formData.estimatedEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Estimated End Date</Label>
            </div>
            <div className="relative">
              <Input
                type="date"
                value={formData.actualEndDate}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Actual End Date</Label>
            </div>
            <div className="relative">
              <Input
                value={formData.status}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Status</Label>
            </div>
            <div className="relative">
              <Input
                value={formData.comment}
                readOnly
                className="bg-gray-100 w-full p-2 border border-gray-500 rounded-md focus:outline-none focus:ring-1 focus:ring-[#FF6F3D] focus:border-transparent text-sm"
              />
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Comment</Label>
            </div>
          </div>
        </div>
        <div className="bg-white rounded-lg mb-6 p-4">
          <h2 className="text-xl font-medium mb-4">Mechanic Note</h2>
          <div className="grid grid-cols-1 sm:grid-cols-3 gap-4 mb-4">
            <div className="relative">
              <Label className="absolute left-2 top-[-7px] bg-white px-1 text-xs text-gray-600">
                Status
              </Label>
              <Select
                value={formData.mechanicNoteStatus}
                onValueChange={value => handleInputChange("mechanicNoteStatus", value)}
              >
                <SelectTrigger className="w-full p-2 border rounded focus:border-transparent text-sm">
                  <SelectValue placeholder="Select status" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="Done">Done</SelectItem>
                  <SelectItem value="Pending">Pending</SelectItem>
                  <SelectItem value="In Progress">In Progress</SelectItem>
                </SelectContent>
              </Select>
            </div></div>
            <div className="col-span-2 relative">
              <Label className="absolute left-2 -top-2 text-xs text-gray-700 bg-white px-1">Comment</Label>
              <Input
                value={formData.mechanicNoteComment}
                onChange={e => handleInputChange("mechanicNoteComment", e.target.value)}
              />
            
          </div>
          <div className="flex gap-4 mt-6">
            <Button className="bg-[#330101] text-white" onClick={() => {/* handle update */}}>Update</Button>
            <Button variant="outline" onClick={() => navigate("/mechanic/accident-service")}>Cancel</Button>
          </div>
        </div>
      </div>

      {/* Image Preview Modal */}
      {selectedImage && (
        <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
          <div className="bg-white p-4 rounded-lg">
            <img src={selectedImage} alt="Preview" className="max-w-full max-h-96" />
            <Button
              variant="outline"
              className="mt-4 w-full"
              onClick={() => setSelectedImage(null)}
            >
              Close
            </Button>
          </div>
        </div>
      )}
    </div>
  );
}