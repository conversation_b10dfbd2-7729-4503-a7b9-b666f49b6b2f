import { useState } from 'react';
import { NavigateFunction } from 'react-router-dom';
import { mockEditProfile } from '../common/mockdata';

export const useEditProfile = (initialData: any = mockEditProfile) => {
  const [profilePhoto, setProfilePhoto] = useState<File | null>(null);
  const [idPhoto, setIdPhoto] = useState<File | null>(null);
  const [isCorporate, setIsCorporate] = useState<boolean | null>(initialData.isCorporate);
  const [editAddress, setEditAddress] = useState(initialData.address);
  const [editPostCode, setEditPostCode] = useState(initialData.postCode);
  const [editCountry, setEditCountry] = useState(initialData.country);
  const [editBirthday, setEditBirthday] = useState(initialData.birthday);
  const [editphonenumber, setEditPhoneNumber] = useState(initialData.phonenumber.toString());
  const [editcompany, setEditCompany] = useState(initialData.companyName);
  const [editEmergencyContactName, setEditEmergencyContactName] = useState(initialData.emergencyContactName);
  const [editEmergencyContactPhone, setEditEmergencyContactPhone] = useState(initialData.emergencyContactPhone);

  const handleProfilePhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setProfilePhoto(event.target.files[0]);
    }
  };

  const handleIdPhotoChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files && event.target.files[0]) {
      setIdPhoto(event.target.files[0]);
    }
  };

  const handleBack = (navigate: NavigateFunction) => {
    navigate(-1);
  };

  const handleSubmit = (navigate: NavigateFunction) => {
    console.log('Profile updated:', { editAddress, editPostCode, editCountry, editEmergencyContactName, editEmergencyContactPhone });
    navigate(-1);
  };

  return {
    profilePhoto,
    idPhoto,
    isCorporate,
    setIsCorporate,
    editAddress,
    setEditAddress,
    editPostCode,
    setEditPostCode,
    editCountry,
    setEditCountry,
    editBirthday,
    setEditBirthday,
    editphonenumber,
    setEditPhoneNumber,
    editcompany,
    setEditCompany,
    editEmergencyContactName,
    setEditEmergencyContactName,
    editEmergencyContactPhone,
    setEditEmergencyContactPhone,
    handleProfilePhotoChange,
    handleIdPhotoChange,
    handleBack,
    handleSubmit,
  };
};