import { BrowserRouter, Routes, Route, Navigate } from 'react-router-dom'
import { LoginPage } from './pages/login'
import { ForgotPasswordPage } from './pages/forgot-password'
import { ForgotPasswordSmsPage } from './pages/forgot-password-sms'
import { ForgotPasswordEmailPage } from './pages/forgot-password-email'
import { VerifyOtpPage } from './pages/verify-otp'
import { Signup } from './pages/signup'
import { ResetPassword } from './pages/reset-password'
import { CustomerRoutes, AdminRoutes, StaffRoutes} from './routes'
import { ToastContainer } from 'react-toastify';
import 'react-toastify/dist/ReactToastify.css';
import { SearchRoutes } from './routes/searchForm/search-routes'
import { ReceptionRoutes } from './routes/reception/reception-routes'
import { TeamLeaderRoutes } from './routes/teamleader/teamleader-routes'
import { MechanicRoutes } from './routes/mechanic/mechanic-routes'
import 'react-phone-input-2/lib/style.css';




export function App() {
  return (
    <BrowserRouter>
     <ToastContainer
        position="top-center"
        autoClose={3000}
        hideProgressBar={false}
        closeOnClick
        pauseOnHover
        draggable
      />
      <Routes>
        <Route path="/" element={<Navigate to="/login" replace />} />
        <Route path="/login" element={<LoginPage />} />
        <Route path="/forgot-password" element={<ForgotPasswordPage />} />
        <Route
          path="/forgot-password/sms"
          element={<ForgotPasswordSmsPage />}
        />
        <Route
          path="/forgot-password/email"
          element={<ForgotPasswordEmailPage />}
        />
        <Route path="/verify-otp" element={<VerifyOtpPage />} />
        <Route path="/verify-otp-email" element={<VerifyOtpPage />} />
        <Route path="/signup" element={<Signup/>} />
        <Route path="/reset-password" element={<ResetPassword/>}/>
        
        {/* Role-based routing */}
        <Route path="/customer/*" element={<CustomerRoutes />} />
        <Route path="/admin/*" element={<AdminRoutes />} />
        <Route path="/staff/*" element={<StaffRoutes />} />
        <Route path="/reception/*" element={<ReceptionRoutes/>} />
        <Route path="/teamleader/*" element={<TeamLeaderRoutes/>}/>
        <Route path="/mechanic/*" element={<MechanicRoutes/>}/>
        
        {/* Legacy routes redirect to customer routes for backward compatibility */}
        <Route path="/dashboard" element={<Navigate to="/customer/dashboard" replace />} />
        <Route path="/profile" element={<Navigate to="/customer/profile" replace />} />
        <Route path="/add-rental" element={<Navigate to="/customer/add-rental" replace />} />
        <Route path="/booking-history" element={<Navigate to="/customer/booking-history" replace />} />
        <Route path="/incident-reporting" element={<Navigate to="/customer/incident-reporting" replace />} />
        <Route path="/change-requests" element={<Navigate to="/customer/change-requests" replace />} />
        <Route path="/invoices" element={<Navigate to="/customer/invoices" replace />} />
        <Route path="/fines" element={<Navigate to="/customer/fines" replace />} />
        <Route path="/notifications" element={<Navigate to="/customer/notifications" replace />} />
        <Route path="/activity-log" element={<Navigate to="/customer/activity-log" replace />} />
        <Route path="/settings" element={<Navigate to="/customer/settings" replace />} />


        {/* Search routes */}
        <Route path="/search/*" element={<SearchRoutes />} />
        
       {/* Legacy routes redirect to customer routes for backward compatibility */}
        <Route path="/reception-reservantionmanagement" element={<Navigate to="/reception/reception-reservantionmanagement" replace />} />
        <Route path="/reception-bookingsummery" element={<Navigate to="/reception/reception-bookingsummery" replace />} />
        

      </Routes>
    </BrowserRouter>
  )
}
