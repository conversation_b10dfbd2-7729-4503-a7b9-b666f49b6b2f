import React, { useState } from 'react';
import { Search, Plus, FileText, CalendarDays } from 'lucide-react';
import { Pagination } from '@/components/layout/Pagination';
import { Button } from '@/components/ui/button';
import { Input } from '@/components/ui/input';
import { useNavigate } from 'react-router-dom';

import { Table, TableHeader, TableBody, TableRow, TableHead, TableCell } from '@/components/ui/table'; 

export function ReceptionReservationManagement() {
  const navigate = useNavigate();
  const [searchTerm, setSearchTerm] = useState('');
  const [activeTab, setActiveTab] = useState('All');
  const [filterType, setFilterType] = useState('Filter');
  

  const tabButtons = [
    'All', 'Website Bookings', "Today's Returns", "Tomorrow's Pickups", 
    'Today\'s Pickups', 'Tomorrow\'s Returns', 'On Rent', 'Completed', 
    'Cancelled', 'Outstanding Payment'
  ];

  const reservationData = [
     {
      rentalId: 'R0035',
      customer: 'Smith',
      pickupDate: '18-07-2025 11:30',
      returnDate: '20-07-2025 14:30',
      category: 'Passenger',
      vehicleClass: 'Economy Plus Car',
      vehicle: 'AWZ 802',
      totalPrice: '$650.00',
      totalRevenue: '$650.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$550.00',
      status: 'Open',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
     {
      rentalId: 'R0034',
      customer: 'Julia Fernando',
      pickupDate: '17-07-2025 11:30',
      returnDate: '18-07-2025 14:30',
      category: 'Passenger',
      vehicleClass: 'Economy Plus Car',
      vehicle: 'QCB 456',
      totalPrice: '$592.00',
      totalRevenue: '$592.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$492.00',
      status: 'Rental',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
     {
      rentalId: 'R0033',
      customer: 'Thisara',
      pickupDate: '15-07-2025 11:30',
      returnDate: '17-07-2025 14:30',
      category: 'Passenger',
      vehicleClass: 'Economy Plus Car',
      vehicle: 'QOB 115',
      totalPrice: '$502.00',
      totalRevenue: '$502.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$402.00',
      status: 'Completed',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0026',
      customer: 'Davidson Thomson',
      pickupDate: '15-06-2025 11:30',
      returnDate: '14-06-2025 14:30',
      category: 'Passenger',
      vehicleClass: 'Economy Plus Car',
      vehicle: 'PCB 456',
      totalPrice: '$592.00',
      totalRevenue: '$592.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$492.00',
      status: 'Rental',
      walkInCustomer: 'No',
      loanVehicle: 'Yes',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0009',
      customer: 'Igor Peets',
      pickupDate: '02-03-2025 06:16',
      returnDate: '10-03-2025 04:30',
      category: 'Passenger',
      vehicleClass: 'Economy Premium',
      vehicle: 'Isuzu Nhr 45 150 - 2AC5GR',
      totalPrice: '$550.00',
      totalRevenue: '$550.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$450.00',
      status: 'Open',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0015',
      customer: 'Alyssa Dorschad',
      pickupDate: '11-01-2025 07:30',
      returnDate: '24-01-2025 11:30',
      category: 'Commercial',
      vehicleClass: 'SUV 7 Seats Premium',
      vehicle: 'Toyota Hiace Commuter - 2BH8BD',
      totalPrice: '$592.00',
      totalRevenue: '$592.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$492.00',
      status: 'Completed',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0017',
      customer: 'Jaymar Elvin',
      pickupDate: '20-12-2024 21:16',
      returnDate: '24-12-2024 21:16',
      category: 'Passenger',
      vehicleClass: 'Mini Bus 12 Seats',
      vehicle: 'DEF 456',
      totalPrice: '$548.00',
      totalRevenue: '$548.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$448.00',
      status: 'Cancelled',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    },
    {
      rentalId: 'R0022',
      customer: 'Juliet Semathrna',
      pickupDate: '11-10-2024 21:16',
      returnDate: '14-11-2024 21:16',
      category: 'Passenger',
      vehicleClass: 'Economy Plus',
      vehicle: 'GHI 987',
      totalPrice: '$548.00',
      totalRevenue: '$548.00',
      totalPaid: '$100.00',
      totalRefunded: '$0.00',
      outstandingBalance: '$448.00',
      status: 'Open',
      walkInCustomer: 'No',
      loanVehicle: 'No',
      reservationType: 'Short Term'
    }
  ];

  // Helper function to check if a date is today
  const isToday = (dateString: string) => {
    const today = new Date();
    const date = new Date(dateString.split(' ')[0].split('-').reverse().join('-'));
    return date.toDateString() === today.toDateString();
  };

  // Helper function to check if a date is tomorrow
  const isTomorrow = (dateString: string) => {
    const tomorrow = new Date();
    tomorrow.setDate(tomorrow.getDate() + 1);
    const date = new Date(dateString.split(' ')[0].split('-').reverse().join('-'));
    return date.toDateString() === tomorrow.toDateString();
  };

  // Filter data based on active tab
  const getFilteredDataByTab = () => {
    switch (activeTab) {
      case 'Website Bookings':
        return reservationData.filter(item => ['Open', 'Rental'].includes(item.status));
      
      case "Today's Pickups":
        return reservationData.filter(item => 
          isToday(item.pickupDate) && ['Rental', 'Open', 'Cancelled'].includes(item.status)
        );
      
      case "Tomorrow's Pickups":
        return reservationData.filter(item => 
          isTomorrow(item.pickupDate) && item.status === 'Open'
        );
      
      case "Today's Returns":
        return reservationData.filter(item => 
          isToday(item.returnDate) && ['Completed', 'Rental', 'Cancelled'].includes(item.status)
        );
      
      case "Tomorrow's Returns":
        return reservationData.filter(item => 
          isTomorrow(item.returnDate)
        );
      
      case 'On Rent':
        return reservationData.filter(item => item.status === 'Rental');
      
      case 'Completed':
        return reservationData.filter(item => item.status === 'Completed');
      
      case 'Cancelled':
        return reservationData.filter(item => item.status === 'Cancelled');
      
      case 'Outstanding Payment':
        return reservationData.filter(item => 
          parseFloat(item.outstandingBalance.replace('$', '')) > 0 && 
          ['Open', 'Cancelled'].includes(item.status)
        );
      
      default: // 'All'
        return reservationData;
    }
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Rental':
        return 'bg-[#1FA042] text-white rounded-md';
      case 'Open':
        return 'bg-[#F2CF05] text-white rounded-md';
      case 'Completed':
        return 'bg-[#2B409A] text-white rounded-md';
      case 'Cancelled':
        return 'bg-[#BD2D2D] text-white rounded-md';
      default:
        return 'bg-gray-100 text-black rounded-md';
    }
  };

  const getYesNoColor = (value: string) => {
    return value === 'Yes' 
      ? 'bg-[#1FA042] text-white rounded-md' 
      : 'bg-[#BD2D2D] text-white rounded-md';
  };

  const tabFilteredData = getFilteredDataByTab();
  const filteredData = tabFilteredData.filter(item =>
    item.customer.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.rentalId.toLowerCase().includes(searchTerm.toLowerCase()) ||
    item.vehicle.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const [currentPage, setCurrentPage] = useState(1);
  const [recordsPerPage, setRecordsPerPage] = useState(5);

  const totalRecords = filteredData.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentReservations = filteredData.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  const handleEditClick = (rentalId: string) => {
    // Remove the # from the ID and navigate to edit page
    const cleanId = rentalId.replace('#', '');
    navigate(`/reception/reception-fines/reception-editFine/${cleanId}`);
  };

  return (
    <div className="min-h-screen p-6">
      <div className="bg-white rounded-lg shadow-sm">
        {/* Header - Fixed width, no scroll */}
        <div className="border-b border-gray-200 p-4">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center gap-2">
              <CalendarDays className="text-gray-600 w-5 h-5" />
              <h1 className="text-xl font-semibold text-gray-800">Reservation Management</h1>
            </div>
            <div className="flex items-center gap-3">
              <Button className="px-4 py-2 bg-white text-black border border-gray-400 rounded-md transition-colors flex items-center gap-2"
              onClick={() => navigate('/reception/reception-checkavailability')}>
                <Search className="w-4 h-4" />
                Availability Check
              </Button>
              <Button className="px-4 py-2 bg-[#330101] text-white rounded-md transition-colors flex items-center gap-2">
                <Plus className="w-4 h-4" />
                Add New Reservation
              </Button>
            </div>
          </div>
          
          {/* Tab Buttons */}
          <div className="flex flex-wrap gap-2 mb-4">
            {tabButtons.map((tab) => (
              <Button
                key={tab}
                onClick={() => {
                  setActiveTab(tab);
                  setCurrentPage(1); // Reset to first page when changing tabs
                }}
                className={`px-3 py-1 rounded-md text-sm transition-colors ${
                  activeTab === tab
                    ? 'bg-blue-100 text-blue-700 border border-blue-300'
                    : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                }`}
              >
                {tab}
              </Button>
            ))}
          </div>
          
          {/* Search and Filter */}
          <div className="flex items-center gap-4">
            <div className="relative">
              <select 
                value={filterType}
                onChange={(e) => setFilterType(e.target.value)}
                className="px-3 py-2 border border-gray-300 rounded-md bg-white text-sm focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option>Filter</option>
                <option>Passenger</option>
                <option>Commercial</option>
                <option>Rental</option>
                <option>Completed</option>
                <option>Overdue</option>
              </select>
            </div>
            
            <div className="relative flex-1 max-w-full">
              <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
              <Input
                type="text"
                placeholder="Start typing here..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-full pl-10 pr-4 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
            
            <button className="px-4 py-2 bg-gray-100 text-gray-600 rounded-md hover:bg-gray-200 transition-colors">
              Save this search
            </button>
          </div>
        </div>

        {/* Table Container with Horizontal Scroll */}
        <div className="overflow-x-auto">
          <Table className="w-full min-w-[2000px]">
            <TableHeader>
              <TableRow className="bg-gray-50 border-b border-gray-200">
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[80px]">
                  Rental ID
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]">
                  Customer
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[130px]">
                  Pickup Date
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[130px]">
                  Return Date
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Category
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[150px]">
                  Vehicle Class
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Vehicle
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Total Price
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[110px]">
                  Total Revenue
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Total Paid
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Total Refunded
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Outstanding Balance
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Status
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Walk In Customer?
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Loan Vehicle
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[120px]">
                  Reservation Type
                </TableHead>
                <TableHead className="px-3 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider min-w-[100px]">
                  Action
                </TableHead>
              </TableRow>
            </TableHeader>
            <TableBody className="bg-white divide-y divide-gray-200">
              {currentReservations.map((item, index) => (
                <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.rentalId}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-blue-600" onClick={() => navigate(`reception-bookingsummary/${item.rentalId}`)}>{item.customer}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.pickupDate}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.returnDate}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.category}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.vehicleClass}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.vehicle}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalPrice}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalRevenue}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalPaid}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.totalRefunded}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-blue-600 cursor-pointer" onClick={() => navigate(`reception-outstanding/${item.rentalId}`)}>{item.outstandingBalance}</TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getStatusColor(item.status)}`}>
                      {item.status}
                    </span>
                  </TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getYesNoColor(item.walkInCustomer)}`}>
                      {item.walkInCustomer}
                    </span>
                  </TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap">
                    <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${getYesNoColor(item.loanVehicle)}`}>
                      {item.loanVehicle}
                    </span>
                  </TableCell>
                  <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">{item.reservationType}</TableCell>
                <TableCell className="px-3 py-4 whitespace-nowrap text-sm text-gray-900">
  <div className="flex space-x-2 justify-end">
    <Button
      onClick={() => handleEditClick(item.rentalId)}
      variant="ghost"
      className="text-gray-600 hover:text-gray-800 p-2"
    >
      <FileText className="w-4 h-4" />
    </Button>
    <Button
      onClick={() => handleEditClick(item.rentalId)}
      variant="ghost"
      className="text-gray-600 hover:text-gray-800 p-2"
    >
      <FileText className="w-4 h-4" />
    </Button>
  </div>
</TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </div>

        {/* Pagination */}
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalRecords}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
          className="mt-6"
        />
      </div>
    </div>
  );
}