import React, { useState } from 'react';
import { Search, Eye, Edit } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { Button } from '@/components/ui/button';
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from '@/components/ui/table';
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from '@/components/ui/select';
import { Pagination } from '@/components/layout/Pagination';
import { Card } from '@/components/ui/card';
import { useNavigate } from 'react-router-dom';

export function ServiceBreakdown() {
  const navigate = useNavigate();

  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState('All');
  const [activeTab, setActiveTab] = useState('Breakdowns');
  const [recordsPerPage, setRecordsPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  // Sample data for the table with new headers
  const breakdownData = [
    {
      vehicleId: 'VID0010',
      vehicle: 'Isuzu Npr 200 - AFS 009',
      incidentDate: '21-02-2025',
      actualDate: '-',
      estimatedDate: '25-02-2025',
      mechanicAssigned: 'Mike Smith',
      status: 'InProgress'
    },
    {
      vehicleId: 'VID0011',
      vehicle: 'Toyota Camry - ABC 123',
      incidentDate: '20-02-2025',
      actualDate: '-',
      estimatedDate: '23-02-2025',
      mechanicAssigned: 'John Doe',
      status: 'Pending'
    },
    {
      vehicleId: 'VID0012',
      vehicle: 'Honda Civic - XYZ 789',
      incidentDate: '19-02-2025',
      actualDate: '20-02-2025',
      estimatedDate: '22-02-2025',
      mechanicAssigned: 'Jane Smith',
      status: 'Done'
    },
  ];

  // Filter data based on search term and status
  const filteredData = breakdownData.filter((item) =>
    (item.vehicle.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.incidentDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.actualDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.estimatedDate.toLowerCase().includes(searchTerm.toLowerCase()) ||
      item.mechanicAssigned.toLowerCase().includes(searchTerm.toLowerCase())) &&
    (filterStatus === 'All' || item.status === filterStatus)
  );

  const handleVehicleIdClick = (vehicleId: string) => {
    navigate(`/admin/workshopMasterAdmin/service-breakdown-edit/${vehicleId}`);
  };

  // Calculate pagination
  const totalEntries = filteredData.length;
  const totalPages = Math.ceil(totalEntries / recordsPerPage);
  const startIndex = (currentPage - 1) * recordsPerPage;
  const endIndex = startIndex + recordsPerPage;
  const currentData = filteredData.slice(startIndex, endIndex);

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Done':
        return 'bg-green-500 text-white';
      case 'Pending':
        return 'bg-gray-400 text-white';
      case 'InProgress':
        return 'bg-blue-500 text-white';
      default:
        return 'bg-gray-300 text-gray-700';
    }
  };

  return (
    <div className="p-4 md:p-6 min-h-screen">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-4 md:mb-6">
        <h1 className="text-xl md:text-2xl font-bold text-earth-dark">Breakdowns</h1>
      </div>

      {/* Tab Bar */}
      <div className="flex flex-wrap gap-2 mb-4 md:mb-6">
        {['All', 'Due This Week', 'General Maintenance', 'Accident', 'Breakdowns', 'Damage'].map((tab) => (
          <Button
            key={tab}
            variant={activeTab === tab ? 'default' : 'outline'}
            onClick={() => {
              if (tab === 'All') {
                navigate('/admin/workshopMasterAdmin/service-management');
              } else if (tab === 'Accident') {
                navigate('/admin/workshopMasterAdmin/service-accident');
              } else if (tab === 'General Maintenance') {
                navigate('/admin/workshopMasterAdmin/service-maintenance');
              } else if (tab === 'Damage') {
                navigate('/admin/workshopMasterAdmin/service-damage');
              } else {
                setActiveTab(tab);
              }
            }}
            className="text-sm md:text-base"
          >
            {tab}
          </Button>
        ))}
      </div>

      {/* Search and Filter Bar */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 md:gap-4 mb-4 md:mb-6">
        <div className="relative w-full sm:w-auto">
          <Select value={filterStatus} onValueChange={(value) => setFilterStatus(value)}>
            <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Pending">Pending</SelectItem>
              <SelectItem value="InProgress">InProgress</SelectItem>
              <SelectItem value="Done">Done</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-gray-400 w-4 h-4" />
          <Input
            placeholder="Start typing a name..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 focus:outline-none focus:ring-0 focus:border-none"
          />
        </div>
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 whitespace-nowrap">
          Save this search
        </Button>
      </div>

      {/* Responsive Card View for xs, sm, md (below lg) */}
      <div className="lg:hidden space-y-4 mb-4">
        {filteredData.length === 0 && (
          <div className="text-center text-gray-500 py-8">No records found.</div>
        )}
        {filteredData.map((item, index) => (
          <Card key={index} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
            {/* Status badge - top right */}
            <div className="absolute top-4 right-4 flex flex-col items-end gap-1 min-w-[120px]">
              <span
                className={`inline-flex items-center justify-center rounded px-3 py-1.5 text-xs font-semibold w-[120px] text-center ${getStatusColor(
                  item.status
                )} whitespace-nowrap`}
              >
                {item.status}
              </span>
            </div>
            {/* Card content */}
            <div className="mb-3 pr-[110px]">
              <span
                className="text-blue-600 hover:text-blue-800 font-semibold cursor-pointer text-base"
                onClick={() => handleVehicleIdClick(item.vehicleId)}
              >
                {item.vehicleId}
              </span>
            </div>
            {/* Vehicle name under status badge, left aligned */}
            <div className="mb-2 mt-8 pl-0">
              <span className="text-gray-900 font-medium text-left block">{item.vehicle}</span>
            </div>
            <div className="mb-2 pl-0">
              <div className="text-xs text-gray-500 uppercase font-medium mb-1">Incident Date</div>
              <div className="text-sm">{item.incidentDate}</div>
            </div>
            <div className="grid grid-cols-2 gap-4 mb-2">
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Actual Date</div>
                <div className="text-sm">{item.actualDate}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Estimated Date</div>
                <div className="text-sm">{item.estimatedDate}</div>
              </div>
              <div>
                <div className="text-xs text-gray-500 uppercase font-medium mb-1">Mechanic Assigned</div>
                <div className="text-sm">{item.mechanicAssigned}</div>
              </div>
            </div>
            {/* Actions at the bottom */}
            <div className="flex justify-end gap-2 mt-4">
              {/* <Button variant="ghost" className="text-gray-600 hover:text-gray-800 mr-2" onClick={() => navigate(`/teamleader/service-viewbreakdown/${item.vehicle.split(' - ')[1]}`)}>
                <Eye className="w-4 h-4" />
              </Button> */}
              <Button variant="ghost" className="text-gray-600 hover:text-gray-800"
                onClick={() => handleVehicleIdClick(item.vehicleId)}>
                <Edit className="w-5 h-5" />
                <span className="ml-2 text-xs">Edit</span>
              </Button>
            </div>
          </Card>
        ))}
      </div>

      {/* Table View for lg and up */}
      <div className="hidden lg:block rounded-md border bg-white">
        <Table>
          <TableHeader>
            <TableRow className="bg-gray-50 uppercase">
              <TableHead>Vehicle ID</TableHead>
              <TableHead>Vehicle</TableHead>
              <TableHead>Incident Date</TableHead>
              <TableHead>Actual Date</TableHead>
              <TableHead>Estimated Date</TableHead>
              <TableHead>Mechanic Assigned</TableHead>
              <TableHead>Status</TableHead>
              <TableHead>Action</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {currentData.map((item, index) => (
              <TableRow key={index} className="hover:bg-gray-50 transition-colors">
                <TableCell>{item.vehicleId}</TableCell>
                <TableCell>{item.vehicle}</TableCell>
                <TableCell>{item.incidentDate}</TableCell>
                <TableCell>{item.actualDate}</TableCell>
                <TableCell>{item.estimatedDate}</TableCell>
                <TableCell>{item.mechanicAssigned}</TableCell>
                <TableCell className="px-3 py-4">
                  <span className={`inline-flex px-1 py-1 text-xs font-semibold w-28 justify-center rounded ${getStatusColor(item.status)}`}>
                    {item.status}
                  </span>
                </TableCell>
                <TableCell>
                  {/* <Button variant="ghost" className="text-gray-600 hover:text-gray-800 mr-2" onClick={() => navigate(`/teamleader/service-viewbreakdown/${item.vehicle.split(' - ')[1]}`)}>
                    <Eye className="w-4 h-4" />
                  </Button> */}
                  <Button variant="ghost" className="text-gray-600 hover:text-gray-800"
                    onClick={() => handleVehicleIdClick(item.vehicleId)}>
                    <Edit className="w-4 h-4" />
                  </Button>
                </TableCell>
              </TableRow>
            ))}
          </TableBody>
        </Table>
      </div>

      {/* Pagination only for lg and up */}
      <div className="mt-6 hidden lg:block">
        <Pagination
          currentPage={currentPage}
          totalPages={totalPages}
          totalRecords={totalEntries}
          recordsPerPage={recordsPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setRecordsPerPage(records);
            setCurrentPage(1);
          }}
        />
      </div>
    </div>
  );
}