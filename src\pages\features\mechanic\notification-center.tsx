import React, { useState } from "react";
import { Search, BellPlus } from "lucide-react"; // <-- Import BellPlus icon
import { Pagination } from "@/components/layout/Pagination";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";

const notifications = [
  {
    id: 1,
    type: "info",
    message:
      "<PERSON> assigned Rego ACX 221 for Accident service at 11.00 am, on 28th of May 2025",
    date: "2025-05-28",
  },
  {
    id: 2,
    type: "info",
    message:
      "<PERSON> assigned Rego ACX 221 for breakdown service at 11.00 am, on 25th of May 2025",
    date: "2025-05-25",
  },
  {
    id: 3,
    type: "info",
    message:
      "<PERSON> changed the service type from breakdown service to General  Maintenance at 2.30 pm, on 21st of May 2025",
    date: "2025-05-21",
  },
  {
    id: 4,
    type: "info",
    message:
      "<PERSON> left comment to Rego 1PX 1ZR at 4.00 pm, on 15th of May 2025",
    date: "2025-05-15",
  },
  {
    id: 5,
    type: "info",
    message:
      "<PERSON> changed the service type from breakdown service to General  Maintenance at 2.30 pm, on 21st of May 2025",
    date: "2025-05-21",
  },
];

export function NotificationCenter() {
  const [searchTerm, setSearchTerm] = useState<string>("");
  const [filterStatus, setFilterStatus] = useState<string>("All");
  const [recordsPerPage, setRecordsPerPage] = useState<number>(5);
  const [currentPage, setCurrentPage] = useState<number>(1);

  const isDateInRange = (
    notificationDate: string,
    filter: string
  ): boolean => {
    const today = new Date();
    const notifDate = new Date(notificationDate);

    switch (filter) {
      case "Today":
        return notifDate.toDateString() === today.toDateString();

      case "This Week":
        const weekStart = new Date(today);
        weekStart.setDate(today.getDate() - today.getDay());
        weekStart.setHours(0, 0, 0, 0);

        const weekEnd = new Date(weekStart);
        weekEnd.setDate(weekStart.getDate() + 6);
        weekEnd.setHours(23, 59, 59, 999);

        return notifDate >= weekStart && notifDate <= weekEnd;

      case "This Month":
        const monthStart = new Date(today.getFullYear(), today.getMonth(), 1);
        const monthEnd = new Date(today.getFullYear(), today.getMonth() + 1, 0);
        monthEnd.setHours(23, 59, 59, 999);

        return notifDate >= monthStart && notifDate <= monthEnd;

      case "Last 30 Days":
        const thirtyDaysAgo = new Date(today);
        thirtyDaysAgo.setDate(today.getDate() - 30);
        thirtyDaysAgo.setHours(0, 0, 0, 0);

        return notifDate >= thirtyDaysAgo && notifDate <= today;

      default:
        return true;
    }
  };

  // Filter notifications based on search and date filter
  const filteredNotifications = notifications.filter((notification) => {
    const matchesSearch =
      notification.message.toLowerCase().includes(searchTerm.toLowerCase()) ||
      notification.id.toString().includes(searchTerm.toLowerCase());
    const matchesFilter =
      filterStatus === "All" || isDateInRange(notification.date, filterStatus);
    return matchesSearch && matchesFilter;
  });

  // Pagination
  const totalRecords = filteredNotifications.length;
  const totalPages = Math.ceil(totalRecords / recordsPerPage);
  const currentNotifications = filteredNotifications.slice(
    (currentPage - 1) * recordsPerPage,
    currentPage * recordsPerPage
  );

  // Responsive breakpoint for mobile (below md)
  const isMobile = typeof window !== "undefined" && window.innerWidth < 768;

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Title */}
      <div className="flex items-center mb-4 sm:mb-6">
        <BellPlus className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3" style={{ color: "#4A0000" }} />
        <h1 className="text-xl sm:text-2xl font-bold text-gray-800">
          Notification Centre
        </h1>
      </div>

      {/* Search and Filter Bar */}
      {/* Mobile: filter, search, save (vertical); Desktop: existing order */}
      <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
        {/* Filter always first */}
        <div className="relative w-full sm:w-auto order-1">
          <Select
            value={filterStatus}
            onValueChange={(value) => setFilterStatus(value)}
          >
            <SelectTrigger className="focus:outline-none focus:ring-1 focus:ring-gray-300 focus:border-none">
              <SelectValue placeholder="Filter" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="All">All</SelectItem>
              <SelectItem value="Today">Today</SelectItem>
              <SelectItem value="This Week">This Week</SelectItem>
              <SelectItem value="This Month">This Month</SelectItem>
              <SelectItem value="Last 30 Days">Last 30 Days</SelectItem>
            </SelectContent>
          </Select>
        </div>
        {/* Search second on mobile, flex-1 on desktop */}
        <div className="relative w-full sm:flex-1 order-2">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
          <Input
            placeholder="Start typing here..."
            value={searchTerm}
            onChange={(e) => setSearchTerm(e.target.value)}
            className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
          />
        </div>
        {/* Save button last */}
        <Button className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap w-full sm:w-auto">
          <span className="hidden sm:inline">Save this search</span>
          <span className="sm:hidden">Save</span>
        </Button>
      </div>

      {/* Results Summary */}
      <div className="mb-3 sm:mb-4">
        {filterStatus !== "All" && (
          <span className="ml-1">
            filtered by{" "}
            <span className="font-medium">{filterStatus}</span>
          </span>
        )}
      </div>

      {/* Responsive Notifications List */}
      {/* Mobile: No pagination, show all filtered notifications */}
      <div className="block md:hidden space-y-3 sm:space-y-4">
        {filteredNotifications.length === 0 ? (
          <div className="text-center py-6 sm:py-8">
            <p className="text-gray-500 text-sm sm:text-base">
              No notifications found matching your criteria.
            </p>
          </div>
        ) : (
          filteredNotifications.map((notification) => (
            <div
              key={notification.id}
              className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow flex items-start space-x-2 sm:space-x-3"
            >
              <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#4A0000] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 sm:mt-0">
                <span className="text-white text-xs sm:text-sm font-medium">
                  i
                </span>
              </div>
              <div className="flex-1 min-w-0">
                <p className="text-gray-800 text-sm sm:text-base leading-relaxed break-words">
                  {notification.message}
                </p>
              </div>
            </div>
          ))
        )}
      </div>

      {/* Desktop: Pagination and paged notifications */}
      <div className="hidden md:block">
        <div className="space-y-3 sm:space-y-4">
          {currentNotifications.length === 0 ? (
            <div className="text-center py-6 sm:py-8">
              <p className="text-gray-500 text-sm sm:text-base">
                No notifications found matching your criteria.
              </p>
            </div>
          ) : (
            currentNotifications.map((notification) => (
              <div
                key={notification.id}
                className="bg-white rounded-lg shadow-sm border border-gray-200 p-3 sm:p-4 hover:shadow-md transition-shadow flex items-start space-x-2 sm:space-x-3"
              >
                <div className="w-5 h-5 sm:w-6 sm:h-6 bg-[#4A0000] rounded-full flex items-center justify-center flex-shrink-0 mt-0.5 sm:mt-0">
                  <span className="text-white text-xs sm:text-sm font-medium">
                    i
                  </span>
                </div>
                <div className="flex-1 min-w-0">
                  <p className="text-gray-800 text-sm sm:text-base leading-relaxed break-words">
                    {notification.message}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
        {/* Pagination for desktop only */}
        {totalRecords > 0 && (
          <div className="mt-4 sm:mt-6">
            <Pagination
              currentPage={currentPage}
              totalPages={totalPages}
              totalRecords={totalRecords}
              recordsPerPage={recordsPerPage}
              onPageChange={setCurrentPage}
              onRecordsPerPageChange={(records) => {
                setRecordsPerPage(records);
                setCurrentPage(1);
              }}
              className="w-full"
            />
          </div>
        )}
      </div>
    </div>
  );
}