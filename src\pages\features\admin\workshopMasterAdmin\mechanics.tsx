import React, { useState } from 'react';
import { Search, UserCircle } from 'lucide-react';
import { Input } from '@/components/ui/input';
import { But<PERSON> } from '@/components/ui/button';
import { Pagination } from '@/components/layout/Pagination';
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from '@/components/ui/table';
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from '@/components/ui/select';
import { Card } from '@/components/ui/card';

interface Mechanic {
  id: string;
  name: string;
  phone: string;
  address: string;
  enabled: boolean;
}

export const Mechanics = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [filterStatus, setFilterStatus] = useState<string>('All');
  const [entriesPerPage, setEntriesPerPage] = useState(10);
  const [currentPage, setCurrentPage] = useState(1);

  const mechanics: Mechanic[] = [
    { id: 'MC008', name: '<PERSON>', phone: '0421 433 500', address: '123 Main St, Downtown', enabled: true },
    { id: 'MC007', name: '<PERSON>', phone: '0421 343 566', address: '456 Oak Ave, Midtown', enabled: true },
    { id: 'MC006', name: 'Mike Wilson', phone: '0421 400 982', address: '789 Pine Rd, Uptown', enabled: false },
    { id: 'MC005', name: 'Emily Davis', phone: '0421 550 126', address: '321 Elm St, Westside', enabled: true },
    { id: 'MC004', name: 'Robert Brown', phone: '0421 643 768', address: '654 Maple Dr, Eastside', enabled: true },
    { id: 'MC003', name: 'Lisa Garcia', phone: '0421 294 509', address: '987 Cedar Ln, Northside', enabled: false },
  ];

  const filteredMechanics = mechanics
    .filter((mechanic) =>
      mechanic.name.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mechanic.phone.toLowerCase().includes(searchTerm.toLowerCase()) ||
      mechanic.address.toLowerCase().includes(searchTerm.toLowerCase())
    )
    .filter((mechanic) =>
      filterStatus === 'All' ? true : mechanic.enabled === (filterStatus === 'Yes')
    );

  const totalEntries = filteredMechanics.length;
  const paginatedMechanics = filteredMechanics.slice(
    (currentPage - 1) * entriesPerPage,
    currentPage * entriesPerPage
  );

  // Helper for status badge color
  const getStatusColor = (enabled: boolean) =>
    enabled ? 'bg-blue-100 text-blue-800' : 'bg-red-100 text-red-800';

  return (
    <div className="p-3 sm:p-4 md:p-6 bg-white min-h-screen">
      {/* Header */}
      <div className="mb-6">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4 sm:mb-6 gap-3 sm:gap-0">
          <div className="flex items-center mb-4 sm:mb-0">
            <UserCircle className="w-5 h-5 sm:w-6 sm:h-6 mr-2 sm:mr-3 text-earth-dark" />
            <h1 className="text-xl sm:text-2xl font-bold text-earth-dark">Mechanics</h1>
          </div>
        </div>

        {/* Search and Filter Controls */}
        <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-2 sm:gap-4 mb-4 sm:mb-6">
          {/* On mobile: All filter first, then search input, then Save button */}
          <div className="relative w-full sm:w-auto order-1">
            <Select value={filterStatus} onValueChange={setFilterStatus}>
              <SelectTrigger className="h-12 border border-gray-200 focus:outline-none focus:ring-0 focus:border-gray-300">
                <SelectValue placeholder="All" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="All">All</SelectItem>
                <SelectItem value="Yes">Enabled</SelectItem>
                <SelectItem value="No">Disabled</SelectItem>
              </SelectContent>
            </Select>
          </div>

          <div className="relative flex-1 order-2">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400" />
            <Input
              placeholder="Search by name, phone, or address..."
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
              className="w-full pl-10 pr-4 py-2 rounded-md focus:border-transparent text-sm sm:text-base"
            />
          </div>
          <Button
            className="bg-gray-100 text-gray-700 hover:bg-gray-200 order-3 text-sm sm:text-base px-3 sm:px-4 py-2 whitespace-nowrap"
            onClick={() => alert('Save search functionality not implemented')}
          >
            <span className="hidden sm:inline">Save this search</span>
            <span className="sm:hidden">Save</span>
          </Button>
        </div>
      </div>

      {/* Table for Desktop */}
      <div className="hidden md:block rounded-md border overflow-hidden">
        <div className="overflow-x-auto">
          <Table>
            <TableHeader>
              <TableRow className="bg-gray-50 uppercase">
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Full Name</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Phone Number</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Address</TableHead>
                <TableHead className="text-xs lg:text-sm px-2 lg:px-4">Enabled</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {paginatedMechanics.length > 0 ? (
                paginatedMechanics.map((mechanic) => (
                  <TableRow key={mechanic.id} className="hover:bg-gray-50">
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{mechanic.name}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{mechanic.phone}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">{mechanic.address}</TableCell>
                    <TableCell className="text-xs lg:text-sm px-2 lg:px-4">
                      <span
                        className={`inline-flex px-2 py-1 text-xs font-medium rounded w-[90px] justify-center ${getStatusColor(mechanic.enabled)}`}
                      >
                        {mechanic.enabled ? 'Yes' : 'No'}
                      </span>
                    </TableCell>
                  </TableRow>
                ))
              ) : (
                <TableRow>
                  <TableCell colSpan={4} className="text-center text-sm text-gray-500 py-4">
                    No mechanics found.
                  </TableCell>
                </TableRow>
              )}
            </TableBody>
          </Table>
        </div>
      </div>

      {/* Mobile/Tablet Card View (xs, sm, md) */}
      <div className="md:hidden space-y-4 mb-4">
        {filteredMechanics.length > 0 ? (
          filteredMechanics.map((mechanic) => (
            <Card key={mechanic.id} className="bg-white rounded-lg shadow-md border border-gray-200 p-4 hover:shadow-lg transition-shadow relative">
              {/* Status badge - top right, fixed width */}
              <div className="absolute top-4 right-4 flex flex-col items-end gap-2">
                <span
                  className={`inline-flex items-center justify-center rounded px-3 py-1 text-xs font-semibold w-[90px] text-center ${getStatusColor(
                    mechanic.enabled
                  )} whitespace-nowrap`}
                >
                  {mechanic.enabled ? 'Enabled' : 'Disabled'}
                </span>
              </div>
              {/* Card content */}
              <div className="mb-3 pr-[100px]">
                <span className="text-sm font-medium text-gray-700 break-words whitespace-pre-line">
                  {mechanic.name}
                </span>
              </div>
              <div className="grid grid-cols-2 gap-4 mb-2">
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Phone Number</div>
                  <div className="flex items-center text-sm">{mechanic.phone}</div>
                </div>
                <div>
                  <div className="text-xs text-gray-500 uppercase font-medium mb-1">Address</div>
                  <div className="flex items-center text-sm">{mechanic.address}</div>
                </div>
              </div>
            </Card>
          ))
        ) : (
          <div className="text-center text-sm text-gray-500 py-4">No mechanics found.</div>
        )}
      </div>

      {/* Pagination only for md and up */}
      <div className="mt-4 sm:mt-6 hidden md:block">
        <Pagination
          currentPage={currentPage}
          totalPages={Math.ceil(totalEntries / entriesPerPage)}
          totalRecords={totalEntries}
          recordsPerPage={entriesPerPage}
          onPageChange={setCurrentPage}
          onRecordsPerPageChange={(records) => {
            setEntriesPerPage(records);
            setCurrentPage(1);
          }}
          className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-3 sm:gap-4"
        />
      </div>
    </div>
  );
};